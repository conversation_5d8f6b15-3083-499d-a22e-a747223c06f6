import pandas as pd

# 检查新模板
print("=== 建表模板.xlsx 列名 ===")
try:
    df_new = pd.read_excel('福建/建表模板.xlsx', sheet_name=0)
    columns_new = list(df_new.columns)
    for i, col in enumerate(columns_new):
        print(f"{i+1}. {col}")
except Exception as e:
    print(f"读取建表模板.xlsx出错: {str(e)}")

print("\n\n=== 三医数据采集标准梳理.xlsx 列名 ===")
try:
    df_old = pd.read_excel('福建/三医数据采集标准梳理.xlsx', sheet_name=0)
    columns_old = list(df_old.columns)
    for i, col in enumerate(columns_old):
        print(f"{i+1}. {col}")
except Exception as e:
    print(f"读取三医数据采集标准梳理.xlsx出错: {str(e)}")

# 比较两个文件的列
print("\n\n=== 列比较 ===")
try:
    # 在旧文件中但不在新文件中的列
    old_not_in_new = [col for col in columns_old if col not in columns_new]
    print(f"在三医数据采集标准梳理.xlsx中但不在建表模板.xlsx中的列: {old_not_in_new}")
    
    # 在新文件中但不在旧文件中的列
    new_not_in_old = [col for col in columns_new if col not in columns_old]
    print(f"在建表模板.xlsx中但不在三医数据采集标准梳理.xlsx中的列: {new_not_in_old}")
except Exception as e:
    print(f"比较列时出错: {str(e)}") 