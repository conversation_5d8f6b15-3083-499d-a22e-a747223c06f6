import pandas as pd
import os

# 打印当前工作目录和Excel文件路径
print("当前工作目录:", os.getcwd())
excel_path = "D:/work/demo/福建/质控模板/医院184质控sql添加包装模板.xlsx"
print("Excel文件路径:", excel_path)
print("Excel文件是否存在:", os.path.exists(excel_path))

try:
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    
    # 打印基本信息
    print("\n文件基本信息:")
    print("列名:", list(df.columns))
    print("行数:", len(df))
    
    # 检查"质控规则"列
    if "质控规则" in df.columns:
        print("\n找到'质控规则'列")
        # 打印前5个非空的质控规则
        print("\n前5个非空的质控规则示例:")
        count = 0
        for i, rule in enumerate(df["质控规则"]):
            if isinstance(rule, str) and rule.strip() and "SELECT" in rule.upper():
                print(f"\n规则 {i+1}:\n{rule}\n")
                print("-" * 80)
                count += 1
                if count >= 5:
                    break
        
        # 计算非空规则总数
        non_empty_rules = df["质控规则"].apply(lambda x: isinstance(x, str) and x.strip() != "" and "SELECT" in x.upper()).sum()
        print(f"\n质控规则列中包含SQL的非空规则总数: {non_empty_rules}")
    else:
        print("\n未找到'质控规则'列")
        # 尝试查找可能包含SQL的列
        for col in df.columns:
            non_empty_sql_count = df[col].apply(
                lambda x: isinstance(x, str) and "SELECT" in x.upper()
            ).sum()
            
            if non_empty_sql_count > 0:
                print(f"\n发现可能包含SQL的列: {col}，包含SQL的行数: {non_empty_sql_count}")
                # 打印前3个示例
                count = 0
                for i, val in enumerate(df[col]):
                    if isinstance(val, str) and "SELECT" in val.upper():
                        print(f"\n示例 {count+1} (行 {i+1}):\n{val}\n")
                        print("-" * 80)
                        count += 1
                        if count >= 3:
                            break
except Exception as e:
    print("读取Excel文件时出错:", str(e)) 