import pandas as pd
import jieba
import re
import os
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def validate_file_paths(paths):
    """验证文件路径是否存在"""
    for path in paths:
        if not os.path.exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

def load_abbreviations(excel_path):
    """加载缩略语对照表"""
    try:
        validate_file_paths([excel_path])
        df = pd.read_excel(excel_path, sheet_name='列表')
        if '中文名称' not in df.columns or '英文缩写' not in df.columns:
            raise ValueError("Excel文件缺少必要的列：'中文名称'或'英文缩写'")
        # 创建中文到英文缩写的映射字典
        abbr_dict = dict(zip(df['中文名称'], df['英文缩写']))
        logging.info(f"成功加载缩略语对照表，共{len(abbr_dict)}个映射")
        return abbr_dict
    except Exception as e:
        logging.error(f"加载缩略语对照表时出错: {str(e)}")
        raise

def preprocess_chinese_name(text):
    """去除指定符号：-、（、）、(、)"""
    # 定义要去除的符号
    pattern = r'[/*\-（）()]'

    return re.sub(pattern, '', text)

def convert_chinese_to_english(text, abbr_dict):
    """将中文转换为英文"""
    try:
        # 预处理：去除指定符号
        text = preprocess_chinese_name(text)
        # 使用jieba进行分词
        words = list(jieba.cut(text))
        
        # 转换每个词
        converted_words = []
        for word in words:
            # 如果词在缩略语字典中，使用对应的英文缩写
            if word in abbr_dict:
                converted_words.append(abbr_dict[word])
            else:
                # 否则保持原样（这里可以根据需要添加其他转换规则）
                converted_words.append(word)
        
        # 用下划线连接所有词并转换为小写
        result = '_'.join(converted_words).lower()
        return result
    except Exception as e:
        logging.error(f"转换文本时出错: {str(e)}")
        raise

def main():
    try:
        # 文件路径
        abbr_excel_path = r'D:\work\demo\福建\数据组-缩略语-20250414165127.xlsx'
        input_file_path = r'D:\work\demo\福建\中文名转换英文名.txt'
        output_excel_path = r'D:\work\demo\福建\中文名转换英文名结果.xlsx'  # 修改为.xlsx扩展名
        
        # 验证所有输入文件路径
        validate_file_paths([abbr_excel_path, input_file_path])
        
        # 加载缩略语对照表
        abbr_dict = load_abbreviations(abbr_excel_path)
        
        # 读取待转换的中文名
        with open(input_file_path, 'r', encoding='utf-8') as f:
            chinese_names = [line.strip() for line in f if line.strip()]
        
        logging.info(f"开始处理{len(chinese_names)}个中文名称")
        
        # 转换每个中文名
        results = []
        for i, name in enumerate(chinese_names, 1):
            try:
                english_name = convert_chinese_to_english(name, abbr_dict)
                results.append({
                    '中文名': name,
                    '英文名': english_name
                })
                if i % 100 == 0:  # 每处理100个名称显示一次进度
                    logging.info(f"已处理: {i}/{len(chinese_names)}")
            except Exception as e:
                logging.error(f"处理名称 '{name}' 时出错: {str(e)}")
                continue
        
        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(results)
        df.to_excel(output_excel_path, index=False)
        logging.info(f"转换完成，结果已保存到: {output_excel_path}")
        
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() 