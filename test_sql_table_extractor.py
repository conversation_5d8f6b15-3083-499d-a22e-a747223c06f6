#!/usr/bin/env python
# -*- coding: utf-8 -*-

import unittest
from sql_table_extractor import SQLTableExtractor

class TestSQLTableExtractor(unittest.TestCase):
    """测试SQL表名提取器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.extractor = SQLTableExtractor()
        
    def test_basic_select(self):
        """测试基本的SELECT语句"""
        sql = "SELECT * FROM TABLE1"
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(tables, ["TABLE1"])
        
    def test_multiple_tables(self):
        """测试多表查询"""
        sql = "SELECT * FROM TABLE1, TABLE2"
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2"])
        
    def test_join(self):
        """测试JOIN语句"""
        sql = """
        SELECT * FROM TABLE1 
        LEFT JOIN TABLE2 ON TABLE1.id = TABLE2.id
        INNER JOIN TABLE3 ON TABLE2.id = TABLE3.id
        """
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2", "TABLE3"])
        
    def test_subquery(self):
        """测试子查询"""
        sql = """
        SELECT * FROM (
            SELECT id FROM TABLE1 WHERE id IN (
                SELECT id FROM TABLE2
            )
        ) t1 JOIN TABLE3
        """
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2", "TABLE3"])
        
    def test_with_schema(self):
        """测试带schema的表名"""
        sql = "SELECT * FROM INSUCENT_DB.TABLE1, CUSTCENT_DB.TABLE2"
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2"])
        
    def test_with_alias(self):
        """测试带别名的表"""
        sql = "SELECT * FROM TABLE1 t1, TABLE2 AS t2"
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2"])
        
    def test_exists_not_exists(self):
        """测试EXISTS和NOT EXISTS子句"""
        sql = """
        SELECT * FROM TABLE1 WHERE EXISTS (
            SELECT 1 FROM TABLE2
        ) AND NOT EXISTS (
            SELECT 1 FROM TABLE3
        )
        """
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["TABLE1", "TABLE2", "TABLE3"])
        
    def test_complex_query(self):
        """测试复杂查询"""
        sql = """
        SELECT COUNT(1) FROM (
            SELECT A.UEBMI_ACCT_CRTYEAR_PAY, 
                   SUM(B.UEBMI_ACCT_CRTYEAR_PAY) SUMACCT,
                   A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID 
            FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B 
            WHERE A.YEAR = B.YEAR 
              AND A.PSN_NO = B.PSN_NO 
              AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID 
              AND A.VALI_FLAG = 1 
              AND B.VALI_FLAG = 1 
            GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, 
                     A.UEBMI_ACCT_CRTYEAR_PAY 
            HAVING UEBMI_ACCT_CRTYEAR_PAY != SUMACCT
        ) C
        """
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(sorted(tables), ["ACCT_PAY_DETL_D", "ACCT_PAY_LED_D"])
        
    def test_empty_input(self):
        """测试空输入"""
        sql = ""
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(tables, [])
        
    def test_invalid_sql(self):
        """测试无效的SQL"""
        sql = "This is not a SQL statement"
        tables = self.extractor.extract_tables(sql)
        self.assertEqual(tables, [])
        
if __name__ == '__main__':
    unittest.main() 