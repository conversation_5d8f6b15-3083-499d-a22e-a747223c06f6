-- 会诊明细记录表
CREATE TABLE  fct0_emr_consult_detail AS
SELECT
    -- 核心标识维度（以明细表字段为主）
    emr_consult_detail.rid,
    emr_consult_detail.consult_doc_sn,
    emr_consult_detail.consult_sn,
    emr_consult_detail.patient_no,
    emr_consult_detail.mdtrt_sn,

    -- 机构信息维度（以明细表字段为主）
    emr_consult_detail.org_name,
    emr_consult_detail.uscid,
    emr_consult_detail.sys_prdr_code,
    emr_consult_detail.sys_prdr_name,

    -- 会诊医生信息维度（来自明细表）
    emr_consult_detail.doc_code,
    emr_consult_detail.doc_name,
    emr_consult_detail.condoc_dept_code,
    emr_consult_detail.condoc_dept_name,
    emr_consult_detail.condoc_uscid,
    emr_consult_detail.condoc_org_name,
    emr_consult_detail.sort,
    emr_consult_detail.consult_ad,
    emr_consult_detail.consult_time,

    -- 患者就诊信息维度（来自info表）
    emr_consult_info.patient_name,
    emr_consult_info.patient_file_no,
    emr_consult_info.age_year,
    emr_consult_info.age_month,
    emr_consult_info.age_day,
    emr_consult_info.otp_ipt_flag,
    emr_consult_info.otp_ipt_name,
    emr_consult_info.ipt_no,
    emr_consult_info.otp_no,

    -- 科室病区信息维度（来自info表）
    emr_consult_info.dept_code,
    emr_consult_info.dept_name,
    emr_consult_info.wardarea_name,
    emr_consult_info.wardno,
    emr_consult_info.bedno,

    -- 诊断信息维度（来自info表）
    emr_consult_info.medi_rec_sum,
    emr_consult_info.asst_exam_rslt,
    emr_consult_info.tcm4d_rslt,
    emr_consult_info.prnp_trt,
    emr_consult_info.wm_diag_code,
    emr_consult_info.wm_diag_name,
    emr_consult_info.tcm_dise_code,
    emr_consult_info.tcm_dise_name,
    emr_consult_info.tcmsymp_code,
    emr_consult_info.tcmsymp_name,
    emr_consult_info.trt_proc_name,
    emr_consult_info.trt_proc_dscr,

    -- 会诊申请信息维度（来自info表）
    emr_consult_info.consult_type,
    emr_consult_info.consult_reason,
    emr_consult_info.consult_purp,
    emr_consult_info.con_apply_doc_code,
    emr_consult_info.con_apply_doc_name,
    emr_consult_info.con_apply_dept_code,
    emr_consult_info.con_apply_dept_name,
    emr_consult_info.con_apply_org_name,
    emr_consult_info.consult_rel,
    emr_consult_info.ele_apply_no,

    -- 时间维度
    emr_consult_detail.rec_time,
    emr_consult_detail.upload_time,
    emr_consult_detail.crte_time,
    emr_consult_detail.updt_time,

    -- 状态维度
    emr_consult_detail.state,
    emr_consult_detail.deleted,
    emr_consult_detail.deleted_time,
    emr_consult_detail.data_clct_prdr_name,
    emr_consult_detail.rec_code,
    emr_consult_detail.rec_name,
    emr_consult_detail.data_rank

FROM  emr_consult_detail
          LEFT JOIN  emr_consult_info ON emr_consult_detail.consult_sn = emr_consult_info.consult_sn
    AND emr_consult_detail.patient_no = emr_consult_info.patient_no
    AND emr_consult_detail.mdtrt_sn = emr_consult_info.mdtrt_sn
WHERE emr_consult_detail.deleted = '0';



-- 急诊留观病历记录信息
CREATE TABLE  fct0_emr_observmedi_info AS
SELECT
    -- 核心标识维度（以留观病历表为主）
    emr_observmedi.rid,
    emr_observmedi.observ_record_sno,
    emr_observmedi.patient_no,
    emr_observmedi.mdtrt_sn,

    -- 机构信息维度
    emr_observmedi.org_name,
    emr_observmedi.uscid,
    emr_observmedi.sys_prdr_code,
    emr_observmedi.sys_prdr_name,

    -- 患者基本信息维度
    emr_observmedi.patient_name,
    emr_observmedi.certno,
    emr_observmedi.psncert_type_code,
    emr_observmedi.psncert_type_name,
    emr_observmedi.age_year,
    emr_observmedi.age_month,
    emr_observmedi.age_day,

    -- 就诊信息维度
    emr_observmedi.otp_no,
    emr_observmedi.dept_code,
    emr_observmedi.dept_name,
    emr_observmedi.mdtrt_time,
    emr_observmedi.first_visit_flag,
    emr_observmedi.first_visit_name,
    emr_observmedi.observe_room_time,

    -- 病历记录维度
    emr_observmedi.course,
    emr_observmedi.chfcomp,
    emr_observmedi.algs_flag,
    emr_observmedi.algs_his,
    emr_observmedi.dise_now,
    emr_observmedi.past_his,
    emr_observmedi.phys_exam,
    emr_observmedi.assist_exam_proj,
    emr_observmedi.asst_exam_rslt,
    emr_observmedi.notice,

    -- 诊断信息维度
    emr_observmedi.tcm4d_rslt,
    emr_observmedi.discriminate_accord,
    emr_observmedi.prnp_trt,
    emr_observmedi.prel_wm_diag_code,
    emr_observmedi.prel_wm_diag_name,
    emr_observmedi.prel_tcm_dise_code,
    emr_observmedi.prel_tcm_dise_name,
    emr_observmedi.prel_tcmsymp_code,
    emr_observmedi.prel_tcmsymp_name,

    -- 手术操作信息维度（来自observoprn表）
    emr_observoprn.oprn_oprt_sn,
    emr_observoprn.oprn_oprt_code,
    emr_observoprn.oprn_oprt_name,
    emr_observoprn.oprn_oprt_part_code,
    emr_observoprn.oprn_oprt_part_name,
    emr_observoprn.itvt_name,
    emr_observoprn.oprn_oprt_mtd_dscr,
    emr_observoprn.oprn_oprt_times,
    emr_observoprn.oprn_oprt_time,

    -- 抢救信息维度（来自rescue表）
    emr_rescue.resc_rec_sno,
    emr_rescue.resc_begntime,
    emr_rescue.resc_endtime,
    emr_rescue.cond_chg,
    emr_rescue.resc_mes,
    emr_rescue.resc_psn_code_list,
    emr_rescue.resc_psn_name_list,
    emr_rescue.resc_proftechttl_code,
    emr_rescue.resc_proftechttl_name,

    -- 去向信息维度
    emr_observmedi.patient_to_place_code,
    emr_observmedi.patient_to_place_name,

    -- 医生信息维度
    emr_observmedi.doc_code,
    emr_observmedi.doc_name,

    -- 时间维度
    emr_observmedi.upload_time,
    emr_observmedi.crte_time,
    emr_observmedi.updt_time,

    -- 状态维度
    emr_observmedi.data_rank,
    emr_observmedi.deleted,
    emr_observmedi.deleted_time,
    emr_observmedi.data_clct_prdr_name

FROM  emr_observmedi
          LEFT JOIN  emr_observoprn ON emr_observmedi.observ_record_sno = emr_observoprn.observ_record_sno
    AND emr_observmedi.patient_no = emr_observoprn.patient_no
    AND emr_observmedi.mdtrt_sn = emr_observoprn.mdtrt_sn
          LEFT JOIN  emr_rescue ON emr_observmedi.patient_no = emr_rescue.patient_no
    AND emr_observmedi.mdtrt_sn = emr_rescue.mdtrt_sn
          LEFT JOIN  emr_resc_rec ON emr_rescue.resc_rec_sno = emr_resc_rec.resc_rec_sno
    AND emr_rescue.patient_no = emr_resc_rec.patient_no
    AND emr_rescue.mdtrt_sn = emr_resc_rec.mdtrt_sn
WHERE emr_observmedi.deleted = '0';

-- 剖宫产手术信息
CREATE TABLE  fct0_emr_pregnancy_info AS
SELECT
    -- 核心标识维度（以剖宫产手术记录表为主）
    emr_pregnancy.rid,
    emr_pregnancy.caesar_sn,
    emr_pregnancy.patient_no,
    emr_pregnancy.mdtrt_sn,
    emr_pregnancy.ipt_no,

    -- 机构信息维度
    emr_pregnancy.org_name,
    emr_pregnancy.uscid,
    emr_pregnancy.sys_prdr_code,
    emr_pregnancy.sys_prdr_name,

    -- 患者基本信息维度
    emr_pregnancy.preg_name,
    emr_pregnancy.age_year,
    emr_predelivery.height,
    emr_predelivery.pregnancy_weight,
    emr_predelivery.predelivery_wt,
    emr_predelivery.abde,

    -- 科室病区信息维度
    emr_pregnancy.dept_code,
    emr_pregnancy.dept_name,
    emr_pregnancy.wardarea_name,
    emr_pregnancy.wardno,
    emr_pregnancy.bedno,

    -- 待产信息维度（来自predelivery表）
    emr_predelivery.expectant_sn,
    emr_predelivery.expectant_time,
    emr_predelivery.prg_cnt,
    emr_predelivery.matn_cnt,
    emr_predelivery.last_mena_date,
    emr_predelivery.conception_form_code,
    emr_predelivery.conception_form_name,
    emr_predelivery.expect_deliver_date,
    emr_predelivery.preg_exam_flag,
    emr_predelivery.antenatal_exam_abnorm_flag,
    emr_predelivery.spe_case_rec_predelivery,
    emr_predelivery.past_his,
    emr_predelivery.oprn_his,
    emr_predelivery.pregnant_his,
    emr_predelivery.eval_futes_dtate,
    emr_predelivery.cephalic_dystocia_assess,
    emr_predelivery.exam_method_code,
    emr_predelivery.exam_mode_name,
    emr_predelivery.deal_plan,
    emr_predelivery.deliver_way_code,
    emr_predelivery.deliver_way_name,
    emr_predelivery.phys_exam,

    -- 手术信息维度
    emr_pregnancy.bfpn_diag_code,
    emr_pregnancy.bfpn_diag_name,
    emr_pregnancy.proc_indication,
    emr_pregnancy.oprn_oprt_code,
    emr_pregnancy.oprn_oprt_name,
    emr_pregnancy.oprn_begin_time,
    emr_pregnancy.anst_mtd_code,
    emr_pregnancy.anst_mtd_name,
    emr_pregnancy.anst_part,
    emr_pregnancy.anst_efft,
    emr_pregnancy.proc_proc_descr,
    emr_pregnancy.bleeding_num,
    emr_pregnancy.transfuse_blood_composition,
    emr_pregnancy.bld_amt,
    emr_pregnancy.transfuse_num,
    emr_pregnancy.oxygen_supply_time,
    emr_pregnancy.oprn_end_time,
    emr_pregnancy.proc_whole_min,
    emr_pregnancy.afpn_diag,

    -- 胎儿和胎盘信息维度
    emr_pregnancy.uterus_case,
    emr_pregnancy.fetal_deliver_way,
    emr_pregnancy.amn_fluid_descr,
    emr_pregnancy.amn_fluid_amount,
    emr_pregnancy.placenta_deliver_time,
    emr_pregnancy.placenta_case,
    emr_pregnancy.membranes_complete_case_flag,
    emr_pregnancy.umb_lenghth,
    emr_pregnancy.umb_around_circle,
    emr_pregnancy.umb_turn_circle,
    emr_pregnancy.umb_around_case,
    emr_pregnancy.umbilical_abnormal_desc,
    emr_pregnancy.cord_blood_flag,
    emr_pregnancy.placenta_yellow_flag,
    emr_pregnancy.fetus_position_code,
    emr_pregnancy.fetus_position_name,

    -- 新生儿信息维度（来自nwb表）
    emr_pregnancy_nwb.nwb_report_sno,
    emr_pregnancy_nwb.nwb_gend_code,
    emr_pregnancy_nwb.nwb_gend_name,
    emr_pregnancy_nwb.nwb_bir_wt,
    emr_pregnancy_nwb.birth_length,
    emr_pregnancy_nwb.nwb_brdy_time,
    emr_pregnancy_nwb.produce_tumour_size,
    emr_pregnancy_nwb.produce_tumour_part,
    emr_pregnancy_nwb.apgar_interval_time_code,
    emr_pregnancy_nwb.apgar_interval_time_name,
    emr_pregnancy_nwb.apgar_score,
    emr_pregnancy_nwb.nwb_abnm_case_code,
    emr_pregnancy_nwb.nwb_abnm_case_name,

    -- 产后观察信息维度（来自observ表）
    emr_pregnancy_observ.observ_sn,
    emr_pregnancy_observ.postpar_observ_time,
    emr_pregnancy_observ.postpar_exam_time,
    emr_pregnancy_observ.postoprn_sbp,
    emr_pregnancy_observ.postoprn_dbp,
    emr_pregnancy_observ.postpar_pulse_rate,
    emr_pregnancy_observ.postpar_heart_rate,
    emr_pregnancy_observ.after_proc_bleeding,
    emr_pregnancy_observ.after_birth_uc,
    emr_pregnancy_observ.postpar_cervix_height,

    -- 医护人员信息维度
    emr_pregnancy.oprn_doc_name,
    emr_pregnancy.oprn_doc_code,
    emr_pregnancy.anst_doc_name,
    emr_pregnancy.anst_doc_code,
    emr_pregnancy.instrument_nurse_name,
    emr_pregnancy.instrument_nurse_code,
    emr_pregnancy.oprn_asit_name,
    emr_pregnancy.oprn_asit_code,
    emr_pregnancy.pediatrics_doc_name,
    emr_pregnancy.pediatrics_doc_code,
    emr_pregnancy.babysitter_name,
    emr_pregnancy.babysitter_code,
    emr_pregnancy.guider_name,
    emr_pregnancy.guider_code,

    -- 时间维度
    emr_pregnancy.upload_time,
    emr_pregnancy.crte_time,
    emr_pregnancy.updt_time,

    -- 状态维度
    emr_pregnancy.data_rank,
    emr_pregnancy.deleted,
    emr_pregnancy.deleted_time,
    emr_pregnancy.data_clct_prdr_name

FROM  emr_pregnancy
          LEFT JOIN  emr_predelivery ON emr_pregnancy.patient_no = emr_predelivery.patient_no
    AND emr_pregnancy.mdtrt_sn = emr_predelivery.mdtrt_sn
          LEFT JOIN  emr_pregnancy_nwb ON emr_pregnancy.caesar_sn = emr_pregnancy_nwb.caesar_sn
    AND emr_pregnancy.patient_no = emr_pregnancy_nwb.patient_no
    AND emr_pregnancy.mdtrt_sn = emr_pregnancy_nwb.mdtrt_sn
          LEFT JOIN  emr_pregnancy_observ ON emr_pregnancy.caesar_sn = emr_pregnancy_observ.caesar_sn
    AND emr_pregnancy.patient_no = emr_pregnancy_observ.patient_no
    AND emr_pregnancy.mdtrt_sn = emr_pregnancy_observ.mdtrt_sn
WHERE emr_pregnancy.deleted = '0';


-- 手术记录信息
CREATE TABLE fct0_emr_opr_nurse AS
SELECT
    -- 手术记录表(emr_opr_rec)的全部字段
    emr_opr_rec.rid,
    emr_opr_rec.org_name,
    emr_opr_rec.uscid,
    emr_opr_rec.upload_time,
    emr_opr_rec.sys_prdr_code,
    emr_opr_rec.sys_prdr_name,
    emr_opr_rec.opr_detl_sn,
    emr_opr_rec.ipt_doc_code,
    emr_opr_rec.ipt_doc_name,
    emr_opr_rec.patient_name,
    emr_opr_rec.gender_code,
    emr_opr_rec.gender_name,
    emr_opr_rec.age_year,
    emr_opr_rec.age_month,
    emr_opr_rec.age_day,
    emr_opr_rec.otp_no,
    emr_opr_rec.ipt_no,
    emr_opr_rec.patient_no,
    emr_opr_rec.mdtrt_sn,
    emr_opr_rec.otp_ipt_flag,
    emr_opr_rec.otp_ipt_name,
    emr_opr_rec.oprn_oprt_code,
    emr_opr_rec.oprn_oprt_name,
    emr_opr_rec.bfpn_diag_code,
    emr_opr_rec.bfpn_diag_name,
    emr_opr_rec.afpn_diag_code,
    emr_opr_rec.afpn_diag_name,
    emr_opr_rec.oprn_begin_time,
    emr_opr_rec.oprn_end_time,
    emr_opr_rec.oprn_lv_code,
    emr_opr_rec.oprn_lv_name,
    emr_opr_rec.oprn_doc_code,
    emr_opr_rec.oprn_doc_name,
    emr_opr_rec.oprn_asit_doc_code1,
    emr_opr_rec.oprn_asit_doc_name1,
    emr_opr_rec.oprn_asit_doc_code2,
    emr_opr_rec.oprn_asit_doc_name2,
    emr_opr_rec.anst_doc_code,
    emr_opr_rec.anst_doc_name,
    emr_opr_rec.anst_mtd_code,
    emr_opr_rec.anst_mtd_name,
    emr_opr_rec.ele_apply_no,
    emr_opr_rec.mdtrt_dept_code,
    emr_opr_rec.mdtrt_dept_name,
    emr_opr_rec.wardarea_code,
    emr_opr_rec.wardarea_name,
    emr_opr_rec.wardno,
    emr_opr_rec.bedno,
    emr_opr_rec.proc_room_no,
    emr_opr_rec.itvt_name,
    emr_opr_rec.proc_position_code,
    emr_opr_rec.proc_position_name,
    emr_opr_rec.skin_disinfect_descr,
    emr_opr_rec.proc_proc_descr,
    emr_opr_rec.op_detail_desc,
    emr_opr_rec.oprn_his_flag,
    emr_opr_rec.sinc_desc,
    emr_opr_rec.bleeding_num,
    emr_opr_rec.transfuse_num,
    emr_opr_rec.bld_amt,
    emr_opr_rec.urine_amt,
    emr_opr_rec.pre_proc_drug_use_way,
    emr_opr_rec.in_proc_drug_use_way,
    emr_opr_rec.transfuse_reaction_flag,
    emr_opr_rec.instrument_nurse_name,
    emr_opr_rec.instrument_nurse_code,
    emr_opr_rec.tour_nurse_name,
    emr_opr_rec.tour_nurse_code,
    emr_opr_rec.oprn_oprt_part_code,
    emr_opr_rec.oprn_oprt_part_name,
    emr_opr_rec.drain_flag,
    emr_opr_rec.drain_mater_name,
    emr_opr_rec.drain_mater_cnt,
    emr_opr_rec.place_part,
    emr_opr_rec.rec_time,
    emr_opr_rec.rec_code,
    emr_opr_rec.rec_name,
    emr_opr_rec.data_rank,
    emr_opr_rec.state,
    emr_opr_rec.data_clct_prdr_name,
    emr_opr_rec.crte_time,
    emr_opr_rec.updt_time,
    emr_opr_rec.deleted,
    emr_opr_rec.deleted_time,

    -- 护理记录表(emr_inout_rec)的特有字段
    emr_inout_rec.weight,
    emr_inout_rec.diag_code,
    emr_inout_rec.diag_name,
    emr_inout_rec.nurscare_lv_code,
    emr_inout_rec.nurscare_lv_name,
    emr_inout_rec.care_type_code,
    emr_inout_rec.care_type_name,
    emr_inout_rec.nurscare_obsv_item_name,
    emr_inout_rec.nurscare_obsv_rslt,
    emr_inout_rec.nurse_op_name,
    emr_inout_rec.nurse_op_proj_name,
    emr_inout_rec.nurse_op_res,
    emr_inout_rec.vomit_flag,
    emr_inout_rec.dysuria_flag,
    emr_inout_rec.medi_tcm_type_code,
    emr_inout_rec.medi_tcm_type_name,
    emr_inout_rec.nurs_name,
    emr_inout_rec.nurs_code,
    emr_inout_rec.sign_time,

    -- 用药记录表(emr_inout_usedrug)的特有字段
    emr_inout_usedrug.drug_name,
    emr_inout_usedrug.drug_used_way,
    emr_inout_usedrug.drug_used_frqu_code,
    emr_inout_usedrug.drug_used_frqu_name,
    emr_inout_usedrug.drug_dosform_code,
    emr_inout_usedrug.drug_dosform_name,
    emr_inout_usedrug.drug_used_sdose,
    emr_inout_usedrug.drug_used_dosunt,
    emr_inout_usedrug.drug_used_idose,
    emr_inout_usedrug.drug_used_way_code,
    emr_inout_usedrug.drug_used_way_name,

    -- 首次病程记录表(emr_first_dis_course)的特有字段
    emr_first_dis_course.certno,
    emr_first_dis_course.psncert_type_code,
    emr_first_dis_course.psncert_type_name,
    emr_first_dis_course.chfcomp,
    emr_first_dis_course.cas_ftur,
    emr_first_dis_course.diag_evid,
    emr_first_dis_course.dise_plan,
    emr_first_dis_course.treat_plan_desc,
    emr_first_dis_course.super_doc_name,
    emr_first_dis_course.super_doc_code,
    emr_first_dis_course.tcm4d_rslt,
    emr_first_dis_course.discriminate_treatment_descr,
    emr_first_dis_course.prnp_trt,
    emr_first_dis_course.prel_wm_diag_code,
    emr_first_dis_course.prel_wm_diag_name,
    emr_first_dis_course.prel_tcm_dise_code,
    emr_first_dis_course.prel_tcm_dise_name,
    emr_first_dis_course.prel_tcmsymp_code,
    emr_first_dis_course.prel_tcmsymp_name,
    emr_first_dis_course.finl_wm_diag_code,
    emr_first_dis_course.finl_wm_diag_name,
    emr_first_dis_course.finl_tcm_dise_code,
    emr_first_dis_course.finl_tcm_dise_name,
    emr_first_dis_course.finl_tcmsymp_code,
    emr_first_dis_course.finl_tcmsymp_name,
    emr_first_dis_course.dis_pro_code,
    emr_first_dis_course.dis_pro_name

FROM emr_opr_rec
         LEFT JOIN emr_inout_rec
                   ON emr_opr_rec.patient_no = emr_inout_rec.patient_no
                       AND emr_opr_rec.mdtrt_sn = emr_inout_rec.mdtrt_sn
         LEFT JOIN emr_inout_usedrug
                   ON emr_opr_rec.patient_no = emr_inout_usedrug.patient_no
                       AND emr_opr_rec.mdtrt_sn = emr_inout_usedrug.mdtrt_sn
         LEFT JOIN emr_first_dis_course
                   ON emr_opr_rec.patient_no = emr_first_dis_course.patient_no
                       AND emr_opr_rec.mdtrt_sn = emr_first_dis_course.mdtrt_sn;


-- 诊疗用药明细信息
CREATE TABLE fct0_emr_treat_drug AS
SELECT
    -- 用药明细表(emr_rreat_drug)的全部字段
    emr_rreat_drug.rid,
    emr_rreat_drug.org_name,
    emr_rreat_drug.uscid,
    emr_rreat_drug.upload_time,
    emr_rreat_drug.sys_prdr_code,
    emr_rreat_drug.sys_prdr_name,
    emr_rreat_drug.treat_drug_sn,
    emr_rreat_drug.treat_rec_sn,
    emr_rreat_drug.otp_ipt_flag,
    emr_rreat_drug.otp_ipt_name,
    emr_rreat_drug.patient_no,
    emr_rreat_drug.mdtrt_sn,
    emr_rreat_drug.ipt_no,
    emr_rreat_drug.otp_no,
    emr_rreat_drug.drug_name,
    emr_rreat_drug.drug_used_way,
    emr_rreat_drug.drug_used_frqu_code,
    emr_rreat_drug.drug_used_frqu_name,
    emr_rreat_drug.drug_dosform_code,
    emr_rreat_drug.drug_dosform_name,
    emr_rreat_drug.drug_used_sdose,
    emr_rreat_drug.drug_used_dosunt,
    emr_rreat_drug.drug_used_idose,
    emr_rreat_drug.drug_used_way_code,
    emr_rreat_drug.drug_used_way_name,
    emr_rreat_drug.rec_time,
    emr_rreat_drug.state,
    emr_rreat_drug.data_clct_prdr_name,
    emr_rreat_drug.crte_time,
    emr_rreat_drug.updt_time,
    emr_rreat_drug.deleted,
    emr_rreat_drug.deleted_time,

    -- 诊疗记录表(emr_treat_rec)的特有字段
    emr_treat_rec.dept_code,
    emr_treat_rec.dept_name,
    emr_treat_rec.age_year,
    emr_treat_rec.age_month,
    emr_treat_rec.age_day,
    emr_treat_rec.wardarea_name,
    emr_treat_rec.wardno,
    emr_treat_rec.bedno,
    emr_treat_rec.weight,
    emr_treat_rec.diag_code,
    emr_treat_rec.diag_name,
    emr_treat_rec.process_guide_ad,
    emr_treat_rec.wound_healing_flag,
    emr_treat_rec.oprn_oprt_code,
    emr_treat_rec.oprn_oprt_name,
    emr_treat_rec.oprn_oprt_part_code,
    emr_treat_rec.oprn_oprt_part_name,
    emr_treat_rec.itvt_name,
    emr_treat_rec.oprt_mtd_dscr,
    emr_treat_rec.oprn_oprt_times,
    emr_treat_rec.oper_time,
    emr_treat_rec.algs_flag,
    emr_treat_rec.algs_his,
    emr_treat_rec.drord_use_memo,
    emr_treat_rec.future_treat_plan,
    emr_treat_rec.drord_ptr_name,
    emr_treat_rec.drord_ptr_code,
    emr_treat_rec.exe_order_sign_time,
    emr_treat_rec.medi_tcm_type_code,
    emr_treat_rec.medi_tcm_type_name,
    emr_treat_rec.visit_way_code,
    emr_treat_rec.visit_way_name,
    emr_treat_rec.visit_date,
    emr_treat_rec.visit_prd_sug_code,
    emr_treat_rec.visit_prd_sug_name

FROM emr_rreat_drug
         LEFT JOIN emr_treat_rec
                   ON emr_rreat_drug.treat_rec_sn = emr_treat_rec.treat_rec_sn
                       AND emr_rreat_drug.patient_no = emr_treat_rec.patient_no
                       AND emr_rreat_drug.mdtrt_sn = emr_treat_rec.mdtrt_sn;

-- 死亡记录信息
CREATE TABLE fct0_emr_death_info AS
SELECT
    -- 24小时内死亡记录表(emr_inhosp_die_in24h)的全部字段
    emr_inhosp_die_in24h.rid,
    emr_inhosp_die_in24h.org_name,
    emr_inhosp_die_in24h.uscid,
    emr_inhosp_die_in24h.upload_time,
    emr_inhosp_die_in24h.sys_prdr_code,
    emr_inhosp_die_in24h.sys_prdr_name,
    emr_inhosp_die_in24h.ipt_die_rec_sn,
    emr_inhosp_die_in24h.patient_no,
    emr_inhosp_die_in24h.mdtrt_sn,
    emr_inhosp_die_in24h.age_year,
    emr_inhosp_die_in24h.age_month,
    emr_inhosp_die_in24h.age_day,
    emr_inhosp_die_in24h.ipt_no,
    emr_inhosp_die_in24h.chfcomp,
    emr_inhosp_die_in24h.illhis_stte_name,
    emr_inhosp_die_in24h.illhis_stte_rltl,
    emr_inhosp_die_in24h.illhis_stte_rltl_name,
    emr_inhosp_die_in24h.stte_rele,
    emr_inhosp_die_in24h.adm_time,
    emr_inhosp_die_in24h.death_time,
    emr_inhosp_die_in24h.adm_info,
    emr_inhosp_die_in24h.trt_proc_dscr,
    emr_inhosp_die_in24h.tcm4d_rslt,
    emr_inhosp_die_in24h.prnp_trt,
    emr_inhosp_die_in24h.die_reason,
    emr_inhosp_die_in24h.accept_doc_name,
    emr_inhosp_die_in24h.accept_doc_code,
    emr_inhosp_die_in24h.ipt_doc_name,
    emr_inhosp_die_in24h.ipt_doc_code,
    emr_inhosp_die_in24h.atd_doc_name,
    emr_inhosp_die_in24h.atd_doc_code,
    emr_inhosp_die_in24h.chf_doc_name,
    emr_inhosp_die_in24h.chf_doc_code,
    emr_inhosp_die_in24h.adm_wm_diag_code,
    emr_inhosp_die_in24h.adm_wm_diag_name,
    emr_inhosp_die_in24h.adm_tcm_dise_code,
    emr_inhosp_die_in24h.adm_tcm_dise_name,
    emr_inhosp_die_in24h.adm_tcmsymp_code,
    emr_inhosp_die_in24h.adm_tcmsymp_name,
    emr_inhosp_die_in24h.die_wm_diag_code,
    emr_inhosp_die_in24h.die_wm_diag_name,
    emr_inhosp_die_in24h.die_tcm_dise_code,
    emr_inhosp_die_in24h.die_tcm_dise_name,
    emr_inhosp_die_in24h.die_tcmsymp_code,
    emr_inhosp_die_in24h.die_tcmsymp_name,
    emr_inhosp_die_in24h.data_rank,
    emr_inhosp_die_in24h.data_clct_prdr_name,
    emr_inhosp_die_in24h.crte_time,
    emr_inhosp_die_in24h.updt_time,
    emr_inhosp_die_in24h.deleted,
    emr_inhosp_die_in24h.deleted_time,

    -- 死亡记录表(emr_death_record)的特有字段
    emr_death_record.patient_name,
    emr_death_record.certno,
    emr_death_record.psncert_type_code,
    emr_death_record.psncert_type_name,
    emr_death_record.wardarea_name,
    emr_death_record.dept_code,
    emr_death_record.dept_name,
    emr_death_record.wardno,
    emr_death_record.bedno,
    emr_death_record.act_ipt_days,
    emr_death_record.adm_diag_code,
    emr_death_record.adm_diag_name,
    emr_death_record.agre_corp_dset,

    -- 死亡病例讨论表(emr_death_case_discu)的特有字段
    emr_death_case_discu.die_discuss_sn,
    emr_death_case_discu.discu_time,
    emr_death_case_discu.discu_place,
    emr_death_case_discu.presenter_name,
    emr_death_case_discu.presenter_code,
    emr_death_case_discu.presenter_proftechttl_code,
    emr_death_case_discu.presenter_proftechttl_name,
    emr_death_case_discu.discu_code,
    emr_death_case_discu.discu_name,
    emr_death_case_discu.discu_proftechttl_code,
    emr_death_case_discu.discu_proftechttl_name,
    emr_death_case_discu.death_discu_conclu,
    emr_death_case_discu.presenter_process_guide_ad

FROM emr_inhosp_die_in24h
         LEFT JOIN emr_death_record
                   ON emr_inhosp_die_in24h.patient_no = emr_death_record.patient_no
                       AND emr_inhosp_die_in24h.mdtrt_sn = emr_death_record.mdtrt_sn
         LEFT JOIN emr_death_case_discu
                   ON emr_inhosp_die_in24h.patient_no = emr_death_case_discu.patient_no
                       AND emr_inhosp_die_in24h.mdtrt_sn = emr_death_case_discu.mdtrt_sn;

-- 阴道分娩信息
CREATE TABLE fct0_emr_vagina_deliver_info AS
SELECT
    -- 阴道分娩观察表(emr_vagina_deliver_observ)的全部字段
    emr_vagina_deliver_observ.rid,
    emr_vagina_deliver_observ.org_name,
    emr_vagina_deliver_observ.uscid,
    emr_vagina_deliver_observ.upload_time,
    emr_vagina_deliver_observ.sys_prdr_code,
    emr_vagina_deliver_observ.sys_prdr_name,
    emr_vagina_deliver_observ.observ_sn,
    emr_vagina_deliver_observ.patient_no,
    emr_vagina_deliver_observ.mdtrt_sn,
    emr_vagina_deliver_observ.ipt_no,
    emr_vagina_deliver_observ.preg_name,
    emr_vagina_deliver_observ.vagina_deliver_sn,
    emr_vagina_deliver_observ.postpar_observ_time,
    emr_vagina_deliver_observ.postpar_exam_time,
    emr_vagina_deliver_observ.postpar_sbp,
    emr_vagina_deliver_observ.postpar_dbp,
    emr_vagina_deliver_observ.postpar_pulse_rate,
    emr_vagina_deliver_observ.postpar_heart_rate,
    emr_vagina_deliver_observ.postpar_blood_num,
    emr_vagina_deliver_observ.after_birth_uc,
    emr_vagina_deliver_observ.postpar_cervix_height,
    emr_vagina_deliver_observ.postpar_bladder_fill_flag,
    emr_vagina_deliver_observ.data_rank,
    emr_vagina_deliver_observ.data_clct_prdr_name,
    emr_vagina_deliver_observ.crte_time,
    emr_vagina_deliver_observ.updt_time,
    emr_vagina_deliver_observ.deleted,
    emr_vagina_deliver_observ.deleted_time,

    -- 阴道分娩记录表(emr_vagina_deliver)的特有字段
    emr_vagina_deliver.age_year,
    emr_vagina_deliver.dept_name,
    emr_vagina_deliver.dept_code,
    emr_vagina_deliver.wardarea_name,
    emr_vagina_deliver.wardno,
    emr_vagina_deliver.bedno,
    emr_vagina_deliver.prg_cnt,
    emr_vagina_deliver.matn_cnt,
    emr_vagina_deliver.last_mena_date,
    emr_vagina_deliver.expect_deliver_date,
    emr_vagina_deliver.parturient_time,
    emr_vagina_deliver.adm_diag,
    emr_vagina_deliver.uc_begn_time,
    emr_vagina_deliver.membranes_rupture_time,
    emr_vagina_deliver.pre_amn_fluid_descr,
    emr_vagina_deliver.pre_amn_fluid_num,
    emr_vagina_deliver.first_labor_min,
    emr_vagina_deliver.uterus_full_open_time,
    emr_vagina_deliver.second_labor_min,
    emr_vagina_deliver.third_labor_min,
    emr_vagina_deliver.total_reproduct_min,
    emr_vagina_deliver.fetus_position_code,
    emr_vagina_deliver.fetus_position_name,
    emr_vagina_deliver.vagina_assist_dilivery_flag,
    emr_vagina_deliver.vagina_assist_dilivery_way,
    emr_vagina_deliver.amn_fluid_descr,
    emr_vagina_deliver.amn_fluid_amount,
    emr_vagina_deliver.placenta_deliver_time,
    emr_vagina_deliver.placenta_case,
    emr_vagina_deliver.membranes_complete_case_flag,
    emr_vagina_deliver.umb_around_circle,
    emr_vagina_deliver.umb_lenghth,
    emr_vagina_deliver.umbilical_exam_abnorm_flag,
    emr_vagina_deliver.umbilical_abnormal_desc,
    emr_vagina_deliver.birthing_drug_name,
    emr_vagina_deliver.prevent_measures,
    emr_vagina_deliver.perineum_cut_flag,
    emr_vagina_deliver.perineum_cut_part,
    emr_vagina_deliver.perine_suture_num,
    emr_vagina_deliver.vaginal_laceration_code,
    emr_vagina_deliver.vaginal_laceration_name,
    emr_vagina_deliver.obstetric_wound_flag,
    emr_vagina_deliver.obstetric_wound_size,
    emr_vagina_deliver.perinum_hematoma_process,
    emr_vagina_deliver.anst_mtd_code,
    emr_vagina_deliver.anst_mtd_name,
    emr_vagina_deliver.anst_medn_code,
    emr_vagina_deliver.anst_medn_name,
    emr_vagina_deliver.vaginal_laceration_flag,
    emr_vagina_deliver.vaginal_hematoma_flag,
    emr_vagina_deliver.vaginal_hematoma_size,
    emr_vagina_deliver.vaginal_hematoma_process,
    emr_vagina_deliver.cervical_lacer_flag,
    emr_vagina_deliver.uterus_neck_suture_needle_num,
    emr_vagina_deliver.anus_exam_rslt,
    emr_vagina_deliver.after_birth_drug_name,
    emr_vagina_deliver.spe_case_rec_deliver,
    emr_vagina_deliver.uc_case,
    emr_vagina_deliver.perinaeum_case,
    emr_vagina_deliver.uterus_case,
    emr_vagina_deliver.lochia_status,
    emr_vagina_deliver.repair_proc_course,
    emr_vagina_deliver.cord_blood_flag,
    emr_vagina_deliver.postpar_diag,
    emr_vagina_deliver.prenatal_sympt_descr,
    emr_vagina_deliver.postpar_sympt_descr,
    emr_vagina_deliver.deliver_res_code,
    emr_vagina_deliver.deliver_res_name,
    emr_vagina_deliver.deliver_name,
    emr_vagina_deliver.deliver_code,
    emr_vagina_deliver.assist_delivery_name,
    emr_vagina_deliver.assist_delivery_code,
    emr_vagina_deliver.assist_name,
    emr_vagina_deliver.assist_code,
    emr_vagina_deliver.babysitter_name,
    emr_vagina_deliver.babysitter_code,
    emr_vagina_deliver.guider_name,
    emr_vagina_deliver.guider_code,
    emr_vagina_deliver.oprn_doc_name,
    emr_vagina_deliver.oprn_doc_code,
    emr_vagina_deliver.pediatrics_doc_name,
    emr_vagina_deliver.pediatrics_doc_code,

    -- 新生儿记录表(emr_vagina_deliver_nwb)的特有字段
    emr_vagina_deliver_nwb.nwb_report_sno,
    emr_vagina_deliver_nwb.nwb_gend_code,
    emr_vagina_deliver_nwb.nwb_gend_name,
    emr_vagina_deliver_nwb.nwb_bir_wt,
    emr_vagina_deliver_nwb.birth_length,
    emr_vagina_deliver_nwb.nwb_brdy_time,
    emr_vagina_deliver_nwb.produce_tumour_size,
    emr_vagina_deliver_nwb.produce_tumour_part,
    emr_vagina_deliver_nwb.apgar_interval_time_code,
    emr_vagina_deliver_nwb.apgar_interval_time_name,
    emr_vagina_deliver_nwb.apgar_score,
    emr_vagina_deliver_nwb.nwb_abnm_case_code,
    emr_vagina_deliver_nwb.nwb_abnm_case_name

FROM emr_vagina_deliver_observ
         LEFT JOIN emr_vagina_deliver
                   ON emr_vagina_deliver_observ.vagina_deliver_sn = emr_vagina_deliver.vagina_deliver_sn
                       AND emr_vagina_deliver_observ.patient_no = emr_vagina_deliver.patient_no
                       AND emr_vagina_deliver_observ.mdtrt_sn = emr_vagina_deliver.mdtrt_sn
         LEFT JOIN emr_vagina_deliver_nwb
                   ON emr_vagina_deliver_observ.vagina_deliver_sn = emr_vagina_deliver_nwb.vagina_deliver_sn
                       AND emr_vagina_deliver_observ.patient_no = emr_vagina_deliver_nwb.patient_no
                       AND emr_vagina_deliver_observ.mdtrt_sn = emr_vagina_deliver_nwb.mdtrt_sn;
