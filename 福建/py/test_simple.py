from 质控mysql转其他库 import translate_sql

# 简单测试
sql = "SELECT * FROM table WHERE name REGEXP '^test'"
result = translate_sql(sql, 'oracle')
print(f"原始SQL: {sql}")
print(f"转换结果: {result}")

# 测试复杂案例
complex_sql = "SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');"
complex_result = translate_sql(complex_sql, 'oracle')
print(f"\n复杂SQL: {complex_sql}")
print(f"复杂SQL转换结果: {complex_result}") 