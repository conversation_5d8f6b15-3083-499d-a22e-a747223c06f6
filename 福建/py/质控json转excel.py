#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将质控json数据填充到Excel模板中
写一个将json内容填充到excel的脚本,'D:\work\demo\福建\py\质控json转excel.py'
excel模板路径'D:\work\demo\福建\质控模板'下的所有.xlsx文件
json路径'D:\work\demo\福建\质控json'下所有.json文件
操作步骤：按顺序打开excel文件，逐个读取sheet页名称，提取名称中的英文并去除两端空格，在英文名前端拼接'coreflddq_'，用拼接后的文本和json文件名称做比对，获取对应表名所对应的json文件
如'【病案手术记录表】 case_operate_record' 提取出case_operate_record，匹配的json文件就是coreflddq_case_operate_record.json
只读取json文件中的rows节点，rows下有多条数据，对应excel中的多行数据，先获取key'hosname'，对应的value和'机构名称'列匹配，则这条json对应的就是该医院所在行，再用json中每条数据的key的值去和excel表头对比，匹配上了，则将对应key的value填写在excel对应的行中。

调整填充数据的逻辑，先删除所有模板中'行政区划'列为'宁德市'的行，从json数据出发，获取key'hosname'数据不为空的所有数据块，在将数据按顺序填充到excel中，
"""

# 导入所需库
import os
import json
import re
import pandas as pd
from openpyxl import load_workbook
import logging
import datetime
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment, Protection, Color
from openpyxl.utils.dataframe import dataframe_to_rows
from copy import copy

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("质控json转excel.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 文件路径配置
EXCEL_DIR = r'D:\work\demo\福建\质控模板'
JSON_DIR = r'D:\work\demo\福建\质控json'
OUTPUT_DIR = r'D:\work\demo\福建\质控结果'

# 如果输出目录不存在，创建它
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_header_style(excel_dir):
    """
    获取第一个Excel文件第一个sheet页的第一行样式
    提取的样式信息包括：字体(font)、背景颜色(fill)、边框(border)、对齐方式(alignment)、
    数字格式(number_format)、保护设置(protection)等
    
    Args:
        excel_dir (str): Excel文件目录
    
    Returns:
        list: 包含样式信息的列表，每个元素是一个单元格的样式字典
    """
    try:
        # 获取第一个Excel文件
        excel_files = [os.path.join(excel_dir, f) for f in os.listdir(excel_dir) 
                      if f.endswith('.xlsx') and not f.startswith('~$')]  # 排除临时文件
        
        if not excel_files:
            logging.warning(f"在 {excel_dir} 中未找到Excel文件")
            return None
        
        # 取第一个Excel文件
        first_excel = excel_files[0]
        logging.info(f"正在获取 {os.path.basename(first_excel)} 的第一行样式")
        
        # 使用openpyxl加载工作簿
        wb = load_workbook(first_excel)
        
        # 获取第一个sheet
        first_sheet = wb.worksheets[0]
        
        # 获取第一行
        first_row = first_sheet[1]
        
        # 存储样式信息
        styles = []
        for cell in first_row:
            # 复制每个单元格的样式
            style = {
                'font': copy(cell.font),  # 字体样式：字体类型、大小、颜色、加粗等
                'fill': copy(cell.fill),  # 背景填充：颜色、图案等
                'border': copy(cell.border),  # 边框样式：线条类型、颜色等
                'alignment': copy(cell.alignment),  # 对齐方式：水平、垂直对齐等
                'number_format': cell.number_format,  # 数字格式：日期、货币等
                'protection': copy(cell.protection),  # 保护设置：锁定、隐藏等
                'value': cell.value  # 单元格值，用于调试
            }
            styles.append(style)
        
        logging.info(f"已成功获取第一行样式，共 {len(styles)} 个单元格")
        return styles
        
    except Exception as e:
        logging.error(f"获取第一行样式时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return None

def extract_english_name(sheet_name):
    """
    从sheet名称中提取英文部分
    例如：从"【病案手术记录表】 case_operate_record"中提取"case_operate_record"
    
    Args:
        sheet_name (str): Excel工作表名称
    
    Returns:
        str: 提取的英文名称，如果没有找到则返回None
    """
    # 使用正则表达式提取英文和数字组成的部分
    english_pattern = re.compile(r'[a-zA-Z][a-zA-Z0-9_]+')
    match = english_pattern.search(sheet_name)
    
    if match:
        # 提取匹配的部分并去除两端空格
        return match.group().strip()
    else:
        logging.warning(f"无法从sheet名称 '{sheet_name}' 中提取英文部分")
        return None

def get_json_filename(sheet_english_name):
    """
    根据sheet的英文名生成对应的JSON文件名
    
    Args:
        sheet_english_name (str): sheet的英文名
    
    Returns:
        str: 对应的JSON文件名
    """
    if not sheet_english_name:
        return None
    return f"coreflddq_{sheet_english_name}.txt"

def find_json_file(json_dir, sheet_english_name):
    """
    在json目录中查找对应的json文件
    
    Args:
        json_dir (str): JSON文件目录
        sheet_english_name (str): 工作表的英文名称
    
    Returns:
        str: JSON文件的完整路径，如果未找到则返回None
    """
    if not sheet_english_name:
        return None
    
    # 生成对应的JSON文件名
    json_filename = get_json_filename(sheet_english_name)
    
    # 构建完整的文件路径
    json_file_path = os.path.join(json_dir, json_filename)
    
    # 检查文件是否存在
    if os.path.exists(json_file_path):
        logging.info(f"找到匹配的文件: {json_filename}")
        return json_file_path
    else:
        logging.warning(f"未找到匹配的文件: {json_filename}")
        return None

def load_json_data(json_file_path):
    """
    加载JSON文件，并提取rows节点数据
    
    Args:
        json_file_path (str): JSON文件路径
    
    Returns:
        list: rows节点的数据列表
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # 检查JSON结构是否包含details和rows节点
        if 'details' in json_data and 'rows' in json_data['details']:
            return json_data['details']['rows']
        else:
            logging.error(f"JSON文件 {json_file_path} 中未找到有效的rows节点")
            return []
    except Exception as e:
        logging.error(f"读取JSON文件 {json_file_path} 时出错: {str(e)}")
        return []

def get_field_mapping():
    """
    获取JSON字段名和Excel列名的映射关系
    
    Returns:
        dict: JSON字段名到Excel列名的映射字典
    """
    # 定义已知的映射关系
    return {
        "hosname": "机构名称",
        "cnt": "数据量",
        # 可以根据需要添加更多映射
    }

# 此函数在新的逻辑中不再需要，因为我们现在是从JSON数据出发，而不是从Excel行匹配
# 保留函数定义但添加废弃提示，以防其他地方仍有调用
def find_hospital_row(df, hospital_name):
    """
    [已废弃] 根据医院名称查找对应的Excel行
    新的逻辑中不再需要此函数，因为我们现在是从JSON数据出发，而不是从Excel行匹配
    
    Args:
        df (pandas.DataFrame): Excel数据表
        hospital_name (str): 医院名称
    
    Returns:
        int: 匹配的行索引，如果未找到则返回None
    """
    logging.warning("find_hospital_row函数已废弃，使用了新的数据填充逻辑")
    
    # 获取映射关系
    field_mapping = get_field_mapping()
    excel_column = field_mapping.get("hosname", "机构名称")
    
    # 检查Excel是否包含指定的列
    if excel_column in df.columns:
        # 查找医院名称匹配的行
        matching_rows = df[df[excel_column] == hospital_name].index.tolist()
        if matching_rows:
            return matching_rows[0]  # 返回第一个匹配的行索引
    else:
        logging.warning(f"Excel中未找到 '{excel_column}' 列")
    
    return None

def fill_excel_with_json_data(excel_file, sheet_name, json_file_path, header_styles=None, output_file=None):
    """
    将JSON数据填充到Excel表格中
    
    Args:
        excel_file (str): Excel文件路径
        sheet_name (str): 工作表名称
        json_file_path (str): JSON文件路径
        header_styles (list): 表头样式列表，用于应用到DataFrame的表头
        output_file (str, optional): 输出文件路径，默认为None时不保存
    
    Returns:
        pandas.DataFrame: 处理后的Excel数据
    """
    try:
        # 读取Excel文件指定sheet页
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        # 删除"行政区划"列不为空的所有行
        if "行政区划" in df.columns:
            logging.info(f"删除行政区划不为空的所有行，删除前行数: {len(df)}")
            df = df[df["行政区划"].isna() | (df["行政区划"] == "")].reset_index(drop=True)
            logging.info(f"删除后行数: {len(df)}")
        else:
            logging.warning(f"未找到'行政区划'列，无法删除相关行")
        
        # 加载JSON数据
        json_rows = load_json_data(json_file_path)
        if not json_rows:
            logging.error(f"未能从 {json_file_path} 加载有效的JSON数据")
            return df
        
        # 过滤出hosname不为空的数据块
        valid_rows = [row for row in json_rows if row.get('hosname')]
        if not valid_rows:
            logging.warning(f"在JSON数据中未找到有效的医院数据（hosname不为空）")
            return df
        
        logging.info(f"从JSON中提取了 {len(valid_rows)} 条有效医院数据")
        
        # 处理计数器
        added_rows = 0
        updated_cells = 0
        
        # 获取字段映射
        field_mapping = get_field_mapping()
        
        # 创建新的DataFrame用于保存结果
        result_df = df.copy()
        
        # 从JSON数据出发，将每条数据按顺序填充到Excel中
        for row_data in valid_rows:
            hospital_name = row_data['hosname']
            
            # 创建新行数据（复制DataFrame的结构，但值为空）
            new_row = pd.Series(index=result_df.columns)
            
            # 填充机构名称
            hospital_column = field_mapping.get("hosname", "机构名称")
            if hospital_column in new_row.index:
                new_row[hospital_column] = hospital_name
            
            # 填充其他字段
            for json_key, json_value in row_data.items():
                if json_key == 'hosname':
                    continue
                
                # 检查是否有预定义的映射
                excel_column = field_mapping.get(json_key)
                if excel_column and excel_column in new_row.index:
                    new_row[excel_column] = json_value
                    updated_cells += 1
                    continue
                
                # 检查JSON字段是否直接出现在Excel的列名中
                if json_key in new_row.index:
                    new_row[json_key] = json_value
                    updated_cells += 1
                else:
                    # 检查是否有包含此字段的列名
                    matching_cols = [col for col in new_row.index if json_key in col]
                    if matching_cols:
                        col = matching_cols[0]
                        new_row[col] = json_value
                        updated_cells += 1
                        logging.debug(f"通过部分匹配将 '{json_key}' 映射到列 '{col}'")
            
            # 将新行添加到结果DataFrame
            result_df = pd.concat([result_df, pd.DataFrame([new_row])], ignore_index=True)
            added_rows += 1
        
        logging.info(f"已添加 {added_rows} 条医院数据，共更新 {updated_cells} 个单元格")
        
        # 如果指定了输出文件，保存修改后的Excel
        if output_file:
            result_df.to_excel(output_file, sheet_name=sheet_name, index=False)
            logging.info(f"已保存更新后的数据到 {output_file}")
        
        return result_df
    
    except Exception as e:
        logging.error(f"处理Excel文件时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return None

def process_excel_file(excel_file_path, header_styles=None):
    """
    处理单个Excel文件，匹配JSON并填充数据
    
    Args:
        excel_file_path (str): Excel文件路径
        header_styles (list): 表头样式列表，用于应用到表头行
    
    Returns:
        bool: 处理成功返回True，否则返回False
    """
    try:
        # 获取文件名，不包含路径
        excel_basename = os.path.basename(excel_file_path)
        logging.info(f"正在处理Excel文件: {excel_basename}")
        
        # 加载Excel文件，获取所有sheet名称
        excel = pd.ExcelFile(excel_file_path)
        sheet_names = excel.sheet_names
        
        # 输出文件路径
        output_file_path = os.path.join(OUTPUT_DIR, f"已填充_{excel_basename}")
        
        # 创建一个writer，用于保存处理后的Excel
        with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
            # 处理每个sheet
            for sheet_name in sheet_names:
                logging.info(f"正在处理工作表: {sheet_name}")
                
                # 提取英文名称
                sheet_english_name = extract_english_name(sheet_name)
                if not sheet_english_name:
                    logging.warning(f"未能从 '{sheet_name}' 提取英文名称，跳过此工作表")
                    # 仍然需要保存原始数据到新文件
                    df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    continue
                
                # 查找对应的JSON文件
                json_file_path = find_json_file(JSON_DIR, sheet_english_name)
                if not json_file_path:
                    logging.warning(f"未找到对应的JSON文件，跳过工作表 {sheet_name}")
                    # 仍然需要保存原始数据到新文件
                    df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    continue
                
                logging.info(f"开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行")
                
                # 填充数据
                df = fill_excel_with_json_data(excel_file_path, sheet_name, json_file_path, header_styles)
                if df is not None:
                    # 保存到指定工作表
                    try:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        
                        # 如果有样式信息，应用到当前sheet的第一行
                        if header_styles:
                            # 获取当前工作表
                            ws = writer.sheets[sheet_name]
                            
                            # 应用样式到第一行（表头）
                            for col_idx, style in enumerate(header_styles, 1):
                                if col_idx > len(df.columns):
                                    break
                                
                                # 获取单元格
                                cell = ws.cell(row=1, column=col_idx)
                                
                                # 应用样式: 从第一个Excel文件的第一行复制所有样式属性到当前单元格
                                cell.font = style['font']         # 应用字体样式（类型、大小、颜色等）
                                cell.fill = style['fill']         # 应用背景填充样式
                                cell.border = style['border']     # 应用边框样式
                                cell.alignment = style['alignment']  # 应用对齐方式
                                cell.protection = style['protection']  # 应用保护设置
                                
                                # 应用数字格式（如日期、货币格式等）
                                if style['number_format']:
                                    cell.number_format = style['number_format']
                            
                            logging.info(f"已应用表头样式到工作表 {sheet_name}")
                            
                            # 自动调整列宽，使所有内容都能完整显示
                            for col in ws.columns:
                                max_length = 0
                                column = col[0].column_letter  # 获取列字母
                                for cell in col:
                                    try:
                                        if len(str(cell.value)) > max_length:
                                            max_length = len(str(cell.value))
                                    except:
                                        pass
                                adjusted_width = (max_length + 2) * 1.2  # 稍微增加一些宽度以便更好显示
                                ws.column_dimensions[column].width = adjusted_width
                            
                            logging.info(f"已自动调整工作表 {sheet_name} 的列宽")
                        
                        logging.info(f"工作表 {sheet_name} 处理成功，总行数: {len(df)}")
                    except ValueError as ve:
                        # 如果出现索引相关错误，尝试修复后重新保存
                        logging.warning(f"保存工作表 {sheet_name} 时出现错误: {str(ve)}，尝试修复...")
                        # 确保DataFrame的索引是连续的
                        df = df.reset_index(drop=True)
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # 处理失败，仍然需要保存原始数据
                    logging.error(f"处理工作表 {sheet_name} 失败，将保存原始数据")
                    df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logging.info(f"已保存处理后的Excel文件: {output_file_path}")
        return True
    
    except Exception as e:
        logging.error(f"处理Excel文件 {excel_file_path} 时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    start_time = datetime.datetime.now()
    logging.info("=== 开始处理质控JSON转Excel ===")
    logging.info(f"Excel模板目录: {EXCEL_DIR}")
    logging.info(f"JSON数据目录: {JSON_DIR}")
    logging.info(f"输出目录: {OUTPUT_DIR}")
    
    try:
        # 获取所有Excel文件
        excel_files = [os.path.join(EXCEL_DIR, f) for f in os.listdir(EXCEL_DIR) 
                      if f.endswith('.xlsx') and not f.startswith('~$')]  # 排除临时文件
        
        if not excel_files:
            logging.warning(f"在 {EXCEL_DIR} 中未找到Excel文件")
            return
        
        logging.info(f"找到 {len(excel_files)} 个Excel文件")
        
        # 获取第一个Excel文件的第一个sheet的第一行样式
        logging.info("获取第一个Excel文件的第一行样式")
        header_styles = get_header_style(EXCEL_DIR)
        if header_styles:
            logging.info(f"成功获取第一行样式，将应用到所有输出文件")
        else:
            logging.warning(f"未能获取第一行样式，输出文件将使用默认样式")
        
        # 处理每个Excel文件
        success_count = 0
        for excel_file in excel_files:
            if process_excel_file(excel_file, header_styles):
                success_count += 1
        
        # 总结
        end_time = datetime.datetime.now()
        elapsed_time = end_time - start_time
        logging.info(f"处理完成，成功处理 {success_count}/{len(excel_files)} 个文件")
        logging.info(f"总耗时: {elapsed_time}")
    
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
    
    logging.info("=== 处理结束 ===")

if __name__ == "__main__":
    main() 