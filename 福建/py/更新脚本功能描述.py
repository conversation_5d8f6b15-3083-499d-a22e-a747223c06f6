#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本脚本用于分析福建/py目录下的Python文件，提取更完整的功能描述，
并更新福建/MD目录下对应的Markdown文档中的功能描述部分。
"""

import os
import re
import ast
import logging
from typing import Dict, List, Optional, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("更新脚本功能描述.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 文件路径配置
PY_DIR = r'D:\work\demo\福建\py'
MD_DIR = r'D:\work\demo\福建\MD'

def analyze_python_file(file_path: str) -> str:
    """
    分析Python文件，提取完整的功能描述
    
    策略：
    1. 尝试获取模块级文档字符串
    2. 如果没有，尝试获取main函数的文档字符串
    3. 如果没有，尝试获取其他主要函数的文档字符串
    4. 如果没有，通过文件内容进行智能分析
    
    Args:
        file_path: Python文件的路径
        
    Returns:
        str: 提取的功能描述
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        # 尝试解析Python代码
        try:
            tree = ast.parse(file_content)
            
            # 获取模块级文档字符串
            module_docstring = ast.get_docstring(tree)
            if module_docstring and len(module_docstring) > 10:
                return module_docstring.strip()
            
            # 如果模块级文档字符串为空或太短，尝试获取主要函数的文档字符串
            main_function = None
            important_functions = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # 寻找main函数
                    if node.name == 'main':
                        main_function = node
                    
                    # 收集其他重要函数（名称中包含generate, create, process, convert等关键词的函数）
                    important_keywords = ['generate', 'create', 'process', 'convert', 'extract', 'transform', 'build', 'make']
                    if any(keyword in node.name.lower() for keyword in important_keywords):
                        important_functions.append(node)
            
            # 使用main函数的文档字符串
            if main_function:
                main_docstring = ast.get_docstring(main_function)
                if main_docstring and len(main_docstring) > 10:
                    return main_docstring.strip()
            
            # 使用重要函数的文档字符串
            for func in important_functions:
                func_docstring = ast.get_docstring(func)
                if func_docstring and len(func_docstring) > 30:  # 较长的文档字符串更可能包含详细功能描述
                    return func_docstring.strip()
            
            # 如果上述方法都无法获取有效的文档字符串，从文件中提取关键信息
            return analyze_file_content(file_path, file_content)
            
        except SyntaxError:
            # 如果代码解析失败，尝试手动提取文档字符串
            module_docstring_match = re.search(r'"""(.*?)"""', file_content, re.DOTALL)
            if module_docstring_match:
                docstring = module_docstring_match.group(1).strip()
                if len(docstring) > 10:
                    return docstring
            
            # 如果无法解析语法并且没有找到有效的文档字符串，分析文件内容
            return analyze_file_content(file_path, file_content)
    
    except Exception as e:
        logging.error(f"分析文件 {file_path} 时出错: {str(e)}")
        return f"无法提取功能描述: {str(e)}"

def analyze_file_content(file_path: str, content: str) -> str:
    """
    通过文件内容分析脚本功能
    
    Args:
        file_path: 文件路径
        content: 文件内容
        
    Returns:
        str: 功能描述
    """
    filename = os.path.basename(file_path)
    
    # 基于文件名的初步推断
    description = generate_description_from_filename(filename)
    
    # 提取导入语句，了解脚本依赖
    imports = re.findall(r'^(?:import|from)\s+[^\n]+', content, re.MULTILINE)
    
    # 提取关键变量和路径，了解脚本处理的数据
    paths = re.findall(r'(?:path|dir|file|directory)\s*=\s*[\'"]([^\'"]+)[\'"]', content)
    excel_paths = [p for p in paths if p.endswith('.xlsx') or p.endswith('.xls')]
    sql_paths = [p for p in paths if p.endswith('.sql')]
    json_paths = [p for p in paths if p.endswith('.json')]
    
    # 查找是否有输出文件操作
    output_operations = re.findall(r'(?:write|save|output|export|生成)', content.lower())
    
    # 根据提取的信息，完善功能描述
    additional_info = []
    
    if 'pandas' in str(imports) or 'pd' in str(imports):
        additional_info.append("处理Excel或CSV等表格数据")
    
    if 'openpyxl' in str(imports):
        additional_info.append("详细操作Excel文件")
    
    if 'json' in str(imports):
        additional_info.append("处理JSON格式数据")
    
    if 'docx' in str(imports) or 'python-docx' in str(imports):
        additional_info.append("生成或处理Word文档")
    
    if 're' in str(imports):
        additional_info.append("使用正则表达式进行文本处理")
    
    if len(excel_paths) > 0:
        additional_info.append(f"操作Excel文件")
    
    if len(sql_paths) > 0:
        additional_info.append(f"处理SQL文件")
    
    if len(json_paths) > 0:
        additional_info.append(f"处理JSON文件")
    
    if len(output_operations) > 0:
        additional_info.append("生成或导出数据")
    
    # 结合基本描述和附加信息
    if additional_info:
        description += "，" + "，".join(additional_info)
    
    return description

def generate_description_from_filename(filename: str) -> str:
    """
    根据文件名生成基本的功能描述
    
    Args:
        filename: 文件名
        
    Returns:
        str: 基本功能描述
    """
    name_without_ext = os.path.splitext(filename)[0]
    
    # 特殊文件名处理
    if name_without_ext.startswith("生成") and "建表语句" in name_without_ext:
        db_type = name_without_ext[2:].split("建表")[0].strip()
        return f"根据Excel模板生成{db_type}数据库的建表SQL语句"
    
    if "转" in name_without_ext:
        parts = name_without_ext.split("转")
        if len(parts) == 2:
            return f"将{parts[0]}格式数据转换为{parts[1]}格式"
    
    if "json转excel" in name_without_ext:
        return "将JSON格式数据转换并填充到Excel模板中"
    
    if "提取" in name_without_ext:
        return f"从{name_without_ext.split('提取')[0]}中提取数据"
    
    if "检测" in name_without_ext or "检查" in name_without_ext:
        return f"检测或验证{name_without_ext.split('检测')[-1].split('检查')[-1]}"
    
    if "添加" in name_without_ext:
        return f"向数据中添加{name_without_ext.split('添加')[-1]}"
    
    if "更新" in name_without_ext:
        return f"更新{name_without_ext.split('更新')[-1]}数据"
    
    if "处理" in name_without_ext:
        return f"处理{name_without_ext.split('处理')[-1]}数据"
    
    if name_without_ext.startswith("test_"):
        test_type = name_without_ext[5:]
        return f"测试{test_type}相关功能"
    
    # 一般情况
    return f"处理{name_without_ext}相关任务"

def update_markdown_file(md_file: str, description: str) -> None:
    """
    更新Markdown文件中的功能描述部分
    
    Args:
        md_file: Markdown文件路径
        description: 新的功能描述
    """
    try:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 处理描述中可能包含的特殊字符，避免正则表达式问题
        # 1. 将反斜杠替换为双反斜杠，避免转义问题
        safe_description = description.replace('\\', '\\\\')
        # 2. 处理其他可能影响正则表达式的特殊字符
        safe_description = safe_description.replace('$', '\\$')
        
        # 使用更简单的方式查找和替换功能描述部分
        sections = content.split("## 脚本功能\n\n")
        if len(sections) < 2:
            logging.warning(f"无法在文件中找到脚本功能部分: {md_file}")
            return
            
        first_part = sections[0] + "## 脚本功能\n\n"
        rest_parts = sections[1].split("\n\n## 依赖项")
        if len(rest_parts) < 2:
            logging.warning(f"无法在文件中找到依赖项部分: {md_file}")
            return
            
        rest_part = "\n\n## 依赖项" + rest_parts[1]
        
        # 组合新内容
        updated_content = first_part + safe_description + rest_part
        
        # 写回文件
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logging.info(f"已更新文档: {md_file}")
    
    except Exception as e:
        logging.error(f"更新文档 {md_file} 时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

def main():
    """
    主函数，扫描目录并更新文档
    """
    logging.info("开始扫描Python脚本并更新文档...")
    
    # 获取所有py文件
    py_files = []
    for root, _, files in os.walk(PY_DIR):
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    logging.info(f"找到 {len(py_files)} 个Python脚本")
    
    # 更新每个脚本对应的文档
    for py_file in py_files:
        # 构建对应的MD文件路径
        rel_path = os.path.relpath(py_file, PY_DIR)
        base_name = os.path.splitext(os.path.basename(rel_path))[0]
        md_file = os.path.join(MD_DIR, f"{base_name}.md")
        
        if os.path.exists(md_file):
            # 分析Python文件，提取功能描述
            description = analyze_python_file(py_file)
            
            # 更新Markdown文件
            update_markdown_file(md_file, description)
        else:
            logging.warning(f"未找到对应的Markdown文档: {md_file}")
    
    logging.info("文档更新完成!")

if __name__ == "__main__":
    main() 