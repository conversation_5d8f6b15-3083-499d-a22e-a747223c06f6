import pandas as pd
import os
import datetime

def get_oracle_type(data_type: str, length: str) -> str:
    """
    将Excel中的数据类型转换为Oracle数据类型

    Args:
        data_type: Excel中的数据类型
        length: 长度字段的值

    Returns:
        str: Oracle数据类型
    """
    data_type = str(data_type).lower().strip()
    length = str(length).strip()

    # 处理空值
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR2(255)'

    # 处理整数类型
    if '整数' in data_type:
        return 'NUMBER(16)'  # Oracle使用NUMBER类型表示整数

    # 处理数值类型（小数）
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'NUMBER({total_len},{decimal_len})'
                return f'NUMBER({length})'  # 如果没有指定小数位，使用整数精度
            except ValueError:
                return 'NUMBER(10,2)'  # 如果无法解析长度，使用默认值
        return 'NUMBER(10,2)'  # 如果没有指定长度，使用默认值

    # 处理字符串类型
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                # 当字段长度超过4000时，自动转换为CLOB类型
                if length_val > 4000:
                    return 'CLOB'
                return f'VARCHAR2({length})'
            except ValueError:
                return 'VARCHAR2(255)'
        return 'VARCHAR2(255)'

    # 处理日期时间类型
    if '日期' in data_type:
        # Oracle统一使用DATE类型，它包含日期和时间
        return 'DATE'

    # 处理时间类型（不含日期的情况）
    if '时间' in data_type:
        return 'DATE'  # Oracle中使用DATE存储时间

    # 默认返回VARCHAR2(255)
    return 'VARCHAR2(255)'

def clean_comment_text(text: str) -> str:
    """
    清理注释文本中的特殊字符

    Args:
        text: 原始文本

    Returns:
        str: 清理后的文本
    """
    # 处理空值
    if pd.isna(text) or text == 'nan':
        return ''

    # 转换为字符串并清理
    text = str(text).strip()

    # 替换换行符、回车符为空格
    text = text.replace('\n', ' ').replace('\r', ' ')

    # 替换制表符为空格
    text = text.replace('\t', ' ')

    # 替换多个空格为单个空格
    text = ' '.join(text.split())

    return text

def generate_create_table_sql(df: pd.DataFrame, table_counter: int) -> tuple[str, str]:
    """
    根据DataFrame生成Oracle建表SQL语句

    Args:
        df: 包含表结构的DataFrame
        table_counter: 表序号，用于生成唯一的约束名和索引名

    Returns:
        tuple: (建表SQL语句, 表名)
    """
    # 获取表名
    table_name = df.iloc[0]['表名'].strip()
    table_comment = clean_comment_text(df.iloc[0]['表中文名'])

    # SQL语句头部
    sql = f"CREATE TABLE {table_name} (\n"

    # 记录主键字段
    primary_keys = []
    # 记录唯一索引字段
    unique_index_fields = []

    # 添加字段定义
    comments = []  # 收集所有注释，Oracle需要单独添加注释
    for _, row in df.iterrows():
        field_name = row['字段名'].strip()
        data_type = get_oracle_type(row['数据类型'], row['长度'])
        is_primary = str(row['是否主键']).strip().upper()

        # 组合数据项和说明作为注释
        data_item = clean_comment_text(row.get('数据项', ''))
        description = clean_comment_text(row['说明'])

        # 检查是否需要创建唯一索引
        if '唯一索引' in str(row['说明']):
            unique_index_fields.append(field_name)

        comment_parts = []
        if data_item:
            comment_parts.append(data_item)
        if description:
            comment_parts.append(description)
        comment = ' '.join(comment_parts)

        nullable = '不能为空' in str(row['填报要求'])

        # 构建字段定义
        sql += f"    {field_name} {data_type}"

        # 添加是否可为空
        if nullable:
            sql += " NOT NULL"

        sql += ",\n"

        # 记录主键
        if is_primary == '是' or is_primary == 'Y' or is_primary == '1':
            primary_keys.append(field_name)

        # 收集注释（Oracle需要单独添加注释）
        if comment:
            comments.append(f"COMMENT ON COLUMN {table_name}.{field_name} IS ''{comment}''")

    # 修改主键约束名称格式，使用更短的命名并确保唯一性
    if primary_keys:
        # 使用PK_序号_表名前3个字符的格式
        constraint_name = f"PK_{table_counter}_{table_name[:3]}"
        sql += f"    CONSTRAINT {constraint_name} PRIMARY KEY ({', '.join(primary_keys)})\n"
    else:
        sql = sql.rstrip(",\n") + "\n"

    # 结束表定义
    sql += ")\n\n"

    # 添加表注释
    sql += f"COMMENT ON TABLE {table_name} IS ''{table_comment}''\n\n"

    # 添加字段注释
    sql += '\n'.join(comments) + '\n\n'

    # 修改唯一索引名称格式，使用更短的命名并确保唯一性
    if unique_index_fields:
        # 使用IDX_U_序号_表名前3个字符的格式
        index_name = f"IDX_U_{table_counter}_{table_name[:3]}"
        sql += f"CREATE UNIQUE INDEX {index_name} ON {table_name} ({', '.join(unique_index_fields)})\n\n"

    return sql, table_name

def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"oracle{current_date}.sql")

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建\n\n')

            # 创建日志表
            f.write("""
ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS';

-- 删除已存在的对象
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE table_creation_log PURGE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 创建日志表
CREATE TABLE table_creation_log (
    id NUMBER PRIMARY KEY,
    table_name VARCHAR2(255) NOT NULL,
    status VARCHAR2(10) NOT NULL,
    message CLOB,
    error_code VARCHAR2(10),
    create_time TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- 创建序列
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE table_creation_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE table_creation_log_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

-- 创建触发器
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER table_creation_log_trg';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE OR REPLACE TRIGGER table_creation_log_trg
BEFORE INSERT ON table_creation_log
FOR EACH ROW
BEGIN
    IF :new.id IS NULL THEN
        SELECT table_creation_log_seq.nextval INTO :new.id FROM dual;
    END IF;
END;
/

-- 创建存储过程
CREATE OR REPLACE PROCEDURE log_table_result(
    p_table_name IN VARCHAR2,
    p_status IN VARCHAR2,
    p_message IN VARCHAR2,
    p_error_code IN VARCHAR2
) AS
BEGIN
    INSERT INTO table_creation_log (table_name, status, message, error_code)
    VALUES (p_table_name, p_status, p_message, p_error_code);
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error logging result for table ' || p_table_name || ': ' || SQLERRM);
END;
/\n\n""")

            # 为每个表添加错误处理
            for idx, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                sql, table_name = generate_create_table_sql(group, idx)

                # 分离建表语句和注释语句
                create_table = sql.split('\n\n')[0]  # 第一个语句是建表语句（不带分号）
                comments = '\n'.join(sql.split('\n\n')[1:]).strip()  # 剩余的都是注释语句

                # 执行建表语句（带错误处理）
                f.write(f"""
-- 创建表 {table_name}
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[{create_table.strip()}]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
""")

                # 分别写入每条注释语句
                for comment in comments.strip().split('\n'):
                    if comment.strip():
                        f.write(f"""    EXECUTE IMMEDIATE '{comment.strip()}';\n""")

                f.write(f"""
    log_table_result('{table_name}', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('{table_name}', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/\n\n""")

            # 添加最终结果统计查询
            f.write("""
-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = '成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = '失败' THEN 1 ELSE 0 END) as failed_count
FROM table_creation_log;

-- 显示失败的表详情
SELECT 
    table_name, 
    message, 
    error_code,
    create_time 
FROM table_creation_log 
WHERE status = '失败' 
ORDER BY create_time;""")

        print(f"\n成功生成SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("请注意：每个表的创建都包含了错误处理逻辑，如果某个表创建失败，脚本会继续执行后续表的创建")
        print("所有表的创建结果都会记录在 table_creation_log 表中，包含具体的错误代码和错误信息")

    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
