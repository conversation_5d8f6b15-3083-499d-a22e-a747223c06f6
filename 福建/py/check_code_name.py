def check_code_name(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    for i in range(len(lines) - 1):  # 遍历每一行，注意最后一行不需要检查
        current_line = lines[i].strip()
        next_line = lines[i + 1].strip()
        # 排除包含“医疗机构统一社会信用代码”的行
        if '统一社会信用代码' in current_line:
            continue
        if '代码' in current_line and '名称' not in next_line:
            print(f"行号: {i+1}, 内容: {current_line}")  # 行号从1开始，因此i+2
            # print(current_line)  # 行号从1开始，因此i+2


# 调用函数
file_path = r'D:\work\demo\福建\查找代码名称.txt'
check_code_name(file_path)

