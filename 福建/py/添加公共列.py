import pandas as pd
import openpyxl
from openpyxl.styles import Pattern<PERSON>ill, Font, Border, Alignment, Color
from copy import copy

def copy_row_with_formatting(source_ws, target_ws, source_row, target_row):
    """
    复制行数据并保持原格式

    Args:

        source_ws: 源工作表
        target_ws: 目标工作表
        source_row: 源行号
        target_row: 目标行号
    """
    for col in range(1, source_ws.max_column + 1):
        source_cell = source_ws.cell(row=source_row, column=col)
        target_cell = target_ws.cell(row=target_row, column=col)

        # 复制值
        target_cell.value = source_cell.value

        # 复制填充颜色
        if source_cell.fill and source_cell.fill.start_color:
            try:
                # 检查是否有有效的RGB值
                if hasattr(source_cell.fill.start_color, 'rgb') and source_cell.fill.start_color.rgb:
                    rgb = source_cell.fill.start_color.rgb
                    # 如果RGB值是自动的或None，跳过颜色复制
                    if rgb in ('00000000', None):
                        continue
                    # 如果RGB值以'FF'开头，去掉它
                    if rgb.startswith('FF'):
                        rgb = rgb[2:]
                    target_cell.fill = PatternFill(fill_type='solid', start_color=rgb)
                elif source_cell.fill.start_color.type == 'theme':
                    # 如果是主题颜色，直接复制整个fill对象
                    target_cell.fill = copy(source_cell.fill)
            except:
                # 如果复制填充失败，保持默认
                pass

        # 复制字体格式
        if source_cell.font:
            target_cell.font = copy(source_cell.font)

        # 复制边框样式
        if source_cell.border:
            target_cell.border = copy(source_cell.border)

        # 复制对齐方式
        if source_cell.alignment:
            target_cell.alignment = copy(source_cell.alignment)

def process_excel_file():
    # 读取 Excel 文件
    file_path = r'D:\work\demo\福建\添加公共字段.xlsx'

    # 读取三个 sheet
    sheet1 = pd.read_excel(file_path, sheet_name='Sheet1')
    sheet2 = pd.read_excel(file_path, sheet_name='Sheet2', header=None)
    sheet3 = pd.read_excel(file_path, sheet_name='Sheet3', header=None)

    # 提取Sheet2和Sheet3的数据
    pre_data = sheet2.values.tolist()
    post_data = sheet3.values.tolist()

    # 加载 Excel 文件
    wb = openpyxl.load_workbook(file_path)
    ws1 = wb['Sheet1']

    # 创建一个新的工作表
    new_sheet = wb.create_sheet('ProcessedSheet')

    # 获取Sheet1的最大行数
    max_row = ws1.max_row
    current_row = 1

    # 设置黄色填充
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

    # 获取上一个表名
    last_table_name = None

    try:
        # 遍历Sheet1中的每一行
        for i in range(1, max_row + 1):
            # 获取当前行的表名
            table_name = ws1.cell(row=i, column=2).value

            # 获取当前行的前4列数据
            first_four_columns = [ws1.cell(row=i, column=col).value for col in range(1, 5)]

            # 判断是否进入新的一组（当前表名与上一表名不同）
            if last_table_name != table_name:
                # 在当前分组的第一行前插入Sheet2的数据
                for j in range(len(pre_data)):
                    row_data = first_four_columns + pre_data[j][4:] if len(pre_data[j]) > 4 else pre_data[j]
                    for col in range(len(row_data)):
                        new_cell = new_sheet.cell(row=current_row, column=col + 1)
                        new_cell.value = row_data[col]
                        new_cell.fill = yellow_fill
                    current_row += 1

            # 复制当前行数据，保留格式
            copy_row_with_formatting(ws1, new_sheet, i, current_row)
            current_row += 1

            # 判断是否是当前分组的最后一行（表名变化或最后一行）
            if i == max_row or ws1.cell(row=i + 1, column=2).value != table_name:
                # 插入Sheet3的数据
                for j in range(len(post_data)):
                    row_data = first_four_columns + post_data[j][4:] if len(post_data[j]) > 4 else post_data[j]
                    for col in range(len(row_data)):
                        new_cell = new_sheet.cell(row=current_row, column=col + 1)
                        new_cell.value = row_data[col]
                        new_cell.fill = yellow_fill
                    current_row += 1

            # 更新上一个表名
            last_table_name = table_name

        # 保存修改后的文件
        output_path = r'/福建/processed_file_with_groups.xlsx'
        wb.save(output_path)
        print(f"数据处理完成，结果保存在 '{output_path}'")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise  # 为了调试目的，重新抛出异常
    finally:
        wb.close()

if __name__ == "__main__":
    process_excel_file()
