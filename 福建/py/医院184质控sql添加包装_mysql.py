#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""

脚本名称：医院184质控sql添加包装_mysql.py
描述：从Excel文件中读取SQL查询语句，并生成带有错误处理机制的MySQL执行文件
作者：chenlu
日期：2025-07-04
"""

import pandas as pd
import re
import os
import logging
import datetime
from pathlib import Path

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def read_sql_queries_from_excel(excel_path):
    """
    从Excel文件中读取SQL查询
    
    Args:
        excel_path (str): Excel文件路径
        
    Returns:
        list: SQL查询列表
    """
    try:
        logger.info(f"开始读取Excel文件: {excel_path}")
        
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
        
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        # 检查是否包含"质控规则mysql"列
        if '质控规则mysql' not in df.columns:
            raise ValueError(f"Excel文件中未找到'质控规则mysql'列，当前列名: {list(df.columns)}")
        
        # 过滤出非空且包含SELECT的SQL查询
        sql_queries = []
        for i, query in enumerate(df['质控规则mysql']):
            if isinstance(query, str) and 'SELECT' in query.upper():
                sql_queries.append(query)
        
        logger.info(f"成功从Excel文件中读取了 {len(sql_queries)} 条SQL查询")
        
        if len(sql_queries) == 0:
            logger.warning("未从Excel文件中读取到有效的SQL查询")
        
        return sql_queries
    except Exception as e:
        logger.error(f"读取Excel文件时出错: {str(e)}")
        raise

def extract_metadata_from_sql(sql_query):
    """
    从SQL查询中提取元数据：tabname, rule_no, rule_desc
    
    Args:
        sql_query (str): SQL查询语句
        
    Returns:
        dict: 包含tabname, rule_no, rule_desc的字典
    """
    try:
        # 初始化结果
        metadata = {
            'tabname': None,
            'rule_no': None,
            'rule_desc': None
        }
        
        # 提取tabname
        tabname_pattern = r"'([^']*)'\s+AS\s+tabname"
        tabname_match = re.search(tabname_pattern, sql_query, re.IGNORECASE)
        if tabname_match:
            metadata['tabname'] = tabname_match.group(1)
        
        # 提取rule_no
        rule_no_pattern = r"'([^']*)'\s+AS\s+rule_no"
        rule_no_match = re.search(rule_no_pattern, sql_query, re.IGNORECASE)
        if rule_no_match:
            metadata['rule_no'] = rule_no_match.group(1)
        
        # 提取rule_desc
        rule_desc_pattern = r"'([^']*)'\s+AS\s+rule_desc"
        rule_desc_match = re.search(rule_desc_pattern, sql_query, re.IGNORECASE)
        if rule_desc_match:
            metadata['rule_desc'] = rule_desc_match.group(1)
        
        # 检查是否成功提取到元数据
        missing_fields = [field for field, value in metadata.items() if value is None]
        if missing_fields:
            logger.warning(f"无法从SQL查询中提取以下元数据: {', '.join(missing_fields)}")
            logger.debug(f"SQL查询: {sql_query}")
        
        return metadata
    except Exception as e:
        logger.error(f"提取SQL元数据时出错: {str(e)}")
        # 出错时返回空元数据，以便程序能够继续执行
        return {'tabname': None, 'rule_no': None, 'rule_desc': None}

def escape_sql_string(s):
    """
    转义SQL字符串中的单引号
    
    Args:
        s (str): 需要转义的字符串
        
    Returns:
        str: 转义后的字符串
    """
    if s is None:
        return 'NULL'
    return s.replace("'", "''")

def generate_sql_script(sql_queries):
    """
    生成包含错误处理机制的MySQL执行文件
    
    Args:
        sql_queries (list): SQL查询列表
        
    Returns:
        str: 生成的SQL脚本
    """
    logger.info("开始生成SQL脚本...")
    
    # 1. 创建日志表的SQL
    create_table_sql = """-- 质控执行脚本 - 自动生成
-- 生成时间: """ + pd.Timestamp('now').strftime("%Y-%m-%d %H:%M:%S") + """

-- 删除可能存在的日志表
DROP TABLE IF EXISTS quality_control_log;

-- 创建日志表
CREATE TABLE quality_control_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rule_no VARCHAR(50),
    tabname VARCHAR(100),
    rule_desc TEXT,
    dty_count INT,
    error_desc TEXT,
    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

"""
    
    # 2. 存储过程开始部分
    proc_start = """-- 创建存储过程，用于执行质控查询
DELIMITER //

CREATE PROCEDURE execute_quality_control_queries()
BEGIN
    -- 声明变量
    DECLARE v_tabname VARCHAR(100);
    DECLARE v_rule_no VARCHAR(50);
    DECLARE v_rule_desc TEXT;
    DECLARE v_dty_count INT;
    DECLARE v_error_text TEXT;
    
    -- 声明错误处理器
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 获取错误信息
        GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, 
        @errno = MYSQL_ERRNO, @text = MESSAGE_TEXT;
        SET @full_error = CONCAT('ERROR ', @errno, ' (', @sqlstate, '): ', @text);
        
        -- 记录错误信息
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        VALUES (v_rule_no, v_tabname, v_rule_desc, -1, @full_error);
    END;

"""
    
    # 3. 为每个SQL查询生成执行代码
    query_executions = []
    
    for i, sql in enumerate(sql_queries):
        # 提取元数据
        metadata = extract_metadata_from_sql(sql)
        tabname = metadata['tabname']
        rule_no = metadata['rule_no']
        rule_desc = metadata['rule_desc']
        
        # 处理可能的空值
        tabname = tabname if tabname else f'unknown_table_{i}'
        rule_no = rule_no if rule_no else f'unknown_rule_{i}'
        rule_desc = rule_desc if rule_desc else f'查询 #{i+1} - 未找到描述'
        
        # 转义字符串
        tabname_escaped = escape_sql_string(tabname)
        rule_no_escaped = escape_sql_string(rule_no)
        rule_desc_escaped = escape_sql_string(rule_desc)
        
        # 构建执行代码
        query_execution = f"""
    -- 执行第 {i+1} 条质控规则mysql
    SET v_tabname = '{tabname_escaped}';
    SET v_rule_no = '{rule_no_escaped}';
    SET v_rule_desc = '{rule_desc_escaped}';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TEMPORARY TABLE IF EXISTS temp_result;
        CREATE TEMPORARY TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        {sql.rstrip(';')};
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
    END;
"""
        query_executions.append(query_execution)
    
    # 4. 存储过程结束部分
    proc_end = """
END //

DELIMITER ;

-- 执行存储过程
CALL execute_quality_control_queries();

DROP PROCEDURE IF EXISTS execute_quality_control_queries;

-- 显示结果
SELECT * FROM quality_control_log ORDER BY id;
"""
    
    # 5. 组合成完整的SQL脚本
    complete_sql = create_table_sql + proc_start + ''.join(query_executions) + proc_end
    
    logger.info(f"SQL脚本生成完成，总长度: {len(complete_sql)} 字符")
    return complete_sql

def write_sql_script_to_file(sql_script, output_path):
    """
    将生成的SQL脚本写入文件
    
    Args:
        sql_script (str): 生成的SQL脚本
        output_path (str): 输出文件路径
    """
    try:
        logger.info(f"开始将SQL脚本写入文件: {output_path}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")
        
        # 写入文件，使用utf-8编码，并添加BOM标记
        with open(output_path, 'w', encoding='utf-8-sig') as f:
            f.write(sql_script)
        
        logger.info(f"SQL脚本已成功写入文件: {output_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(output_path)
        logger.info(f"输出文件大小: {file_size} 字节")
    except Exception as e:
        logger.error(f"写入SQL脚本文件时出错: {str(e)}")
        raise

# 主函数将在此处实现
def main():
    """
    主函数
    """
    # 配置输入和输出路径
    excel_path = r"D:\work\demo\福建\质控模板\医院184质控sql添加包装模板.xlsx"
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = f"D:\work\demo\福建\建表sql\质控sql\mysql_184质控_记录结果版_{current_date}.sql"
    
    logger.info("脚本开始执行")
    logger.info(f"输入Excel文件: {excel_path}")
    logger.info(f"输出SQL文件: {output_path}")
    
    try:
        # 1. 读取Excel文件中的SQL查询
        sql_queries = read_sql_queries_from_excel(excel_path)
        
        # 2. 测试元数据提取功能
        if sql_queries:
            logger.info("测试元数据提取功能:")
            sample_sql = sql_queries[0]
            metadata = extract_metadata_from_sql(sample_sql)
            logger.info(f"  - 表名 (tabname): {metadata['tabname']}")
            logger.info(f"  - 规则编号 (rule_no): {metadata['rule_no']}")
            logger.info(f"  - 规则描述 (rule_desc): {metadata['rule_desc']}")
        
        # 3. 生成SQL脚本
        sql_script = generate_sql_script(sql_queries)
        
        # 4. 将SQL脚本写入文件
        write_sql_script_to_file(sql_script, output_path)
        
        logger.info("脚本执行完成")
    except Exception as e:
        logger.error(f"脚本执行出错: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 