import re
import openpyxl

def parse_sql_fields(sql):
    # 提取 SELECT 到 FROM 之间的字段内容
    match = re.search(r"select(.*?)from", sql, re.IGNORECASE | re.DOTALL)
    if not match:
        print("未找到 SELECT 和 FROM 之间的字段")
        return []

    fields_str = match.group(1)
    # 按逗号分隔字段，考虑换行和空格
    fields = [f.strip() for f in fields_str.split(",") if f.strip()]

    result = []
    for field in fields:
        # 去除别名（AS 或 直接空格形式）
        field = re.sub(r"\s+AS\s+[\w\"\.]+", "", field, flags=re.IGNORECASE)
        field = re.sub(r"\s+[\w\"\.]+$", "", field) if " " in field else field

        if "." in field:
            table, col = field.split(".", 1)
            result.append((table.strip(), col.strip().strip('"')))
        else:
            result.append(("", field.strip().strip('"')))  # 没有表名时保留字段名

    return result

def write_to_excel(data, filename="字段解析结果.xlsx"):
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "字段解析结果"
    ws.append(["表名", "字段名"])

    for table, column in data:
        ws.append([table, column])

    wb.save(filename)
    print(f"已保存到 {filename}")

def main():
    input_file = "D:\work\demo\福建\宽表sql.sql"
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            sql = f.read()
    except FileNotFoundError:
        print(f"未找到文件: {input_file}")
        return

    parsed_fields = parse_sql_fields(sql)
    import re
import openpyxl

def clean_sql_lines(sql):
    # 去掉注释和空白行
    cleaned_lines = []
    in_block_comment = False

    for line in sql.splitlines():
        line = line.strip()
        if not line:
            continue
        if line.startswith('--'):
            continue
        if '/*' in line:
            in_block_comment = True
        if '*/' in line:
            in_block_comment = False
            continue
        if in_block_comment:
            continue
        cleaned_lines.append(line)

    return " ".join(cleaned_lines)  # 将所有有效 SQL 拼成一行

def split_sql_segments(sql):
    # 将多个 SELECT ... FROM 块拆分
    # 用正则寻找所有的 SELECT ... FROM ... WHERE ... 或 SELECT ... FROM ... JOIN ...
    # 假设每个 SELECT 开头的语句为一个块
    sql = sql.replace('\n', ' ')
    return re.findall(r"(select .*? from .*?)(?=select|$)", sql, flags=re.IGNORECASE | re.DOTALL)

def parse_sql_fields(sql_segment):
    # 提取字段
    match = re.search(r"select(.*?)from", sql_segment, re.IGNORECASE | re.DOTALL)
    if not match:
        return []

    fields_str = match.group(1)
    fields = [f.strip() for f in fields_str.split(",") if f.strip()]

    result = []
    for field in fields:
        field = re.sub(r"\s+AS\s+[\w\"\.]+", "", field, flags=re.IGNORECASE)
        field = re.sub(r"\s+[\w\"\.]+$", "", field) if " " in field else field

        if "." in field:
            table, col = field.split(".", 1)
            result.append((table.strip(), col.strip().strip('"')))
        else:
            result.append(("", field.strip().strip('"')))
    return result

def extract_main_table(sql_segment):
    # 提取 FROM 后的第一个表名
    match = re.search(r"from\s+([a-zA-Z0-9_\.]+)", sql_segment, flags=re.IGNORECASE)
    if match:
        return match.group(1)
    return "未知表"

def write_to_excel(all_results, filename="字段解析结果.xlsx"):
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "字段解析结果"
    ws.append(["表名", "字段名"])

    for section in all_results:
        table_name = section['table']
        ws.append([f"【{table_name}】", ""])  # 添加表名标题行
        for table, column in section['fields']:
            ws.append([table, column])
        ws.append([])  # 添加空行分隔不同SQL段

    wb.save(filename)
    print(f"✅ 已保存到 {filename}")

def main():
    input_file = "D:\work\demo\福建\宽表sql.sql"
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            raw_sql = f.read()
    except FileNotFoundError:
        print(f"❌ 未找到文件: {input_file}")
        return

    cleaned_sql = clean_sql_lines(raw_sql)
    sql_segments = split_sql_segments(cleaned_sql)

    all_results = []
    for seg in sql_segments:
        fields = parse_sql_fields(seg)
        table_name = extract_main_table(seg)
        all_results.append({
            'table': table_name,
            'fields': fields
        })

    write_to_excel(all_results)

# if __name__ == "__main__":
#     main()
# write_to_excel(parsed_fields)

if __name__ == "__main__":
    main()
