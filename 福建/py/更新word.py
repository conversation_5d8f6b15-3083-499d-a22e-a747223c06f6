import os
from docx import Document
import pandas as pd
from docx.shared import Pt

def load_replacement_map(excel_path: str) -> dict:
    """
    从Excel文件加载替换对照关系

    Args:
        excel_path: Excel文件路径

    Returns:
        dict: 替换对照字典，key为要替换的文本，value为替换后的文本
    """
    try:
        # 读取Excel文件的前两列
        df = pd.read_excel(excel_path, usecols=[0, 1])
        # 创建替换字典
        replacement_map = dict(zip(df.iloc[:, 0], df.iloc[:, 1]))
        print(f"已加载 {len(replacement_map)} 个替换规则")
        return replacement_map
    except Exception as e:
        print(f"加载替换规则时发生错误: {str(e)}")
        return {}

def replace_text_in_doc(input_path: str, output_path: str, filename: str, replacement_map: dict) -> bool:
    """
    根据替换规则更新Word文档中的文本

    Args:
        input_path: 输入文件完整路径
        output_path: 输出文件完整路径
        filename: 文件名
        replacement_map: 替换对照字典

    Returns:
        bool: 处理是否成功
    """
    try:
        # 打开文档
        doc = Document(input_path)
        update_count = 0

        # 更新表格中的文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    # 检查单元格文本是否需要替换
                    for old_text, new_text in replacement_map.items():
                        if old_text in cell_text:
                            cell.text = cell_text.replace(old_text, str(new_text))
                            # 设置单元格中段落的字体
                            for paragraph in cell.paragraphs:
                                if paragraph.runs:
                                    paragraph.runs[0].font.size = Pt(9)
                                    paragraph.runs[0].font.name = u'宋体'
                            update_count += 1
                            print(f"更新表格单元格: {old_text} -> {new_text}")

        # 更新段落中的文本
        for paragraph in doc.paragraphs:
            original_text = paragraph.text
            modified_text = original_text

            # 检查段落文本是否需要替换
            for old_text, new_text in replacement_map.items():
                if old_text in modified_text:
                    modified_text = modified_text.replace(old_text, str(new_text))
                    update_count += 1
                    print(f"更新段落文本: {old_text} -> {new_text}")

            if modified_text != original_text:
                paragraph.text = modified_text
                # 设置段落字体
                if paragraph.runs:
                    paragraph.runs[0].font.size = Pt(9)
                    paragraph.runs[0].font.name = u'宋体'

        # 保存文档
        doc.save(output_path)
        print(f"已处理文件: {filename} (更新了 {update_count} 处文本)")
        return True

    except Exception as e:
        print(f"处理文件 {filename} 时发生错误: {str(e)}")
        return False

def process_all_docs():
    """处理所有文档"""
    # 设置路径
    base_path = r'/福建'
    input_dir = os.path.join(base_path, '待替换')
    output_dir = os.path.join(base_path, '已替换')
    excel_path = os.path.join(base_path, '替换word.xlsx')

    # 加载替换规则
    replacement_map = load_replacement_map(excel_path)
    if not replacement_map:
        print("未能加载替换规则，程序终止")
        return

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")

    # 确保输入目录存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        return

    # 获取所有word文档
    docx_files = [f for f in os.listdir(input_dir) if f.endswith('.docx')]

    if not docx_files:
        print("未找到Word文档")
        return

    print(f"找到 {len(docx_files)} 个Word文档")
    success_count = 0

    # 处理每个文档
    for filename in docx_files:
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename)

        if replace_text_in_doc(input_path, output_path, filename, replacement_map):
            success_count += 1

    # 打印处理结果
    print(f"\n处理完成:")
    print(f"总文件数: {len(docx_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {len(docx_files) - success_count}")

if __name__ == "__main__":
    print("开始处理文档...")
    process_all_docs()
    print("处理结束")
