import os
from docx import Document
import shutil
from datetime import datetime

def remove_tables_and_titles_from_doc(input_path: str, output_path: str, filename: str) -> bool:
    """
    从Word文档中删除除前两个表格外的所有表格

    Args:
        input_path: 输入文件完整路径
        output_path: 输出文件完整路径
        filename: 文件名

    Returns:
        bool: 处理是否成功
    """
    try:
        # 打开文档
        doc = Document(input_path)

        # 获取所有表格
        tables = doc.tables
        total_tables = len(tables)

        # 只删除第三个表格开始的表格
        tables_to_remove = tables[0:]
        removed_count = len(tables_to_remove)

        # 从后往前删除表格，避免索引变化的问题
        for table in reversed(tables_to_remove):
            element = table._element
            element.getparent().remove(element)

        # 确保输出文件的目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存文档
        doc.save(output_path)
        print(f"已处理文件: {filename}")
        print(f"- 总表格数: {total_tables}")
        # print(f"- 保留了前2个表格")
        print(f"- 删除了 {removed_count} 个表格")
        return True

    except Exception as e:
        print(f"处理文件 {filename} 时发生错误: {str(e)}")
        return False

def process_all_docs():
    """递归处理所有文档，保持文件夹结构"""
    # 设置路径
    base_path = r'D:\work\demo\福建'
    input_dir = os.path.join(base_path, '待删除表格文档')
    output_dir = os.path.join(base_path, '已删除表格文档')

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")

    # 确保输入目录存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        return

    success_count = 0
    total_files = 0

    # 递归遍历所有子文件夹
    for root, dirs, files in os.walk(input_dir):
        # 获取相对路径，用于在输出目录中创建相同的结构
        rel_path = os.path.relpath(root, input_dir)

        # 处理当前文件夹中的所有word文档
        for filename in files:
            if filename.endswith('.docx'):
                total_files += 1
                input_path = os.path.join(root, filename)
                # 构建输出路径，保持相同的目录结构
                output_path = os.path.join(output_dir, rel_path, filename)

                if remove_tables_and_titles_from_doc(input_path, output_path, os.path.join(rel_path, filename)):
                    success_count += 1

    # 打印处理结果
    print(f"\n处理完成:")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {total_files - success_count}")

if __name__ == "__main__":
    print("开始处理文档...")
    process_all_docs()
    print("处理结束")
