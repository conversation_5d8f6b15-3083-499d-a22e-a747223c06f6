from 质控mysql转其他库 import process_single_sql

# 用户提供的原始 SQL
user_sql = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,date_format(rec_time,'%Y%m') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= '2020-01-01 00:00:00' AND rec_time <= '2025-05-31 23:59:59' 
AND deleted = '0' 
GROUP BY uscid,date_format(rec_time,'%Y%m') 
ORDER BY date_format(rec_time,'%Y%m')"""

# 处理 SQL
process_single_sql(user_sql) 