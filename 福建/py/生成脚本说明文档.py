#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本脚本用于扫描福建/py目录下的所有Python脚本，
并在福建/MD目录下为每个脚本生成对应的Markdown文件说明其功能和用法
"""

import os
import re
import ast
from typing import Dict, List, Tuple, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("生成脚本说明文档.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 文件路径配置
PY_DIR = r'D:\work\demo\福建\py'
MD_DIR = r'D:\work\demo\福建\MD'

# 如果输出目录不存在，创建它
if not os.path.exists(MD_DIR):
    os.makedirs(MD_DIR)

def extract_docstring(file_path: str) -> <PERSON><PERSON>[Optional[str], Dict[str, str]]:
    """
    提取Python文件中的文档字符串和函数信息

    Args:
        file_path: Python文件的路径

    Returns:
        Tuple[Optional[str], Dict[str, str]]: 模块文档字符串和函数信息的字典
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        # 解析Python代码
        try:
            tree = ast.parse(file_content)
            
            # 提取模块级文档字符串
            module_docstring = ast.get_docstring(tree)
            
            # 提取函数信息
            functions = {}
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_name = node.name
                    func_docstring = ast.get_docstring(node)
                    if func_docstring:
                        functions[func_name] = func_docstring
            
            return module_docstring, functions
        
        except SyntaxError:
            # 如果无法解析代码，尝试手动提取文档字符串
            module_docstring_match = re.search(r'"""(.*?)"""', file_content, re.DOTALL)
            module_docstring = module_docstring_match.group(1).strip() if module_docstring_match else None
            
            return module_docstring, {}
    
    except Exception as e:
        logging.error(f"处理文件 {file_path} 时出错: {str(e)}")
        return None, {}

def extract_imports(file_path: str) -> List[str]:
    """
    提取Python文件中的导入语句

    Args:
        file_path: Python文件的路径

    Returns:
        List[str]: 导入语句列表
    """
    imports = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 匹配import语句
                if line.strip().startswith(('import ', 'from ')):
                    imports.append(line.strip())
    except Exception as e:
        logging.error(f"提取导入语句时出错 {file_path}: {str(e)}")
    
    return imports

def extract_main_function(file_path: str) -> Optional[str]:
    """
    提取Python文件中的main函数或主执行代码

    Args:
        file_path: Python文件的路径

    Returns:
        Optional[str]: main函数代码或None
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找main函数定义
        main_func_match = re.search(r'def\s+main\s*\(\s*\)\s*:.*?(?=\n\S|$)', content, re.DOTALL)
        if main_func_match:
            return main_func_match.group(0)
        
        # 查找if __name__ == "__main__"部分
        main_block_match = re.search(r'if\s+__name__\s*==\s*[\'"]__main__[\'"]\s*:.*?(?=\n\S|$)', content, re.DOTALL)
        if main_block_match:
            return main_block_match.group(0)
        
        return None
    
    except Exception as e:
        logging.error(f"提取main函数时出错 {file_path}: {str(e)}")
        return None

def get_file_stats(file_path: str) -> Dict:
    """
    获取文件的统计信息

    Args:
        file_path: 文件的路径

    Returns:
        Dict: 包含文件统计信息的字典
    """
    try:
        stats = {
            'size': os.path.getsize(file_path),
            'lines': 0,
            'functions': 0,
            'classes': 0
        }
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            stats['lines'] = len(content.splitlines())
            
            # 计算函数和类的数量
            try:
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        stats['functions'] += 1
                    elif isinstance(node, ast.ClassDef):
                        stats['classes'] += 1
            except:
                pass  # 解析失败时不计算函数和类
        
        return stats
    
    except Exception as e:
        logging.error(f"获取文件统计信息时出错 {file_path}: {str(e)}")
        return {'size': 0, 'lines': 0, 'functions': 0, 'classes': 0}

def generate_markdown_doc(py_file: str, md_file: str) -> None:
    """
    为Python脚本生成Markdown文档

    Args:
        py_file: Python文件的路径
        md_file: 输出的Markdown文件路径
    """
    try:
        # 提取文件名作为标题
        file_name = os.path.basename(py_file)
        
        # 获取文档字符串和函数信息
        docstring, functions = extract_docstring(py_file)
        
        # 获取导入语句
        imports = extract_imports(py_file)
        
        # 获取main函数或主执行代码
        main_code = extract_main_function(py_file)
        
        # 获取文件统计信息
        stats = get_file_stats(py_file)
        
        # 组织Markdown内容
        with open(md_file, 'w', encoding='utf-8') as f:
            # 标题
            f.write(f"# {file_name}\n\n")
            
            # 基本信息
            f.write(f"- **文件路径**: `{py_file}`\n")
            f.write(f"- **文件大小**: {stats['size'] // 1024 + 1}KB\n")
            f.write(f"- **代码行数**: {stats['lines']}行\n")
            f.write(f"- **函数数量**: {stats['functions']}个\n")
            f.write(f"- **类数量**: {stats['classes']}个\n\n")
            
            # 脚本描述
            f.write("## 脚本功能\n\n")
            if docstring:
                f.write(f"{docstring}\n\n")
            else:
                f.write("*未提供脚本功能描述*\n\n")
            
            # 依赖项
            f.write("## 依赖项\n\n")
            if imports:
                f.write("```python\n")
                for imp in imports:
                    f.write(f"{imp}\n")
                f.write("```\n\n")
            else:
                f.write("*无导入语句*\n\n")
            
            # 主要函数和用法
            f.write("## 主要函数\n\n")
            if functions:
                for func_name, func_doc in functions.items():
                    f.write(f"### `{func_name}`\n\n")
                    f.write(f"{func_doc}\n\n")
            else:
                f.write("*未识别到函数文档*\n\n")
            
            # 使用示例
            f.write("## 使用示例\n\n")
            if main_code:
                f.write("```python\n")
                f.write(f"{main_code}\n")
                f.write("```\n\n")
            else:
                f.write("*未找到主函数或使用示例*\n\n")
            
            # 注意事项和说明
            f.write("## 注意事项\n\n")
            f.write("- 运行此脚本前请确保已安装所需的依赖库\n")
            f.write("- 请确保文件路径和权限设置正确\n\n")
            
            # 生成时间
            import datetime
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"*文档生成时间: {current_time}*\n")
        
        logging.info(f"已生成文档: {md_file}")
    
    except Exception as e:
        logging.error(f"生成文档 {md_file} 时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

def main():
    """
    主函数，扫描目录并生成文档
    """
    logging.info("开始扫描Python脚本并生成文档...")
    
    # 获取所有py文件
    py_files = []
    for root, _, files in os.walk(PY_DIR):
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    logging.info(f"找到 {len(py_files)} 个Python脚本")
    
    # 为每个脚本生成文档
    for py_file in py_files:
        # 构建对应的MD文件路径
        rel_path = os.path.relpath(py_file, PY_DIR)
        base_name = os.path.splitext(os.path.basename(rel_path))[0]
        md_file = os.path.join(MD_DIR, f"{base_name}.md")
        
        generate_markdown_doc(py_file, md_file)
    
    logging.info("文档生成完成!")

if __name__ == "__main__":
    main() 