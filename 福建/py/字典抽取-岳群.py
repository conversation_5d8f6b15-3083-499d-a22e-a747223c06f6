import os
from docx import Document
import pandas as pd
import re
from datetime import datetime
from tqdm import tqdm

# 定义固定的Excel表头
EXCEL_COLUMNS = ['大类', '字典类别', '字典编码', '字典名称', '备注', '执行标准']

def clean_text(text):
    """清理文本，去除多余的空白字符"""
    if not isinstance(text, str):
        return ""
    return re.sub(r'\s+', ' ', text).strip()

def clean_title(title):
    """清理标题，去除数字编号和特殊字符"""
    # 去掉标题开头的数字和点，并清理空白
    cleaned = re.sub(r'^\d+\.?\d*\s*', '', title)
    # 移除多余的空白字符
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 移除特殊字符
    cleaned = re.sub(r'[【】\[\]（）\(\),，]', '', cleaned)
    # 移除表格、图等词
    cleaned = re.sub(r'^(表|图)\s*\d*\s*', '', cleaned)
    return cleaned.strip()

def get_all_tables_and_titles(doc):
    """获取文档中所有的表格和对应的标题"""
    tables_and_titles = []
    current_title = ""
    table_count = 0
    
    # 预处理：收集所有段落和表格
    elements = []
    for element in doc.element.body.iter():
        if element.tag.endswith('p') or element.tag.endswith('tbl'):
            elements.append(element)
    
    # 处理收集到的元素
    for element in elements:
        if element.tag.endswith('p'):
            # 处理段落
            for para in doc.paragraphs:
                if para._element == element:
                    if hasattr(para, 'style') and para.style:
                        style_name = para.style.name
                        if ('Heading' in style_name or '标题' in style_name or 'toc' in style_name):
                            text = para.text.strip()
                            if text:
                                current_title = clean_title(text)
                    break
        elif element.tag.endswith('tbl'):
            # 处理表格
            table_count += 1
            for table in doc.tables:
                if table._element == element:
                    tables_and_titles.append((table, current_title, table_count))
                    break
    
    return tables_and_titles

def is_valid_table(table):
    """检查是否是有效的表格"""
    if len(table.rows) < 2:
        return False
    
    # 获取表头
    headers = [clean_text(cell.text) for cell in table.rows[0].cells]
    if len(headers) < 2:  # 至少需要两列（编码和名称）
        return False
        
    # 检查第一行是否包含数据
    first_row = [clean_text(cell.text) for cell in table.rows[1].cells]
    if not any(first_row):
        return False
        
    return True

def process_table(table, title, table_count):
    """处理单个表格"""
    data = []
    
    # 跳过无效表格
    if not is_valid_table(table):
        return data
        
    # 处理表格数据
    for row_index, row in enumerate(table.rows[1:], 1):
        row_data = [clean_text(cell.text) for cell in row.cells]
        if not any(row_data):  # 跳过空行
            continue
            
        # 创建数据字典
        excel_row = {
            '大类': '',  # 暂时留空
            '字典类别': title,  # 使用当前标题作为字典类别
            '字典编码': row_data[0] if row_data else '',  # 第一列作为字典编码
            '字典名称': row_data[1] if len(row_data) > 1 else '',  # 第二列作为字典名称
            '备注': row_data[-1] if len(row_data) > 2 else '',  # 最后一列作为备注
            '执行标准': ''  # 暂时留空
        }
        
        # 只有当编码和名称都不为空时才添加数据
        if excel_row['字典编码'] and excel_row['字典名称']:
            data.append(excel_row)
            
    return data

def extract_tables(doc_path):
    """从Word文档中提取表格数据"""
    print(f"开始处理文档: {doc_path}")
    doc = Document(doc_path)
    all_data = []
    
    # 获取所有表格和标题
    print("正在分析文档结构...")
    tables_and_titles = get_all_tables_and_titles(doc)
    total_tables = len(tables_and_titles)
    print(f"找到 {total_tables} 个表格")
    
    # 处理每个表格
    processed_tables = 0
    valid_tables = 0
    
    print("开始处理表格...")
    for table, title, table_count in tqdm(tables_and_titles, desc="处理进度"):
        table_data = process_table(table, title, table_count)
        if table_data:
            all_data.extend(table_data)
            valid_tables += 1
        processed_tables += 1
        
    print(f"\n表格处理统计:")
    print(f"总表格数: {total_tables}")
    print(f"处理表格数: {processed_tables}")
    print(f"有效表格数: {valid_tables}")
    print(f"提取数据行数: {len(all_data)}")
    
    return all_data

def process_word_file(word_path, excel_path):
    """处理Word文档并将数据保存到Excel"""
    try:
        # 提取表格数据
        data = extract_tables(word_path)
        
        if not data:
            print("未找到符合条件的表格数据")
            return False
            
        # 创建DataFrame，确保列顺序正确
        df = pd.DataFrame(data, columns=EXCEL_COLUMNS)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(excel_path), exist_ok=True)
        
        # 保存到Excel
        print(f"\n正在保存数据到Excel...")
        df.to_excel(excel_path, index=False)
        print(f"数据已保存到: {excel_path}")
        return True
        
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    # 设置文件路径
    word_path = r"D:\work\demo\福建\福建省三医联动平台_数据采集标准规范-值域代码-征求意见稿-0902-岳群.docx"
    excel_path = r"D:\work\demo\福建\值域代码-岳群.xlsx"
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"开始处理: {start_time}")
    
    # 处理文件
    success = process_word_file(word_path, excel_path)
    
    # 记录结束时间
    end_time = datetime.now()
    print(f"\n处理完成: {end_time}")
    print(f"总耗时: {end_time - start_time}")
    
    if success:
        print("文件处理成功！")
    else:
        print("文件处理失败！")

if __name__ == "__main__":
    main() 