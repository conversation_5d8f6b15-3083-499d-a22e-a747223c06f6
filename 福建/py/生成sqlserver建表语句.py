import pandas as pd
import os
import datetime

def get_sqlserver_type(data_type: str, length: str) -> str:
    """
    将Excel中的数据类型转换为SQL Server 2017兼容的数据类型

    Args:
        data_type: Excel中的数据类型
        length: 长度字段的值

    Returns:
        str: SQL Server 2017兼容的数据类型
    """
    data_type = str(data_type).lower().strip()
    length = str(length).strip()

    # 处理空值
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'NVARCHAR(255)'

    # 处理整数类型
    if '整数' in data_type:
        return 'INT'  # SQL Server 2017使用INT类型表示整数

    # 处理数值类型（小数）
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if ',' in length:
                    total_len, decimal_len = length.split(',')
                    return f'DECIMAL({total_len},{decimal_len})'
                return f'DECIMAL({length},0)'
            except ValueError:
                return 'DECIMAL(10,2)'  # 如果无法解析长度，使用默认值
        return 'DECIMAL(10,2)'  # 如果没有指定长度，使用默认值

    # 处理字符串类型
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                # 当字段长度超过4000时，自动转换为NVARCHAR(MAX)类型
                if length_val > 4000:
                    return 'NVARCHAR(MAX)'
                # 确保长度在合理范围内
                length_val = min(max(length_val, 1), 4000)
                return f'NVARCHAR({length_val})'
            except ValueError:
                return 'NVARCHAR(255)'
        return 'NVARCHAR(255)'

    # 处理日期时间类型
    if '日期' in data_type:
        return 'DATE'  # SQL Server 2017推荐使用DATETIME2，精度为0表示只存储到秒

    # 处理时间类型（不含日期的情况）
    if '时间' in data_type:
        return 'DATETIME'  # SQL Server 2017推荐使用TIME(0)，精度为0表示只存储到秒

    # 默认返回NVARCHAR(255)
    return 'NVARCHAR(255)'

def clean_comment_text(text: str) -> str:
    """
    清理注释文本中的特殊字符

    Args:
        text: 原始文本

    Returns:
        str: 清理后的文本
    """
    # 处理空值
    if pd.isna(text) or text == 'nan':
        return ''

    # 转换为字符串并清理
    text = str(text).strip()

    # 替换换行符、回车符为空格
    text = text.replace('\n', ' ').replace('\r', ' ')

    # 替换制表符为空格
    text = text.replace('\t', ' ')

    # 替换多个空格为单个空格
    text = ' '.join(text.split())

    return text

def generate_create_table_sql(df: pd.DataFrame, table_counter: int) -> tuple[str, str]:
    """
    根据DataFrame生成SQL Server 2017兼容的建表SQL语句

    Args:
        df: 包含表结构的DataFrame
        table_counter: 表序号，用于生成唯一的约束名和索引名

    Returns:
        tuple: (建表SQL语句, 表名)
    """
    # 获取表名
    table_name = df.iloc[0]['表名'].strip()
    table_comment = clean_comment_text(df.iloc[0]['表中文名'])

    # SQL语句头部
    sql = f"IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{table_name}]') AND type in (N'U'))\nBEGIN\n"
    sql += f"CREATE TABLE [dbo].[{table_name}] (\n"

    # 记录主键字段和唯一索引字段
    primary_keys = []
    unique_index_fields = []
    comments = []

    # 添加字段定义
    for _, row in df.iterrows():
        field_name = row['字段名'].strip()
        data_type = get_sqlserver_type(row['数据类型'], row['长度'])
        is_primary = str(row['是否主键']).strip().upper()
        nullable = '不能为空' in str(row['填报要求'])

        # 组合字段注释
        comment_parts = []
        data_item = clean_comment_text(row.get('数据项', ''))
        description = clean_comment_text(row['说明'])

        if data_item:
            comment_parts.append(data_item)
        if description:
            comment_parts.append(description)
        comment = ' '.join(comment_parts)

        # 检查是否需要创建唯一索引
        if '唯一索引' in str(row['说明']):
            unique_index_fields.append(field_name)

        # 构建字段定义
        sql += f"    [{field_name}] {data_type}"
        if nullable:
            sql += " NOT NULL"
        sql += ",\n"

        # 记录主键
        if is_primary in ('是', 'Y', '1'):
            primary_keys.append(field_name)

        # 收集注释
        if comment:
            comments.append((field_name, comment))

    # 添加主键约束
    if primary_keys:
        constraint_name = f"PK_{table_counter}_{table_name[:3]}"
        sql += f"    CONSTRAINT [{constraint_name}] PRIMARY KEY ([{'], ['.join(primary_keys)}])\n"
    else:
        sql = sql.rstrip(",\n") + "\n"

    sql += ")\nEND\n"

    # 批量添加注释
    if table_comment or comments:
        # 添加表注释
        if table_comment:
            sql += f"""IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[{table_name}]') AND minor_id = 0
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'{table_comment}',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'{table_name}'
END\n\n"""

        # 批量添加字段注释
        if comments:
            for field_name, comment in comments:
                sql += f"""IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[{table_name}]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[{table_name}]') 
        AND name = N'{field_name}'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'{comment}',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'{table_name}',
        @level2type = N'COLUMN',
        @level2name = N'{field_name}'
END\n"""

    # 添加唯一索引
    if unique_index_fields:
        index_name = f"IDX_U_{table_counter}_{table_name[:3]}"
        sql += f"""IF NOT EXISTS (
    SELECT * FROM sys.indexes 
    WHERE name = '{index_name}' 
    AND object_id = OBJECT_ID(N'[dbo].[{table_name}]')
)
BEGIN
    CREATE UNIQUE INDEX [{index_name}] ON [dbo].[{table_name}] ([{'], ['.join(unique_index_fields)}])
END\n\n"""

    return sql, table_name

def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"sqlserver{current_date}.sql")

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建\n\n')

            # 创建日志表
            f.write("""
-- 创建日志表
IF OBJECT_ID('dbo.table_creation_log', 'U') IS NOT NULL
BEGIN
    DROP TABLE [dbo].[table_creation_log]
END
GO

CREATE TABLE [dbo].[table_creation_log] (
    [id] INT IDENTITY(1,1) PRIMARY KEY,
    [table_name] NVARCHAR(255) NOT NULL,
    [status] NVARCHAR(10) NOT NULL,
    [message] NVARCHAR(MAX),
    [error_code] NVARCHAR(10),
    [create_time] DATETIME DEFAULT GETDATE()
)
GO

-- 创建存储过程
IF OBJECT_ID('dbo.log_table_result', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[log_table_result]
END
GO

CREATE PROCEDURE [dbo].[log_table_result]
    @table_name NVARCHAR(255),
    @status NVARCHAR(10),
    @message NVARCHAR(MAX),
    @error_code NVARCHAR(10)
AS
BEGIN
    INSERT INTO [dbo].[table_creation_log] ([table_name], [status], [message], [error_code])
    VALUES (@table_name, @status, @message, @error_code)
END
GO\n\n""")

            # 为每个表添加错误处理
            for idx, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                sql, table_name = generate_create_table_sql(group, idx)

                # 执行建表语句（带错误处理）
                f.write(f"""
-- 创建表 {table_name}
BEGIN TRY
    {sql.strip()}
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE()
    DECLARE @ErrorNumber NVARCHAR(10) = CAST(ERROR_NUMBER() AS NVARCHAR(10))
    
    -- 记录失败
    EXEC [dbo].[log_table_result]
        @table_name = N'{table_name}',
        @status = N'失败',
        @message = @ErrorMessage,
        @error_code = @ErrorNumber
END CATCH
GO

-- 记录成功（如果表创建成功）
IF NOT EXISTS (
    SELECT 1 FROM [dbo].[table_creation_log] 
    WHERE [table_name] = N'{table_name}'
)
BEGIN
    EXEC [dbo].[log_table_result] 
        @table_name = N'{table_name}',
        @status = N'成功',
        @message = N'表创建成功',
        @error_code = NULL
END
GO

-- 如果发生错误，继续执行下一个表
IF @@ERROR <> 0
BEGIN
    PRINT '创建表 {table_name} 时发生错误，继续执行下一个表'
END
GO\n\n""")

            # 添加最终结果统计查询
            f.write("""
-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = N'成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = N'失败' THEN 1 ELSE 0 END) as failed_count
FROM [dbo].[table_creation_log]
GO

-- 显示失败的表详情
SELECT 
    [table_name], 
    [message], 
    [error_code],
    [create_time] 
FROM [dbo].[table_creation_log] 
WHERE [status] = N'失败' 
ORDER BY [create_time]
GO""")

        print(f"\n成功生成SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("请注意：每个表的创建都包含了错误处理逻辑，如果某个表创建失败，脚本会继续执行后续表的创建")
        print("所有表的创建结果都会记录在 table_creation_log 表中，包含具体的错误代码和错误信息")

    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
