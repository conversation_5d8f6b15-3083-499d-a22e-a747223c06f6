'''
提示词：
写一个py脚本'转换对照生成数转sql-处理字典关联-基卫.py'  对excel表格中的内容进行加工， excel路径D:\work\demo\福建\转换对照生成数转sql模板.xlsx
读取第一个sheet页内容，excel内容包括以下列：责任人	业务大类	文件名称	表全名	表中文名	表名	是否字典	字段中文名-元数据项	数据项	字段名	数据类型	长度	填报要求	是否字典	引用业务表	引用业务数据项	数据加工方式	主副表关联过滤	复杂关联质控规则描述	引用业务字段名	引用业务数据类型	引用业务数据长度	引用业务数据填报要求	辅助列业务表&业务字段	是否主键	是否索引	说明	字典匹配	拼接表字段	与分册对比	表中文名数据项	是否重复项	字段名小写	判断字段名有无大写	表名小写	判断表名有无大写

读取'引用业务数据项'列，对内容去掉空格和换行符，判断内容是否等于'dic_val_name'或者'dic_val_code':

当内容等于'dic_val_name'时，获取该行的'表名'列的值为table，'字段名'列的值为name，'引用业务表'列的值为table_name,
对name的值进行处理，去掉空格和换行符'_name'替换成'_code',获取新值 code，
将表名列=table，字段名列=code，匹配到行，获取该行的'引用业务字段名'列的值，去掉空格和换行符，作为新值 old_code，
对字典值名称行的'主副表关联过滤'列进行赋值，值的内容为
left join data_dic_a as 'table_name' on 'table_name'.dic_type_code=''code'' and 'table_name'.dic_val_code=t.'old_code'；

当内容等于'dic_val_code'时，获取该行的'表名'列的值为table，'字段名'列的值为code，'引用业务表'列的值为table_name,
对code的值进行处理，去掉空格和换行符'_code'替换成'_name',获取新值 name,
将表名列=table，字段名列=name，匹配到行，获取该行的'引用业务字段名'列的值，去掉空格和换行符，作为新值 old_name，
对字典值名称行的'主副表关联过滤'列进行赋值，值的内容为
left join data_dic_a as 'table_name' on 'table_name'.dic_type_code=''code'' and 'table_name'.dic_val_name=t.'old_name'；
'''
# 转换对照生成数转sql-处理字典关联-基卫.py
import pandas as pd
import re

# 定义列名常量以便维护和可读性
COL_YWYWSJX = '引用业务数据项'
COL_BM = '表名'
COL_ZDM = '字段名'
COL_YYYWB = '引用业务表'  # 该列用作 'data_dic_a' 的别名 ('table_name' in user's SQL template)
COL_YYYWZDM = '引用业务字段名'  # 该列提供 'old_code' 或 'old_name' 的值
COL_ZFBGLGL = '主副表关联过滤'  # 需要更新的目标列
COL_YYYWSJLX = '引用业务数据类型'  # 新增：用于判断是否需要类型转换


def clean_text_val(text):
    """
    清除文本中的所有空白字符（空格、换行符、制表符等）。
    如果输入为NA或None，则返回空字符串。
    """
    if pd.isna(text) or text is None:
        return ""
    text_str = str(text)
    return re.sub(r'\s+', '', text_str)


def process_excel_and_generate_sql(file_path, output_file_path):
    """
    读取Excel文件，根据指定规则处理数据，并生成SQL关联语句。

    Args:
        file_path (str): 输入的Excel文件路径。
        output_file_path (str): 处理后保存的Excel文件路径。
    """
    try:
        df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个sheet页

        # 检查目标列是否存在，如果不存在则创建，并用pandas的NA值填充
        if COL_ZFBGLGL not in df.columns:
            df[COL_ZFBGLGL] = pd.NA
            print(f"信息: 列 '{COL_ZFBGLGL}' 在Excel模板中未找到，已自动创建。")

    except FileNotFoundError:
        print(f"错误：文件 '{file_path}' 未找到。请检查路径是否正确。")
        return
    except Exception as e:
        print(f"读取Excel文件 '{file_path}' 时发生错误: {e}")
        return

    # 迭代DataFrame的每一行来处理数据
    for index, row in df.iterrows():
        # 从当前行获取相关数据并清洗
        ywywsjx_val = clean_text_val(row.get(COL_YWYWSJX))
        current_row_table = clean_text_val(row.get(COL_BM))  # 当前行的 '表名'
        current_row_field = clean_text_val(row.get(COL_ZDM))  # 当前行的 '字段名'
        dict_table_alias = clean_text_val(row.get(COL_YYYWB))  # '引用业务表' -> 作为字典表的别名
        data_type = clean_text_val(row.get(COL_YYYWSJLX))  # 获取数据类型

        # 如果关键的 '引用业务表' (字典表别名) 为空，打印警告，但继续处理，SQL中别名将为空字符串
        if not dict_table_alias and (ywywsjx_val == 'dic_val_name' or ywywsjx_val == 'dic_val_code'):
            print(f"警告: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{current_row_field}'), "
                  f"其 '{COL_YYYWB}' (字典表别名) 为空。生成的SQL语句中该别名将表示为空字符串 ''。")

        # 条件1：当 '引用业务数据项' 等于 'dic_val_name'
        if ywywsjx_val == 'dic_val_name':
            name_current = current_row_field  # 即当前行的 '字段名'

            if not name_current:
                print(f"警告: Excel表格第 {index + 2} 行 (表: '{current_row_table}') 为 'dic_val_name' 类型, "
                      f"但其 '{COL_ZDM}' (字段名) 为空。跳过对此行的处理。")
                continue

            # 处理 '字段名'：去掉空格和换行符 (clean_text_val已处理), '_name' 替换成 '_code'
            code_derived = name_current.replace('_name', '_code')

            if name_current == code_derived:  # 如果替换前后无变化
                if '_name' in name_current:  # 如果包含_name但没变，说明替换逻辑可能有问题或特殊情况
                    print(f"调试信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{name_current}'), "
                          f"字段名包含 '_name' 但替换为 '_code' 后值未改变。派生code为: '{code_derived}'。")
                else:  # 如果字段名本身不含'_name'
                    print(f"信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{name_current}'), "
                          f"其 '{COL_ZDM}' 未包含 '_name'。将使用 '{code_derived}' 查找对应 'old_code'。")

            # 查找匹配行：'表名'列 = current_row_table, '字段名'列 = code_derived
            # 获取该匹配行的 '引用业务字段名' 列的值，作为 old_code_val
            old_code_val = pd.NA  # 初始化为 pandas NA
            found_match_for_old_code = False
            for i_lookup, row_lookup in df.iterrows():
                if clean_text_val(row_lookup.get(COL_BM)) == current_row_table and \
                        clean_text_val(row_lookup.get(COL_ZDM)) == code_derived:
                    old_code_val = clean_text_val(row_lookup.get(COL_YYYWZDM))
                    found_match_for_old_code = True
                    break

            if found_match_for_old_code:
                if pd.isna(old_code_val) or old_code_val == "":
                    print(f"信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{name_current}'), "
                          f"查找到的对应 'code' 行 (字段为 '{code_derived}') 的 '{COL_YYYWZDM}' ('old_code') 为空值。")
                    old_code_val = ""  # 在SQL字符串中表示为空

                # 构建SQL字符串并赋值给 '主副表关联过滤' 列
                # 根据数据类型决定是否添加cast转换
                field_expr = f"t.{old_code_val}"
                if data_type and data_type.lower() == 'number':
                    field_expr = f"cast({field_expr} as string)"

                sql_join_str = f"left join data_dic_a as {dict_table_alias} on {dict_table_alias}.dic_type_code='{code_derived.upper()}' and {dict_table_alias}.dic_val_code={field_expr}"
                df.at[index, COL_ZFBGLGL] = sql_join_str
            else:
                print(f"警告: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{name_current}'), "
                      f"未能找到用以获取 'old_code' 的匹配行 (条件: 表名='{current_row_table}', 字段名='{code_derived}')。"
                      f"'{COL_ZFBGLGL}' 列未更新。")

        # 条件2：当 '引用业务数据项' 等于 'dic_val_code'
        elif ywywsjx_val == 'dic_val_code':
            code_current = current_row_field  # 即当前行的 '字段名'

            if not code_current:
                print(f"警告: Excel表格第 {index + 2} 行 (表: '{current_row_table}') 为 'dic_val_code' 类型, "
                      f"但其 '{COL_ZDM}' (字段名/code) 为空。跳过对此行的处理。")
                continue

            # 处理 '字段名'：去掉空格和换行符 (clean_text_val已处理), '_code' 替换成 '_name'
            name_derived = code_current.replace('_code', '_name')

            if code_current == name_derived:  # 如果替换前后无变化
                if '_code' in code_current:
                    print(f"调试信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{code_current}'), "
                          f"字段名包含 '_code' 但替换为 '_name' 后值未改变。派生name为: '{name_derived}'。")
                else:  # 如果字段名本身不含'_code'
                    print(f"信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{code_current}'), "
                          f"其 '{COL_ZDM}' 未包含 '_code'。将使用 '{name_derived}' 查找对应 'old_name'。")

            # 查找匹配行：'表名'列 = current_row_table, '字段名'列 = name_derived
            # 获取该匹配行的 '引用业务字段名' 列的值，作为 old_name_val
            old_name_val = pd.NA  # 初始化
            found_match_for_old_name = False
            for i_lookup, row_lookup in df.iterrows():
                if clean_text_val(row_lookup.get(COL_BM)) == current_row_table and \
                        clean_text_val(row_lookup.get(COL_ZDM)) == name_derived:
                    old_name_val = clean_text_val(row_lookup.get(COL_YYYWZDM))
                    found_match_for_old_name = True
                    break

            if found_match_for_old_name:
                if pd.isna(old_name_val) or old_name_val == "":
                    print(f"信息: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{code_current}'), "
                          f"查找到的对应 'name' 行 (字段为 '{name_derived}') 的 '{COL_YYYWZDM}' ('old_name') 为空值。")
                    old_name_val = ""  # 在SQL字符串中表示为空

                # 构建SQL字符串并赋值给 '主副表关联过滤' 列
                # 根据数据类型决定是否添加cast转换
                field_expr = f"t.{old_name_val}"
                if data_type and data_type.lower() == 'number':
                    field_expr = f"cast({field_expr} as string)"

                sql_join_str = f"left join data_dic_a as {dict_table_alias} on {dict_table_alias}.dic_type_code='{code_current.upper()}' and {dict_table_alias}.dic_val_name={field_expr}"
                df.at[index, COL_ZFBGLGL] = sql_join_str
            else:
                print(f"警告: Excel表格第 {index + 2} 行 (表: '{current_row_table}', 字段: '{code_current}'), "
                      f"未能找到用以获取 'old_name' 的匹配行 (条件: 表名='{current_row_table}', 字段名='{name_derived}')。"
                      f"'{COL_ZFBGLGL}' 列未更新。")

    # 保存修改后的DataFrame到新的Excel文件
    try:
        df.to_excel(output_file_path, index=False)
        print(f"处理完成，结果已成功保存到: '{output_file_path}'")
    except Exception as e:
        print(f"保存Excel文件 '{output_file_path}' 时发生错误: {e}")


if __name__ == '__main__':
    # 定义Excel文件路径
    # 使用r""定义原始字符串，避免转义问题，或使用双反斜杠"\\"
    excel_file_input_path = r"D:\work\demo\福建\转换对照生成数转sql模板.xlsx"

    # 定义输出文件路径（在原文件名后添加_processed）
    if '.xlsx' in excel_file_input_path:
        excel_file_output_path = excel_file_input_path.replace('.xlsx', '_processed.xlsx')
    elif '.xls' in excel_file_input_path:  # 也可处理 .xls 后缀
        excel_file_output_path = excel_file_input_path.replace('.xls', '_processed.xls')
    else:  # 如果是没有标准Excel后缀的文件名，则简单追加
        excel_file_output_path = excel_file_input_path + '_processed'

    print(f"输入文件: {excel_file_input_path}")
    print(f"输出文件: {excel_file_output_path}")

    process_excel_and_generate_sql(excel_file_input_path, excel_file_output_path)