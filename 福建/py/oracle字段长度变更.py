#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
from collections import defaultdict
from datetime import datetime

def read_excel_files():
    # 读取字段长度变更列表
    change_fields_df = pd.read_excel(r'D:\work\demo\福建\字段长度变更列表.xlsx', sheet_name=0)
    # 获取第一列的所有字段名（去除空值和重复值）
    fields_to_change = change_fields_df.iloc[:, 0].dropna().unique().tolist()

    # 读取建表模板
    template_df = pd.read_excel(r'D:\work\demo\福建\建表模板.xlsx', sheet_name=0)
    return fields_to_change, template_df

def generate_alter_statements(fields_to_change, template_df, db_type):
    # 按文件名称分组存储SQL语句，使用字典存储表名和SQL语句的映射
    sql_by_file = defaultdict(lambda: defaultdict(list))

    for field in fields_to_change:
        # 在建表模板中查找匹配的行
        matching_rows = template_df[template_df['数据项'] == field]

        for _, row in matching_rows.iterrows():
            table_name = row['表英文名']
            column_name = row['字段名']
            new_length = row['长度']
            file_name = row['文件名称']

            # 根据数据库类型生成不同的ALTER TABLE语句
            if db_type == 'oracle':
                alter_sql = f"ALTER TABLE {table_name} MODIFY {column_name} NUMBER({new_length});"
            elif db_type == 'mysql':
                alter_sql = f"ALTER TABLE {table_name} MODIFY COLUMN {column_name} NUMBER({new_length});"
            elif db_type == 'sqlserver':
                alter_sql = f"ALTER TABLE {table_name} ALTER COLUMN {column_name} NUMBER({new_length});"

            sql_by_file[file_name][table_name].append(alter_sql)

    # 转换为最终的格式，对每个文件内的表名进行排序
    result = defaultdict(list)
    for file_name, table_dict in sql_by_file.items():
        for table_name in sorted(table_dict.keys()):
            result[file_name].extend(table_dict[table_name])

    return result

def write_sql_file(sql_by_file, db_type):
    # 创建输出目录（如果不存在）
    output_dir = r'D:\work\demo\福建\建表sql'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    def natural_sort_key(file_name):
        # 将文件名中的数字部分转换为整数进行比较
        import re
        def convert(text):
            return int(text) if text.isdigit() else text.lower()
        return [convert(c) for c in re.split('([0-9]+)', file_name)]

    # 获取当前日期
    current_date = datetime.now().strftime('%Y%m%d')

    # 根据数据库类型设置文件名
    file_name_mapping = {
        'oracle': f'oracle更新脚本{current_date}.sql',
        'mysql': f'mysql更新脚本{current_date}.sql',
        'sqlserver': f'sqlserver更新脚本{current_date}.sql'
    }

    output_file = os.path.join(output_dir, file_name_mapping[db_type])

    # 合并所有SQL语句到一个文件，按文件名称分组
    with open(output_file, 'w', encoding='utf-8') as f:
        # 添加数据库类型注释
        f.write(f"-- {db_type.upper()} 数据库字段长度变更脚本\n")
        f.write(f"-- 生成日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 使用自然排序对文件名进行排序
        for file_name in sorted(sql_by_file.keys(), key=natural_sort_key):
            f.write(f"\n-- {file_name} 相关表字段长度变更\n")
            for sql in sql_by_file[file_name]:
                f.write(sql + '\n')
            f.write('\n')

def main():
    try:
        print("开始处理字段长度变更...")

        # 读取Excel文件
        fields_to_change, template_df = read_excel_files()

        # 支持的数据库类型
        # db_types = ['oracle', 'mysql', 'sqlserver']
        db_types = ['oracle']

        # 为每种数据库类型生成SQL文件
        for db_type in db_types:
            print(f"正在生成 {db_type.upper()} 的SQL脚本...")
            sql_by_file = generate_alter_statements(fields_to_change, template_df, db_type)
            write_sql_file(sql_by_file, db_type)

        print("处理完成！SQL语句已生成到 D:\work\demo\福建 目录下")

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")

if __name__ == '__main__':
    main()
