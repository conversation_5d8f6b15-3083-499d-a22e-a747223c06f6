import re
import pandas as pd
import os

# 读取转换.txt文件
file_path = r'/福建/转换.txt'
try:
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
except UnicodeDecodeError:
    # 如果UTF-8编码失败，尝试使用GBK编码
    with open(file_path, 'r', encoding='gbk') as file:
        content = file.read()

# 使用正则表达式匹配：中文表名 + 英文表名(BM_开头) + 可选的后缀
pattern = r'([\u4e00-\u9fa5]+)(BM_[A-Z0-9]+)(?:([\u4e00-\u9fa5]+))?'
matches = re.findall(pattern, content)

# 创建数据框
data = []
for match in matches:
    chinese_name = match[0].strip()  # 中文表名
    english_name = match[1].strip()  # 英文表名
    suffix = match[2].strip() if match[2] else ""  # 后缀(如果有)
    
    # 如果有后缀，将其添加到中文表名中
    if suffix:
        display_name = f"{chinese_name}（{suffix}）"
    else:
        display_name = chinese_name
    
    data.append([display_name, english_name])

# 去重
unique_data = []
seen = set()
for item in data:
    # 使用元组作为键来检查是否已经存在
    key = (item[0], item[1])
    if key not in seen:
        seen.add(key)
        unique_data.append(item)

# 创建DataFrame
df = pd.DataFrame(unique_data, columns=['中文表名', '英文表名'])

# 保存为Excel文件
output_path = '/转换结果.xlsx'
df.to_excel(output_path, index=False)

print(f"转换完成，结果已保存至: {output_path}")
print(f"共转换 {len(unique_data)} 条记录")
