import pandas as pd
import os

def calculate_field_length(data_type, length):
    """计算字段长度和字节数"""
    if pd.isna(data_type) or pd.isna(length):
        return 0

    data_type = str(data_type).upper()

    # 处理日期时间类型
    if "时间" in data_type or "TIMESTAMP" in data_type:
        return 8
    if "日期" in data_type:
        return 3

    # 处理VARCHAR类型
    if "字符串" in data_type:
        try:
            # 如果length大于255，则返回0
            if int(length) > 255:
                return 0
            # 使用指定的长度
            return int(length) * 3
        except:
            return 0

    # 处理INT类型
    if "整数" in data_type:
        return 4

    # 处理DECIMAL类型
    if "数值" in data_type:
        return 12

    # 其他类型可以根据需要添加
    return 0

def process_excel():
    # 读取Excel文件的第一个sheet
    input_file = '../建表模板.xlsx'
    df = pd.read_excel(input_file)  # 默认读取第一个sheet

    # 存储结果的列表
    results = []
    # 存储所有字段详情的列表
    all_field_details = []

    # 获取原始表顺序
    original_table_order = df[['表中文名']].drop_duplicates()['表中文名'].tolist()

    # 添加原始行号作为排序依据
    df['original_row_number'] = range(len(df))

    # 按表中文名分组处理数据
    for table_cn_name, group in df.groupby('表中文名', dropna=False):
        try:
            if pd.isna(table_cn_name):
                continue

            total_bytes = 0
            field_details = []

            # 获取该表的第一行数据
            first_row = group.iloc[0]
            # 获取文件名称和表英文名从第一行
            file_name = first_row['文件名称'] if not pd.isna(first_row['文件名称']) else input_file
            table_en_name = first_row['表英文名'] if not pd.isna(first_row['表英文名']) else ''

            # 处理该表的所有字段，保持原始顺序
            for _, row in group.sort_values('original_row_number').iterrows():
                # 计算字段字节数
                field_bytes = calculate_field_length(row['数据类型'], row['长度'])
                total_bytes += field_bytes

                # 记录字段详情
                field_detail = {
                    '文件名称': file_name,
                    '表中文名': table_cn_name,
                    '表英文名': table_en_name,
                    '字段中文名-元数据项': row['字段中文名-元数据项'],
                    '数据项': row['数据项'],
                    '字段名': row['字段名'],
                    '数据类型': row['数据类型'],
                    '长度': row['长度'],
                    '占用字节数': field_bytes,
                    'original_row_number': row['original_row_number']  # 保存原始行号用于排序
                }
                field_details.append(field_detail)
                all_field_details.append(field_detail)

            # 判断是否超过65535字节
            is_exceeded = total_bytes > 65535

            # 添加结果
            results.append({
                '文件名称': file_name,
                '表中文名': table_cn_name,
                '表英文名': table_en_name,
                '字段数量': len(field_details),
                '占用字节数': total_bytes,
                '是否超过65535字节': '是' if is_exceeded else '否'
            })

        except Exception as e:
            print(f"处理表 {table_cn_name} 时出错: {str(e)}")

    # 创建结果DataFrame并按原始顺序排序
    result_df = pd.DataFrame(results)
    # 创建排序字典，用于保持原始顺序
    order_dict = {name: idx for idx, name in enumerate(original_table_order)}
    # 应用排序
    result_df['sort_order'] = result_df['表中文名'].map(order_dict)
    result_df = result_df.sort_values('sort_order').drop('sort_order', axis=1)

    # 创建字段详情DataFrame并按原始顺序排序
    details_df = pd.DataFrame(all_field_details)
    # 先按表顺序排序，再按字段原始行号排序
    details_df['sort_order'] = details_df['表中文名'].map(order_dict)
    details_df = details_df.sort_values(['sort_order', 'original_row_number']).drop(['sort_order', 'original_row_number'], axis=1)

    # 保存结果
    output_file = '../检测字段长度结果.xlsx'

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存主要结果
        main_results = result_df[['文件名称', '表中文名', '表英文名', '字段数量', '占用字节数', '是否超过65535字节']]
        main_results.to_excel(writer, sheet_name='总体结果', index=False)

        # 保存所有字段详情到一个sheet
        details_df.to_excel(writer, sheet_name='字段详情', index=False)

    print(f'结果已保存到: {output_file}')

if __name__ == '__main__':
    try:
        process_excel()
        print('处理完成！')
    except Exception as e:
        print(f'发生错误: {str(e)}')
