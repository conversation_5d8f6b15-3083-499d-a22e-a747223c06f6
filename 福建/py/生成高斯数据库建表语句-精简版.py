import pandas as pd
import os
import datetime

def get_mysql_type(data_type: str, length: str) -> str:
    # ... existing code ...
    data_type = str(data_type).lower().strip()
    length = str(length).strip()
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR(255)'
    if '整数' in data_type:
        return 'INTEGER'
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'NUMERIC({total_len},{decimal_len})'
                return f'NUMERIC({length})'
            except ValueError:
                return 'NUMERIC(10,2)'
        return 'NUMERIC(10,2)'
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                if length_val > 500:
                    return 'CLOB'
                return f'VARCHAR({length})'
            except ValueError:
                return 'VARCHAR(255)'
        return 'VARCHAR(255)'
    if '日期' in data_type:
        if '时间' not in data_type:
            return 'DATE'
        return 'TIMESTAMP'
    if '时间' in data_type:
        return 'TIMESTAMP'
    return 'VARCHAR(255)'

def generate_create_table_sql(df: pd.DataFrame, table_counter: int) -> tuple[str, str]:
    # ... existing code ...
    table_name = df.iloc[0]['表名'].strip()
    table_comment = df.iloc[0]['表中文名'].strip()
    sql = f"CREATE TABLE IF NOT EXISTS \"{table_name}\" (\n"
    primary_keys = []
    unique_index_fields = []
    for _, row in df.iterrows():
        field_name = row['字段名'].strip()
        data_type = get_mysql_type(row['数据类型'], row['长度'])
        is_primary = str(row['是否主键']).strip().upper()
        data_item = str(row.get('数据项', '')).strip()
        description = str(row['说明']).strip()
        comment_parts = []
        if data_item and data_item != 'nan':
            comment_parts.append(data_item)
        if description and description != 'nan':
            comment_parts.append(description)
        comment = ' '.join(comment_parts)
        nullable = '不能为空' in str(row['填报要求'])
        sql += f"    \"{field_name}\" {data_type}"
        if nullable:
            sql += " NOT NULL"
        sql += ",\n"
        if is_primary == '是' or is_primary == 'Y' or is_primary == '1':
            primary_keys.append(field_name)
        if '唯一索引' in str(row['说明']):
            unique_index_fields.append(field_name)
    if primary_keys:
        # 使用新的命名格式：pk_序号_表名前3个字符
        constraint_name = f"pk_{table_counter}_{table_name[:3]}"
        sql += f"    CONSTRAINT \"{constraint_name}\" PRIMARY KEY ({', '.join([f'\"{pk}\"' for pk in primary_keys])})"
        if unique_index_fields:
            sql += ",\n"
        else:
            sql += "\n"
    if unique_index_fields:
        # 使用新的命名格式：idx_u_序号_表名前3个字符
        index_name = f"idx_u_{table_counter}_{table_name[:3]}"
        sql += f"    CONSTRAINT \"{index_name}\" UNIQUE ({', '.join([f'\"{field}\"' for field in unique_index_fields])})\n"
    else:
        if not primary_keys:
            sql = sql.rstrip(",\n") + "\n"
    sql += ") WITH (orientation=row)\n"
    # sql += "DISTRIBUTE BY HASH ("
    # if primary_keys:
    #     sql += f'"{primary_keys[0]}"'
    # else:
    #     for _, row in df.iterrows():
    #         field_name = row['字段名'].strip()
    #         if '不能为空' in str(row['填报要求']):
    #             sql += f'"{field_name}"'
    #             break
    #     else:
    #         sql += f'"{df.iloc[0]["字段名"].strip()}"'
    # sql += ");\n\n"
    sql += ";\n\n"
    sql += f"COMMENT ON TABLE \"{table_name}\" IS '{table_comment}';\n"
    for _, row in df.iterrows():
        field_name = row['字段名'].strip()
        data_item = str(row.get('数据项', '')).strip()
        description = str(row['说明']).strip()
        comment_parts = []
        if data_item and data_item != 'nan':
            comment_parts.append(data_item)
        if description and description != 'nan':
            comment_parts.append(description)
        if comment_parts:
            comment = ' '.join(comment_parts)
            sql += f"COMMENT ON COLUMN \"{table_name}\".\"{field_name}\" IS '{comment}';\n"
    sql += "\n"
    return sql, table_name

def main():
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"高斯_{current_date}_精简版.sql")
    try:
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 仅包含建表及注释SQL，无存储过程和日志表\n\n')
            for table_counter, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                sql, table_name = generate_create_table_sql(group, table_counter)
                f.write(sql)
        print(f"\n成功生成SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("输出文件仅包含建表和注释SQL，无存储过程和日志表")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main() 