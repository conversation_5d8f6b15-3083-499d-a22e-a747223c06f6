import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Alignment, Color
from copy import copy
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def copy_row_with_formatting(source_ws, target_ws, source_row, target_row):
    """
    复制行数据并保持原格式

    Args:
        source_ws: 源工作表
        target_ws: 目标工作表
        source_row: 源行号
        target_row: 目标行号
    """
    start_time = time.time()
    for col in range(1, source_ws.max_column + 1):
        source_cell = source_ws.cell(row=source_row, column=col)
        target_cell = target_ws.cell(row=target_row, column=col)

        # 复制值
        target_cell.value = source_cell.value

        # 复制填充颜色
        if source_cell.fill and source_cell.fill.start_color:
            try:
                # 检查是否有有效的RGB值
                if hasattr(source_cell.fill.start_color, 'rgb') and source_cell.fill.start_color.rgb:
                    rgb = source_cell.fill.start_color.rgb
                    # 如果RGB值是自动的或None，跳过颜色复制
                    if rgb in ('00000000', None):
                        continue
                    # 如果RGB值以'FF'开头，去掉它
                    if rgb.startswith('FF'):
                        rgb = rgb[2:]
                    target_cell.fill = PatternFill(fill_type='solid', start_color=rgb)
                elif source_cell.fill.start_color.type == 'theme':
                    # 如果是主题颜色，直接复制整个fill对象
                    target_cell.fill = copy(source_cell.fill)
            except:
                # 如果复制填充失败，保持默认
                pass

        # 复制字体格式
        if source_cell.font:
            target_cell.font = copy(source_cell.font)

        # 复制边框样式
        if source_cell.border:
            target_cell.border = copy(source_cell.border)

        # 复制对齐方式
        if source_cell.alignment:
            target_cell.alignment = copy(source_cell.alignment)
    
    end_time = time.time()
    logging.debug(f"复制行 {source_row} 到 {target_row} 耗时: {end_time - start_time:.4f}秒")

def has_valid_data(worksheet, column=8):  # H列是第8列
    """
    检查工作表的指定列是否有有效数据
    
    Args:
        worksheet: 要检查的工作表
        column: 要检查的列号（默认是H列）
    
    Returns:
        bool: 如果列中有非空数据返回True，否则返回False
    """
    for row in range(1, worksheet.max_row + 1):
        cell_value = worksheet.cell(row=row, column=column).value
        if cell_value and str(cell_value).strip():
            return True
    return False

def process_excel_file():
    """
    处理Excel文件，在每个表的前后添加公共字段，并保持正确的格式
    """
    start_time = time.time()
    logging.info("开始处理Excel文件")
    
    # 读取 Excel 文件
    file_path = r'D:\work\demo\福建\添加公共字段.xlsx'
    logging.info(f"正在加载Excel文件: {file_path}")
    
    # 加载Excel文件
    load_start = time.time()
    wb = openpyxl.load_workbook(file_path)
    ws1 = wb['Sheet1']
    ws2 = wb['Sheet4']
    ws3 = wb['Sheet5']
    logging.info(f"Excel文件加载完成，耗时: {time.time() - load_start:.4f}秒")
    
    # 检查Sheet4和Sheet5的H列是否有有效数据
    has_sheet4_data = has_valid_data(ws2)
    has_sheet5_data = has_valid_data(ws3)
    logging.info(f"Sheet4 H列是否有数据: {has_sheet4_data}")
    logging.info(f"Sheet5 H列是否有数据: {has_sheet5_data}")
    
    # 创建新的工作表
    new_sheet = wb.create_sheet('ProcessedSheet')
    
    # 设置黄色填充
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    
    # 预先读取Sheet1的表名和行号
    logging.info("开始读取表名和行号")
    table_read_start = time.time()
    table_rows = {}
    current_table = None
    total_rows = 0
    for row in range(2, ws1.max_row + 1):  # 从第2行开始，跳过标题行
        table_name = ws1.cell(row=row, column=4).value
        if table_name:  # 只处理有表名的行
            if table_name != current_table:
                current_table = table_name
                table_rows[current_table] = []
                logging.info(f"发现新表: {table_name}")
            table_rows[current_table].append(row)
            total_rows += 1
    logging.info(f"表名和行号读取完成，共发现 {len(table_rows)} 个表，{total_rows} 行数据，耗时: {time.time() - table_read_start:.4f}秒")
    
    # 处理每个表
    current_row = 1
    table_process_start = time.time()
    for table_name, rows in table_rows.items():
        table_start_time = time.time()
        logging.info(f"开始处理表: {table_name}，共 {len(rows)} 行")
        
        # 获取表的第一行数据（用于前置和后置数据）
        first_row = rows[0]
        first_five_columns = [ws1.cell(row=first_row, column=col).value for col in range(1, 6)]
        
        # 添加前置数据（Sheet4）
        if has_sheet4_data:
            pre_data_start = time.time()
            for row in range(1, ws2.max_row + 1):
                row_data = first_five_columns + [ws2.cell(row=row, column=col).value for col in range(6, ws2.max_column + 1)]
                for col in range(len(row_data)):
                    new_cell = new_sheet.cell(row=current_row, column=col + 1)
                    new_cell.value = row_data[col]
                    new_cell.fill = yellow_fill
                current_row += 1
            logging.debug(f"添加前置数据耗时: {time.time() - pre_data_start:.4f}秒")
        
        # 复制表数据，保留格式
        table_data_start = time.time()
        for row in rows:
            copy_row_with_formatting(ws1, new_sheet, row, current_row)
            current_row += 1
        logging.debug(f"复制表数据耗时: {time.time() - table_data_start:.4f}秒")
        
        # 添加后置数据（Sheet5）
        if has_sheet5_data:
            post_data_start = time.time()
            for row in range(1, ws3.max_row + 1):
                row_data = first_five_columns + [ws3.cell(row=row, column=col).value for col in range(6, ws3.max_column + 1)]
                for col in range(len(row_data)):
                    new_cell = new_sheet.cell(row=current_row, column=col + 1)
                    new_cell.value = row_data[col]
                    new_cell.fill = yellow_fill
                current_row += 1
            logging.debug(f"添加后置数据耗时: {time.time() - post_data_start:.4f}秒")
        
        logging.info(f"表 {table_name} 处理完成，耗时: {time.time() - table_start_time:.4f}秒")
    
    logging.info(f"所有表处理完成，总耗时: {time.time() - table_process_start:.4f}秒")
    
    try:
        # 保存修改后的文件
        output_path = r'D:\work\demo\福建\添加公共字段结果.xlsx'
        save_start = time.time()
        wb.save(output_path)
        logging.info(f"文件保存完成，耗时: {time.time() - save_start:.4f}秒")
        logging.info(f"数据处理完成，结果保存在 '{output_path}'")
        logging.info(f"总处理时间: {time.time() - start_time:.4f}秒")
    
    except Exception as e:
        logging.error(f"处理过程中发生错误: {str(e)}")
        raise
    finally:
        wb.close()

if __name__ == "__main__":
    process_excel_file()
