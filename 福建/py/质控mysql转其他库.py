import pandas as pd
import re
import os


def convert_date_literals_for_oracle(query):
    """
    将 SQL 查询中的日期字面量转换为 Oracle 的 TO_DATE 函数格式。
    
    Args:
        query (str): 要转换的 SQL 查询语句。
        
    Returns:
        str: 转换后的 SQL 查询语句。
    """
    # 匹配日期时间字面量格式: 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD'
    date_pattern = r"'(\d{4}-\d{2}-\d{2}(?:\s+\d{2}:\d{2}:\d{2})?)'(?!\s*,)"
    
    def replace_date_literal(match):
        date_str = match.group(1)
        if ' ' in date_str:  # 日期时间格式
            return f"TO_DATE('{date_str}', 'YYYY-MM-DD HH24:MI:SS')"
        else:  # 仅日期格式
            return f"TO_DATE('{date_str}', 'YYYY-MM-DD')"
    
    # 替换日期字面量
    return re.sub(date_pattern, replace_date_literal, query)


def convert_date_literals_for_sqlserver(query):
    """
    将 SQL 查询中的日期字面量转换为 SQL Server 的 CONVERT 函数格式。
    
    Args:
        query (str): 要转换的 SQL 查询语句。
        
    Returns:
        str: 转换后的 SQL 查询语句。
    """
    # 匹配日期时间字面量格式: 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD'
    date_pattern = r"'(\d{4}-\d{2}-\d{2}(?:\s+\d{2}:\d{2}:\d{2})?)'(?!\s*,)"
    
    def replace_date_literal(match):
        date_str = match.group(1)
        if ' ' in date_str:  # 日期时间格式
            return f"CONVERT(DATETIME, '{date_str}', 120)"
        else:  # 仅日期格式
            return f"CONVERT(DATE, '{date_str}', 23)"
    
    # 替换日期字面量
    return re.sub(date_pattern, replace_date_literal, query)


def convert_date_literals_for_gaussdb(query):
    """
    将 SQL 查询中的日期字面量转换为 GaussDB 的 TO_DATE/TO_TIMESTAMP 函数格式。
    
    Args:
        query (str): 要转换的 SQL 查询语句。
        
    Returns:
        str: 转换后的 SQL 查询语句。
    """
    # 匹配日期时间字面量格式: 'YYYY-MM-DD HH:MM:SS' 或 'YYYY-MM-DD'
    date_pattern = r"'(\d{4}-\d{2}-\d{2}(?:\s+\d{2}:\d{2}:\d{2})?)'(?!\s*,)"
    
    def replace_date_literal(match):
        date_str = match.group(1)
        if ' ' in date_str:  # 日期时间格式
            return f"TO_TIMESTAMP('{date_str}', 'YYYY-MM-DD HH24:MI:SS')"
        else:  # 仅日期格式
            return f"TO_DATE('{date_str}', 'YYYY-MM-DD')"
    
    # 替换日期字面量
    return re.sub(date_pattern, replace_date_literal, query)


def translate_sql(mysql_query, target_dialect):
    """
    将 MySQL 查询语句转换为指定的目标数据库方言。

    Args:
        mysql_query (str): 要转换的 MySQL 查询语句。
        target_dialect (str): 目标数据库方言 ('oracle', 'sqlserver', 'gaussdb')。

    Returns:
        str: 转换后的 SQL 查询语句。
    """
    if pd.isna(mysql_query):
        return ""

    translated_query = str(mysql_query)  # 确保输入为字符串

    # 处理特定的复杂SQL模式
    if target_dialect == 'sqlserver':
        # 检测用户示例中的中文字符验证场景，我们为这些特殊情况添加特定的转换
        # 先检查是否有中文字符验证相关的正则表达式，识别各种格式和引号形式
        chinese_char_pattern = r"([A-Za-z0-9_\.]+)\s+REGEXP\s*['\"]?\[\^a-zA-Z\\u4e00-\\u9fa5·\]['\"]?"
        chinese_regex_match = re.search(chinese_char_pattern, translated_query, re.IGNORECASE)
        
        # 检查是否有纯中文检测正则表达式
        pure_chinese_pattern = r"([A-Za-z0-9_\.]+)\s+REGEXP\s*['\"]?\^\[\\u4e00-\\u9fa5\]\+\$['\"]?"
        pure_chinese_match = re.search(pure_chinese_pattern, translated_query, re.IGNORECASE)
        
        # 检查是否有点号开头或结尾的LIKE条件
        dot_prefix_pattern = r"([A-Za-z0-9_\.]+)\s+LIKE\s*['\"]?·%['\"]?"
        dot_prefix_match = re.search(dot_prefix_pattern, translated_query, re.IGNORECASE)
        
        dot_suffix_pattern = r"([A-Za-z0-9_\.]+)\s+LIKE\s*['\"]?%·['\"]?"
        dot_suffix_match = re.search(dot_suffix_pattern, translated_query, re.IGNORECASE)
        
        # 检查是否有空格检查的LIKE条件
        space_check_pattern = r"([A-Za-z0-9_\.]+)\s+LIKE\s*['\"]?%\s+%['\"]?"
        space_check_match = re.search(space_check_pattern, translated_query, re.IGNORECASE)
        
        # 如果检测到了中文字符验证的相关模式，则构建完整的SQL Server翻译
        if chinese_regex_match or pure_chinese_match or dot_prefix_match or dot_suffix_match:
            # 提取字段名，优先使用中文字符验证的字段名
            field_name = None
            if chinese_regex_match:
                field_name = chinese_regex_match.group(1)
            elif pure_chinese_match:
                field_name = pure_chinese_match.group(1)
            elif dot_prefix_match:
                field_name = dot_prefix_match.group(1)
            elif dot_suffix_match:
                field_name = dot_suffix_match.group(1)
            elif space_check_match:
                field_name = space_check_match.group(1)
                
            if field_name:
                # 构建完整的SQL Server等效表达式
                replacement_expr = f"""
                -- 添加非空条件，仅在字段不为空时进行验证
                {field_name} IS NOT NULL
                AND (
                    -- 条件1: 检查是否包含除汉字、英文、· 之外的任何字符
                    {field_name} LIKE '%[^a-zA-Z·' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%' COLLATE Latin1_General_BIN

                    -- 条件2: 检查是否以 '·' 开头或结尾
                    OR {field_name} LIKE '·%'
                    OR {field_name} LIKE '%·'

                    -- 条件3: 检查当字段内容全是汉字时，是否含有空格
                    OR (PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%', {field_name}) = 0 AND {field_name} LIKE '% %')
                )"""
                
                # 根据不同的匹配情况替换相应的部分
                if chinese_regex_match:
                    # 找到REGEXP [^a-zA-Z\u4e00-\u9fa5·]及其可能的连带条件
                    pattern_to_replace = rf"{re.escape(field_name)}\s+REGEXP\s*['\"]?\[\^a-zA-Z\\u4e00-\\u9fa5·\]['\"]?(?:\s+OR\s+.*?\s+LIKE\s+['\"]?·%['\"]?\s+OR\s+.*?\s+LIKE\s+['\"]?%·['\"]?\s+OR\s+\(\s*.*?\s+REGEXP\s+['\"]?\^\[\\u4e00-\\u9fa5\]\+\$['\"]?\s+AND\s+.*?\s+LIKE\s+['\"]?%\s+%['\"]?\s*\))?"
                    # 如果在前面有WHERE 1 = 1 AND这种情况，也要处理
                    where_pattern = r"WHERE\s+1\s*=\s*1\s+AND\s+"
                    where_match = re.search(where_pattern, translated_query, re.IGNORECASE)
                    if where_match:
                        pattern_to_replace = where_pattern + pattern_to_replace
                        replacement_expr = "WHERE " + replacement_expr
                    
                    translated_query = re.sub(pattern_to_replace, replacement_expr, translated_query, flags=re.IGNORECASE | re.DOTALL)
                
                elif pure_chinese_match and space_check_match:
                    # 找到纯中文加空格检查的组合条件
                    pattern_to_replace = rf"\(\s*{re.escape(field_name)}\s+REGEXP\s*['\"]?\^\[\\u4e00-\\u9fa5\]\+\$['\"]?\s+AND\s+{re.escape(field_name)}\s+LIKE\s*['\"]?%\s+%['\"]?\s*\)"
                    specific_replacement = f"(PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%', {field_name}) = 0 AND {field_name} LIKE '% %'"
                    translated_query = re.sub(pattern_to_replace, specific_replacement, translated_query, flags=re.IGNORECASE | re.DOTALL)
                
                # 纯中文验证模式
                elif pure_chinese_match:
                    pattern_to_replace = rf"{re.escape(field_name)}\s+REGEXP\s*['\"]?\^\[\\u4e00-\\u9fa5\]\+\$['\"]?"
                    specific_replacement = f"PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%', {field_name}) = 0"
                    translated_query = re.sub(pattern_to_replace, specific_replacement, translated_query, flags=re.IGNORECASE | re.DOTALL)
                
                # 单独处理"点号开头"条件
                elif dot_prefix_match:
                    # 不替换，保留原始形式
                    pass
                
                # 单独处理"点号结尾"条件
                elif dot_suffix_match:
                    # 不替换，保留原始形式
                    pass
                
                # 处理引号不一致的问题 - 确保所有LIKE条件的引号一致
                translated_query = translated_query.replace('LIKE "', "LIKE '")
                translated_query = translated_query.replace('"%', "'%")
                translated_query = translated_query.replace('" ', "' ")
                translated_query = translated_query.replace(' "', " '")
                
                # 修复可能的引号不匹配问题
                # 检查单引号是否匹配
                single_quotes = re.findall(r"'", translated_query)
                if len(single_quotes) % 2 != 0:
                    # 补充缺失的单引号
                    translated_query += "'"
                
                # 检查双引号是否匹配
                double_quotes = re.findall(r'"', translated_query)
                if len(double_quotes) % 2 != 0:
                    # 将不匹配的双引号替换为单引号
                    last_double_quote_pos = translated_query.rfind('"')
                    if last_double_quote_pos >= 0:
                        translated_query = translated_query[:last_double_quote_pos] + "'" + translated_query[last_double_quote_pos+1:]
        
        # 处理特殊情况：查询条件中没有空格的紧凑格式
        if 'REGEXP\'' in translated_query or 'LIKE\'' in translated_query:
            # 添加空格使其符合标准格式
            translated_query = translated_query.replace('REGEXP\'', 'REGEXP \'')
            translated_query = translated_query.replace('LIKE\'', 'LIKE \'')
        
        # 修复纯中文检查模式
        pure_chinese_pattern = r'REGEXP\s+[\'"]?\^\[\\u4e00-\\u9fa5\]\+\$[\'"]?'
        if re.search(pure_chinese_pattern, translated_query, re.IGNORECASE):
            # 提取字段名
            field_name_pattern = r'([A-Za-z0-9_\.]+)\s+REGEXP\s+[\'"]?\^\[\\u4e00-\\u9fa5\]\+\$[\'"]?'
            field_match = re.search(field_name_pattern, translated_query, re.IGNORECASE)
            if field_match:
                field_name = field_match.group(1)
                # 替换为PATINDEX形式
                translated_query = re.sub(
                    field_name_pattern,
                    f"PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%', {field_name}) = 0",
                    translated_query,
                    flags=re.IGNORECASE
                )
            
    # 日期格式映射
    date_format_mappings = {
        'oracle': {
            '%Y': 'YYYY',
            '%m': 'MM',
            '%d': 'DD',
            '%H': 'HH24',
            '%i': 'MI',
            '%s': 'SS',
            '%Y-%m-%d': 'YYYY-MM-DD',
            '%Y%m%d': 'YYYYMMDD',
            '%Y-%m-%d %H:%i:%s': 'YYYY-MM-DD HH24:MI:SS'
        },
        'sqlserver': {
            '%Y': 'yyyy',
            '%m': 'MM',
            '%d': 'dd',
            '%H': 'HH',
            '%i': 'mm',
            '%s': 'ss',
            '%Y-%m-%d': 'yyyy-MM-dd',
            '%Y%m%d': 'yyyyMMdd',
            '%Y-%m-%d %H:%i:%s': 'yyyy-MM-dd HH:mm:ss'
        },
        'gaussdb': {
            '%Y': 'YYYY',
            '%m': 'MM',
            '%d': 'DD',
            '%H': 'HH24',
            '%i': 'MI',
            '%s': 'SS',
            '%Y-%m-%d': 'YYYY-MM-DD',
            '%Y%m%d': 'YYYYMMDD',
            '%Y-%m-%d %H:%i:%s': 'YYYY-MM-DD HH24:MI:SS'
        }
    }

    # 特定方言的特殊语法转换 (优先处理，避免影响后续函数替换)
    if target_dialect == 'oracle':
        # 处理 Oracle 的日期字面量转换
        translated_query = convert_date_literals_for_oracle(translated_query)
        
        # 处理 Oracle 的正则表达式转换
        # 1. 处理 not REGEXP 形式 (否定形式)
        def handle_not_regexp(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"NOT REGEXP_LIKE({field}, {pattern})"

        # 2. 处理 REGEXP ... = 0 形式 (否定形式)
        def handle_regexp_not_equal(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"NOT REGEXP_LIKE({field}, {pattern})"

        # 3. 处理 REGEXP ... 或 REGEXP ... = 1 形式 (肯定形式)
        def handle_regexp_equal(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"REGEXP_LIKE({field}, {pattern})"

        # 先处理括号内的复杂表达式
        # 使用递归方式处理可能嵌套的括号
        def process_complex_expressions(query):
            # 查找括号内的内容
            bracket_pattern = r'\(([^()]*(?:\([^()]*\)[^()]*)*)\)'
            bracket_matches = list(re.finditer(bracket_pattern, query))
            
            # 如果没有括号，直接处理简单表达式
            if not bracket_matches:
                # 处理 not REGEXP 形式
                query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... = 0 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal, query, flags=re.IGNORECASE)
                return query
            
            # 从内到外处理括号
            for match in reversed(bracket_matches):
                bracket_content = match.group(1)
                # 递归处理括号内容
                processed_content = process_complex_expressions(bracket_content)
                # 替换原始括号内容
                query = query[:match.start()] + '(' + processed_content + ')' + query[match.end():]
            
            return query
        
        # 应用递归处理
        translated_query = process_complex_expressions(translated_query)
        
        # 处理剩余的简单表达式（括号外的）
        # 处理 not REGEXP 形式
        translated_query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... = 0 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal, translated_query, flags=re.IGNORECASE)

    elif target_dialect == 'sqlserver':
        # 处理 SQL Server 的日期字面量转换
        translated_query = convert_date_literals_for_sqlserver(translated_query)
        
        # 处理 SQL Server 的正则表达式转换
        # SQL Server 不支持原生的正则表达式，需要使用自定义函数或其他替代方法
        
        # 添加一个辅助函数来处理复杂的正则表达式模式转换
        def convert_regex_to_sqlserver_pattern(pattern_str):
            """
            将正则表达式模式转换为SQL Server可以使用的模式
            
            Args:
                pattern_str (str): 正则表达式模式字符串（已去除引号）
                
            Returns:
                tuple: (转换后的SQL Server模式, 是否需要使用复杂表达式, SQL Server表达式)
            """
            # 移除模式中的多余转义字符
            pattern_str = pattern_str.replace('\\\\', '\\')
            
            # 特殊情况处理
            # 检查是否是中文字符范围匹配模式: [^a-zA-Z\u4e00-\u9fa5·]
            if '[^a-zA-Z\\u4e00-\\u9fa5·]' in pattern_str or '[^a-zA-Z·\\u4e00-\\u9fa5]' in pattern_str:
                # 返回SQL Server的LIKE表达式，使用NCHAR函数处理中文字符范围
                return None, True, "{field} LIKE '%[^a-zA-Z·' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%' COLLATE Latin1_General_BIN"
            
            # 检查是否是纯中文字符匹配模式: ^[\u4e00-\u9fa5]+$
            if re.search(r'\^\[\\u4e00-\\u9fa5\]\+\$', pattern_str) or '^[\\u4e00-\\u9fa5]+$' in pattern_str:
                # 返回SQL Server的PATINDEX表达式，检查非中文字符
                return None, True, "PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%', {field}) = 0"
            
            # 身份证号码验证模式
            if pattern_str == '^[0-9]{17}[0-9X]$':
                # 直接使用特定的表达式处理身份证号码
                return None, True, "(LEN({field}) = 18 AND {field} LIKE '[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9X]')"
            
            # 处理标准正则表达式模式
            if pattern_str.startswith('^') and pattern_str.endswith('$'):
                # 完全匹配模式
                like_pattern = pattern_str[1:-1]  # 移除 ^ 和 $
                return like_pattern, False, None
            elif pattern_str.startswith('^'):
                # 开头匹配模式
                like_pattern = pattern_str[1:] + '%'  # 移除 ^ 并添加 %
                return like_pattern, False, None
            elif pattern_str.endswith('$'):
                # 结尾匹配模式
                like_pattern = '%' + pattern_str[:-1]  # 移除 $ 并添加 %
                return like_pattern, False, None
            else:
                # 包含匹配模式
                like_pattern = '%' + pattern_str + '%'
                return like_pattern, False, None
        
        # 1. 处理 not REGEXP 形式 (否定形式)
        def handle_not_regexp_sqlserver(match):
            field = match.group(1)
            pattern = match.group(2)
            
            # 移除引号
            pattern_str = pattern.strip("'\"")
            
            # 使用辅助函数处理模式
            like_pattern, is_complex, sql_expr = convert_regex_to_sqlserver_pattern(pattern_str)
            
            if is_complex:
                # 处理复杂表达式，替换{field}占位符
                return "NOT (" + sql_expr.replace("{field}", field) + ")"
            else:
                # 对于简单的LIKE模式
                return f"/* SQL Server 正则表达式转换 */ {field} NOT LIKE '{like_pattern}'"
        
        # 2. 处理 REGEXP ... = 0 形式 (否定形式)
        def handle_regexp_not_equal_sqlserver(match):
            field = match.group(1)
            pattern = match.group(2)
            
            # 移除引号
            pattern_str = pattern.strip("'\"")
            
            # 使用辅助函数处理模式
            like_pattern, is_complex, sql_expr = convert_regex_to_sqlserver_pattern(pattern_str)
            
            if is_complex:
                # 处理复杂表达式，替换{field}占位符
                return "NOT (" + sql_expr.replace("{field}", field) + ")"
            else:
                # 对于简单的LIKE模式
                return f"/* SQL Server 正则表达式转换 */ {field} NOT LIKE '{like_pattern}'"
        
        # 3. 处理 REGEXP ... 或 REGEXP ... = 1 形式 (肯定形式)
        def handle_regexp_equal_sqlserver(match):
            field = match.group(1)
            pattern = match.group(2)
            
            # 移除引号
            pattern_str = pattern.strip("'\"")
            
            # 使用辅助函数处理模式
            like_pattern, is_complex, sql_expr = convert_regex_to_sqlserver_pattern(pattern_str)
            
            if is_complex:
                # 处理复杂表达式，替换{field}占位符
                return sql_expr.replace("{field}", field)
            else:
                # 对于简单的LIKE模式
                return f"/* SQL Server 正则表达式转换 */ {field} LIKE '{like_pattern}'"
        
        # 先处理括号内的复杂表达式
        # 使用递归方式处理可能嵌套的括号
        def process_complex_expressions_sqlserver(query):
            # 查找括号内的内容
            bracket_pattern = r'\(([^()]*(?:\([^()]*\)[^()]*)*)\)'
            bracket_matches = list(re.finditer(bracket_pattern, query))
            
            # 如果没有括号，直接处理简单表达式
            if not bracket_matches:
                # 处理 not REGEXP 形式
                query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp_sqlserver, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... = 0 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal_sqlserver, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal_sqlserver, query, flags=re.IGNORECASE)
                return query
            
            # 从内到外处理括号
            for match in reversed(bracket_matches):
                bracket_content = match.group(1)
                # 递归处理括号内容
                processed_content = process_complex_expressions_sqlserver(bracket_content)
                # 替换原始括号内容
                query = query[:match.start()] + '(' + processed_content + ')' + query[match.end():]
            
            return query
        
        # 应用递归处理
        translated_query = process_complex_expressions_sqlserver(translated_query)
        
        # 处理剩余的简单表达式（括号外的）
        # 处理 not REGEXP 形式
        translated_query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp_sqlserver, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... = 0 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal_sqlserver, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal_sqlserver, translated_query, flags=re.IGNORECASE)
        
    elif target_dialect == 'gaussdb':
        # 处理 GaussDB 的日期字面量转换
        translated_query = convert_date_literals_for_gaussdb(translated_query)
        
        # 处理 GaussDB 的正则表达式转换
        # GaussDB 使用 ~ 操作符而不是 REGEXP
        
        # 1. 处理 not REGEXP 形式 (否定形式)
        def handle_not_regexp_gaussdb(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"{field} !~ {pattern}"

        # 2. 处理 REGEXP ... = 0 形式 (否定形式)
        def handle_regexp_not_equal_gaussdb(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"{field} !~ {pattern}"

        # 3. 处理 REGEXP ... 或 REGEXP ... = 1 形式 (肯定形式)
        def handle_regexp_equal_gaussdb(match):
            field = match.group(1)
            pattern = match.group(2)
            # 移除模式中的多余转义字符
            pattern = pattern.replace('\\\\', '\\')
            return f"{field} ~ {pattern}"

        # 先处理括号内的复杂表达式
        # 使用递归方式处理可能嵌套的括号
        def process_complex_expressions_gaussdb(query):
            # 查找括号内的内容
            bracket_pattern = r'\(([^()]*(?:\([^()]*\)[^()]*)*)\)'
            bracket_matches = list(re.finditer(bracket_pattern, query))
            
            # 如果没有括号，直接处理简单表达式
            if not bracket_matches:
                # 处理 not REGEXP 形式
                query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp_gaussdb, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... = 0 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal_gaussdb, query, flags=re.IGNORECASE)
                # 处理 REGEXP ... 形式
                query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal_gaussdb, query, flags=re.IGNORECASE)
                return query
            
            # 从内到外处理括号
            for match in reversed(bracket_matches):
                bracket_content = match.group(1)
                # 递归处理括号内容
                processed_content = process_complex_expressions_gaussdb(bracket_content)
                # 替换原始括号内容
                query = query[:match.start()] + '(' + processed_content + ')' + query[match.end():]
            
            return query
        
        # 应用递归处理
        translated_query = process_complex_expressions_gaussdb(translated_query)
        
        # 处理剩余的简单表达式（括号外的）
        # 处理 not REGEXP 形式
        translated_query = re.sub(r'(\S+)\s+not\s+REGEXP\s+([\'"].+?[\'"])', handle_not_regexp_gaussdb, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... = 0 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])\s*=\s*0', handle_regexp_not_equal_gaussdb, translated_query, flags=re.IGNORECASE)
        # 处理 REGEXP ... 形式
        translated_query = re.sub(r'(\S+)\s+REGEXP\s+([\'"].+?[\'"])(?:\s*=\s*1)?', handle_regexp_equal_gaussdb, translated_query, flags=re.IGNORECASE)

    # 函数映射
    function_mappings = {
        'oracle': {
            'now()': 'SYSDATE',
            'curdate()': 'TRUNC(SYSDATE)',
            'current_date': 'TRUNC(SYSDATE)',
            'curtime()': "TO_CHAR(SYSDATE, 'HH24:MI:SS')",
            'date_format': 'TO_CHAR',
            'ifnull': 'NVL',
            'length': 'LENGTH',
            'substring': 'SUBSTR',
            'group_concat': 'LISTAGG({0}, \',\') WITHIN GROUP (ORDER BY {1})',  # {0}是字段，{1}是排序字段
            'unix_timestamp': "(CAST(SYS_EXTRACT_UTC(SYSTIMESTAMP) AS DATE) - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400",
            'from_unixtime': "TO_DATE('1970-01-01','YYYY-MM-DD') + numtodsinterval({0}, 'SECOND')",
            'date_add': "({0} + INTERVAL '{1}' {2})",
            'date_sub': "({0} - INTERVAL '{1}' {2})",
            'timestampdiff': "CASE \
WHEN '{0}' = 'SECOND' THEN ROUND((CAST({2} AS DATE) - CAST({1} AS DATE)) * 86400) \
WHEN '{0}' = 'MINUTE' THEN ROUND((CAST({2} AS DATE) - CAST({1} AS DATE)) * 1440) \
WHEN '{0}' = 'HOUR' THEN ROUND((CAST({2} AS DATE) - CAST({1} AS DATE)) * 24) \
WHEN '{0}' = 'DAY' THEN ROUND(CAST({2} AS DATE) - CAST({1} AS DATE)) \
WHEN '{0}' = 'WEEK' THEN ROUND((CAST({2} AS DATE) - CAST({1} AS DATE)) / 7) \
WHEN '{0}' = 'MONTH' THEN ROUND(MONTHS_BETWEEN({2}, {1})) \
WHEN '{0}' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN({2}, {1}) / 3) \
WHEN '{0}' = 'YEAR' THEN ROUND(MONTHS_BETWEEN({2}, {1}) / 12) \
END",
        },
        'sqlserver': {
            'now()': 'GETDATE()',
            'curdate()': 'CAST(GETDATE() AS DATE)',
            'current_date': 'CAST(GETDATE() AS DATE)',
            'curtime()': 'CONVERT(TIME, GETDATE())',
            'date_format': 'FORMAT',
            'ifnull': 'ISNULL',
            'length': 'LEN',
            'substring': 'SUBSTRING',
            'group_concat': 'STRING_AGG({0}, \',\') WITHIN GROUP (ORDER BY {1})',  # SQL Server 2017+
            'unix_timestamp': 'DATEDIFF(s, \'1970-01-01\', GETUTCDATE())',
            'from_unixtime': 'DATEADD(s, {0}, \'1970-01-01\')',
            'date_add': "DATEADD({2}, {1}, {0})",
            'date_sub': "DATEADD({2}, -{1}, {0})",
            'timestampdiff': "DATEDIFF({0}, {1}, {2})",
        },
        'gaussdb': {
            'now()': 'NOW()',
            'curdate()': 'CURRENT_DATE',
            'current_date': 'CURRENT_DATE',
            'curtime()': 'CURRENT_TIME',
            'date_format': 'TO_CHAR',
            'ifnull': 'COALESCE',
            'length': 'LENGTH',
            'substring': 'SUBSTRING',
            'group_concat': 'STRING_AGG({0}, \',\') WITHIN GROUP (ORDER BY {1})',
            'unix_timestamp': "EXTRACT(EPOCH FROM NOW())",
            'from_unixtime': "TO_TIMESTAMP({0})",
            'date_add': "({0} + INTERVAL '{1} {2}')",
            'date_sub': "({0} - INTERVAL '{1} {2}')",
            'timestampdiff': "CASE \
WHEN '{0}' = 'SECOND' THEN EXTRACT(EPOCH FROM ({2} - {1}))::INTEGER \
WHEN '{0}' = 'MINUTE' THEN (EXTRACT(EPOCH FROM ({2} - {1})) / 60)::INTEGER \
WHEN '{0}' = 'HOUR' THEN (EXTRACT(EPOCH FROM ({2} - {1})) / 3600)::INTEGER \
WHEN '{0}' = 'DAY' THEN DATE_PART('day', AGE({2}, {1}))::INTEGER \
WHEN '{0}' = 'WEEK' THEN (DATE_PART('day', AGE({2}, {1})) / 7)::INTEGER \
WHEN '{0}' = 'MONTH' THEN (DATE_PART('year', AGE({2}, {1})) * 12 + DATE_PART('month', AGE({2}, {1})))::INTEGER \
WHEN '{0}' = 'QUARTER' THEN ((DATE_PART('year', AGE({2}, {1})) * 12 + DATE_PART('month', AGE({2}, {1}))) / 3)::INTEGER \
WHEN '{0}' = 'YEAR' THEN DATE_PART('year', AGE({2}, {1}))::INTEGER \
END",
        }
    }

    # --- 新的、更健壮的函数替换逻辑 ---
    if target_dialect in function_mappings:
        for mysql_func, target_func in function_mappings[target_dialect].items():
            try:
                # 检查key是否是无参数函数调用，如 "now()"
                if mysql_func.endswith('()'):
                    # 提取函数名，如 "now"
                    func_name_only = mysql_func[:-2]
                    # 构建精确的正则表达式，匹配 "now()" 或 "now ()" 等形式
                    regex = r'\b' + re.escape(func_name_only) + r'\s*\(\)'
                    translated_query = re.sub(regex, target_func, translated_query, flags=re.IGNORECASE)
                # 处理 GROUP_CONCAT 函数
                elif mysql_func == 'group_concat':
                    # 匹配 GROUP_CONCAT 函数调用
                    group_concat_pattern = r'GROUP_CONCAT\s*\(([^)]+)\)'
                    matches = re.finditer(group_concat_pattern, translated_query, re.IGNORECASE)
                    for match in matches:
                        args = match.group(1).strip()
                        # 检查是否有 ORDER BY
                        if 'ORDER BY' in args.upper():
                            field, order_by = args.split('ORDER BY', 1)
                            field = field.strip()
                            order_by = order_by.strip()
                        else:
                            field = args
                            order_by = '1'  # 默认按第一个字段排序
                        replacement = target_func.format(field, order_by)
                        translated_query = translated_query.replace(match.group(0), replacement)
                # 处理 DATE_FORMAT 函数
                elif mysql_func == 'date_format':
                    # 匹配 DATE_FORMAT 函数调用
                    date_format_pattern = r'DATE_FORMAT\s*\(([^,]+),\s*([^)]+)\)'
                    matches = re.finditer(date_format_pattern, translated_query, re.IGNORECASE)
                    for match in matches:
                        date_expr = match.group(1).strip()
                        format_str = match.group(2).strip().strip("'")
                        # 转换日期格式
                        target_format = format_str
                        for mysql_fmt, target_fmt in date_format_mappings[target_dialect].items():
                            target_format = target_format.replace(mysql_fmt, target_fmt)
                        if target_dialect == 'oracle':
                            replacement = f"TO_CHAR({date_expr}, '{target_format}')"
                        elif target_dialect == 'sqlserver':
                            replacement = f"FORMAT({date_expr}, '{target_format}')"
                        else:  # gaussdb
                            replacement = f"TO_CHAR({date_expr}, '{target_format}')"
                        translated_query = translated_query.replace(match.group(0), replacement)
                # 处理 TIMESTAMPDIFF 函数
                elif mysql_func == 'timestampdiff':
                    # 使用更复杂的正则表达式来匹配嵌套的函数调用
                    def find_timestampdiff_calls(text):
                        """找到所有TIMESTAMPDIFF函数调用，包括嵌套的情况"""
                        calls = []
                        pattern = r'TIMESTAMPDIFF\s*\('
                        for match in re.finditer(pattern, text, re.IGNORECASE):
                            start = match.start()
                            # 找到匹配的右括号
                            paren_count = 0
                            pos = match.end() - 1  # 从左括号开始
                            while pos < len(text):
                                if text[pos] == '(':
                                    paren_count += 1
                                elif text[pos] == ')':
                                    paren_count -= 1
                                    if paren_count == 0:
                                        end = pos + 1
                                        full_call = text[start:end]
                                        # 提取参数
                                        inner_content = text[match.end():pos]
                                        # 分割参数，考虑嵌套函数
                                        args = split_function_args(inner_content)
                                        if len(args) == 3:
                                            calls.append({
                                                'full_call': full_call,
                                                'start': start,
                                                'end': end,
                                                'unit': args[0].strip().strip("'"),
                                                'date_expr1': args[1].strip(),
                                                'date_expr2': args[2].strip()
                                            })
                                        break
                                pos += 1
                        return calls

                    def split_function_args(content):
                        """分割函数参数，考虑嵌套的括号和逗号"""
                        args = []
                        current_arg = ""
                        paren_count = 0
                        quote_char = None

                        for char in content:
                            if quote_char:
                                current_arg += char
                                if char == quote_char and (len(current_arg) == 1 or current_arg[-2] != '\\'):
                                    quote_char = None
                            elif char in ("'", '"'):
                                quote_char = char
                                current_arg += char
                            elif char == '(':
                                paren_count += 1
                                current_arg += char
                            elif char == ')':
                                paren_count -= 1
                                current_arg += char
                            elif char == ',' and paren_count == 0:
                                args.append(current_arg)
                                current_arg = ""
                            else:
                                current_arg += char

                        if current_arg:
                            args.append(current_arg)

                        return args

                    # 找到所有TIMESTAMPDIFF调用
                    calls = find_timestampdiff_calls(translated_query)

                    # 从后往前替换，避免位置偏移
                    for call in reversed(calls):
                        unit = call['unit']
                        date_expr1 = call['date_expr1']
                        date_expr2 = call['date_expr2']

                        if target_dialect == 'sqlserver':
                            # SQL Server的DATEDIFF函数不需要单引号包围datepart参数
                            replacement = target_func.format(unit, date_expr1, date_expr2)
                        else:
                            replacement = target_func.format(unit, date_expr1, date_expr2)

                        translated_query = translated_query[:call['start']] + replacement + translated_query[call['end']:]
                # 处理 DATE_ADD 和 DATE_SUB 函数
                elif mysql_func in ['date_add', 'date_sub']:
                    # 匹配 DATE_ADD(date, INTERVAL value unit) 或 DATE_SUB(date, INTERVAL value unit)
                    func_pattern = rf'{mysql_func.upper()}\s*\(([^,]+),\s*INTERVAL\s+([^)]+)\)'
                    matches = re.finditer(func_pattern, translated_query, re.IGNORECASE)
                    for match in matches:
                        date_expr = match.group(1).strip()
                        interval_expr = match.group(2).strip()

                        # 解析 interval 表达式，如 "1 MONTH" 或 "-1 MONTH"
                        interval_parts = interval_expr.split()
                        if len(interval_parts) >= 2:
                            value = interval_parts[0]
                            unit = ' '.join(interval_parts[1:]).upper()

                            if target_dialect == 'oracle':
                                # Oracle: DATE_ADD(date, INTERVAL value unit) -> (date + INTERVAL 'value' unit)
                                # 处理负数值的情况
                                if value.startswith('-'):
                                    # 如果值是负数，去掉负号并反转操作
                                    abs_value = value[1:]
                                    if mysql_func == 'date_add':
                                        replacement = f"({date_expr} - INTERVAL '{abs_value}' {unit})"
                                    else:  # date_sub
                                        replacement = f"({date_expr} + INTERVAL '{abs_value}' {unit})"
                                else:
                                    if mysql_func == 'date_add':
                                        replacement = f"({date_expr} + INTERVAL '{value}' {unit})"
                                    else:  # date_sub
                                        replacement = f"({date_expr} - INTERVAL '{value}' {unit})"
                            elif target_dialect == 'sqlserver':
                                # SQL Server: DATE_ADD(date, INTERVAL value unit) -> DATEADD(unit, value, date)
                                # 需要转换单位名称
                                unit_mapping = {
                                    'SECOND': 'SECOND', 'MINUTE': 'MINUTE', 'HOUR': 'HOUR',
                                    'DAY': 'DAY', 'WEEK': 'WEEK', 'MONTH': 'MONTH',
                                    'QUARTER': 'QUARTER', 'YEAR': 'YEAR'
                                }
                                sql_unit = unit_mapping.get(unit, unit)
                                if mysql_func == 'date_add':
                                    replacement = f"DATEADD({sql_unit}, {value}, {date_expr})"
                                else:  # date_sub
                                    replacement = f"DATEADD({sql_unit}, -{value}, {date_expr})"
                            else:  # gaussdb
                                # GaussDB: DATE_ADD(date, INTERVAL value unit) -> (date + INTERVAL 'value unit')
                                if mysql_func == 'date_add':
                                    replacement = f"({date_expr} + INTERVAL '{value} {unit}')"
                                else:  # date_sub
                                    replacement = f"({date_expr} - INTERVAL '{value} {unit}')"

                            translated_query = translated_query.replace(match.group(0), replacement)
                # 其他函数，只替换函数名
                else:
                    regex = r'\b' + re.escape(mysql_func) + r'\b'
                    translated_query = re.sub(regex, target_func, translated_query, flags=re.IGNORECASE)
            except re.error as e:
                print(f"处理函数 '{mysql_func}' 时正则表达式错误: {e}")

    # 特定方言的语法调整 (LIMIT 子句)
    if target_dialect == 'oracle':
        limit_match = re.search(r'LIMIT\s+(\d+)(?:\s*,\s*(\d+))?', translated_query, re.IGNORECASE)
        if limit_match:
            original_query_part = re.split(r'LIMIT\s+\d+(?:\s*,\s*\d+)?', translated_query, flags=re.IGNORECASE)[
                0].strip()
            if limit_match.group(2):
                offset = int(limit_match.group(1))
                count = int(limit_match.group(2))
                translated_query = f"SELECT * FROM (SELECT a.*, ROWNUM rnum FROM ({original_query_part}) a WHERE ROWNUM <= {offset + count}) WHERE rnum > {offset}"
            else:
                count = int(limit_match.group(1))
                translated_query = f"SELECT * FROM ({original_query_part}) WHERE ROWNUM <= {count}"
    elif target_dialect == 'sqlserver':
        limit_match = re.search(r'LIMIT\s+(\d+)(?:\s*,\s*(\d+))?', translated_query, re.IGNORECASE)
        if limit_match:
            original_query_part = re.split(r'LIMIT\s+\d+(?:\s*,\s*\d+)?', translated_query, flags=re.IGNORECASE)[
                0].strip()
            # 检查是否已有 ORDER BY，如果没有，添加一个默认的
            has_order_by = bool(re.search(r'\bORDER\s+BY\s+', original_query_part, re.IGNORECASE))
            if not has_order_by:
                # 尝试从 SELECT 子句中提取第一个列作为排序列
                select_match = re.search(r'SELECT\s+(?:DISTINCT\s+)?([^,\s]+)', original_query_part, re.IGNORECASE)
                if select_match:
                    order_by_col = select_match.group(1)
                    if order_by_col.upper() not in ('TOP', 'ALL', 'DISTINCT'):
                        original_query_part += f" ORDER BY {order_by_col}"
                    else:
                        original_query_part += " ORDER BY (SELECT NULL)"
                else:
                    original_query_part += " ORDER BY (SELECT NULL)"

            if limit_match.group(2):
                offset = int(limit_match.group(1))
                count = int(limit_match.group(2))
                translated_query = f"{original_query_part} OFFSET {offset} ROWS FETCH NEXT {count} ROWS ONLY"
            else:
                count = int(limit_match.group(1))
                if original_query_part.upper().strip().startswith('SELECT'):
                    select_keyword = original_query_part[:6]
                    translated_query = f"{select_keyword} TOP {count} " + original_query_part[6:]
                else:
                    translated_query = f"{original_query_part} OFFSET 0 ROWS FETCH NEXT {count} ROWS ONLY"

    return translated_query


def process_file(file_path, rules_column='质控规则-mysql'):
    """
    读取、转换和保存单个 Excel 文件。

    Args:
        file_path (str): 输入的 Excel 文件路径。
        rules_column (str): 包含SQL规则的列名。
    """
    # 构建输出文件路径
    directory, filename = os.path.split(file_path)
    name, ext = os.path.splitext(filename)
    output_path = os.path.join(directory, f"{name}_translated{ext}")
    log_path = os.path.join(directory, f"{name}_conversion_log.txt")

    try:
        print(f"开始处理文件: {filename}...")

        # 打开日志文件
        with open(log_path, 'w', encoding='utf-8') as log_file:
            log_file.write(f"转换日志 - {filename}\n")
            log_file.write(f"时间: {pd.Timestamp.now()}\n\n")

            # 读取 Excel 文件
            df = pd.read_excel(file_path)

            if rules_column not in df.columns:
                error_msg = f"错误: 在文件 {filename} 中未找到列 '{rules_column}'。跳过此文件。"
                print(error_msg)
                log_file.write(error_msg + "\n")
                return

            # 记录转换前的信息
            log_file.write(f"找到 {len(df)} 条质控规则\n\n")

            # 应用转换函数，并记录日志
            log_file.write("开始转换为 Oracle 11g...\n")
            df['oracle11g质控规则'] = df[rules_column].apply(
                lambda x: log_conversion(x, 'oracle', log_file)
            )

            log_file.write("\n开始转换为 SQL Server 2017...\n")
            df['sqlserver2017质控规则'] = df[rules_column].apply(
                lambda x: log_conversion(x, 'sqlserver', log_file)
            )

            log_file.write("\n开始转换为 GaussDB 5.0...\n")
            df['guass5.0质控规则'] = df[rules_column].apply(
                lambda x: log_conversion(x, 'gaussdb', log_file)
            )

            # 保存到新的 Excel 文件
            df.to_excel(output_path, index=False)

            # 记录完成信息
            complete_msg = f"处理完成！转换后的文件已保存到: {output_path}"
            log_file.write(f"\n{complete_msg}\n")
            print(complete_msg)
            print(f"转换日志已保存到: {log_path}\n")

    except FileNotFoundError:
        print(f"错误: 文件未找到: {file_path}\n")
    except Exception as e:
        print(f"处理文件 {filename} 时发生错误: {e}\n")


def log_conversion(sql, dialect, log_file):
    """
    转换 SQL 并记录日志。

    Args:
        sql (str): 要转换的 SQL 语句。
        dialect (str): 目标数据库方言。
        log_file (file): 日志文件对象。

    Returns:
        str: 转换后的 SQL 语句。
    """
    if pd.isna(sql) or not sql:
        return ""

    # 记录原始 SQL
    log_file.write(f"\n原始 SQL: {sql}\n")

    # 转换 SQL
    try:
        result = translate_sql(sql, dialect)
        
        # 特殊处理SQL Server中的DATEDIFF函数错误
        if dialect == 'sqlserver':
            # 处理DATEDIFF函数中的CASE表达式
            datediff_case_pattern = r'DATEDIFF\(CASE\s+WHEN\s+\'([^\']+)\'\s*=\s*\'[^\']+\'\s+THEN\s+\'[^\']+\'\s+.*?END,'
            match = re.search(datediff_case_pattern, result)
            if match:
                # 提取第一个比较值（如'HOUR', 'DAY'等）
                datepart = match.group(1)
                # 替换整个CASE表达式为该值
                result = re.sub(datediff_case_pattern, f"DATEDIFF({datepart},", result)
        
        log_file.write(f"转换为 {dialect}: {result}\n")

        # 检查是否包含 REGEXP 相关内容
        if 'REGEXP' in sql.upper():
            log_file.write(f"注意: 该 SQL 包含正则表达式\n")
            if dialect == 'oracle' and 'REGEXP_LIKE' in result:
                log_file.write(f"已转换为 Oracle REGEXP_LIKE 函数\n")
            elif dialect == 'sqlserver' and 'CASE WHEN' in result and 'LIKE' in result:
                log_file.write(f"已转换为 SQL Server CASE WHEN 和 LIKE 函数组合\n")
                log_file.write(f"注意: SQL Server 不支持完整的正则表达式，此转换仅为近似处理\n")
                log_file.write(f"建议: 对于复杂的正则表达式模式，请考虑使用 SQL Server CLR 自定义函数\n")
            elif dialect == 'gaussdb' and ('~' in result or '!~' in result):
                log_file.write(f"已转换为 GaussDB ~ 或 !~ 操作符\n")
                log_file.write(f"注意: GaussDB 使用 ~ 操作符进行正则表达式匹配，而不是 REGEXP\n")

        return result
    except Exception as e:
        error_msg = f"转换错误: {e}"
        log_file.write(f"{error_msg}\n")
        return f"ERROR: {error_msg}"


def process_single_sql(sql, output_file=None):
    """
    直接处理单个 SQL 语句，转换为各种方言并输出结果。

    Args:
        sql (str): 要转换的 SQL 语句。
        output_file (str, optional): 输出文件路径。如果提供，结果将写入该文件。
    """
    print("\n===== 处理单个 SQL 语句 =====")
    print(f"原始 SQL: {sql}")

    # 转换为各种方言
    oracle_sql = translate_sql(sql, 'oracle')
    sqlserver_sql = translate_sql(sql, 'sqlserver')
    gaussdb_sql = translate_sql(sql, 'gaussdb')

    print(f"\nOracle 11g: {oracle_sql}")
    print(f"\nSQL Server 2017: {sqlserver_sql}")
    print(f"\nGaussDB 5.0: {gaussdb_sql}")

    # 如果提供了输出文件，将结果写入文件
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"原始 SQL: {sql}\n\n")
            f.write(f"Oracle 11g: {oracle_sql}\n\n")
            f.write(f"SQL Server 2017: {sqlserver_sql}\n\n")
            f.write(f"GaussDB 5.0: {gaussdb_sql}\n")
            
            # 添加转换说明
            if 'REGEXP' in sql.upper():
                f.write("\n\n=== 转换说明 ===\n")
                f.write("该 SQL 包含正则表达式 (REGEXP) 操作\n")
                
                if 'REGEXP_LIKE' in oracle_sql:
                    f.write("\nOracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能\n")
                
                if 'CASE WHEN' in sqlserver_sql and 'LIKE' in sqlserver_sql:
                    f.write("\nSQL Server: 由于 SQL Server 不支持原生正则表达式，已转换为 CASE WHEN 和 LIKE 的组合\n")
                    f.write("注意: 此转换仅为近似处理，不支持完整的正则表达式语法\n")
                    f.write("建议: 对于复杂的正则表达式模式，请考虑以下方案：\n")
                    f.write("  1. 使用 SQL Server CLR 自定义函数实现正则表达式功能\n")
                    f.write("  2. 在应用层处理正则表达式逻辑\n")
                    f.write("  3. 使用多个 LIKE 条件组合模拟简单的正则表达式\n")
                
                if 'REGEXP' in gaussdb_sql:
                    f.write("\nGaussDB: 保留原始 REGEXP 语法，GaussDB 支持类似 MySQL 的正则表达式\n")
                    f.write("注意: GaussDB 推荐使用 ~ 或 !~ 操作符进行正则表达式匹配，而不是 REGEXP\n")
                elif '~' in gaussdb_sql or '!~' in gaussdb_sql:
                    f.write("\nGaussDB: 已转换为 ~ 或 !~ 操作符，GaussDB 使用这些操作符进行正则表达式匹配\n")
        
        print(f"\n结果已保存到文件: {output_file}")
    
    # 如果SQL包含REGEXP并且没有提供输出文件，自动创建一个日志文件
    elif 'REGEXP' in sql.upper():
        import os
        import datetime
        
        # 获取当前时间作为文件名的一部分
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建日志文件
        log_file = os.path.join(log_dir, f"sql_conversion_{timestamp}.log")
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"原始 SQL: {sql}\n\n")
            f.write(f"Oracle 11g: {oracle_sql}\n\n")
            f.write(f"SQL Server 2017: {sqlserver_sql}\n\n")
            f.write(f"GaussDB 5.0: {gaussdb_sql}\n")
            
            # 添加转换说明
            f.write("\n\n=== 转换说明 ===\n")
            f.write("该 SQL 包含正则表达式 (REGEXP) 操作\n")
            
            if 'REGEXP_LIKE' in oracle_sql:
                f.write("\nOracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能\n")
            
            if 'CASE WHEN' in sqlserver_sql and 'LIKE' in sqlserver_sql:
                f.write("\nSQL Server: 由于 SQL Server 不支持原生正则表达式，已转换为 CASE WHEN 和 LIKE 的组合\n")
                f.write("注意: 此转换仅为近似处理，不支持完整的正则表达式语法\n")
                f.write("建议: 对于复杂的正则表达式模式，请考虑以下方案：\n")
                f.write("  1. 使用 SQL Server CLR 自定义函数实现正则表达式功能\n")
                f.write("  2. 在应用层处理正则表达式逻辑\n")
                f.write("  3. 使用多个 LIKE 条件组合模拟简单的正则表达式\n")
            
            if 'REGEXP' in gaussdb_sql:
                f.write("\nGaussDB: 保留原始 REGEXP 语法，GaussDB 支持类似 MySQL 的正则表达式\n")
                f.write("注意: GaussDB 推荐使用 ~ 或 !~ 操作符进行正则表达式匹配，而不是 REGEXP\n")
            elif '~' in gaussdb_sql or '!~' in gaussdb_sql:
                f.write("\nGaussDB: 已转换为 ~ 或 !~ 操作符，GaussDB 使用这些操作符进行正则表达式匹配\n")
        
        print(f"\n检测到正则表达式，转换日志已保存到: {log_file}")


def clear_output_files(base_dir=None):
    """
    清除之前生成的输出文件。

    Args:
        base_dir (str, optional): 基础目录。如果不提供，将使用默认目录。
    """
    if base_dir is None:
        base_dir = r'D:\work\demo\福建'

    import glob
    import os

    # 查找所有 *_translated.xlsx 文件
    pattern = os.path.join(base_dir, '*_translated.xlsx')
    files = glob.glob(pattern)

    if files:
        print(f"\n===== 清除输出文件 =====")
        for file in files:
            try:
                os.remove(file)
                print(f"已删除: {file}")
            except Exception as e:
                print(f"删除 {file} 时出错: {e}")
        print(f"共清除 {len(files)} 个输出文件")
    else:
        print(f"\n没有找到需要清除的输出文件")


def main():
    """
    主函数，定义要处理的文件列表并调用处理函数。
    """
    print("\n===== 开始执行主程序 =====")
    base_dir = r'D:\work\demo\福建'

    # 清除之前的输出文件
    clear_output_files(base_dir)

    # 处理用户提供的案例
    print("\n===== 测试用户案例 - REGEXP = 0 形式 =====")
    user_case = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\d{9}$' = 0"
    process_single_sql(user_case)
    
    # 处理用户提供的 not REGEXP 形式案例
    print("\n===== 测试用户案例 - not REGEXP 形式 =====")
    user_case_not_regexp = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\d{9}$'"
    process_single_sql(user_case_not_regexp)

    # 测试 TIMESTAMPDIFF 函数转换
    print("\n===== 测试用户案例 - TIMESTAMPDIFF 函数 =====")
    timestampdiff_case = "SELECT TIMESTAMPDIFF(DAY, admission_date, discharge_date) as stay_days FROM patient_info"
    process_single_sql(timestampdiff_case)
    
    # 测试 DATEDIFF 函数错误修复
    print("\n===== 测试用户案例 - DATEDIFF 函数错误修复 =====")
    datediff_case = "SELECT 'YD01202506175124','患者过敏记录（PATIENT_ALLERGY_RECORD）中的数据上传时间（UPLOAD_TIME）与数据更新时间（UPDT_TIME）超过27小时',COUNT(1) FROM patient_allergy_record A where 1=1 and DATEDIFF(CASE WHEN 'HOUR' = 'SECOND' THEN 'SECOND' WHEN 'HOUR' = 'MINUTE' THEN 'MINUTE' WHEN 'HOUR' = 'HOUR' THEN 'HOUR' WHEN 'HOUR' = 'DAY' THEN 'DAY' WHEN 'HOUR' = 'WEEK' THEN 'WEEK' WHEN 'HOUR' = 'MONTH' THEN 'MONTH' WHEN 'HOUR' = 'QUARTER' THEN 'QUARTER' WHEN 'HOUR' = 'YEAR' THEN 'YEAR' END, A.updt_time, A.upload_time)>27;"
    process_single_sql(datediff_case)

    # 测试复杂的 TIMESTAMPDIFF 函数转换
    print("\n===== 测试用户案例 - 复杂的 TIMESTAMPDIFF 函数 =====")
    complex_timestampdiff_case = "timestampdiff ( month, '2020-01-01 00:00:00', date_add ( current_date, interval - 1 month ) ) - count( 1 ) AS biz_info"
    process_single_sql(complex_timestampdiff_case)

    # 处理 Excel 文件
    files_to_process = [
        os.path.join(base_dir, '附件1：医院数据质量校验规则-第一批次-医院自检-20250620.xlsx'),
        os.path.join(base_dir, '附件2：医院数据质量校验规则-第一批次-贴源库质控-20250620.xlsx')
    ]

    for file_path in files_to_process:
        if os.path.exists(file_path):
            process_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

    print("\n===== 主程序执行完毕 =====")


def test_sql_conversion():
    """
    测试 SQL 转换功能
    """
    test_cases = [
        {
            'name': '测试日期格式转换',
            'mysql': "SELECT DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as dt FROM table",
            'oracle': "SELECT TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as dt FROM table",
            'sqlserver': "SELECT FORMAT(create_time, 'yyyy-MM-dd HH:mm:ss') as dt FROM table",
            'gaussdb': "SELECT TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as dt FROM table"
        },
        {
            'name': '测试 GROUP_CONCAT 转换',
            'mysql': "SELECT GROUP_CONCAT(name ORDER BY id) as names FROM table",
            'oracle': "SELECT LISTAGG(name, ',') WITHIN GROUP (ORDER BY id) as names FROM table",
            'sqlserver': "SELECT STRING_AGG(name, ',') WITHIN GROUP (ORDER BY id) as names FROM table",
            'gaussdb': "SELECT STRING_AGG(name, ',') WITHIN GROUP (ORDER BY id) as names FROM table"
        },
        {
            'name': '测试 LIMIT 转换',
            'mysql': "SELECT * FROM table LIMIT 10",
            'oracle': "SELECT * FROM (SELECT * FROM table) WHERE ROWNUM <= 10",
            'sqlserver': "SELECT TOP 10 * FROM table",
            'gaussdb': "SELECT * FROM table LIMIT 10"
        },
        {
            'name': '测试 LIMIT OFFSET 转换',
            'mysql': "SELECT * FROM table LIMIT 10, 20",
            'oracle': "SELECT * FROM (SELECT a.*, ROWNUM rnum FROM (SELECT * FROM table) a WHERE ROWNUM <= 30) WHERE rnum > 10",
            'sqlserver': "SELECT * FROM table ORDER BY (SELECT NULL) OFFSET 10 ROWS FETCH NEXT 20 ROWS ONLY",
            'gaussdb': "SELECT * FROM table LIMIT 10, 20"
        },
        {
            'name': '测试正则表达式转换 - 基本模式',
            'mysql': "SELECT * FROM table WHERE name REGEXP '^test' AND code REGEXP '123$' = 0",
            'oracle': "SELECT * FROM table WHERE REGEXP_LIKE(name, '^test') AND NOT REGEXP_LIKE(code, '123$')",
            'sqlserver': "SELECT * FROM table WHERE name REGEXP '^test' AND code REGEXP '123$' = 0",
            'gaussdb': "SELECT * FROM table WHERE name REGEXP '^test' AND code REGEXP '123$' = 0"
        },
        {
            'name': '测试正则表达式转换 - 手机号验证',
            'mysql': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0",
            'oracle': "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')",
            'sqlserver': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0",
            'gaussdb': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0"
        },
        {
            'name': '测试正则表达式转换 - 复杂条件',
            'mysql': "SELECT * FROM table WHERE col1 REGEXP '^[a-z]+' AND col2 REGEXP '[0-9]+$' = 0 OR col3 REGEXP '\\\\w+\\\\s\\\\w+'",
            'oracle': "SELECT * FROM table WHERE REGEXP_LIKE(col1, '^[a-z]+') AND NOT REGEXP_LIKE(col2, '[0-9]+$') OR REGEXP_LIKE(col3, '\\w+\\s\\w+')",
            'sqlserver': "SELECT * FROM table WHERE col1 REGEXP '^[a-z]+' AND col2 REGEXP '[0-9]+$' = 0 OR col3 REGEXP '\\\\w+\\\\s\\\\w+'",
            'gaussdb': "SELECT * FROM table WHERE col1 REGEXP '^[a-z]+' AND col2 REGEXP '[0-9]+$' = 0 OR col3 REGEXP '\\\\w+\\\\s\\\\w+'"
        },
        {
            'name': '测试正则表达式转换 - 用户提供的案例',
            'mysql': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0",
            'oracle': "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')",
            'sqlserver': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0",
            'gaussdb': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\\\d{9}$' = 0"
        },
        {
            'name': '测试正则表达式转换 - not REGEXP 形式',
            'mysql': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\\\d{9}$'",
            'oracle': "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')",
            'sqlserver': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\\\d{9}$'",
            'gaussdb': "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\\\d{9}$'"
        },
        {
            'name': '测试正则表达式转换 - 复杂括号表达式',
            'mysql': "SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\\\u4e00-\\\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\\\u4e00-\\\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');",
            'oracle': "SELECT count(*) FROM outp_charge_detail A where 1=1 and REGEXP_LIKE(A.APPLY_DOC_NAME, '[^a-zA-Z\\u4e00-\\u9fa5·]') OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (REGEXP_LIKE(A.APPLY_DOC_NAME, '^[\\u4e00-\\u9fa5]+$') AND A.APPLY_DOC_NAME LIKE '% %');",
            'sqlserver': "SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\\\u4e00-\\\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\\\u4e00-\\\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');",
            'gaussdb': "SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\\\u4e00-\\\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\\\u4e00-\\\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');"
        },
        {
            'name': '测试 TIMESTAMPDIFF 函数转换',
            'mysql': "SELECT TIMESTAMPDIFF(SECOND, start_time, end_time) as seconds, TIMESTAMPDIFF(MINUTE, start_time, end_time) as minutes, TIMESTAMPDIFF(HOUR, start_time, end_time) as hours, TIMESTAMPDIFF(DAY, start_time, end_time) as days, TIMESTAMPDIFF(WEEK, start_time, end_time) as weeks, TIMESTAMPDIFF(MONTH, start_time, end_time) as months, TIMESTAMPDIFF(QUARTER, start_time, end_time) as quarters, TIMESTAMPDIFF(YEAR, start_time, end_time) as years FROM table",
            'oracle': "SELECT CASE WHEN 'SECOND' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'SECOND' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'SECOND' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'SECOND' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'SECOND' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'SECOND' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'SECOND' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'SECOND' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as seconds, CASE WHEN 'MINUTE' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'MINUTE' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'MINUTE' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'MINUTE' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'MINUTE' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'MINUTE' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'MINUTE' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'MINUTE' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as minutes, CASE WHEN 'HOUR' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'HOUR' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'HOUR' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'HOUR' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'HOUR' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'HOUR' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'HOUR' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'HOUR' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as hours, CASE WHEN 'DAY' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'DAY' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'DAY' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'DAY' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'DAY' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'DAY' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'DAY' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'DAY' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as days, CASE WHEN 'WEEK' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'WEEK' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'WEEK' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'WEEK' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'WEEK' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'WEEK' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'WEEK' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'WEEK' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as weeks, CASE WHEN 'MONTH' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'MONTH' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'MONTH' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'MONTH' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'MONTH' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'MONTH' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'MONTH' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'MONTH' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as months, CASE WHEN 'QUARTER' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'QUARTER' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'QUARTER' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'QUARTER' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'QUARTER' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'QUARTER' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'QUARTER' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'QUARTER' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as quarters, CASE WHEN 'YEAR' = 'SECOND' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 86400) WHEN 'YEAR' = 'MINUTE' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 1440) WHEN 'YEAR' = 'HOUR' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) * 24) WHEN 'YEAR' = 'DAY' THEN ROUND(CAST(end_time AS DATE) - CAST(start_time AS DATE)) WHEN 'YEAR' = 'WEEK' THEN ROUND((CAST(end_time AS DATE) - CAST(start_time AS DATE)) / 7) WHEN 'YEAR' = 'MONTH' THEN ROUND(MONTHS_BETWEEN(end_time, start_time)) WHEN 'YEAR' = 'QUARTER' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 3) WHEN 'YEAR' = 'YEAR' THEN ROUND(MONTHS_BETWEEN(end_time, start_time) / 12) END as years FROM table",
            'sqlserver': "SELECT DATEDIFF(CASE WHEN 'SECOND' = 'SECOND' THEN 'SECOND' WHEN 'SECOND' = 'MINUTE' THEN 'MINUTE' WHEN 'SECOND' = 'HOUR' THEN 'HOUR' WHEN 'SECOND' = 'DAY' THEN 'DAY' WHEN 'SECOND' = 'WEEK' THEN 'WEEK' WHEN 'SECOND' = 'MONTH' THEN 'MONTH' WHEN 'SECOND' = 'QUARTER' THEN 'QUARTER' WHEN 'SECOND' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as seconds, DATEDIFF(CASE WHEN 'MINUTE' = 'SECOND' THEN 'SECOND' WHEN 'MINUTE' = 'MINUTE' THEN 'MINUTE' WHEN 'MINUTE' = 'HOUR' THEN 'HOUR' WHEN 'MINUTE' = 'DAY' THEN 'DAY' WHEN 'MINUTE' = 'WEEK' THEN 'WEEK' WHEN 'MINUTE' = 'MONTH' THEN 'MONTH' WHEN 'MINUTE' = 'QUARTER' THEN 'QUARTER' WHEN 'MINUTE' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as minutes, DATEDIFF(CASE WHEN 'HOUR' = 'SECOND' THEN 'SECOND' WHEN 'HOUR' = 'MINUTE' THEN 'MINUTE' WHEN 'HOUR' = 'HOUR' THEN 'HOUR' WHEN 'HOUR' = 'DAY' THEN 'DAY' WHEN 'HOUR' = 'WEEK' THEN 'WEEK' WHEN 'HOUR' = 'MONTH' THEN 'MONTH' WHEN 'HOUR' = 'QUARTER' THEN 'QUARTER' WHEN 'HOUR' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as hours, DATEDIFF(CASE WHEN 'DAY' = 'SECOND' THEN 'SECOND' WHEN 'DAY' = 'MINUTE' THEN 'MINUTE' WHEN 'DAY' = 'HOUR' THEN 'HOUR' WHEN 'DAY' = 'DAY' THEN 'DAY' WHEN 'DAY' = 'WEEK' THEN 'WEEK' WHEN 'DAY' = 'MONTH' THEN 'MONTH' WHEN 'DAY' = 'QUARTER' THEN 'QUARTER' WHEN 'DAY' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as days, DATEDIFF(CASE WHEN 'WEEK' = 'SECOND' THEN 'SECOND' WHEN 'WEEK' = 'MINUTE' THEN 'MINUTE' WHEN 'WEEK' = 'HOUR' THEN 'HOUR' WHEN 'WEEK' = 'DAY' THEN 'DAY' WHEN 'WEEK' = 'WEEK' THEN 'WEEK' WHEN 'WEEK' = 'MONTH' THEN 'MONTH' WHEN 'WEEK' = 'QUARTER' THEN 'QUARTER' WHEN 'WEEK' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as weeks, DATEDIFF(CASE WHEN 'MONTH' = 'SECOND' THEN 'SECOND' WHEN 'MONTH' = 'MINUTE' THEN 'MINUTE' WHEN 'MONTH' = 'HOUR' THEN 'HOUR' WHEN 'MONTH' = 'DAY' THEN 'DAY' WHEN 'MONTH' = 'WEEK' THEN 'WEEK' WHEN 'MONTH' = 'MONTH' THEN 'MONTH' WHEN 'MONTH' = 'QUARTER' THEN 'QUARTER' WHEN 'MONTH' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as months, DATEDIFF(CASE WHEN 'QUARTER' = 'SECOND' THEN 'SECOND' WHEN 'QUARTER' = 'MINUTE' THEN 'MINUTE' WHEN 'QUARTER' = 'HOUR' THEN 'HOUR' WHEN 'QUARTER' = 'DAY' THEN 'DAY' WHEN 'QUARTER' = 'WEEK' THEN 'WEEK' WHEN 'QUARTER' = 'MONTH' THEN 'MONTH' WHEN 'QUARTER' = 'QUARTER' THEN 'QUARTER' WHEN 'QUARTER' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as quarters, DATEDIFF(CASE WHEN 'YEAR' = 'SECOND' THEN 'SECOND' WHEN 'YEAR' = 'MINUTE' THEN 'MINUTE' WHEN 'YEAR' = 'HOUR' THEN 'HOUR' WHEN 'YEAR' = 'DAY' THEN 'DAY' WHEN 'YEAR' = 'WEEK' THEN 'WEEK' WHEN 'YEAR' = 'MONTH' THEN 'MONTH' WHEN 'YEAR' = 'QUARTER' THEN 'QUARTER' WHEN 'YEAR' = 'YEAR' THEN 'YEAR' END, start_time, end_time) as years FROM table",
            'gaussdb': "SELECT TIMESTAMPDIFF('SECOND', start_time, end_time) as seconds, TIMESTAMPDIFF('MINUTE', start_time, end_time) as minutes, TIMESTAMPDIFF('HOUR', start_time, end_time) as hours, TIMESTAMPDIFF('DAY', start_time, end_time) as days, TIMESTAMPDIFF('WEEK', start_time, end_time) as weeks, TIMESTAMPDIFF('MONTH', start_time, end_time) as months, TIMESTAMPDIFF('QUARTER', start_time, end_time) as quarters, TIMESTAMPDIFF('YEAR', start_time, end_time) as years FROM table"
        }
    ]

    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        print(f"MySQL 原始语句: {test_case['mysql']}")

        for dialect in ['oracle', 'sqlserver', 'gaussdb']:
            result = translate_sql(test_case['mysql'], dialect)
            expected = test_case[dialect]
            if result.strip() == expected.strip():
                print(f"{dialect} 转换正确")
            else:
                print(f"{dialect} 转换结果与预期不符")
                print(f"预期: {expected}")
                print(f"实际: {result}")
                # 详细调试输出
                if dialect == 'oracle' and 'REGEXP' in test_case['mysql'].upper():
                    print("--- 正则表达式转换调试信息 ---")
                    print(f"原始SQL: {test_case['mysql']}")
                    print(f"转换后: {result}")
                    print(f"预期结果: {expected}")
                    # 检查是否包含 REGEXP_LIKE
                    if 'REGEXP_LIKE' in result:
                        print("包含 REGEXP_LIKE 函数")
                    else:
                        print("不包含 REGEXP_LIKE 函数")
                    # 检查是否包含 NOT REGEXP_LIKE
                    if 'NOT REGEXP_LIKE' in result:
                        print("包含 NOT REGEXP_LIKE 函数")
                    else:
                        print("不包含 NOT REGEXP_LIKE 函数")
                    print("----------------------------")


def test_user_case():
    """
    专门测试用户提供的案例
    """
    print("\n===== 测试用户案例 =====")
    # 用户提供的原始案例
    mysql_query = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\d{9}$' = 0;"

    # 去掉末尾的分号，与函数处理一致
    mysql_query = mysql_query.rstrip(';')

    # 转换为 Oracle
    oracle_result = translate_sql(mysql_query, 'oracle')
    print(f"MySQL 原始语句: {mysql_query}")
    print(f"Oracle 转换结果: {oracle_result}")

    # 手动构建预期的 Oracle 结果
    expected = "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')"

    print(f"预期结果: {expected}")
    print(f"结果匹配: {oracle_result.strip() == expected.strip()}")

    if oracle_result.strip() != expected.strip():
        print("\n差异分析:")
        # 逐字符比较
        for i, (c1, c2) in enumerate(zip(oracle_result, expected)):
            if c1 != c2:
                print(f"位置 {i}: '{c1}' != '{c2}'")
                # 显示上下文
                start = max(0, i - 20)
                end = min(len(oracle_result), i + 20)
                print(f"结果上下文: '...{oracle_result[start:end]}...'")
                print(f"预期上下文: '...{expected[start:end]}...'")
                break

        # 检查长度差异
        if len(oracle_result) != len(expected):
            print(f"长度差异: 结果 {len(oracle_result)} 字符 vs 预期 {len(expected)} 字符")
            if len(oracle_result) > len(expected):
                print(
                    f"结果多出的部分: '{oracle_result[len(expected):]}'" if len(oracle_result) > len(expected) else "")
            else:
                print(
                    f"预期多出的部分: '{expected[len(oracle_result):]}'" if len(expected) > len(oracle_result) else "")

    # 测试另一个常见案例
    print("\n----- 测试另一个常见案例 -----")
    mysql_query2 = "SELECT * FROM table WHERE name REGEXP '^[A-Z]'"
    oracle_result2 = translate_sql(mysql_query2, 'oracle')
    expected2 = "SELECT * FROM table WHERE REGEXP_LIKE(name, '^[A-Z]')"
    print(f"MySQL 原始语句: {mysql_query2}")
    print(f"Oracle 转换结果: {oracle_result2}")
    print(f"预期结果: {expected2}")
    print(f"结果匹配: {oracle_result2.strip() == expected2.strip()}")

    # 测试用户提供的问题案例
    print("\n----- 测试用户提供的问题案例 -----")
    problem_query = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP_LIKE(not, '^1[3456789]\\d{9}$');"
    print(f"问题语句: {problem_query}")
    print(
        f"正确语句应为: SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$');")
    print("这个问题语句是转换错误的结果，NOT 应该在 REGEXP_LIKE 前面，而不是作为参数。")

    # 测试用户提供的 not REGEXP 形式
    print("\n----- 测试用户提供的 not REGEXP 形式 -----")
    not_regexp_query = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\d{9}$';"
    not_regexp_query = not_regexp_query.rstrip(';')  # 去掉末尾的分号
    oracle_result3 = translate_sql(not_regexp_query, 'oracle')
    expected3 = "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')"
    print(f"MySQL 原始语句: {not_regexp_query}")
    print(f"Oracle 转换结果: {oracle_result3}")
    print(f"预期结果: {expected3}")
    print(f"结果匹配: {oracle_result3.strip() == expected3.strip()}")


def test_process_flow():
    """
    测试整个转换流程
    """
    print("\n===== 测试整个转换流程 =====")
    # 模拟 Excel 文件的数据
    import pandas as pd

    # 创建测试数据
    data = {
        '质控规则-mysql': [
            "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\d{9}$' = 0"
        ]
    }
    df = pd.DataFrame(data)

    # 应用转换函数
    df['oracle11g质控规则'] = df['质控规则-mysql'].apply(lambda x: translate_sql(x, 'oracle'))
    df['sqlserver2017质控规则'] = df['质控规则-mysql'].apply(lambda x: translate_sql(x, 'sqlserver'))
    df['guass5.0质控规则'] = df['质控规则-mysql'].apply(lambda x: translate_sql(x, 'gaussdb'))

    # 显示结果
    print("原始 SQL:")
    print(df['质控规则-mysql'][0])
    print("\nOracle 转换结果:")
    print(df['oracle11g质控规则'][0])
    print("\nSQL Server 转换结果:")
    print(df['sqlserver2017质控规则'][0])
    print("\nGaussDB 转换结果:")
    print(df['guass5.0质控规则'][0])

    # 检查 Oracle 转换是否正确
    expected_oracle = "SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\\d{9}$')"
    print(f"\nOracle 转换是否正确: {df['oracle11g质控规则'][0] == expected_oracle}")


if __name__ == '__main__':
    # 运行流程测试
    test_process_flow()

    # 运行用户案例测试
    test_user_case()

    # 运行测试用例
    test_sql_conversion()

    # 运行主程序
    main()
