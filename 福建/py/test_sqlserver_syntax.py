# -*- coding: utf-8 -*-
"""
测试SQL Server语法正确性
"""

from 质控mysql转其他库 import translate_sql

def test_sqlserver_syntax():
    """测试SQL Server语法转换的正确性"""
    print("===== 测试SQL Server语法转换 =====")
    
    # 测试用例
    test_cases = [
        {
            'name': '简单的TIMESTAMPDIFF',
            'mysql': "SELECT TIMESTAMPDIFF(MONTH, '2020-01-01', CURRENT_DATE) as months_diff",
            'expected_keywords': ['DATEDIFF', 'CAST(GETDATE() AS DATE)']
        },
        {
            'name': '复杂的嵌套函数',
            'mysql': "timestampdiff(month, '2020-01-01 00:00:00', date_add(current_date, interval -1 month)) - count(1) AS biz_info",
            'expected_keywords': ['DATEDIFF', 'DATEADD', 'CAST(GETDATE() AS DATE)']
        },
        {
            'name': 'DATE_ADD函数',
            'mysql': "SELECT DATE_ADD(CURRENT_DATE, INTERVAL -1 MONTH) as last_month",
            'expected_keywords': ['DATEADD', 'MONTH', 'CAST(GETDATE() AS DATE)']
        },
        {
            'name': 'DATE_ADD正数',
            'mysql': "SELECT DATE_ADD(CURRENT_DATE, INTERVAL 1 MONTH) as next_month",
            'expected_keywords': ['DATEADD', 'MONTH', 'CAST(GETDATE() AS DATE)']
        },
        {
            'name': 'DATE_SUB函数',
            'mysql': "SELECT DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH) as last_month",
            'expected_keywords': ['DATEADD', 'MONTH', 'CAST(GETDATE() AS DATE)']
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"MySQL原始: {case['mysql']}")
        
        sqlserver_result = translate_sql(case['mysql'], 'sqlserver')
        print(f"SQL Server转换: {sqlserver_result}")
        
        # 检查是否包含期望的关键词
        missing_keywords = []
        for keyword in case['expected_keywords']:
            if keyword not in sqlserver_result:
                missing_keywords.append(keyword)
        
        if missing_keywords:
            print(f"❌ 缺少关键词: {missing_keywords}")
        else:
            print("✅ 包含所有期望的关键词")
        
        # 检查常见的语法错误
        syntax_issues = []
        
        # 检查DATEADD语法
        if "DATEADD(" in sqlserver_result:
            # 检查是否有错误的语法如 "DATEADD(MONTH, -, date)"
            if ", -, " in sqlserver_result or ", -," in sqlserver_result:
                syntax_issues.append("DATEADD语法错误：缺少数值")
            
            # 检查是否有正确的参数格式
            import re
            dateadd_matches = re.findall(r'DATEADD\([^)]+\)', sqlserver_result)
            for match in dateadd_matches:
                if match.count(',') != 2:
                    syntax_issues.append(f"DATEADD参数数量错误: {match}")
        
        # 检查DATEDIFF语法
        if "DATEDIFF(" in sqlserver_result:
            import re
            datediff_matches = re.findall(r'DATEDIFF\([^)]+\)', sqlserver_result)
            for match in datediff_matches:
                if match.count(',') != 2:
                    syntax_issues.append(f"DATEDIFF参数数量错误: {match}")
        
        if syntax_issues:
            print(f"⚠️  潜在语法问题: {syntax_issues}")
        else:
            print("✅ 未发现明显语法问题")

def test_specific_sqlserver_case():
    """测试特定的SQL Server案例"""
    print("\n===== 测试特定SQL Server案例 =====")
    
    # 用户提供的具体SQL
    user_sql = "timestampdiff ( month, '2020-01-01 00:00:00', date_add ( current_date, interval - 1 month ) ) - count( 1 ) AS biz_info"
    
    sqlserver_result = translate_sql(user_sql, 'sqlserver')
    
    print("用户SQL:", user_sql)
    print("\nSQL Server转换结果:")
    print(sqlserver_result)
    
    # 提取关键部分进行分析
    print("\n===== 语法分析 =====")
    
    # 检查DATEDIFF的使用
    if "DATEDIFF(" in sqlserver_result:
        print("✅ 使用了DATEDIFF函数（正确）")
    
    # 检查DATEADD的使用
    if "DATEADD(" in sqlserver_result:
        print("✅ 使用了DATEADD函数（正确）")
        
        # 检查DATEADD的参数
        import re
        dateadd_matches = re.findall(r'DATEADD\([^)]+\)', sqlserver_result)
        for match in dateadd_matches:
            print(f"  DATEADD调用: {match}")
            if ", -, " in match or ", -," in match:
                print("  ❌ 发现语法错误：缺少数值")
            else:
                print("  ✅ 语法正确")
    
    # 检查CAST(GETDATE() AS DATE)的使用
    if "CAST(GETDATE() AS DATE)" in sqlserver_result:
        print("✅ 正确使用了CAST(GETDATE() AS DATE)替代CURRENT_DATE")
    
    # 生成简化的SQL Server SQL用于测试
    print("\n===== 简化的SQL Server SQL（用于测试） =====")
    simplified_sql = """
SELECT 
    DATEDIFF(MONTH, 
        '2020-01-01 00:00:00', 
        DATEADD(MONTH, -1, CAST(GETDATE() AS DATE))
    ) - COUNT(1) AS biz_info
FROM (SELECT 1 as dummy) t
"""
    print(simplified_sql.strip())
    
    print("\n说明：")
    print("1. 使用DATEDIFF()计算月份差")
    print("2. DATEADD()用于日期运算")
    print("3. CAST(GETDATE() AS DATE)获取当前日期")
    print("4. 添加FROM子句使其成为完整的可执行SQL")
    print("5. 避免了列名冲突和语法错误")

if __name__ == "__main__":
    test_sqlserver_syntax()
    test_specific_sqlserver_case()
