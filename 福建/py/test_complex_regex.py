from 质控mysql转其他库 import translate_sql

# 用户提供的复杂SQL案例
sql = "SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');"

# 转换为 Oracle
oracle_sql = translate_sql(sql, 'oracle')

print("原始 SQL:")
print(sql)
print("\nOracle 转换结果:")
print(oracle_sql)

# 期望的正确结果
expected_result = """SELECT count(*) FROM outp_charge_detail A where 1=1 and REGEXP_LIKE(A.APPLY_DOC_NAME, '[^a-zA-Z\\u4e00-\\u9fa5·]') OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (REGEXP_LIKE(A.APPLY_DOC_NAME, '^[\\u4e00-\\u9fa5]+$') AND A.APPLY_DOC_NAME LIKE '% %');"""

print("\n期望的正确结果:")
print(expected_result)

# 检查是否匹配
is_match = oracle_sql.strip() == expected_result.strip()
print(f"\n结果是否匹配: {'是' if is_match else '否'}")

if not is_match:
    print("\n差异分析:")
    # 逐字符比较
    for i, (c1, c2) in enumerate(zip(oracle_sql, expected_result)):
        if c1 != c2:
            print(f"位置 {i}: '{c1}' != '{c2}'")
            # 显示上下文
            start = max(0, i - 20)
            end = min(len(oracle_sql), i + 20)
            print(f"结果上下文: '...{oracle_sql[start:end]}...'")
            print(f"预期上下文: '...{expected_result[start:end]}...'")
            break

    # 检查长度差异
    if len(oracle_sql) != len(expected_result):
        print(f"长度差异: 结果 {len(oracle_sql)} 字符 vs 预期 {len(expected_result)} 字符")
        if len(oracle_sql) > len(expected_result):
            print(f"结果多出的部分: '{oracle_sql[len(expected_result):]}'" if len(oracle_sql) > len(expected_result) else "")
        else:
            print(f"预期多出的部分: '{expected_result[len(oracle_sql):]}'" if len(expected_result) > len(oracle_sql) else "") 