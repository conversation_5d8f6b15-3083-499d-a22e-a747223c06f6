# -*- coding: utf-8 -*-
"""
测试Oracle语法正确性
"""

from 质控mysql转其他库 import translate_sql

def test_oracle_syntax():
    """测试Oracle语法转换的正确性"""
    print("===== 测试Oracle语法转换 =====")
    
    # 测试用例
    test_cases = [
        {
            'name': '简单的TIMESTAMPDIFF',
            'mysql': "SELECT TIMESTAMPDIFF(MONTH, '2020-01-01', CURRENT_DATE) as months_diff",
            'expected_keywords': ['MONTHS_BETWEEN', 'TO_DATE', 'TRUNC(SYSDATE)']
        },
        {
            'name': '复杂的嵌套函数',
            'mysql': "timestampdiff(month, '2020-01-01 00:00:00', date_add(current_date, interval -1 month)) - count(1) AS biz_info",
            'expected_keywords': ['MONTHS_BETWEEN', 'TO_DATE', 'TRUNC(SYSDATE)', 'INTERVAL']
        },
        {
            'name': 'DATE_ADD函数',
            'mysql': "SELECT DATE_ADD(CURRENT_DATE, INTERVAL -1 MONTH) as last_month",
            'expected_keywords': ['TRUNC(SYSDATE)', 'INTERVAL', 'MONTH']
        },
        {
            'name': '日期字面量',
            'mysql': "SELECT '2020-01-01 00:00:00' as test_date",
            'expected_keywords': ['TO_DATE', 'YYYY-MM-DD HH24:MI:SS']
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"MySQL原始: {case['mysql']}")
        
        oracle_result = translate_sql(case['mysql'], 'oracle')
        print(f"Oracle转换: {oracle_result}")
        
        # 检查是否包含期望的关键词
        missing_keywords = []
        for keyword in case['expected_keywords']:
            if keyword not in oracle_result:
                missing_keywords.append(keyword)
        
        if missing_keywords:
            print(f"❌ 缺少关键词: {missing_keywords}")
        else:
            print("✅ 包含所有期望的关键词")
        
        # 检查常见的语法错误
        syntax_issues = []
        
        # 检查INTERVAL语法
        if "INTERVAL ''" in oracle_result:
            syntax_issues.append("INTERVAL语法错误：空字符串")
        
        # 检查TO_DATE格式
        if "TO_DATE(" in oracle_result and "YYYY-MM-DD" not in oracle_result:
            syntax_issues.append("TO_DATE缺少格式字符串")
        
        # 检查MONTHS_BETWEEN参数顺序
        if "MONTHS_BETWEEN(" in oracle_result:
            # 简单检查是否有正确的参数结构
            if oracle_result.count("MONTHS_BETWEEN(") != oracle_result.count(", "):
                pass  # 这个检查太简单，跳过
        
        if syntax_issues:
            print(f"⚠️  潜在语法问题: {syntax_issues}")
        else:
            print("✅ 未发现明显语法问题")

def test_specific_oracle_case():
    """测试特定的Oracle案例"""
    print("\n===== 测试特定Oracle案例 =====")
    
    # 用户提供的具体SQL
    user_sql = "timestampdiff ( month, '2020-01-01 00:00:00', date_add ( current_date, interval - 1 month ) ) - count( 1 ) AS biz_info"
    
    oracle_result = translate_sql(user_sql, 'oracle')
    
    print("用户SQL:", user_sql)
    print("\nOracle转换结果:")
    print(oracle_result)
    
    # 提取关键部分进行分析
    print("\n===== 语法分析 =====")
    
    # 检查MONTHS_BETWEEN的使用
    if "MONTHS_BETWEEN(" in oracle_result:
        print("✅ 使用了MONTHS_BETWEEN函数（正确）")
    
    # 检查TO_DATE的使用
    if "TO_DATE('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')" in oracle_result:
        print("✅ 正确转换了日期字面量为TO_DATE格式")
    
    # 检查INTERVAL语法
    if "INTERVAL '1' MONTH" in oracle_result:
        print("✅ INTERVAL语法正确")
    
    # 检查TRUNC(SYSDATE)的使用
    if "TRUNC(SYSDATE)" in oracle_result:
        print("✅ 正确使用了TRUNC(SYSDATE)替代CURRENT_DATE")
    
    # 生成简化的Oracle SQL用于测试
    print("\n===== 简化的Oracle SQL（用于测试） =====")
    simplified_sql = """
SELECT 
    TRUNC(MONTHS_BETWEEN(
        (TRUNC(SYSDATE) - INTERVAL '1' MONTH), 
        TO_DATE('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
    )) - COUNT(1) AS biz_info
FROM dual
"""
    print(simplified_sql.strip())
    
    print("\n说明：")
    print("1. 使用TRUNC()确保返回整数")
    print("2. MONTHS_BETWEEN()计算月份差")
    print("3. TO_DATE()正确转换日期字面量")
    print("4. INTERVAL语法用于日期运算")
    print("5. 添加FROM dual使其成为完整的可执行SQL")

if __name__ == "__main__":
    test_oracle_syntax()
    test_specific_oracle_case()
