import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Alignment
from copy import copy
from typing import List, Dict

def copy_cell_format(source_cell, target_cell):
    """
    复制单元格格式的辅助函数
    """
    if source_cell.fill:
        target_cell.fill = copy(source_cell.fill)
    if source_cell.font:
        target_cell.font = copy(source_cell.font)
    if source_cell.border:
        target_cell.border = copy(source_cell.border)
    if source_cell.alignment:
        target_cell.alignment = copy(source_cell.alignment)

def get_sheet_data(worksheet, max_col: int) -> List[Dict]:
    """
    预先读取工作表数据到内存
    """
    data = []
    for row in worksheet.iter_rows(min_row=1, max_col=max_col):
        row_data = {
            'values': [cell.value for cell in row],
            'formats': [
                {
                    'fill': copy(cell.fill) if cell.fill else None,
                    'font': copy(cell.font) if cell.font else None,
                    'border': copy(cell.border) if cell.border else None,
                    'alignment': copy(cell.alignment) if cell.alignment else None
                }
                for cell in row
            ]
        }
        data.append(row_data)
    return data

def process_excel_file():
    # 读取Excel文件
    file_path = r'D:\work\demo\福建\添加公共字段.xlsx'
    wb = openpyxl.load_workbook(file_path)

    # 获取工作表
    ws1 = wb['Sheet1']
    ws2 = wb['Sheet9']

    # 创建新的工作表
    new_sheet = wb.create_sheet('ProcessedSheet')

    # 预先读取数据到内存
    max_col = max(ws1.max_column, ws2.max_column)
    sheet1_data = get_sheet_data(ws1, max_col)
    sheet2_first_row = get_sheet_data(ws2, max_col)[0]  # 只需要第一行

    current_row = 1

    # 批量处理数据
    for idx, row_data in enumerate(sheet1_data, 1):
        # 检查H列的值是否为"数据上传日期"
        if row_data['values'][7] == "数据上传日期":  # H列索引为7
            # 插入Sheet2的第一行（带格式）
            for col in range(1, max_col + 1):
                new_cell = new_sheet.cell(row=current_row, column=col)
                col_idx = col - 1

                if col <= 3:
                    # 前三列：使用当前行的值和格式
                    new_cell.value = row_data['values'][col_idx]
                    for format_key, format_value in row_data['formats'][col_idx].items():
                        if format_value:
                            setattr(new_cell, format_key, format_value)
                else:
                    # 其余列：使用Sheet2第一行的值和格式
                    new_cell.value = sheet2_first_row['values'][col_idx]
                    for format_key, format_value in sheet2_first_row['formats'][col_idx].items():
                        if format_value:
                            setattr(new_cell, format_key, format_value)

            current_row += 1

            # 复制当前行（数据上传日期行）
            for col in range(1, max_col + 1):
                new_cell = new_sheet.cell(row=current_row, column=col)
                col_idx = col - 1
                new_cell.value = row_data['values'][col_idx]
                for format_key, format_value in row_data['formats'][col_idx].items():
                    if format_value:
                        setattr(new_cell, format_key, format_value)

            current_row += 1
        else:
            # 直接复制普通行
            for col in range(1, max_col + 1):
                new_cell = new_sheet.cell(row=current_row, column=col)
                col_idx = col - 1
                new_cell.value = row_data['values'][col_idx]
                for format_key, format_value in row_data['formats'][col_idx].items():
                    if format_value:
                        setattr(new_cell, format_key, format_value)

            current_row += 1

    try:
        # 保存修改后的文件
        output_path = r'D:\work\demo\福建\processed_file_with_inserts.xlsx'
        wb.save(output_path)
        print(f"数据处理完成，结果保存在 '{output_path}'")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise
    finally:
        wb.close()

if __name__ == "__main__":
    process_excel_file()
