import pandas as pd
import os
import datetime
import re

def get_oracle_type(data_type: str, length: str) -> str:
    """
    将Excel中的数据类型转换为Oracle数据类型

    Args:
        data_type: Excel中的数据类型
        length: 长度字段的值

    Returns:
        str: Oracle数据类型
    """
    data_type = str(data_type).lower().strip()
    length = str(length).strip()

    # 处理空值
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR2(255)'

    # 处理整数类型
    if '整数' in data_type:
        return 'NUMBER(16)'  # Oracle使用NUMBER类型表示整数

    # 处理数值类型（小数）
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'NUMBER({total_len},{decimal_len})'
                return f'NUMBER({length})'  # 如果没有指定小数位，使用整数精度
            except ValueError:
                return 'NUMBER(10,2)'  # 如果无法解析长度，使用默认值
        return 'NUMBER(10,2)'  # 如果没有指定长度，使用默认值

    # 处理字符串类型
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                # 当字段长度超过4000时，自动转换为CLOB类型
                if length_val > 4000:
                    return 'CLOB'
                return f'VARCHAR2({length})'
            except ValueError:
                return 'VARCHAR2(255)'
        return 'VARCHAR2(255)'

    # 处理日期时间类型
    if '日期' in data_type:
        # Oracle统一使用DATE类型，它包含日期和时间
        return 'DATE'

    # 处理时间类型（不含日期的情况）
    if '时间' in data_type:
        return 'DATE'  # Oracle中使用DATE存储时间

    # 默认返回VARCHAR2(255)
    return 'VARCHAR2(255)'

def natural_sort_key(s):
    # 将字符串中的数字转换为整数，以实现自然排序
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split('([0-9]+)', str(s))]

def generate_oracle_sql():
    try:
        # 读取Excel文件
        df = pd.read_excel('D:\work\demo\福建\新增表字段模板.xlsx', sheet_name=0)
        base_path = r'D:\work\demo\福建'
        current_date = datetime.datetime.now().strftime("%Y%m%d")
        output_path = os.path.join(base_path+'\建表sql', f"oracle更新{current_date}.sql")

        # 按文件名称分组，并按照自然排序排序
        grouped_df = df.groupby('文件名称')
        sorted_groups = sorted(grouped_df, key=lambda x: natural_sort_key(x[0]))

        # 打开输出文件
        with open(output_path, 'w', encoding='utf-8') as f:
            # 遍历每个分组
            for file_name, group in sorted_groups:
                # 写入文件名称作为注释
                f.write(f"-- {file_name}\n")

                # 遍历该分组中的每一行数据
                for _, row in group.iterrows():
                    # 获取字段注释（如果说明为空，则只使用数据项）
                    comment = row['数据项']
                    if pd.notna(row['说明']):
                        comment = f"{row['数据项']} {row['说明']}"

                    # 使用get_oracle_type转换数据类型
                    oracle_type = get_oracle_type(row['数据类型'], row['长度'])

                    # 生成SQL语句
                    sql = f"ALTER TABLE {row['表名']} ADD {row['字段名']} {oracle_type}"

                    # 添加字段注释
                    sql += f";\nCOMMENT ON COLUMN {row['表名']}.{row['字段名']} IS '{comment}';\n\n"

                    # 写入SQL文件
                    f.write(sql)

                # 在不同文件之间添加分隔线
                # f.write("\n" + "="*50 + "\n\n")

        print("SQL文件生成成功！")

    except Exception as e:
        print(f"发生错误：{str(e)}")

if __name__ == "__main__":
    generate_oracle_sql()
