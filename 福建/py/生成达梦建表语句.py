import pandas as pd
import os
import datetime

def get_dm_type(data_type: str, length: str) -> str:
    """
    将Excel中的数据类型转换为达梦数据库(DM8)数据类型

    Args:
        data_type: Excel中的数据类型
        length: 长度字段的值

    Returns:
        str: 达梦数据库数据类型
    """
    data_type = str(data_type).lower().strip()
    length = str(length).strip()

    # 处理空值
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR2(255)'

    # 处理整数类型
    if '整数' in data_type:
        return 'INTEGER'

    # 处理数值类型（小数）
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'NUMBER({total_len},{decimal_len})'
                return f'NUMBER({length})'  # 如果没有指定小数位，默认为整数
            except ValueError:
                return 'NUMBER(10,2)'  # 如果无法解析长度，使用默认值
        return 'NUMBER(10,2)'  # 如果没有指定长度，使用默认值

    # 处理字符串类型
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                # 当字段长度超过2000时，自动转换为CLOB类型
                if length_val > 2000:
                    return 'CLOB'
                return f'VARCHAR2({length})'
            except ValueError:
                return 'VARCHAR2(255)'
        return 'VARCHAR2(255)'

    # 处理日期时间类型
    if '日期' in data_type:
        # 纯日期类型使用DATE
        if '时间' not in data_type:
            return 'DATE'
        # 带时间的使用TIMESTAMP
        return 'TIMESTAMP'

    # 处理时间类型（不含日期的情况）
    if '时间' in data_type:
        return 'TIMESTAMP'

    # 默认返回VARCHAR2(255)
    return 'VARCHAR2(255)'

def generate_create_table_sql(df: pd.DataFrame, table_counter: int) -> tuple[str, str]:
    """
    根据DataFrame生成达梦数据库建表SQL语句

    Args:
        df: 包含表结构的DataFrame
        table_counter: 表序号，用于生成唯一的约束名和索引名

    Returns:
        tuple: (建表SQL语句, 表名)
    """
    # 获取表名，并转换为小写
    table_name = df.iloc[0]['表名'].strip().lower()
    table_comment = df.iloc[0]['表中文名'].strip()

    # SQL语句头部 - 达梦使用双引号引用标识符
    sql = f'CREATE TABLE "{table_name}" (\n'

    # 记录主键字段
    primary_keys = []
    # 记录需要创建唯一索引的字段
    unique_index_fields = []

    # 添加字段定义
    for _, row in df.iterrows():
        field_name = row['字段名'].strip().lower()  # 统一使用小写字段名
        data_type = get_dm_type(row['数据类型'], row['长度'])
        is_primary = str(row['是否主键']).strip().upper()

        # 组合数据项和说明作为注释
        data_item = str(row.get('数据项', '')).strip()
        description = str(row['说明']).strip()
        comment_parts = []
        if data_item and data_item != 'nan':
            comment_parts.append(data_item)
        if description and description != 'nan':
            comment_parts.append(description)
        comment = ' '.join(comment_parts)

        nullable = '不能为空' in str(row['填报要求'])

        # 构建字段定义 - 使用双引号
        sql += f'    "{field_name}" {data_type}'

        # 添加是否可为空
        if nullable:
            sql += " NOT NULL"

        sql += ",\n"

        # 记录主键
        if is_primary == '是' or is_primary == 'Y' or is_primary == '1':
            primary_keys.append(field_name)

        # 检查说明中是否包含"唯一索引"
        if '唯一索引' in str(row['说明']):
            unique_index_fields.append(field_name)

    # 添加主键定义 - 使用表序号和表名前三个字符确保唯一性
    if primary_keys:
        # 使用PK_序号_表名前三个字符的格式，确保名称唯一性
        table_prefix = table_name[:3] if len(table_name) >= 3 else table_name
        quoted_keys = [f'"{pk}"' for pk in primary_keys]
        sql += f'    CONSTRAINT "PK_{table_counter}_{table_prefix}" PRIMARY KEY ({", ".join(quoted_keys)})'
        if unique_index_fields:
            sql += ",\n"
        else:
            sql += "\n"

    # 添加唯一索引定义 - 使用表序号和表名前三个字符确保唯一性
    if unique_index_fields:
        table_prefix = table_name[:3] if len(table_name) >= 3 else table_name
        quoted_fields = [f'"{field}"' for field in unique_index_fields]
        sql += f'    CONSTRAINT "IDX_U_{table_counter}_{table_prefix}" UNIQUE ({", ".join(quoted_fields)})\n'
    else:
        # 如果没有主键也没有唯一索引，删除最后一个逗号和换行符
        if not primary_keys:
            sql = sql.rstrip(",\n") + "\n"

    # 添加表结束符 - 达梦数据库不需要指定ENGINE和CHARSET
    sql += ");\n\n"
    
    # 为表和字段添加注释
    if table_comment:
        sql += f'COMMENT ON TABLE "{table_name}" IS \'{table_comment}\';\n\n'
    
    # 为每个字段添加注释
    for _, row in df.iterrows():
        field_name = row['字段名'].strip().lower()  # 统一使用小写字段名
        data_item = str(row.get('数据项', '')).strip()
        description = str(row['说明']).strip()
        comment_parts = []
        if data_item and data_item != 'nan':
            comment_parts.append(data_item)
        if description and description != 'nan':
            comment_parts.append(description)
        comment = ' '.join(comment_parts)
        
        if comment:
            sql += f'COMMENT ON COLUMN "{table_name}"."{field_name}" IS \'{comment}\';\n'
    
    sql += "\n"

    return sql, table_name

def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"dm8_{current_date}.sql")

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序，转换为小写
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            table_name = table_name.strip().lower()  # 统一使用小写表名
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本为达梦DM8建表SQL\n\n')

            # 为每个表添加建表语句
            for idx, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                # 传递表序号以生成唯一的约束名和索引名
                sql, table_name = generate_create_table_sql(group, idx)  # 现在返回的表名已经是小写的

                # 添加表分隔注释
                f.write(f"""-- -------------------------------------------
-- 创建表: {table_name}
-- -------------------------------------------

-- 尝试删除已存在的表
DROP TABLE IF EXISTS "{table_name}";

""")

                # 写入建表语句
                f.write(sql)

                # 添加提交语句
                f.write("COMMIT;\n\n")

        print(f"\n成功生成达梦SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("请注意：所有表名都已统一使用小写，并用双引号引用保持大小写")

    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main() 