2025-07-03 11:42:59,549 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 11:42:59,549 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 11:42:59,549 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 11:42:59,549 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 11:42:59,549 - INFO - 找到 4 个Excel文件
2025-07-03 11:42:59,549 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:42:59,579 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 11:42:59,580 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 11:42:59,699 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,700 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,700 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,700 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,701 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,701 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:42:59,701 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:42:59,701 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,702 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,703 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,704 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,704 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,704 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,704 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,704 - INFO - 已更新 5 个医院，共 185 个单元格
2025-07-03 11:42:59,714 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 11:42:59,714 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 11:42:59,750 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,750 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,750 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,751 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,751 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,751 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:42:59,751 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:42:59,751 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,753 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,753 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,753 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,753 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,753 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,754 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,755 - INFO - 已更新 5 个医院，共 230 个单元格
2025-07-03 11:42:59,764 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 11:42:59,764 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 11:42:59,826 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,826 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:42:59,826 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:42:59,826 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,827 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,827 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,828 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,829 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,829 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,829 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,829 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,830 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,830 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:42:59,830 - INFO - 已更新 5 个医院，共 120 个单元格
2025-07-03 11:42:59,836 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 11:42:59,836 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 11:42:59,870 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,870 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,870 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:42:59,870 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,872 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,873 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,874 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,874 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,874 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,875 - INFO - 已更新 5 个医院，共 200 个单元格
2025-07-03 11:42:59,883 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 11:42:59,883 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 11:42:59,915 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,915 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,916 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,916 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,916 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:42:59,916 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:42:59,916 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,918 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,919 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,920 - INFO - 已更新 5 个医院，共 165 个单元格
2025-07-03 11:42:59,928 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 11:42:59,928 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 11:42:59,983 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:42:59,983 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:42:59,984 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:42:59,984 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:42:59,986 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:42:59,987 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:42:59,988 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:42:59,988 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:42:59,988 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:42:59,989 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:42:59,989 - INFO - 已更新 5 个医院，共 310 个单元格
2025-07-03 11:43:00,001 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 11:43:00,001 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 11:43:00,039 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:00,039 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:00,039 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,040 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:00,040 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,040 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,041 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,042 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:00,043 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:00,043 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,043 - INFO - 已更新 4 个医院，共 64 个单元格
2025-07-03 11:43:00,048 - INFO - 正在处理工作表: 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:43:00,048 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_charge_de.json
2025-07-03 11:43:00,048 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:43:00,091 - INFO - 正在处理工作表: 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:43:00,091 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_transfer_dept_reco.json
2025-07-03 11:43:00,091 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:43:00,130 - INFO - 正在处理工作表: Sheet5
2025-07-03 11:43:00,131 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet5.json
2025-07-03 11:43:00,131 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet5
2025-07-03 11:43:00,181 - INFO - 正在处理工作表: Sheet17
2025-07-03 11:43:00,181 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet17.json
2025-07-03 11:43:00,181 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet17
2025-07-03 11:43:00,355 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:00,355 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:00,382 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 11:43:00,382 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 11:43:00,412 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:00,412 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:00,412 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,412 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,413 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,413 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福州市第四医院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,414 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,415 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,415 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,416 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,416 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:00,416 - INFO - 已更新 4 个医院，共 128 个单元格
2025-07-03 11:43:00,421 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 11:43:00,421 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 11:43:00,453 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:00,453 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:00,453 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:00,454 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,454 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,454 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,455 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:00,455 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,455 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,456 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,457 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,457 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:00,458 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,458 - INFO - 已更新 5 个医院，共 205 个单元格
2025-07-03 11:43:00,467 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 11:43:00,467 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 11:43:00,529 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:00,529 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:00,529 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,530 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,530 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,531 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:00,531 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,531 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:00,531 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,532 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,533 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:00,533 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,534 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,534 - INFO - 已更新 5 个医院，共 165 个单元格
2025-07-03 11:43:00,541 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 11:43:00,541 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 11:43:00,572 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,573 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,573 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,574 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,575 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,575 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,575 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,575 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,575 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,576 - INFO - 已更新 5 个医院，共 130 个单元格
2025-07-03 11:43:00,582 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 11:43:00,582 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 11:43:00,613 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,614 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:00,614 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,615 - INFO - 已更新 5 个医院，共 140 个单元格
2025-07-03 11:43:00,622 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 11:43:00,622 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 11:43:00,683 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,684 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,684 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,684 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,684 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,685 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,685 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,685 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,685 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,685 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,686 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,686 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,686 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,686 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,686 - INFO - 已更新 3 个医院，共 90 个单元格
2025-07-03 11:43:00,693 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 11:43:00,693 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 11:43:00,726 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,727 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,727 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,728 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,729 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:00,730 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:00,730 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,730 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,730 - INFO - 已更新 4 个医院，共 188 个单元格
2025-07-03 11:43:00,740 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 11:43:00,741 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 11:43:00,770 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,770 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:00,771 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,772 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,772 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,772 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:00,773 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:00,773 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:00,773 - INFO - 已更新 4 个医院，共 88 个单元格
2025-07-03 11:43:00,779 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 11:43:00,779 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 11:43:00,809 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,810 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,810 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:00,810 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:00,810 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,811 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,811 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,811 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:00,811 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,812 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:00,812 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,812 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,812 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,812 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,812 - INFO - 已更新 4 个医院，共 72 个单元格
2025-07-03 11:43:00,817 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 11:43:00,817 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 11:43:00,898 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:00,898 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:00,898 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,899 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,901 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,901 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,901 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:00,901 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:00,901 - INFO - 已更新 2 个医院，共 56 个单元格
2025-07-03 11:43:01,024 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:01,024 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:01,043 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 11:43:01,044 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 11:43:01,076 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,077 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,079 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,079 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,079 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,079 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,080 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,081 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,081 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,082 - INFO - 已更新 4 个医院，共 380 个单元格
2025-07-03 11:43:01,097 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 11:43:01,097 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 11:43:01,119 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,119 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,120 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,120 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,120 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,120 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,120 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,121 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,122 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,123 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,123 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,123 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,123 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,123 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,123 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:43:01,130 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 11:43:01,130 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 11:43:01,151 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,151 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,151 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,151 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,152 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,152 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,152 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,153 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,154 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,154 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,154 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,154 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,154 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,155 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,155 - INFO - 已更新 5 个医院，共 130 个单元格
2025-07-03 11:43:01,161 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 11:43:01,162 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 11:43:01,207 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,207 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,207 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,207 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,209 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,209 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,209 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,209 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,209 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,210 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,211 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,211 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,211 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,211 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,212 - INFO - 已更新 5 个医院，共 215 个单元格
2025-07-03 11:43:01,277 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:01,277 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:01,302 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 11:43:01,302 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 11:43:01,429 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,429 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,429 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,430 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:01,430 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,430 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,430 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,431 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,431 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,432 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,433 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,433 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,433 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,433 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,433 - INFO - 已更新 5 个医院，共 105 个单元格
2025-07-03 11:43:01,437 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 11:43:01,438 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 11:43:01,477 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,478 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:01,478 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,478 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,478 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,479 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,479 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,480 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,480 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,480 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,480 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,481 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,482 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,482 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,482 - INFO - 已更新 5 个医院，共 185 个单元格
2025-07-03 11:43:01,490 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 11:43:01,490 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 11:43:01,539 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,540 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,540 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,540 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:43:01,540 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,541 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,541 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,542 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,543 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,543 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,543 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,543 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,543 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,544 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:01,544 - INFO - 已更新 5 个医院，共 105 个单元格
2025-07-03 11:43:01,549 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 11:43:01,549 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 11:43:01,582 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,582 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,582 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,582 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:01,582 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,583 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,584 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,585 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,586 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:43:01,586 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,586 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:43:01,592 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 11:43:01,592 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 11:43:01,624 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,625 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,626 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,626 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,626 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,626 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:01,626 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,627 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,628 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,629 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,629 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,629 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,629 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,629 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,629 - INFO - 已更新 5 个医院，共 175 个单元格
2025-07-03 11:43:01,637 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 11:43:01,637 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 11:43:01,668 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,668 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,668 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:01,668 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,670 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,671 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,672 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,672 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,672 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,672 - INFO - 已更新 5 个医院，共 155 个单元格
2025-07-03 11:43:01,679 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 11:43:01,679 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 11:43:01,736 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,736 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,737 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,737 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,737 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,738 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,738 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:01,738 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,740 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,741 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,741 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,741 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,741 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,741 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,741 - INFO - 已更新 5 个医院，共 305 个单元格
2025-07-03 11:43:01,753 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 11:43:01,753 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 11:43:01,785 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:43:01,786 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:43:01,786 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:43:01,786 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:43:01,787 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:43:01,788 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:43:01,789 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:43:01,789 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:43:01,789 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:43:01,790 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:43:01,790 - INFO - 已更新 4 个医院，共 64 个单元格
2025-07-03 11:43:01,892 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:43:01,893 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 11:43:01,893 - INFO - 总耗时: 0:00:02.343590
2025-07-03 11:43:01,893 - INFO - === 处理结束 ===
2025-07-03 11:48:56,179 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 11:48:56,179 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 11:48:56,179 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 11:48:56,179 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 11:48:56,181 - INFO - 找到 4 个Excel文件
2025-07-03 11:48:56,181 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:56,209 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 11:48:56,209 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 11:48:56,315 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,317 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,317 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,317 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,318 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,318 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,318 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:56,318 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,319 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,320 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,321 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,321 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,321 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,321 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,321 - INFO - 已更新 5 个医院，共 190 个单元格
2025-07-03 11:48:56,330 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 11:48:56,330 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 11:48:56,366 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,366 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,366 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,367 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,367 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,367 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,367 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:56,367 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,369 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,370 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,370 - INFO - 已更新 5 个医院，共 235 个单元格
2025-07-03 11:48:56,380 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 11:48:56,380 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 11:48:56,437 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,437 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,437 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:56,439 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,440 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,441 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,442 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,442 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,442 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,442 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:56,442 - INFO - 已更新 5 个医院，共 125 个单元格
2025-07-03 11:48:56,448 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 11:48:56,448 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 11:48:56,485 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,485 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,485 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:56,485 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,487 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,488 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,488 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,488 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,488 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,488 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,489 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,489 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,489 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,490 - INFO - 已更新 5 个医院，共 205 个单元格
2025-07-03 11:48:56,500 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 11:48:56,500 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 11:48:56,534 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,534 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,535 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,535 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,535 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,535 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:56,535 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,536 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,537 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,538 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,538 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,538 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,538 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,538 - INFO - 已更新 5 个医院，共 170 个单元格
2025-07-03 11:48:56,547 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 11:48:56,547 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 11:48:56,604 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,604 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,604 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:56,604 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,606 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,606 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,607 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,608 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,608 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,608 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:56,609 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,609 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,609 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,609 - INFO - 已更新 5 个医院，共 315 个单元格
2025-07-03 11:48:56,622 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 11:48:56,622 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 11:48:56,657 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:56,657 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:56,657 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:56,657 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:56,658 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:56,658 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:56,658 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:56,659 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:56,660 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:56,660 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:56,660 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:56,660 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:56,660 - INFO - 已更新 4 个医院，共 68 个单元格
2025-07-03 11:48:56,666 - INFO - 正在处理工作表: 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:48:56,666 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_charge_de.json
2025-07-03 11:48:56,666 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:48:56,710 - INFO - 正在处理工作表: 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:48:56,710 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_transfer_dept_reco.json
2025-07-03 11:48:56,710 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:48:56,751 - INFO - 正在处理工作表: Sheet5
2025-07-03 11:48:56,751 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet5.json
2025-07-03 11:48:56,751 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet5
2025-07-03 11:48:56,800 - INFO - 正在处理工作表: Sheet17
2025-07-03 11:48:56,800 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet17.json
2025-07-03 11:48:56,800 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet17
2025-07-03 11:48:56,959 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:56,959 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:56,984 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 11:48:56,985 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 11:48:57,016 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,016 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,016 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,016 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,016 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,017 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,017 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 福州市第四医院
2025-07-03 11:48:57,018 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,019 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,019 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,019 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,020 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,020 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,020 - INFO - 已更新 4 个医院，共 132 个单元格
2025-07-03 11:48:57,026 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 11:48:57,026 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 11:48:57,058 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,058 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,058 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,059 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,059 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,059 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,060 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,060 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,061 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,062 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,062 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,062 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,063 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,063 - INFO - 已更新 5 个医院，共 210 个单元格
2025-07-03 11:48:57,071 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 11:48:57,071 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 11:48:57,126 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,127 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,127 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,127 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,127 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,128 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,129 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,130 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,130 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,130 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,131 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,131 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,131 - INFO - 已更新 5 个医院，共 170 个单元格
2025-07-03 11:48:57,138 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 11:48:57,138 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 11:48:57,170 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,170 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,170 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,171 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,171 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,171 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,172 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,173 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,173 - INFO - 已更新 5 个医院，共 135 个单元格
2025-07-03 11:48:57,180 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 11:48:57,180 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 11:48:57,212 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,212 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,212 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,214 - INFO - 已更新 5 个医院，共 145 个单元格
2025-07-03 11:48:57,220 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 11:48:57,220 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 11:48:57,276 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,276 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,277 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,278 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,278 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,278 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,278 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,279 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,279 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,279 - INFO - 已更新 3 个医院，共 93 个单元格
2025-07-03 11:48:57,285 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 11:48:57,285 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 11:48:57,319 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,320 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,320 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,321 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,322 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,323 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,323 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,323 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,323 - INFO - 已更新 4 个医院，共 192 个单元格
2025-07-03 11:48:57,333 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 11:48:57,333 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 11:48:57,364 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,364 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,365 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,365 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,365 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,365 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,365 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,366 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,366 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,366 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,367 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,367 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,367 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,367 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,367 - INFO - 已更新 4 个医院，共 92 个单元格
2025-07-03 11:48:57,373 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 11:48:57,373 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 11:48:57,404 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,404 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,404 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,405 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,405 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,405 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,406 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,407 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,407 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,407 - INFO - 已更新 4 个医院，共 76 个单元格
2025-07-03 11:48:57,412 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 11:48:57,412 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 11:48:57,494 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,494 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,494 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,495 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,496 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,496 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,496 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,497 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,497 - INFO - 已更新 2 个医院，共 58 个单元格
2025-07-03 11:48:57,636 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:57,636 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:57,653 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\病案管理分册-宁德-数据质量清单模版V1.xlsx 时出错: [Errno 13] Permission denied: 'D:\\work\\demo\\福建\\质控结果\\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx'
2025-07-03 11:48:57,653 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:57,679 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 11:48:57,679 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 11:48:57,808 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:57,809 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,809 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,809 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,810 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,810 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:57,810 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:57,810 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,811 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,811 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,812 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,813 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,813 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,813 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:48:57,817 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 11:48:57,817 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 11:48:57,881 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,881 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,881 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:57,882 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,882 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:57,882 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:57,882 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,883 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,883 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,883 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,884 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,885 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,885 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,885 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,885 - INFO - 已更新 5 个医院，共 190 个单元格
2025-07-03 11:48:57,894 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 11:48:57,894 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 11:48:57,925 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,925 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,926 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:57,926 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:48:57,926 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,927 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,927 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,927 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,927 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,927 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,928 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,929 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,929 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:57,929 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,929 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:48:57,935 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 11:48:57,935 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 11:48:57,967 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:57,968 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:57,968 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:57,968 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:57,968 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:57,969 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:57,969 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:57,969 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:57,969 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:57,970 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:57,971 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:57,971 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:57,971 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:48:57,972 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:57,972 - INFO - 已更新 5 个医院，共 115 个单元格
2025-07-03 11:48:57,978 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 11:48:57,978 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 11:48:58,011 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:58,012 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:58,012 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:58,012 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:58,012 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:58,013 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:58,013 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:58,014 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:58,014 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:58,014 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:58,014 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:58,015 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:58,016 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:58,016 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:58,016 - INFO - 已更新 5 个医院，共 180 个单元格
2025-07-03 11:48:58,025 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 11:48:58,025 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 11:48:58,074 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:58,075 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:58,075 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:58,075 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:58,076 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:58,076 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:58,076 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:58,076 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:58,077 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:58,078 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:58,078 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:58,078 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:58,078 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:58,079 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:58,079 - INFO - 已更新 5 个医院，共 160 个单元格
2025-07-03 11:48:58,086 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 11:48:58,086 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 11:48:58,121 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:58,122 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:58,122 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:58,123 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:58,123 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:58,123 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:58,123 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:58,124 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:58,125 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:58,125 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:58,126 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:58,127 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:58,127 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:58,127 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:58,127 - INFO - 已更新 5 个医院，共 310 个单元格
2025-07-03 11:48:58,139 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 11:48:58,139 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 11:48:58,170 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:48:58,170 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:48:58,170 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:48:58,171 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:48:58,171 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:48:58,171 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:48:58,172 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:48:58,173 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:48:58,173 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:48:58,173 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:48:58,173 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:48:58,173 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:48:58,174 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:48:58,174 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:48:58,174 - INFO - 已更新 4 个医院，共 68 个单元格
2025-07-03 11:48:58,274 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:48:58,274 - INFO - 处理完成，成功处理 3/4 个文件
2025-07-03 11:48:58,274 - INFO - 总耗时: 0:00:02.094908
2025-07-03 11:48:58,274 - INFO - === 处理结束 ===
2025-07-03 11:49:06,776 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 11:49:06,893 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 11:49:06,893 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 11:49:06,894 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 11:49:06,894 - INFO - 找到 4 个Excel文件
2025-07-03 11:49:06,894 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:06,923 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 11:49:06,923 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 11:49:07,037 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,038 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:07,039 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,041 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,043 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,043 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,043 - INFO - 已更新 5 个医院，共 190 个单元格
2025-07-03 11:49:07,052 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 11:49:07,052 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 11:49:07,086 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,086 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,086 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,087 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,087 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,087 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,087 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:07,087 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,090 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,091 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,091 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,091 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,091 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,091 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,091 - INFO - 已更新 5 个医院，共 235 个单元格
2025-07-03 11:49:07,100 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 11:49:07,100 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 11:49:07,158 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,158 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,158 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:07,158 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,159 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,159 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,159 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,160 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,161 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,161 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,161 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,161 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,162 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,162 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:07,162 - INFO - 已更新 5 个医院，共 125 个单元格
2025-07-03 11:49:07,168 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 11:49:07,168 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 11:49:07,202 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,202 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,202 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,202 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,203 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,204 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,205 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,205 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,205 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,205 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,205 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,206 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,206 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,206 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,206 - INFO - 已更新 5 个医院，共 205 个单元格
2025-07-03 11:49:07,215 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 11:49:07,215 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 11:49:07,247 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,248 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,248 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,248 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,249 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,249 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,249 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,250 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,250 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,250 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,250 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,251 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,252 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,252 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,252 - INFO - 已更新 5 个医院，共 170 个单元格
2025-07-03 11:49:07,260 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 11:49:07,260 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 11:49:07,314 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,314 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,315 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,315 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,317 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,318 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,318 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,318 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,318 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,318 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,319 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:07,319 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,319 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,320 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,320 - INFO - 已更新 5 个医院，共 315 个单元格
2025-07-03 11:49:07,332 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 11:49:07,332 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,367 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,368 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,368 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,368 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,369 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,370 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,370 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,370 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,370 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,371 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,371 - INFO - 已更新 4 个医院，共 68 个单元格
2025-07-03 11:49:07,375 - INFO - 正在处理工作表: 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:49:07,375 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_charge_de.json
2025-07-03 11:49:07,375 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:49:07,419 - INFO - 正在处理工作表: 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:49:07,419 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_transfer_dept_reco.json
2025-07-03 11:49:07,420 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:49:07,459 - INFO - 正在处理工作表: Sheet5
2025-07-03 11:49:07,460 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet5.json
2025-07-03 11:49:07,460 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet5
2025-07-03 11:49:07,509 - INFO - 正在处理工作表: Sheet17
2025-07-03 11:49:07,509 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet17.json
2025-07-03 11:49:07,509 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet17
2025-07-03 11:49:07,666 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:07,666 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:07,692 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 11:49:07,692 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 11:49:07,723 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,724 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:07,724 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,724 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,724 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,726 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,726 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,726 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,726 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,726 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 福州市第四医院
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,727 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,728 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,728 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,729 - INFO - 已更新 4 个医院，共 132 个单元格
2025-07-03 11:49:07,734 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 11:49:07,734 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 11:49:07,767 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:07,767 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:07,768 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,768 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,768 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,768 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,770 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,771 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,771 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,771 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,771 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,771 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,772 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,772 - INFO - 已更新 5 个医院，共 210 个单元格
2025-07-03 11:49:07,781 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 11:49:07,781 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 11:49:07,837 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:07,837 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:07,838 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,838 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,838 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,839 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:07,839 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,839 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:07,840 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:07,841 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,841 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:07,842 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,842 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,842 - INFO - 已更新 5 个医院，共 170 个单元格
2025-07-03 11:49:07,849 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 11:49:07,849 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 11:49:07,881 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,881 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,881 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,882 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,883 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,884 - INFO - 已更新 5 个医院，共 135 个单元格
2025-07-03 11:49:07,891 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 11:49:07,891 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 11:49:07,923 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,923 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:07,923 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,924 - INFO - 已更新 5 个医院，共 145 个单元格
2025-07-03 11:49:07,932 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 11:49:07,932 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 11:49:07,991 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:07,991 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:07,991 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,992 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,993 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:07,993 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,993 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,994 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:07,994 - INFO - 已更新 3 个医院，共 93 个单元格
2025-07-03 11:49:08,001 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 11:49:08,001 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 11:49:08,035 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,035 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,036 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,036 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,037 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,038 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,039 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,039 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,039 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,039 - INFO - 已更新 4 个医院，共 192 个单元格
2025-07-03 11:49:08,048 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 11:49:08,048 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 11:49:08,079 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,079 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,080 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,080 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,080 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,080 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,080 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,081 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,081 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,081 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,082 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,082 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,082 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,082 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,082 - INFO - 已更新 4 个医院，共 92 个单元格
2025-07-03 11:49:08,088 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 11:49:08,088 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 11:49:08,120 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,121 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,121 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,121 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,121 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,121 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,122 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,124 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,124 - INFO - 已更新 4 个医院，共 76 个单元格
2025-07-03 11:49:08,129 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 11:49:08,129 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 11:49:08,213 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,213 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,213 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,214 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,215 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,215 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,215 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,216 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,216 - INFO - 已更新 2 个医院，共 58 个单元格
2025-07-03 11:49:08,340 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:08,340 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:08,358 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 11:49:08,358 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,390 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,392 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,392 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,392 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,392 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,393 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,395 - INFO - 已更新 4 个医院，共 384 个单元格
2025-07-03 11:49:08,411 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 11:49:08,411 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 11:49:08,432 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,433 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,433 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:08,434 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,434 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,434 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,434 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,434 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,435 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,435 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,436 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,437 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,437 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,437 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,437 - INFO - 已更新 5 个医院，共 115 个单元格
2025-07-03 11:49:08,443 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 11:49:08,443 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 11:49:08,466 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,466 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,467 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,467 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,468 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,468 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,468 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,468 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,469 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,470 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,470 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,470 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,470 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,470 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,471 - INFO - 已更新 5 个医院，共 135 个单元格
2025-07-03 11:49:08,478 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 11:49:08,478 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 11:49:08,534 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,534 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,534 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,535 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,536 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,536 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,536 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,536 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,537 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,538 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,538 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:08,538 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,538 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:08,539 - INFO - 已更新 5 个医院，共 220 个单元格
2025-07-03 11:49:08,740 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:08,740 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:08,765 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 11:49:08,765 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 11:49:08,889 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:08,890 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,890 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,890 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:08,891 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,891 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,891 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,891 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,892 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,892 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,892 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,893 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,894 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,894 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,894 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:49:08,898 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 11:49:08,898 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 11:49:08,938 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:08,938 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:08,939 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:08,939 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:08,939 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:08,939 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:08,939 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:08,941 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:08,942 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:08,942 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:08,942 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:08,942 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:08,942 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:08,943 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:08,943 - INFO - 已更新 5 个医院，共 190 个单元格
2025-07-03 11:49:08,950 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 11:49:08,951 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 11:49:09,002 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,003 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,003 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,003 - WARNING - 在Excel表格中未找到医院: 漳平市中医院
2025-07-03 11:49:09,003 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,004 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,004 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,004 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,005 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,006 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,006 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,006 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,006 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:09,006 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:09,006 - INFO - 已更新 5 个医院，共 110 个单元格
2025-07-03 11:49:09,013 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 11:49:09,013 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 11:49:09,047 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,047 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,047 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,047 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:09,047 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,048 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,049 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,050 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,050 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,050 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,050 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,050 - WARNING - 在Excel表格中未找到医院: 厦门医学院附属口腔医院
2025-07-03 11:49:09,051 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,051 - INFO - 已更新 5 个医院，共 115 个单元格
2025-07-03 11:49:09,057 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 11:49:09,057 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 11:49:09,091 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,091 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,092 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,092 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,092 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,092 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:09,092 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,093 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,093 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,094 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,095 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,095 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,095 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,095 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,096 - INFO - 已更新 5 个医院，共 180 个单元格
2025-07-03 11:49:09,103 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 11:49:09,103 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 11:49:09,134 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,134 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,136 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:09,136 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,137 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,137 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,137 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,138 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,139 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,139 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,139 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,139 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,140 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,140 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:09,140 - INFO - 已更新 5 个医院，共 160 个单元格
2025-07-03 11:49:09,146 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 11:49:09,146 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 11:49:09,205 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,205 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:09,206 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,206 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,206 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,207 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,207 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:09,207 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,208 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,208 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,208 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,209 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,210 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,210 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,210 - INFO - 已更新 5 个医院，共 310 个单元格
2025-07-03 11:49:09,221 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 11:49:09,222 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 11:49:09,253 - WARNING - 在Excel表格中未找到医院: 龙岩市第二医院
2025-07-03 11:49:09,253 - WARNING - 在Excel表格中未找到医院: 龙岩市中医院
2025-07-03 11:49:09,253 - WARNING - 在Excel表格中未找到医院: 漳平市医院
2025-07-03 11:49:09,253 - WARNING - 在Excel表格中未找到医院: 龙岩人民医院
2025-07-03 11:49:09,254 - WARNING - 在Excel表格中未找到医院: 福州市皮肤病防治院
2025-07-03 11:49:09,254 - WARNING - 在Excel表格中未找到医院: 福州市中医院
2025-07-03 11:49:09,254 - WARNING - 在Excel表格中未找到医院: 福建省福州儿童医院
2025-07-03 11:49:09,254 - WARNING - 在Excel表格中未找到医院: 福州市第一医院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福建医科大学孟超肝胆医院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福州市第二医院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福州结核病防治院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福州市神经精神病防治院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福州市妇幼保健院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 长乐区人民医院
2025-07-03 11:49:09,255 - WARNING - 在Excel表格中未找到医院: 福建省福清市医院
2025-07-03 11:49:09,256 - WARNING - 在Excel表格中未找到医院: 福清市妇幼保健院
2025-07-03 11:49:09,256 - WARNING - 在Excel表格中未找到医院: 厦门大学附属翔安医院
2025-07-03 11:49:09,256 - WARNING - 在Excel表格中未找到医院: 
2025-07-03 11:49:09,256 - WARNING - 在Excel表格中未找到医院: 厦门市仙岳医院
2025-07-03 11:49:09,256 - WARNING - 在Excel表格中未找到医院: 厦门市儿童医院（复旦大学附属儿科医院厦门医院）
2025-07-03 11:49:09,256 - INFO - 已更新 4 个医院，共 68 个单元格
2025-07-03 11:49:09,350 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:49:09,350 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 11:49:09,350 - INFO - 总耗时: 0:00:02.574022
2025-07-03 11:49:09,350 - INFO - === 处理结束 ===
2025-07-03 11:58:24,798 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 11:58:24,798 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 11:58:24,798 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 11:58:24,798 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 11:58:24,799 - INFO - 找到 4 个Excel文件
2025-07-03 11:58:24,799 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:24,827 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 11:58:24,827 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 11:58:24,827 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:24,931 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:24,931 - INFO - 删除后行数: 2
2025-07-03 11:58:24,932 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:24,961 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 11:58:24,970 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 25
2025-07-03 11:58:24,970 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 11:58:24,970 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 11:58:24,970 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,002 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,002 - INFO - 删除后行数: 2
2025-07-03 11:58:25,003 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:25,034 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-03 11:58:25,042 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 25
2025-07-03 11:58:25,042 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 11:58:25,042 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 11:58:25,042 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,094 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,095 - INFO - 删除后行数: 2
2025-07-03 11:58:25,095 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:25,118 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 11:58:25,124 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 26
2025-07-03 11:58:25,124 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 11:58:25,124 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 11:58:25,124 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,158 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,158 - INFO - 删除后行数: 2
2025-07-03 11:58:25,159 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 11:58:25,185 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-03 11:58:25,193 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 24
2025-07-03 11:58:25,193 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 11:58:25,193 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 11:58:25,193 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,224 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 33
2025-07-03 11:58:25,225 - INFO - 删除后行数: 7
2025-07-03 11:58:25,226 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:25,252 - INFO - 已添加 23 条医院数据，共更新 782 个单元格
2025-07-03 11:58:25,259 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 30
2025-07-03 11:58:25,259 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 11:58:25,260 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 11:58:25,260 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,315 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,315 - INFO - 删除后行数: 2
2025-07-03 11:58:25,316 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:25,354 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-03 11:58:25,365 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 26
2025-07-03 11:58:25,365 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 11:58:25,365 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 11:58:25,365 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,398 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,398 - INFO - 删除后行数: 2
2025-07-03 11:58:25,398 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:25,418 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 11:58:25,422 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 25
2025-07-03 11:58:25,423 - INFO - 正在处理工作表: 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:58:25,423 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_charge_de.json
2025-07-03 11:58:25,423 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算费用明细表】inp_settle_charge_de
2025-07-03 11:58:25,466 - INFO - 正在处理工作表: 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:58:25,466 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_transfer_dept_reco.json
2025-07-03 11:58:25,466 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院转科记录表】inp_transfer_dept_reco
2025-07-03 11:58:25,505 - INFO - 正在处理工作表: Sheet5
2025-07-03 11:58:25,506 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet5.json
2025-07-03 11:58:25,506 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet5
2025-07-03 11:58:25,555 - INFO - 正在处理工作表: Sheet17
2025-07-03 11:58:25,555 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet17.json
2025-07-03 11:58:25,556 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet17
2025-07-03 11:58:25,710 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:25,710 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:25,738 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 11:58:25,738 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 11:58:25,738 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,767 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,767 - INFO - 删除后行数: 2
2025-07-03 11:58:25,767 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 11:58:25,787 - INFO - 已添加 17 条医院数据，共更新 561 个单元格
2025-07-03 11:58:25,792 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 19
2025-07-03 11:58:25,793 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 11:58:25,793 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 11:58:25,793 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,824 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,824 - INFO - 删除后行数: 2
2025-07-03 11:58:25,824 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:25,854 - INFO - 已添加 23 条医院数据，共更新 966 个单元格
2025-07-03 11:58:25,862 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 25
2025-07-03 11:58:25,862 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 11:58:25,862 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 11:58:25,862 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,917 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,917 - INFO - 删除后行数: 2
2025-07-03 11:58:25,918 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 11:58:25,942 - INFO - 已添加 22 条医院数据，共更新 748 个单元格
2025-07-03 11:58:25,950 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 24
2025-07-03 11:58:25,950 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 11:58:25,950 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 11:58:25,950 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:25,981 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:25,981 - INFO - 删除后行数: 2
2025-07-03 11:58:25,982 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 11:58:25,994 - INFO - 已添加 12 条医院数据，共更新 324 个单元格
2025-07-03 11:58:25,999 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 14
2025-07-03 11:58:25,999 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 11:58:25,999 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 11:58:25,999 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,030 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,031 - INFO - 删除后行数: 2
2025-07-03 11:58:26,031 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 11:58:26,040 - INFO - 已添加 8 条医院数据，共更新 232 个单元格
2025-07-03 11:58:26,044 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 10
2025-07-03 11:58:26,044 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 11:58:26,044 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 11:58:26,044 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,074 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,074 - INFO - 删除后行数: 2
2025-07-03 11:58:26,075 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 11:58:26,082 - INFO - 已添加 7 条医院数据，共更新 217 个单元格
2025-07-03 11:58:26,087 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 9
2025-07-03 11:58:26,087 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 11:58:26,087 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 11:58:26,087 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,138 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,138 - INFO - 删除后行数: 2
2025-07-03 11:58:26,139 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 11:58:26,164 - INFO - 已添加 18 条医院数据，共更新 864 个单元格
2025-07-03 11:58:26,172 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 20
2025-07-03 11:58:26,172 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 11:58:26,172 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 11:58:26,172 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,204 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,204 - INFO - 删除后行数: 2
2025-07-03 11:58:26,205 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 11:58:26,222 - INFO - 已添加 18 条医院数据，共更新 414 个单元格
2025-07-03 11:58:26,227 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 20
2025-07-03 11:58:26,227 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 11:58:26,227 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 11:58:26,227 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,257 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,258 - INFO - 删除后行数: 2
2025-07-03 11:58:26,258 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 11:58:26,269 - INFO - 已添加 12 条医院数据，共更新 228 个单元格
2025-07-03 11:58:26,273 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 14
2025-07-03 11:58:26,273 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 11:58:26,273 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 11:58:26,273 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,352 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,353 - INFO - 删除后行数: 2
2025-07-03 11:58:26,353 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 11:58:26,359 - INFO - 已添加 6 条医院数据，共更新 174 个单元格
2025-07-03 11:58:26,364 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 8
2025-07-03 11:58:26,484 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:26,484 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:26,501 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 11:58:26,501 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 11:58:26,501 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,532 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,532 - INFO - 删除后行数: 2
2025-07-03 11:58:26,533 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 11:58:26,581 - INFO - 已添加 21 条医院数据，共更新 2016 个单元格
2025-07-03 11:58:26,595 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 23
2025-07-03 11:58:26,595 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 11:58:26,595 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 11:58:26,595 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,617 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,617 - INFO - 删除后行数: 2
2025-07-03 11:58:26,618 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:26,641 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 11:58:26,646 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 26
2025-07-03 11:58:26,646 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 11:58:26,646 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 11:58:26,646 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,669 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,669 - INFO - 删除后行数: 2
2025-07-03 11:58:26,670 - INFO - 从JSON中提取了 16 条有效医院数据
2025-07-03 11:58:26,687 - INFO - 已添加 16 条医院数据，共更新 432 个单元格
2025-07-03 11:58:26,692 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 18
2025-07-03 11:58:26,692 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 11:58:26,692 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 11:58:26,692 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,715 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,716 - INFO - 删除后行数: 2
2025-07-03 11:58:26,716 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:26,747 - INFO - 已添加 24 条医院数据，共更新 1056 个单元格
2025-07-03 11:58:26,755 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 26
2025-07-03 11:58:26,818 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:26,818 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:26,867 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 11:58:26,867 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 11:58:26,867 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:26,985 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:26,986 - INFO - 删除后行数: 2
2025-07-03 11:58:26,986 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 11:58:27,010 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 11:58:27,015 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 27
2025-07-03 11:58:27,015 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 11:58:27,015 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 11:58:27,015 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,054 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,054 - INFO - 删除后行数: 2
2025-07-03 11:58:27,054 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 11:58:27,084 - INFO - 已添加 25 条医院数据，共更新 950 个单元格
2025-07-03 11:58:27,091 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 27
2025-07-03 11:58:27,092 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 11:58:27,092 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 11:58:27,092 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,119 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,119 - INFO - 删除后行数: 2
2025-07-03 11:58:27,120 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 11:58:27,144 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 11:58:27,149 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 27
2025-07-03 11:58:27,149 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 11:58:27,149 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 11:58:27,149 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,195 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,195 - INFO - 删除后行数: 2
2025-07-03 11:58:27,196 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:27,219 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 11:58:27,225 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 26
2025-07-03 11:58:27,225 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 11:58:27,225 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 11:58:27,225 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,256 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,256 - INFO - 删除后行数: 2
2025-07-03 11:58:27,257 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:27,283 - INFO - 已添加 23 条医院数据，共更新 828 个单元格
2025-07-03 11:58:27,291 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 25
2025-07-03 11:58:27,291 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 11:58:27,291 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 11:58:27,291 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,324 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,324 - INFO - 删除后行数: 2
2025-07-03 11:58:27,324 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:27,351 - INFO - 已添加 24 条医院数据，共更新 768 个单元格
2025-07-03 11:58:27,358 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 26
2025-07-03 11:58:27,358 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 11:58:27,358 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 11:58:27,358 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,391 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,392 - INFO - 删除后行数: 2
2025-07-03 11:58:27,392 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 11:58:27,430 - INFO - 已添加 24 条医院数据，共更新 1488 个单元格
2025-07-03 11:58:27,441 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 26
2025-07-03 11:58:27,441 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 11:58:27,441 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 11:58:27,441 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 11:58:27,492 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 11:58:27,492 - INFO - 删除后行数: 2
2025-07-03 11:58:27,493 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 11:58:27,513 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 11:58:27,517 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 25
2025-07-03 11:58:27,645 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 11:58:27,645 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 11:58:27,646 - INFO - 总耗时: 0:00:02.846945
2025-07-03 11:58:27,646 - INFO - === 处理结束 ===
2025-07-03 12:09:59,496 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 12:09:59,497 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 12:09:59,497 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 12:09:59,497 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 12:09:59,497 - INFO - 找到 4 个Excel文件
2025-07-03 12:09:59,497 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:09:59,518 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 12:09:59,519 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 12:09:59,519 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,615 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:09:59,616 - INFO - 删除后行数: 2
2025-07-03 12:09:59,616 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:09:59,644 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 12:09:59,654 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 25
2025-07-03 12:09:59,654 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 12:09:59,654 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 12:09:59,654 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,681 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:09:59,681 - INFO - 删除后行数: 2
2025-07-03 12:09:59,682 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:09:59,712 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-03 12:09:59,720 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 25
2025-07-03 12:09:59,720 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 12:09:59,720 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 12:09:59,720 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,765 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:09:59,765 - INFO - 删除后行数: 2
2025-07-03 12:09:59,766 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:09:59,790 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 12:09:59,795 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 26
2025-07-03 12:09:59,795 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 12:09:59,796 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 12:09:59,796 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,822 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:09:59,822 - INFO - 删除后行数: 2
2025-07-03 12:09:59,822 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 12:09:59,849 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-03 12:09:59,857 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 24
2025-07-03 12:09:59,857 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 12:09:59,857 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 12:09:59,857 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,882 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 33
2025-07-03 12:09:59,882 - INFO - 删除后行数: 7
2025-07-03 12:09:59,883 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:09:59,909 - INFO - 已添加 23 条医院数据，共更新 782 个单元格
2025-07-03 12:09:59,916 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 30
2025-07-03 12:09:59,916 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 12:09:59,916 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 12:09:59,916 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:09:59,944 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:09:59,944 - INFO - 删除后行数: 2
2025-07-03 12:09:59,946 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:09:59,983 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-03 12:09:59,994 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 26
2025-07-03 12:09:59,994 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 12:09:59,994 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 12:09:59,995 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,044 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,044 - INFO - 删除后行数: 2
2025-07-03 12:10:00,045 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:10:00,064 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 12:10:00,068 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 25
2025-07-03 12:10:00,069 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-03 12:10:00,069 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_charge_detail.json
2025-07-03 12:10:00,069 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,095 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,095 - INFO - 删除后行数: 2
2025-07-03 12:10:00,096 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:10:00,126 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-03 12:10:00,135 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 25
2025-07-03 12:10:00,135 - INFO - 正在处理工作表: 【住院转科记录inp_transfer_dept_record
2025-07-03 12:10:00,135 - INFO - 找到匹配的JSON文件: coreflddq_inp_transfer_dept_record.json
2025-07-03 12:10:00,136 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,163 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,163 - INFO - 删除后行数: 2
2025-07-03 12:10:00,163 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-03 12:10:00,174 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-03 12:10:00,178 - INFO - 工作表 【住院转科记录inp_transfer_dept_record 处理成功，总行数: 13
2025-07-03 12:10:00,295 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:00,295 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:00,320 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 12:10:00,320 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 12:10:00,320 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,373 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,373 - INFO - 删除后行数: 2
2025-07-03 12:10:00,374 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 12:10:00,393 - INFO - 已添加 17 条医院数据，共更新 561 个单元格
2025-07-03 12:10:00,397 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 19
2025-07-03 12:10:00,397 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 12:10:00,397 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 12:10:00,398 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,429 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,430 - INFO - 删除后行数: 2
2025-07-03 12:10:00,430 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:10:00,459 - INFO - 已添加 23 条医院数据，共更新 966 个单元格
2025-07-03 12:10:00,467 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 25
2025-07-03 12:10:00,467 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 12:10:00,467 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 12:10:00,467 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,499 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,499 - INFO - 删除后行数: 2
2025-07-03 12:10:00,500 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 12:10:00,524 - INFO - 已添加 22 条医院数据，共更新 748 个单元格
2025-07-03 12:10:00,531 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 24
2025-07-03 12:10:00,531 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 12:10:00,531 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 12:10:00,531 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,561 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,562 - INFO - 删除后行数: 2
2025-07-03 12:10:00,562 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 12:10:00,575 - INFO - 已添加 12 条医院数据，共更新 324 个单元格
2025-07-03 12:10:00,579 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 14
2025-07-03 12:10:00,579 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 12:10:00,579 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 12:10:00,579 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,635 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,635 - INFO - 删除后行数: 2
2025-07-03 12:10:00,635 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 12:10:00,644 - INFO - 已添加 8 条医院数据，共更新 232 个单元格
2025-07-03 12:10:00,649 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 10
2025-07-03 12:10:00,649 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 12:10:00,649 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 12:10:00,649 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,679 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,679 - INFO - 删除后行数: 2
2025-07-03 12:10:00,679 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 12:10:00,687 - INFO - 已添加 7 条医院数据，共更新 217 个单元格
2025-07-03 12:10:00,691 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 9
2025-07-03 12:10:00,691 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 12:10:00,691 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 12:10:00,691 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,725 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,725 - INFO - 删除后行数: 2
2025-07-03 12:10:00,726 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 12:10:00,749 - INFO - 已添加 18 条医院数据，共更新 864 个单元格
2025-07-03 12:10:00,758 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 20
2025-07-03 12:10:00,758 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 12:10:00,758 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 12:10:00,758 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,813 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,814 - INFO - 删除后行数: 2
2025-07-03 12:10:00,814 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 12:10:00,832 - INFO - 已添加 18 条医院数据，共更新 414 个单元格
2025-07-03 12:10:00,837 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 20
2025-07-03 12:10:00,837 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 12:10:00,837 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 12:10:00,837 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,869 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,869 - INFO - 删除后行数: 2
2025-07-03 12:10:00,870 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 12:10:00,881 - INFO - 已添加 12 条医院数据，共更新 228 个单元格
2025-07-03 12:10:00,885 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 14
2025-07-03 12:10:00,885 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 12:10:00,885 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 12:10:00,885 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:00,942 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:00,943 - INFO - 删除后行数: 2
2025-07-03 12:10:00,943 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 12:10:00,950 - INFO - 已添加 6 条医院数据，共更新 174 个单元格
2025-07-03 12:10:00,955 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 8
2025-07-03 12:10:01,061 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:01,061 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:01,078 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 12:10:01,078 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 12:10:01,078 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,107 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,107 - INFO - 删除后行数: 2
2025-07-03 12:10:01,109 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 12:10:01,157 - INFO - 已添加 21 条医院数据，共更新 2016 个单元格
2025-07-03 12:10:01,198 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 23
2025-07-03 12:10:01,198 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 12:10:01,198 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 12:10:01,198 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,219 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,219 - INFO - 删除后行数: 2
2025-07-03 12:10:01,220 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:10:01,243 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 12:10:01,249 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 26
2025-07-03 12:10:01,249 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 12:10:01,249 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 12:10:01,249 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,270 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,270 - INFO - 删除后行数: 2
2025-07-03 12:10:01,271 - INFO - 从JSON中提取了 16 条有效医院数据
2025-07-03 12:10:01,286 - INFO - 已添加 16 条医院数据，共更新 432 个单元格
2025-07-03 12:10:01,292 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 18
2025-07-03 12:10:01,292 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 12:10:01,293 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 12:10:01,293 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,315 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,315 - INFO - 删除后行数: 2
2025-07-03 12:10:01,316 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:10:01,346 - INFO - 已添加 24 条医院数据，共更新 1056 个单元格
2025-07-03 12:10:01,355 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 26
2025-07-03 12:10:01,415 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:01,415 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:01,439 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 12:10:01,439 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 12:10:01,439 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,570 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,570 - INFO - 删除后行数: 2
2025-07-03 12:10:01,571 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:10:01,593 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 12:10:01,598 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 27
2025-07-03 12:10:01,598 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 12:10:01,598 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 12:10:01,598 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,652 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,653 - INFO - 删除后行数: 2
2025-07-03 12:10:01,653 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:10:01,683 - INFO - 已添加 25 条医院数据，共更新 950 个单元格
2025-07-03 12:10:01,691 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 27
2025-07-03 12:10:01,691 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 12:10:01,691 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 12:10:01,691 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,719 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,719 - INFO - 删除后行数: 2
2025-07-03 12:10:01,720 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:10:01,744 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 12:10:01,749 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 27
2025-07-03 12:10:01,749 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 12:10:01,749 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 12:10:01,749 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,780 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,781 - INFO - 删除后行数: 2
2025-07-03 12:10:01,781 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:10:01,805 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 12:10:01,810 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 26
2025-07-03 12:10:01,810 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 12:10:01,810 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 12:10:01,810 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,843 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,843 - INFO - 删除后行数: 2
2025-07-03 12:10:01,844 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:10:01,871 - INFO - 已添加 23 条医院数据，共更新 828 个单元格
2025-07-03 12:10:01,878 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 25
2025-07-03 12:10:01,878 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 12:10:01,878 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 12:10:01,878 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,929 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,929 - INFO - 删除后行数: 2
2025-07-03 12:10:01,930 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:10:01,956 - INFO - 已添加 24 条医院数据，共更新 768 个单元格
2025-07-03 12:10:01,962 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 26
2025-07-03 12:10:01,962 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 12:10:01,962 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 12:10:01,962 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:01,997 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:01,997 - INFO - 删除后行数: 2
2025-07-03 12:10:01,998 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:10:02,035 - INFO - 已添加 24 条医院数据，共更新 1488 个单元格
2025-07-03 12:10:02,046 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 26
2025-07-03 12:10:02,047 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 12:10:02,047 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 12:10:02,047 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:10:02,076 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:10:02,076 - INFO - 删除后行数: 2
2025-07-03 12:10:02,077 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:10:02,097 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 12:10:02,102 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 25
2025-07-03 12:10:02,226 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:10:02,226 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 12:10:02,226 - INFO - 总耗时: 0:00:02.730360
2025-07-03 12:10:02,226 - INFO - === 处理结束 ===
2025-07-03 12:16:32,368 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 12:16:32,368 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 12:16:32,368 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 12:16:32,368 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 12:16:32,368 - INFO - 找到 4 个Excel文件
2025-07-03 12:16:32,368 - INFO - 正在处理Excel文件: 住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:32,390 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 12:16:32,390 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 12:16:32,390 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,490 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,490 - INFO - 删除后行数: 2
2025-07-03 12:16:32,491 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:32,518 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 12:16:32,527 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 25
2025-07-03 12:16:32,527 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 12:16:32,527 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 12:16:32,527 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,553 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,553 - INFO - 删除后行数: 2
2025-07-03 12:16:32,554 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:32,584 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-03 12:16:32,593 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 25
2025-07-03 12:16:32,593 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 12:16:32,593 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 12:16:32,593 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,642 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,642 - INFO - 删除后行数: 2
2025-07-03 12:16:32,643 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:32,667 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 12:16:32,673 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 26
2025-07-03 12:16:32,673 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 12:16:32,673 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 12:16:32,673 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,699 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,699 - INFO - 删除后行数: 2
2025-07-03 12:16:32,701 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 12:16:32,727 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-03 12:16:32,735 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 24
2025-07-03 12:16:32,735 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 12:16:32,735 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 12:16:32,735 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,760 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 33
2025-07-03 12:16:32,761 - INFO - 删除后行数: 7
2025-07-03 12:16:32,761 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:32,786 - INFO - 已添加 23 条医院数据，共更新 782 个单元格
2025-07-03 12:16:32,795 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 30
2025-07-03 12:16:32,795 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 12:16:32,795 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 12:16:32,795 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,823 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,823 - INFO - 删除后行数: 2
2025-07-03 12:16:32,824 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:32,861 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-03 12:16:32,872 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 26
2025-07-03 12:16:32,872 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 12:16:32,872 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 12:16:32,872 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,916 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,917 - INFO - 删除后行数: 2
2025-07-03 12:16:32,917 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:32,937 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 12:16:32,942 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 25
2025-07-03 12:16:32,942 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-03 12:16:32,942 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_charge_detail.json
2025-07-03 12:16:32,942 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:32,967 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:32,968 - INFO - 删除后行数: 2
2025-07-03 12:16:32,968 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:32,999 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-03 12:16:33,008 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 25
2025-07-03 12:16:33,008 - INFO - 正在处理工作表: 【住院转科记录inp_transfer_dept_record
2025-07-03 12:16:33,008 - INFO - 找到匹配的JSON文件: coreflddq_inp_transfer_dept_record.json
2025-07-03 12:16:33,008 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,037 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,037 - INFO - 删除后行数: 2
2025-07-03 12:16:33,038 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-03 12:16:33,049 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-03 12:16:33,052 - INFO - 工作表 【住院转科记录inp_transfer_dept_record 处理成功，总行数: 13
2025-07-03 12:16:33,166 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:33,166 - INFO - 正在处理Excel文件: 医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:33,192 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 12:16:33,192 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 12:16:33,192 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,249 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,249 - INFO - 删除后行数: 2
2025-07-03 12:16:33,250 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 12:16:33,269 - INFO - 已添加 17 条医院数据，共更新 561 个单元格
2025-07-03 12:16:33,273 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 19
2025-07-03 12:16:33,274 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 12:16:33,274 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 12:16:33,274 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,308 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,309 - INFO - 删除后行数: 2
2025-07-03 12:16:33,309 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:33,339 - INFO - 已添加 23 条医院数据，共更新 966 个单元格
2025-07-03 12:16:33,347 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 25
2025-07-03 12:16:33,347 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 12:16:33,347 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 12:16:33,347 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,377 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,378 - INFO - 删除后行数: 2
2025-07-03 12:16:33,378 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 12:16:33,403 - INFO - 已添加 22 条医院数据，共更新 748 个单元格
2025-07-03 12:16:33,410 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 24
2025-07-03 12:16:33,410 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 12:16:33,410 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 12:16:33,410 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,441 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,442 - INFO - 删除后行数: 2
2025-07-03 12:16:33,442 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 12:16:33,455 - INFO - 已添加 12 条医院数据，共更新 324 个单元格
2025-07-03 12:16:33,459 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 14
2025-07-03 12:16:33,459 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 12:16:33,460 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 12:16:33,460 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,514 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,514 - INFO - 删除后行数: 2
2025-07-03 12:16:33,514 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 12:16:33,523 - INFO - 已添加 8 条医院数据，共更新 232 个单元格
2025-07-03 12:16:33,527 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 10
2025-07-03 12:16:33,527 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 12:16:33,527 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 12:16:33,527 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,559 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,559 - INFO - 删除后行数: 2
2025-07-03 12:16:33,559 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 12:16:33,567 - INFO - 已添加 7 条医院数据，共更新 217 个单元格
2025-07-03 12:16:33,571 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 9
2025-07-03 12:16:33,571 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 12:16:33,571 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 12:16:33,572 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,604 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,604 - INFO - 删除后行数: 2
2025-07-03 12:16:33,605 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 12:16:33,629 - INFO - 已添加 18 条医院数据，共更新 864 个单元格
2025-07-03 12:16:33,638 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 20
2025-07-03 12:16:33,638 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 12:16:33,638 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 12:16:33,638 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,696 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,696 - INFO - 删除后行数: 2
2025-07-03 12:16:33,697 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 12:16:33,714 - INFO - 已添加 18 条医院数据，共更新 414 个单元格
2025-07-03 12:16:33,720 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 20
2025-07-03 12:16:33,720 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 12:16:33,720 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 12:16:33,720 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,751 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,751 - INFO - 删除后行数: 2
2025-07-03 12:16:33,751 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 12:16:33,763 - INFO - 已添加 12 条医院数据，共更新 228 个单元格
2025-07-03 12:16:33,767 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 14
2025-07-03 12:16:33,767 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 12:16:33,767 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 12:16:33,767 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,823 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,824 - INFO - 删除后行数: 2
2025-07-03 12:16:33,824 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 12:16:33,831 - INFO - 已添加 6 条医院数据，共更新 174 个单元格
2025-07-03 12:16:33,835 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 8
2025-07-03 12:16:33,950 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:33,950 - INFO - 正在处理Excel文件: 病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:33,967 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 12:16:33,969 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 12:16:33,969 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:33,997 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:33,997 - INFO - 删除后行数: 2
2025-07-03 12:16:33,998 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 12:16:34,047 - INFO - 已添加 21 条医院数据，共更新 2016 个单元格
2025-07-03 12:16:34,087 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 23
2025-07-03 12:16:34,089 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 12:16:34,089 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 12:16:34,089 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,110 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,110 - INFO - 删除后行数: 2
2025-07-03 12:16:34,111 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:34,134 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 12:16:34,139 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 26
2025-07-03 12:16:34,139 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 12:16:34,139 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 12:16:34,139 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,161 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,163 - INFO - 删除后行数: 2
2025-07-03 12:16:34,163 - INFO - 从JSON中提取了 16 条有效医院数据
2025-07-03 12:16:34,180 - INFO - 已添加 16 条医院数据，共更新 432 个单元格
2025-07-03 12:16:34,185 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 18
2025-07-03 12:16:34,185 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 12:16:34,185 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 12:16:34,185 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,208 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,208 - INFO - 删除后行数: 2
2025-07-03 12:16:34,209 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:34,239 - INFO - 已添加 24 条医院数据，共更新 1056 个单元格
2025-07-03 12:16:34,248 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 26
2025-07-03 12:16:34,313 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:34,313 - INFO - 正在处理Excel文件: 门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:34,339 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 12:16:34,339 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 12:16:34,339 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,470 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,470 - INFO - 删除后行数: 2
2025-07-03 12:16:34,471 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:16:34,495 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 12:16:34,502 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 27
2025-07-03 12:16:34,502 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 12:16:34,502 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 12:16:34,502 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,555 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,555 - INFO - 删除后行数: 2
2025-07-03 12:16:34,556 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:16:34,586 - INFO - 已添加 25 条医院数据，共更新 950 个单元格
2025-07-03 12:16:34,594 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 27
2025-07-03 12:16:34,594 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 12:16:34,594 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 12:16:34,594 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,621 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,622 - INFO - 删除后行数: 2
2025-07-03 12:16:34,622 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 12:16:34,646 - INFO - 已添加 25 条医院数据，共更新 550 个单元格
2025-07-03 12:16:34,652 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 27
2025-07-03 12:16:34,652 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 12:16:34,652 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 12:16:34,652 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,682 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,682 - INFO - 删除后行数: 2
2025-07-03 12:16:34,683 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:34,707 - INFO - 已添加 24 条医院数据，共更新 552 个单元格
2025-07-03 12:16:34,712 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 26
2025-07-03 12:16:34,712 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 12:16:34,712 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 12:16:34,713 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,745 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,745 - INFO - 删除后行数: 2
2025-07-03 12:16:34,746 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:34,773 - INFO - 已添加 23 条医院数据，共更新 828 个单元格
2025-07-03 12:16:34,780 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 25
2025-07-03 12:16:34,780 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 12:16:34,780 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 12:16:34,780 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,831 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,831 - INFO - 删除后行数: 2
2025-07-03 12:16:34,832 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:34,858 - INFO - 已添加 24 条医院数据，共更新 768 个单元格
2025-07-03 12:16:34,865 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 26
2025-07-03 12:16:34,865 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 12:16:34,865 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 12:16:34,865 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,900 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,900 - INFO - 删除后行数: 2
2025-07-03 12:16:34,901 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 12:16:34,938 - INFO - 已添加 24 条医院数据，共更新 1488 个单元格
2025-07-03 12:16:34,950 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 26
2025-07-03 12:16:34,950 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 12:16:34,950 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 12:16:34,950 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 12:16:34,981 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 12:16:34,981 - INFO - 删除后行数: 2
2025-07-03 12:16:34,981 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 12:16:35,002 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 12:16:35,007 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 25
2025-07-03 12:16:35,150 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-宁德-数据质量清单模版V1.xlsx
2025-07-03 12:16:35,150 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 12:16:35,150 - INFO - 总耗时: 0:00:02.782419
2025-07-03 12:16:35,150 - INFO - === 处理结束 ===
2025-07-03 21:06:02,482 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:06:02,482 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:06:02,482 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:06:02,482 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:06:02,482 - INFO - 找到 4 个Excel文件
2025-07-03 21:06:02,482 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:02,512 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 21:06:02,512 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_admission_record.json
2025-07-03 21:06:02,512 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院患者入院记录表】inp_admission_record
2025-07-03 21:06:02,559 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 21:06:02,559 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_discharge_record.json
2025-07-03 21:06:02,559 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院患者出院记录表】inp_discharge_record
2025-07-03 21:06:02,621 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 21:06:02,621 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_diagnosis_record.json
2025-07-03 21:06:02,621 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院诊断记录表】inp_diagnosis_record
2025-07-03 21:06:02,664 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 21:06:02,664 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_order_detail.json
2025-07-03 21:06:02,664 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院医嘱明细表】inp_order_detail
2025-07-03 21:06:02,708 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 21:06:02,709 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_charge_detail.json
2025-07-03 21:06:02,709 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院费用明细表】inp_charge_detail
2025-07-03 21:06:02,752 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 21:06:02,752 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_master.json
2025-07-03 21:06:02,752 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算主表】inp_settle_master
2025-07-03 21:06:02,823 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 21:06:02,823 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_detail.json
2025-07-03 21:06:02,823 - WARNING - 未找到对应的JSON文件，跳过工作表 【住院结算明细表】inp_settle_detail
2025-07-03 21:06:02,862 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-03 21:06:02,862 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_settle_charge_detail.json
2025-07-03 21:06:02,862 - WARNING - 未找到对应的JSON文件，跳过工作表 住院结算费用明inp_settle_charge_detail
2025-07-03 21:06:02,908 - INFO - 正在处理工作表: 住院转科记录表inp_transfer_dept_record
2025-07-03 21:06:02,908 - WARNING - 未找到匹配的JSON文件: coreflddq_inp_transfer_dept_record.json
2025-07-03 21:06:02,908 - WARNING - 未找到对应的JSON文件，跳过工作表 住院转科记录表inp_transfer_dept_record
2025-07-03 21:06:03,102 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:03,102 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:03,134 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 21:06:03,134 - WARNING - 未找到匹配的JSON文件: coreflddq_lis_request_form.json
2025-07-03 21:06:03,134 - WARNING - 未找到对应的JSON文件，跳过工作表 【检验申请表】 lis_request_form
2025-07-03 21:06:03,199 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 21:06:03,200 - WARNING - 未找到匹配的JSON文件: coreflddq_lis_report_master.json
2025-07-03 21:06:03,200 - WARNING - 未找到对应的JSON文件，跳过工作表 【检验报告主表】 lis_report_master
2025-07-03 21:06:03,250 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 21:06:03,251 - WARNING - 未找到匹配的JSON文件: coreflddq_lis_report_detail.json
2025-07-03 21:06:03,251 - WARNING - 未找到对应的JSON文件，跳过工作表 【检验报告细表】 lis_report_detail
2025-07-03 21:06:03,298 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 21:06:03,298 - WARNING - 未找到匹配的JSON文件: coreflddq_lis_bacteria_result.json
2025-07-03 21:06:03,298 - WARNING - 未找到对应的JSON文件，跳过工作表 【检验细菌结果表】 lis_bacteria_result
2025-07-03 21:06:03,363 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 21:06:03,363 - WARNING - 未找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.json
2025-07-03 21:06:03,363 - WARNING - 未找到对应的JSON文件，跳过工作表 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 21:06:03,406 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 21:06:03,407 - WARNING - 未找到匹配的JSON文件: coreflddq_exam_request_form.json
2025-07-03 21:06:03,407 - WARNING - 未找到对应的JSON文件，跳过工作表 【检查申请表】 exam_request_form
2025-07-03 21:06:03,453 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 21:06:03,453 - WARNING - 未找到匹配的JSON文件: coreflddq_exam_report_master.json
2025-07-03 21:06:03,453 - WARNING - 未找到对应的JSON文件，跳过工作表 【检查报告主表】 exam_report_master
2025-07-03 21:06:03,504 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 21:06:03,504 - WARNING - 未找到匹配的JSON文件: coreflddq_exam_report_detail.json
2025-07-03 21:06:03,504 - WARNING - 未找到对应的JSON文件，跳过工作表 【检查报告细表】 exam_report_detail
2025-07-03 21:06:03,575 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 21:06:03,575 - WARNING - 未找到匹配的JSON文件: coreflddq_exam_report_part.json
2025-07-03 21:06:03,576 - WARNING - 未找到对应的JSON文件，跳过工作表 【检查报告部位表】 exam_report_part
2025-07-03 21:06:03,619 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 21:06:03,619 - WARNING - 未找到匹配的JSON文件: coreflddq_exam_sample_record.json
2025-07-03 21:06:03,619 - WARNING - 未找到对应的JSON文件，跳过工作表 【病理标本记录表】 exam_sample_record
2025-07-03 21:06:03,838 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:03,838 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:03,860 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 21:06:03,860 - WARNING - 未找到匹配的JSON文件: coreflddq_case_base_info.json
2025-07-03 21:06:03,860 - WARNING - 未找到对应的JSON文件，跳过工作表 【病案首页基本信息】 case_base_info
2025-07-03 21:06:03,916 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 21:06:03,916 - WARNING - 未找到匹配的JSON文件: coreflddq_case_diagnosis_record.json
2025-07-03 21:06:03,916 - WARNING - 未找到对应的JSON文件，跳过工作表 【病案诊断记录表】 case_diagnosis_record
2025-07-03 21:06:03,972 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 21:06:03,972 - WARNING - 未找到匹配的JSON文件: coreflddq_case_operate_record.json
2025-07-03 21:06:03,972 - WARNING - 未找到对应的JSON文件，跳过工作表 【病案手术记录表】 case_operate_record
2025-07-03 21:06:04,004 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 21:06:04,004 - WARNING - 未找到匹配的JSON文件: coreflddq_case_fee_record.json
2025-07-03 21:06:04,004 - WARNING - 未找到对应的JSON文件，跳过工作表 【病案费用记录表】 case_fee_record
2025-07-03 21:06:04,121 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:04,121 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:04,152 - INFO - 正在处理工作表: Sheet1
2025-07-03 21:06:04,152 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet1.json
2025-07-03 21:06:04,152 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-03 21:06:04,184 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 21:06:04,184 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_register_record.json
2025-07-03 21:06:04,184 - WARNING - 未找到对应的JSON文件，跳过工作表 【门急诊挂号记录表】outp_register_record
2025-07-03 21:06:04,249 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 21:06:04,249 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_visit_record.json
2025-07-03 21:06:04,249 - WARNING - 未找到对应的JSON文件，跳过工作表 【门急诊就诊记录表】outp_visit_record
2025-07-03 21:06:04,302 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 21:06:04,302 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_diagnosis_record.json
2025-07-03 21:06:04,302 - WARNING - 未找到对应的JSON文件，跳过工作表 【门急诊断记录表】outp_diagnosis_record
2025-07-03 21:06:04,346 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 21:06:04,347 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_order_master.json
2025-07-03 21:06:04,347 - WARNING - 未找到对应的JSON文件，跳过工作表 【门诊医嘱主表】outp_order_master
2025-07-03 21:06:04,394 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 21:06:04,394 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_order_detail.json
2025-07-03 21:06:04,394 - WARNING - 未找到对应的JSON文件，跳过工作表 【门诊医嘱明细表】outp_order_detail
2025-07-03 21:06:04,469 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 21:06:04,469 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_charge_detail.json
2025-07-03 21:06:04,469 - WARNING - 未找到对应的JSON文件，跳过工作表 【门诊费用明细表】outp_charge_detail
2025-07-03 21:06:04,520 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 21:06:04,520 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_settle_master.json
2025-07-03 21:06:04,520 - WARNING - 未找到对应的JSON文件，跳过工作表 【门诊结算主表】outp_settle_master
2025-07-03 21:06:04,580 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 21:06:04,581 - WARNING - 未找到匹配的JSON文件: coreflddq_outp_settle_detail.json
2025-07-03 21:06:04,581 - WARNING - 未找到对应的JSON文件，跳过工作表 【门诊结算明细表】outp_settle_detail
2025-07-03 21:06:04,786 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:04,786 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 21:06:04,786 - INFO - 总耗时: 0:00:02.304076
2025-07-03 21:06:04,786 - INFO - === 处理结束 ===
2025-07-03 21:06:52,232 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:06:52,232 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:06:52,232 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:06:52,232 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:06:52,233 - INFO - 找到 4 个Excel文件
2025-07-03 21:06:52,233 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:52,263 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 21:06:52,263 - INFO - 找到匹配的JSON文件: coreflddq_inp_admission_record.txt
2025-07-03 21:06:52,263 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,299 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 29
2025-07-03 21:06:52,299 - INFO - 删除后行数: 24
2025-07-03 21:06:52,300 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:52,334 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 21:06:52,349 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 47
2025-07-03 21:06:52,349 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 21:06:52,349 - INFO - 找到匹配的JSON文件: coreflddq_inp_discharge_record.txt
2025-07-03 21:06:52,349 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,403 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 29
2025-07-03 21:06:52,403 - INFO - 删除后行数: 24
2025-07-03 21:06:52,404 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:52,437 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-03 21:06:52,453 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 47
2025-07-03 21:06:52,453 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 21:06:52,453 - INFO - 找到匹配的JSON文件: coreflddq_inp_diagnosis_record.txt
2025-07-03 21:06:52,453 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,489 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:52,490 - INFO - 删除后行数: 25
2025-07-03 21:06:52,491 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:52,522 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 21:06:52,532 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 49
2025-07-03 21:06:52,532 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 21:06:52,532 - INFO - 找到匹配的JSON文件: coreflddq_inp_order_detail.txt
2025-07-03 21:06:52,532 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,569 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 21:06:52,569 - INFO - 删除后行数: 23
2025-07-03 21:06:52,570 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:06:52,599 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-03 21:06:52,612 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 45
2025-07-03 21:06:52,612 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 21:06:52,612 - INFO - 找到匹配的JSON文件: coreflddq_inp_charge_detail.txt
2025-07-03 21:06:52,613 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,648 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 34
2025-07-03 21:06:52,648 - INFO - 删除后行数: 29
2025-07-03 21:06:52,649 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:52,678 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-03 21:06:52,691 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 53
2025-07-03 21:06:52,691 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 21:06:52,691 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_master.txt
2025-07-03 21:06:52,691 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,753 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:52,754 - INFO - 删除后行数: 24
2025-07-03 21:06:52,754 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:52,795 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-03 21:06:52,814 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 48
2025-07-03 21:06:52,814 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 21:06:52,814 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_detail.txt
2025-07-03 21:06:52,814 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,849 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 25
2025-07-03 21:06:52,849 - INFO - 删除后行数: 21
2025-07-03 21:06:52,850 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:52,871 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 21:06:52,878 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 44
2025-07-03 21:06:52,878 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-03 21:06:52,878 - INFO - 找到匹配的JSON文件: coreflddq_inp_settle_charge_detail.txt
2025-07-03 21:06:52,878 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:52,914 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 25
2025-07-03 21:06:52,914 - INFO - 删除后行数: 25
2025-07-03 21:06:52,914 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:52,948 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-03 21:06:52,964 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 48
2025-07-03 21:06:52,965 - INFO - 正在处理工作表: 住院转科记录表inp_transfer_dept_record
2025-07-03 21:06:52,965 - INFO - 找到匹配的JSON文件: coreflddq_inp_transfer_dept_record.txt
2025-07-03 21:06:52,965 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,002 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 17
2025-07-03 21:06:53,003 - INFO - 删除后行数: 13
2025-07-03 21:06:53,003 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-03 21:06:53,015 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-03 21:06:53,045 - INFO - 工作表 住院转科记录表inp_transfer_dept_record 处理成功，总行数: 24
2025-07-03 21:06:53,296 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:53,296 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:53,329 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 21:06:53,329 - INFO - 找到匹配的JSON文件: coreflddq_lis_request_form.txt
2025-07-03 21:06:53,329 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,368 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 23
2025-07-03 21:06:53,369 - INFO - 删除后行数: 19
2025-07-03 21:06:53,369 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:06:53,389 - INFO - 已添加 17 条医院数据，共更新 595 个单元格
2025-07-03 21:06:53,397 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 36
2025-07-03 21:06:53,397 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 21:06:53,397 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_master.txt
2025-07-03 21:06:53,397 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,439 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 29
2025-07-03 21:06:53,440 - INFO - 删除后行数: 22
2025-07-03 21:06:53,440 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:06:53,471 - INFO - 已添加 22 条医院数据，共更新 968 个单元格
2025-07-03 21:06:53,485 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 44
2025-07-03 21:06:53,485 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 21:06:53,485 - INFO - 找到匹配的JSON文件: coreflddq_lis_report_detail.txt
2025-07-03 21:06:53,486 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,526 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 28
2025-07-03 21:06:53,526 - INFO - 删除后行数: 25
2025-07-03 21:06:53,527 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:06:53,554 - INFO - 已添加 22 条医院数据，共更新 792 个单元格
2025-07-03 21:06:53,566 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 47
2025-07-03 21:06:53,567 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 21:06:53,567 - INFO - 找到匹配的JSON文件: coreflddq_lis_bacteria_result.txt
2025-07-03 21:06:53,567 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,641 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 18
2025-07-03 21:06:53,641 - INFO - 删除后行数: 15
2025-07-03 21:06:53,642 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:06:53,655 - INFO - 已添加 12 条医院数据，共更新 348 个单元格
2025-07-03 21:06:53,663 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 27
2025-07-03 21:06:53,663 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 21:06:53,663 - INFO - 找到匹配的JSON文件: coreflddq_lis_drug_sensitivity.txt
2025-07-03 21:06:53,663 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,701 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 14
2025-07-03 21:06:53,702 - INFO - 删除后行数: 11
2025-07-03 21:06:53,702 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 21:06:53,711 - INFO - 已添加 8 条医院数据，共更新 248 个单元格
2025-07-03 21:06:53,717 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 19
2025-07-03 21:06:53,717 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 21:06:53,717 - INFO - 找到匹配的JSON文件: coreflddq_exam_request_form.txt
2025-07-03 21:06:53,718 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,756 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 13
2025-07-03 21:06:53,757 - INFO - 删除后行数: 12
2025-07-03 21:06:53,757 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 21:06:53,766 - INFO - 已添加 7 条医院数据，共更新 231 个单元格
2025-07-03 21:06:53,772 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 19
2025-07-03 21:06:53,772 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 21:06:53,772 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_master.txt
2025-07-03 21:06:53,772 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,814 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 24
2025-07-03 21:06:53,814 - INFO - 删除后行数: 22
2025-07-03 21:06:53,815 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:06:53,840 - INFO - 已添加 17 条医院数据，共更新 850 个单元格
2025-07-03 21:06:53,854 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 39
2025-07-03 21:06:53,854 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 21:06:53,854 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_detail.txt
2025-07-03 21:06:53,854 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,914 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 24
2025-07-03 21:06:53,914 - INFO - 删除后行数: 22
2025-07-03 21:06:53,914 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 21:06:53,933 - INFO - 已添加 18 条医院数据，共更新 450 个单元格
2025-07-03 21:06:53,941 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 40
2025-07-03 21:06:53,941 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 21:06:53,941 - INFO - 找到匹配的JSON文件: coreflddq_exam_report_part.txt
2025-07-03 21:06:53,942 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:53,980 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 18
2025-07-03 21:06:53,980 - INFO - 删除后行数: 16
2025-07-03 21:06:53,981 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:06:53,993 - INFO - 已添加 12 条医院数据，共更新 252 个单元格
2025-07-03 21:06:53,999 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 28
2025-07-03 21:06:53,999 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 21:06:53,999 - INFO - 找到匹配的JSON文件: coreflddq_exam_sample_record.txt
2025-07-03 21:06:53,999 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,067 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 12
2025-07-03 21:06:54,067 - INFO - 删除后行数: 12
2025-07-03 21:06:54,068 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 21:06:54,075 - INFO - 已添加 6 条医院数据，共更新 186 个单元格
2025-07-03 21:06:54,081 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 18
2025-07-03 21:06:54,235 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:54,235 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:54,257 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 21:06:54,257 - INFO - 找到匹配的JSON文件: coreflddq_case_base_info.txt
2025-07-03 21:06:54,257 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,297 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 27
2025-07-03 21:06:54,297 - INFO - 删除后行数: 23
2025-07-03 21:06:54,298 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 21:06:54,350 - INFO - 已添加 21 条医院数据，共更新 2058 个单元格
2025-07-03 21:06:54,401 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 44
2025-07-03 21:06:54,401 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 21:06:54,402 - INFO - 找到匹配的JSON文件: coreflddq_case_diagnosis_record.txt
2025-07-03 21:06:54,402 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,428 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:54,429 - INFO - 删除后行数: 23
2025-07-03 21:06:54,429 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:06:54,451 - INFO - 已添加 22 条医院数据，共更新 550 个单元格
2025-07-03 21:06:54,460 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 45
2025-07-03 21:06:54,460 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 21:06:54,461 - INFO - 找到匹配的JSON文件: coreflddq_case_operate_record.txt
2025-07-03 21:06:54,461 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,488 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 22
2025-07-03 21:06:54,488 - INFO - 删除后行数: 17
2025-07-03 21:06:54,489 - INFO - 从JSON中提取了 14 条有效医院数据
2025-07-03 21:06:54,505 - INFO - 已添加 14 条医院数据，共更新 406 个单元格
2025-07-03 21:06:54,513 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 31
2025-07-03 21:06:54,513 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 21:06:54,513 - INFO - 找到匹配的JSON文件: coreflddq_case_fee_record.txt
2025-07-03 21:06:54,513 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,544 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:54,545 - INFO - 删除后行数: 25
2025-07-03 21:06:54,545 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:06:54,575 - INFO - 已添加 22 条医院数据，共更新 1012 个单元格
2025-07-03 21:06:54,590 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 47
2025-07-03 21:06:54,692 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:54,692 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:54,724 - INFO - 正在处理工作表: Sheet1
2025-07-03 21:06:54,724 - WARNING - 未找到匹配的JSON文件: coreflddq_Sheet1.txt
2025-07-03 21:06:54,724 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-03 21:06:54,757 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 21:06:54,757 - INFO - 找到匹配的JSON文件: coreflddq_outp_register_record.txt
2025-07-03 21:06:54,757 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,820 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 32
2025-07-03 21:06:54,821 - INFO - 删除后行数: 27
2025-07-03 21:06:54,821 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:06:54,845 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:06:54,854 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 52
2025-07-03 21:06:54,854 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 21:06:54,854 - INFO - 找到匹配的JSON文件: coreflddq_outp_visit_record.txt
2025-07-03 21:06:54,854 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,899 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 31
2025-07-03 21:06:54,900 - INFO - 删除后行数: 26
2025-07-03 21:06:54,901 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:06:54,933 - INFO - 已添加 25 条医院数据，共更新 1000 个单元格
2025-07-03 21:06:54,947 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 51
2025-07-03 21:06:54,947 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 21:06:54,947 - INFO - 找到匹配的JSON文件: coreflddq_outp_diagnosis_record.txt
2025-07-03 21:06:54,947 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:54,987 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 31
2025-07-03 21:06:54,987 - INFO - 删除后行数: 26
2025-07-03 21:06:54,988 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:06:55,014 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:06:55,023 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 51
2025-07-03 21:06:55,023 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 21:06:55,024 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_master.txt
2025-07-03 21:06:55,024 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:55,084 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:55,085 - INFO - 删除后行数: 25
2025-07-03 21:06:55,085 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:55,110 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 21:06:55,120 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 49
2025-07-03 21:06:55,120 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 21:06:55,120 - INFO - 找到匹配的JSON文件: coreflddq_outp_order_detail.txt
2025-07-03 21:06:55,120 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:55,164 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 29
2025-07-03 21:06:55,165 - INFO - 删除后行数: 24
2025-07-03 21:06:55,166 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:55,195 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 21:06:55,207 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 47
2025-07-03 21:06:55,207 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 21:06:55,207 - INFO - 找到匹配的JSON文件: coreflddq_outp_charge_detail.txt
2025-07-03 21:06:55,207 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:55,251 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:55,251 - INFO - 删除后行数: 25
2025-07-03 21:06:55,252 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:55,280 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-03 21:06:55,292 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 49
2025-07-03 21:06:55,292 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 21:06:55,292 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_master.txt
2025-07-03 21:06:55,292 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:55,339 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 30
2025-07-03 21:06:55,339 - INFO - 删除后行数: 25
2025-07-03 21:06:55,340 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:06:55,380 - INFO - 已添加 24 条医院数据，共更新 1536 个单元格
2025-07-03 21:06:55,425 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 49
2025-07-03 21:06:55,425 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 21:06:55,425 - INFO - 找到匹配的JSON文件: coreflddq_outp_settle_detail.txt
2025-07-03 21:06:55,425 - INFO - 开始按照新的填充逻辑处理数据: 删除宁德市行，从JSON数据添加行
2025-07-03 21:06:55,466 - INFO - 删除行政区划为'宁德市'的行，删除前行数: 29
2025-07-03 21:06:55,467 - INFO - 删除后行数: 25
2025-07-03 21:06:55,467 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:06:55,489 - INFO - 已添加 23 条医院数据，共更新 437 个单元格
2025-07-03 21:06:55,497 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 48
2025-07-03 21:06:55,696 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:06:55,696 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 21:06:55,696 - INFO - 总耗时: 0:00:03.464114
2025-07-03 21:06:55,696 - INFO - === 处理结束 ===
2025-07-03 21:17:28,219 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:17:28,219 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:17:28,219 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:17:28,219 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:17:28,220 - INFO - 找到 4 个Excel文件
2025-07-03 21:17:28,220 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:28,489 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:28,551 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:28,551 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:28,935 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:28,936 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:28,936 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:29,080 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:29,081 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:29,082 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:29,449 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:29,450 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:29,450 - INFO - 处理完成，成功处理 0/4 个文件
2025-07-03 21:17:29,450 - INFO - 总耗时: 0:00:01.231042
2025-07-03 21:17:29,451 - INFO - === 处理结束 ===
2025-07-03 21:17:37,680 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:17:37,681 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:17:37,681 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:17:37,681 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:17:37,681 - INFO - 找到 4 个Excel文件
2025-07-03 21:17:37,681 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:37,917 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:37,919 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:37,920 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:38,270 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:38,271 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:38,272 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:38,413 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:38,414 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:38,414 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:17:38,742 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: At least one sheet must be visible
2025-07-03 21:17:38,743 - ERROR - Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 315, in process_excel_file
    writer.book = load_workbook(excel_file_path)
    ^^^^^^^^^^^
AttributeError: property 'book' of 'OpenpyxlWriter' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 313, in process_excel_file
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1353, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1357, in close
    self._save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 110, in _save
    self.book.save(self._handles.handle)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\workbook.py", line 386, in save
    save_workbook(self, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 294, in save_workbook
    writer.save()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 275, in save
    self.write_data()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\writer\excel.py", line 89, in write_data
    archive.writestr(ARC_WORKBOOK, writer.write())
                                   ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 150, in write
    self.write_views()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 137, in write_views
    active = get_active_sheet(self.wb)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\workbook\_writer.py", line 35, in get_active_sheet
    raise IndexError("At least one sheet must be visible")
IndexError: At least one sheet must be visible

2025-07-03 21:17:38,743 - INFO - 处理完成，成功处理 0/4 个文件
2025-07-03 21:17:38,743 - INFO - 总耗时: 0:00:01.063033
2025-07-03 21:17:38,743 - INFO - === 处理结束 ===
2025-07-03 21:23:46,762 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:23:46,762 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:23:46,762 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:23:46,762 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:23:46,762 - INFO - 找到 4 个Excel文件
2025-07-03 21:23:46,762 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:46,780 - ERROR - 处理Excel文件 D:\work\demo\福建\质控模板\住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx 时出错: expected <class 'openpyxl.styles.fills.Fill'>
2025-07-03 21:23:46,828 - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\descriptors\base.py", line 59, in _convert
    value = expected_type(value)
            ^^^^^^^^^^^^^^^^^^^^
TypeError: Fill() takes no arguments

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\work\demo\福建\py\质控json转excel.py", line 297, in process_excel_file
    excel = pd.ExcelFile(excel_file_path)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\reader\excel.py", line 348, in load_workbook
    reader.read()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\reader\excel.py", line 301, in read
    apply_stylesheet(self.archive, self.wb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\styles\stylesheet.py", line 213, in apply_stylesheet
    stylesheet = Stylesheet.from_tree(node)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\styles\stylesheet.py", line 105, in from_tree
    return super(Stylesheet, cls).from_tree(node)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\descriptors\serialisable.py", line 103, in from_tree
    return cls(**attrib)
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\styles\stylesheet.py", line 76, in __init__
    self.fills = fills
    ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\descriptors\sequence.py", line 27, in __set__
    seq = self.container(_convert(self.expected_type, value) for value in seq)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\descriptors\sequence.py", line 27, in <genexpr>
    seq = self.container(_convert(self.expected_type, value) for value in seq)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openpyxl\descriptors\base.py", line 61, in _convert
    raise TypeError('expected ' + str(expected_type))
TypeError: expected <class 'openpyxl.styles.fills.Fill'>

2025-07-03 21:23:46,828 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:46,860 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 21:23:46,861 - INFO - 找到匹配的文件: coreflddq_lis_request_form.txt
2025-07-03 21:23:46,861 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:46,899 - INFO - 删除行政区划不为空的所有行，删除前行数: 23
2025-07-03 21:23:46,899 - INFO - 删除后行数: 0
2025-07-03 21:23:46,900 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:23:46,920 - INFO - 已添加 17 条医院数据，共更新 595 个单元格
2025-07-03 21:23:46,928 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 17
2025-07-03 21:23:46,928 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 21:23:46,928 - INFO - 找到匹配的文件: coreflddq_lis_report_master.txt
2025-07-03 21:23:46,928 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:46,990 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:23:46,991 - INFO - 删除后行数: 0
2025-07-03 21:23:46,991 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:23:47,022 - INFO - 已添加 22 条医院数据，共更新 968 个单元格
2025-07-03 21:23:47,031 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 22
2025-07-03 21:23:47,031 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 21:23:47,031 - INFO - 找到匹配的文件: coreflddq_lis_report_detail.txt
2025-07-03 21:23:47,031 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,071 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-03 21:23:47,072 - INFO - 删除后行数: 0
2025-07-03 21:23:47,072 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:23:47,099 - INFO - 已添加 22 条医院数据，共更新 792 个单元格
2025-07-03 21:23:47,107 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 22
2025-07-03 21:23:47,107 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 21:23:47,107 - INFO - 找到匹配的文件: coreflddq_lis_bacteria_result.txt
2025-07-03 21:23:47,107 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,145 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-03 21:23:47,145 - INFO - 删除后行数: 0
2025-07-03 21:23:47,146 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:23:47,159 - INFO - 已添加 12 条医院数据，共更新 348 个单元格
2025-07-03 21:23:47,164 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 12
2025-07-03 21:23:47,164 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 21:23:47,164 - INFO - 找到匹配的文件: coreflddq_lis_drug_sensitivity.txt
2025-07-03 21:23:47,164 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,201 - INFO - 删除行政区划不为空的所有行，删除前行数: 14
2025-07-03 21:23:47,202 - INFO - 删除后行数: 0
2025-07-03 21:23:47,202 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 21:23:47,211 - INFO - 已添加 8 条医院数据，共更新 248 个单元格
2025-07-03 21:23:47,216 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 8
2025-07-03 21:23:47,216 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 21:23:47,216 - INFO - 找到匹配的文件: coreflddq_exam_request_form.txt
2025-07-03 21:23:47,216 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,282 - INFO - 删除行政区划不为空的所有行，删除前行数: 13
2025-07-03 21:23:47,283 - INFO - 删除后行数: 0
2025-07-03 21:23:47,283 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 21:23:47,291 - INFO - 已添加 7 条医院数据，共更新 231 个单元格
2025-07-03 21:23:47,296 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 7
2025-07-03 21:23:47,296 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 21:23:47,296 - INFO - 找到匹配的文件: coreflddq_exam_report_master.txt
2025-07-03 21:23:47,296 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,335 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-03 21:23:47,336 - INFO - 删除后行数: 0
2025-07-03 21:23:47,336 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:23:47,362 - INFO - 已添加 17 条医院数据，共更新 850 个单元格
2025-07-03 21:23:47,371 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 17
2025-07-03 21:23:47,372 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 21:23:47,372 - INFO - 找到匹配的文件: coreflddq_exam_report_detail.txt
2025-07-03 21:23:47,372 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,409 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-03 21:23:47,409 - INFO - 删除后行数: 0
2025-07-03 21:23:47,409 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 21:23:47,428 - INFO - 已添加 18 条医院数据，共更新 450 个单元格
2025-07-03 21:23:47,434 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 18
2025-07-03 21:23:47,434 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 21:23:47,434 - INFO - 找到匹配的文件: coreflddq_exam_report_part.txt
2025-07-03 21:23:47,434 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,471 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-03 21:23:47,471 - INFO - 删除后行数: 0
2025-07-03 21:23:47,472 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:23:47,483 - INFO - 已添加 12 条医院数据，共更新 252 个单元格
2025-07-03 21:23:47,488 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 12
2025-07-03 21:23:47,488 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 21:23:47,488 - INFO - 找到匹配的文件: coreflddq_exam_sample_record.txt
2025-07-03 21:23:47,488 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,576 - INFO - 删除行政区划不为空的所有行，删除前行数: 12
2025-07-03 21:23:47,577 - INFO - 删除后行数: 0
2025-07-03 21:23:47,577 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 21:23:47,584 - INFO - 已添加 6 条医院数据，共更新 186 个单元格
2025-07-03 21:23:47,589 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 6
2025-07-03 21:23:47,735 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:47,735 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:47,758 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 21:23:47,758 - INFO - 找到匹配的文件: coreflddq_case_base_info.txt
2025-07-03 21:23:47,758 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,797 - INFO - 删除行政区划不为空的所有行，删除前行数: 27
2025-07-03 21:23:47,798 - INFO - 删除后行数: 0
2025-07-03 21:23:47,799 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 21:23:47,854 - INFO - 已添加 21 条医院数据，共更新 2058 个单元格
2025-07-03 21:23:47,868 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 21
2025-07-03 21:23:47,869 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 21:23:47,869 - INFO - 找到匹配的文件: coreflddq_case_diagnosis_record.txt
2025-07-03 21:23:47,869 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,896 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:23:47,897 - INFO - 删除后行数: 0
2025-07-03 21:23:47,897 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:23:47,920 - INFO - 已添加 22 条医院数据，共更新 550 个单元格
2025-07-03 21:23:47,926 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 22
2025-07-03 21:23:47,926 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 21:23:47,926 - INFO - 找到匹配的文件: coreflddq_case_operate_record.txt
2025-07-03 21:23:47,926 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:47,954 - INFO - 删除行政区划不为空的所有行，删除前行数: 22
2025-07-03 21:23:47,955 - INFO - 删除后行数: 0
2025-07-03 21:23:47,955 - INFO - 从JSON中提取了 14 条有效医院数据
2025-07-03 21:23:47,972 - INFO - 已添加 14 条医院数据，共更新 406 个单元格
2025-07-03 21:23:47,977 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 14
2025-07-03 21:23:47,977 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 21:23:47,977 - INFO - 找到匹配的文件: coreflddq_case_fee_record.txt
2025-07-03 21:23:47,977 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,007 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:23:48,008 - INFO - 删除后行数: 0
2025-07-03 21:23:48,009 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:23:48,039 - INFO - 已添加 22 条医院数据，共更新 1012 个单元格
2025-07-03 21:23:48,048 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 22
2025-07-03 21:23:48,117 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:48,117 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:48,172 - INFO - 正在处理工作表: Sheet1
2025-07-03 21:23:48,172 - WARNING - 未找到匹配的文件: coreflddq_Sheet1.txt
2025-07-03 21:23:48,172 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-03 21:23:48,204 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 21:23:48,204 - INFO - 找到匹配的文件: coreflddq_outp_register_record.txt
2025-07-03 21:23:48,204 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,242 - INFO - 删除行政区划不为空的所有行，删除前行数: 32
2025-07-03 21:23:48,242 - INFO - 删除后行数: 0
2025-07-03 21:23:48,243 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:23:48,269 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:23:48,274 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 25
2025-07-03 21:23:48,275 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 21:23:48,275 - INFO - 找到匹配的文件: coreflddq_outp_visit_record.txt
2025-07-03 21:23:48,275 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,319 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-03 21:23:48,319 - INFO - 删除后行数: 0
2025-07-03 21:23:48,320 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:23:48,352 - INFO - 已添加 25 条医院数据，共更新 1000 个单元格
2025-07-03 21:23:48,361 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 25
2025-07-03 21:23:48,361 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 21:23:48,361 - INFO - 找到匹配的文件: coreflddq_outp_diagnosis_record.txt
2025-07-03 21:23:48,362 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,424 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-03 21:23:48,425 - INFO - 删除后行数: 0
2025-07-03 21:23:48,425 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:23:48,451 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:23:48,458 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 25
2025-07-03 21:23:48,458 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 21:23:48,458 - INFO - 找到匹配的文件: coreflddq_outp_order_master.txt
2025-07-03 21:23:48,458 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,500 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:23:48,501 - INFO - 删除后行数: 0
2025-07-03 21:23:48,501 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:23:48,526 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 21:23:48,533 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 24
2025-07-03 21:23:48,533 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 21:23:48,533 - INFO - 找到匹配的文件: coreflddq_outp_order_detail.txt
2025-07-03 21:23:48,533 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,576 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:23:48,576 - INFO - 删除后行数: 0
2025-07-03 21:23:48,577 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:23:48,605 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 21:23:48,613 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 23
2025-07-03 21:23:48,613 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 21:23:48,613 - INFO - 找到匹配的文件: coreflddq_outp_charge_detail.txt
2025-07-03 21:23:48,613 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,655 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:23:48,655 - INFO - 删除后行数: 0
2025-07-03 21:23:48,656 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:23:48,684 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-03 21:23:48,692 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 24
2025-07-03 21:23:48,692 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 21:23:48,692 - INFO - 找到匹配的文件: coreflddq_outp_settle_master.txt
2025-07-03 21:23:48,692 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,768 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:23:48,768 - INFO - 删除后行数: 0
2025-07-03 21:23:48,769 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:23:48,808 - INFO - 已添加 24 条医院数据，共更新 1536 个单元格
2025-07-03 21:23:48,820 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 24
2025-07-03 21:23:48,820 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 21:23:48,820 - INFO - 找到匹配的文件: coreflddq_outp_settle_detail.txt
2025-07-03 21:23:48,820 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:23:48,860 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:23:48,860 - INFO - 删除后行数: 0
2025-07-03 21:23:48,861 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:23:48,882 - INFO - 已添加 23 条医院数据，共更新 437 个单元格
2025-07-03 21:23:48,887 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 23
2025-07-03 21:23:49,032 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:23:49,032 - INFO - 处理完成，成功处理 3/4 个文件
2025-07-03 21:23:49,033 - INFO - 总耗时: 0:00:02.271048
2025-07-03 21:23:49,033 - INFO - === 处理结束 ===
2025-07-03 21:25:43,305 - INFO - === 开始处理质控JSON转Excel ===
2025-07-03 21:25:43,305 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-03 21:25:43,305 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-03 21:25:43,305 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-03 21:25:43,305 - INFO - 找到 4 个Excel文件
2025-07-03 21:25:43,305 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:43,335 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-03 21:25:43,335 - INFO - 找到匹配的文件: coreflddq_inp_admission_record.txt
2025-07-03 21:25:43,335 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,370 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:25:43,371 - INFO - 删除后行数: 0
2025-07-03 21:25:43,371 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:43,403 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 21:25:43,412 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 23
2025-07-03 21:25:43,412 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-03 21:25:43,412 - INFO - 找到匹配的文件: coreflddq_inp_discharge_record.txt
2025-07-03 21:25:43,412 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,465 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:25:43,465 - INFO - 删除后行数: 0
2025-07-03 21:25:43,466 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:43,500 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-03 21:25:43,510 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 23
2025-07-03 21:25:43,511 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-03 21:25:43,511 - INFO - 找到匹配的文件: coreflddq_inp_diagnosis_record.txt
2025-07-03 21:25:43,511 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,547 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:43,548 - INFO - 删除后行数: 0
2025-07-03 21:25:43,548 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:43,578 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 21:25:43,585 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 24
2025-07-03 21:25:43,585 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-03 21:25:43,585 - INFO - 找到匹配的文件: coreflddq_inp_order_detail.txt
2025-07-03 21:25:43,585 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,620 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-03 21:25:43,620 - INFO - 删除后行数: 0
2025-07-03 21:25:43,621 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:25:43,651 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-03 21:25:43,660 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 22
2025-07-03 21:25:43,660 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-03 21:25:43,660 - INFO - 找到匹配的文件: coreflddq_inp_charge_detail.txt
2025-07-03 21:25:43,660 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,696 - INFO - 删除行政区划不为空的所有行，删除前行数: 34
2025-07-03 21:25:43,696 - INFO - 删除后行数: 0
2025-07-03 21:25:43,697 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:43,729 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-03 21:25:43,737 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 24
2025-07-03 21:25:43,737 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-03 21:25:43,737 - INFO - 找到匹配的文件: coreflddq_inp_settle_master.txt
2025-07-03 21:25:43,737 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,794 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:43,795 - INFO - 删除后行数: 0
2025-07-03 21:25:43,796 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:43,837 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-03 21:25:43,849 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 24
2025-07-03 21:25:43,849 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-03 21:25:43,849 - INFO - 找到匹配的文件: coreflddq_inp_settle_detail.txt
2025-07-03 21:25:43,850 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,885 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-03 21:25:43,885 - INFO - 删除后行数: 0
2025-07-03 21:25:43,886 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:43,912 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-03 21:25:43,917 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 23
2025-07-03 21:25:43,918 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-03 21:25:43,918 - INFO - 找到匹配的文件: coreflddq_inp_settle_charge_detail.txt
2025-07-03 21:25:43,918 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:43,954 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-03 21:25:43,955 - INFO - 删除后行数: 23
2025-07-03 21:25:43,956 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:43,991 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-03 21:25:44,006 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 46
2025-07-03 21:25:44,006 - INFO - 正在处理工作表: 住院转科记录表inp_transfer_dept_record
2025-07-03 21:25:44,006 - INFO - 找到匹配的文件: coreflddq_inp_transfer_dept_record.txt
2025-07-03 21:25:44,007 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,044 - INFO - 删除行政区划不为空的所有行，删除前行数: 17
2025-07-03 21:25:44,044 - INFO - 删除后行数: 0
2025-07-03 21:25:44,045 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-03 21:25:44,057 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-03 21:25:44,061 - INFO - 工作表 住院转科记录表inp_transfer_dept_record 处理成功，总行数: 11
2025-07-03 21:25:44,210 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:44,210 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:44,268 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-03 21:25:44,268 - INFO - 找到匹配的文件: coreflddq_lis_request_form.txt
2025-07-03 21:25:44,268 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,307 - INFO - 删除行政区划不为空的所有行，删除前行数: 23
2025-07-03 21:25:44,308 - INFO - 删除后行数: 0
2025-07-03 21:25:44,308 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:25:44,330 - INFO - 已添加 17 条医院数据，共更新 595 个单元格
2025-07-03 21:25:44,335 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 17
2025-07-03 21:25:44,335 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-03 21:25:44,335 - INFO - 找到匹配的文件: coreflddq_lis_report_master.txt
2025-07-03 21:25:44,335 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,378 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:25:44,379 - INFO - 删除后行数: 0
2025-07-03 21:25:44,379 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:25:44,411 - INFO - 已添加 22 条医院数据，共更新 968 个单元格
2025-07-03 21:25:44,420 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 22
2025-07-03 21:25:44,420 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-03 21:25:44,420 - INFO - 找到匹配的文件: coreflddq_lis_report_detail.txt
2025-07-03 21:25:44,420 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,460 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-03 21:25:44,460 - INFO - 删除后行数: 0
2025-07-03 21:25:44,461 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:25:44,490 - INFO - 已添加 22 条医院数据，共更新 792 个单元格
2025-07-03 21:25:44,497 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 22
2025-07-03 21:25:44,497 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-03 21:25:44,498 - INFO - 找到匹配的文件: coreflddq_lis_bacteria_result.txt
2025-07-03 21:25:44,498 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,559 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-03 21:25:44,559 - INFO - 删除后行数: 0
2025-07-03 21:25:44,560 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:25:44,577 - INFO - 已添加 12 条医院数据，共更新 348 个单元格
2025-07-03 21:25:44,582 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 12
2025-07-03 21:25:44,582 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-03 21:25:44,582 - INFO - 找到匹配的文件: coreflddq_lis_drug_sensitivity.txt
2025-07-03 21:25:44,582 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,621 - INFO - 删除行政区划不为空的所有行，删除前行数: 14
2025-07-03 21:25:44,621 - INFO - 删除后行数: 0
2025-07-03 21:25:44,621 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-03 21:25:44,631 - INFO - 已添加 8 条医院数据，共更新 248 个单元格
2025-07-03 21:25:44,635 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 8
2025-07-03 21:25:44,636 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-03 21:25:44,636 - INFO - 找到匹配的文件: coreflddq_exam_request_form.txt
2025-07-03 21:25:44,636 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,674 - INFO - 删除行政区划不为空的所有行，删除前行数: 13
2025-07-03 21:25:44,675 - INFO - 删除后行数: 0
2025-07-03 21:25:44,675 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-03 21:25:44,683 - INFO - 已添加 7 条医院数据，共更新 231 个单元格
2025-07-03 21:25:44,688 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 7
2025-07-03 21:25:44,688 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-03 21:25:44,688 - INFO - 找到匹配的文件: coreflddq_exam_report_master.txt
2025-07-03 21:25:44,688 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,728 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-03 21:25:44,728 - INFO - 删除后行数: 0
2025-07-03 21:25:44,729 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-03 21:25:44,755 - INFO - 已添加 17 条医院数据，共更新 850 个单元格
2025-07-03 21:25:44,765 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 17
2025-07-03 21:25:44,765 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-03 21:25:44,765 - INFO - 找到匹配的文件: coreflddq_exam_report_detail.txt
2025-07-03 21:25:44,765 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,830 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-03 21:25:44,830 - INFO - 删除后行数: 0
2025-07-03 21:25:44,830 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-03 21:25:44,850 - INFO - 已添加 18 条医院数据，共更新 450 个单元格
2025-07-03 21:25:44,855 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 18
2025-07-03 21:25:44,855 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-03 21:25:44,855 - INFO - 找到匹配的文件: coreflddq_exam_report_part.txt
2025-07-03 21:25:44,855 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,893 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-03 21:25:44,893 - INFO - 删除后行数: 0
2025-07-03 21:25:44,893 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-03 21:25:44,906 - INFO - 已添加 12 条医院数据，共更新 252 个单元格
2025-07-03 21:25:44,910 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 12
2025-07-03 21:25:44,910 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-03 21:25:44,910 - INFO - 找到匹配的文件: coreflddq_exam_sample_record.txt
2025-07-03 21:25:44,910 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:44,977 - INFO - 删除行政区划不为空的所有行，删除前行数: 12
2025-07-03 21:25:44,978 - INFO - 删除后行数: 0
2025-07-03 21:25:44,978 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-03 21:25:44,986 - INFO - 已添加 6 条医院数据，共更新 186 个单元格
2025-07-03 21:25:44,990 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 6
2025-07-03 21:25:45,124 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:45,124 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:45,146 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-03 21:25:45,146 - INFO - 找到匹配的文件: coreflddq_case_base_info.txt
2025-07-03 21:25:45,146 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,185 - INFO - 删除行政区划不为空的所有行，删除前行数: 27
2025-07-03 21:25:45,185 - INFO - 删除后行数: 0
2025-07-03 21:25:45,187 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-03 21:25:45,243 - INFO - 已添加 21 条医院数据，共更新 2058 个单元格
2025-07-03 21:25:45,289 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 21
2025-07-03 21:25:45,289 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-03 21:25:45,289 - INFO - 找到匹配的文件: coreflddq_case_diagnosis_record.txt
2025-07-03 21:25:45,289 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,317 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:45,317 - INFO - 删除后行数: 0
2025-07-03 21:25:45,318 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:25:45,341 - INFO - 已添加 22 条医院数据，共更新 550 个单元格
2025-07-03 21:25:45,347 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 22
2025-07-03 21:25:45,347 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-03 21:25:45,347 - INFO - 找到匹配的文件: coreflddq_case_operate_record.txt
2025-07-03 21:25:45,348 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,376 - INFO - 删除行政区划不为空的所有行，删除前行数: 22
2025-07-03 21:25:45,376 - INFO - 删除后行数: 0
2025-07-03 21:25:45,376 - INFO - 从JSON中提取了 14 条有效医院数据
2025-07-03 21:25:45,393 - INFO - 已添加 14 条医院数据，共更新 406 个单元格
2025-07-03 21:25:45,399 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 14
2025-07-03 21:25:45,399 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-03 21:25:45,399 - INFO - 找到匹配的文件: coreflddq_case_fee_record.txt
2025-07-03 21:25:45,399 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,430 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:45,430 - INFO - 删除后行数: 0
2025-07-03 21:25:45,431 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-03 21:25:45,463 - INFO - 已添加 22 条医院数据，共更新 1012 个单元格
2025-07-03 21:25:45,472 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 22
2025-07-03 21:25:45,544 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:45,544 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:45,576 - INFO - 正在处理工作表: Sheet1
2025-07-03 21:25:45,576 - WARNING - 未找到匹配的文件: coreflddq_Sheet1.txt
2025-07-03 21:25:45,576 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-03 21:25:45,609 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-03 21:25:45,609 - INFO - 找到匹配的文件: coreflddq_outp_register_record.txt
2025-07-03 21:25:45,609 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,671 - INFO - 删除行政区划不为空的所有行，删除前行数: 32
2025-07-03 21:25:45,672 - INFO - 删除后行数: 0
2025-07-03 21:25:45,672 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:25:45,699 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:25:45,705 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 25
2025-07-03 21:25:45,705 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-03 21:25:45,705 - INFO - 找到匹配的文件: coreflddq_outp_visit_record.txt
2025-07-03 21:25:45,705 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,751 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-03 21:25:45,752 - INFO - 删除后行数: 0
2025-07-03 21:25:45,752 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:25:45,785 - INFO - 已添加 25 条医院数据，共更新 1000 个单元格
2025-07-03 21:25:45,794 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 25
2025-07-03 21:25:45,794 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-03 21:25:45,795 - INFO - 找到匹配的文件: coreflddq_outp_diagnosis_record.txt
2025-07-03 21:25:45,795 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,834 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-03 21:25:45,834 - INFO - 删除后行数: 0
2025-07-03 21:25:45,835 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-03 21:25:45,862 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-03 21:25:45,869 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 25
2025-07-03 21:25:45,869 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-03 21:25:45,869 - INFO - 找到匹配的文件: coreflddq_outp_order_master.txt
2025-07-03 21:25:45,869 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:45,913 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:45,913 - INFO - 删除后行数: 0
2025-07-03 21:25:45,914 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:45,940 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-03 21:25:45,946 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 24
2025-07-03 21:25:45,946 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-03 21:25:45,946 - INFO - 找到匹配的文件: coreflddq_outp_order_detail.txt
2025-07-03 21:25:45,946 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:46,014 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:25:46,015 - INFO - 删除后行数: 0
2025-07-03 21:25:46,015 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:46,044 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-03 21:25:46,053 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 23
2025-07-03 21:25:46,053 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-03 21:25:46,053 - INFO - 找到匹配的文件: coreflddq_outp_charge_detail.txt
2025-07-03 21:25:46,053 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:46,098 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:46,098 - INFO - 删除后行数: 0
2025-07-03 21:25:46,099 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:46,128 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-03 21:25:46,136 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 24
2025-07-03 21:25:46,136 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-03 21:25:46,136 - INFO - 找到匹配的文件: coreflddq_outp_settle_master.txt
2025-07-03 21:25:46,136 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:46,186 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-03 21:25:46,186 - INFO - 删除后行数: 0
2025-07-03 21:25:46,187 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-03 21:25:46,228 - INFO - 已添加 24 条医院数据，共更新 1536 个单元格
2025-07-03 21:25:46,240 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 24
2025-07-03 21:25:46,241 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-03 21:25:46,241 - INFO - 找到匹配的文件: coreflddq_outp_settle_detail.txt
2025-07-03 21:25:46,241 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-03 21:25:46,282 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-03 21:25:46,283 - INFO - 删除后行数: 0
2025-07-03 21:25:46,283 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-03 21:25:46,306 - INFO - 已添加 23 条医院数据，共更新 437 个单元格
2025-07-03 21:25:46,312 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 23
2025-07-03 21:25:46,462 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-03 21:25:46,462 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-03 21:25:46,462 - INFO - 总耗时: 0:00:03.157070
2025-07-03 21:25:46,462 - INFO - === 处理结束 ===
2025-07-04 17:52:57,049 - INFO - === 开始处理质控JSON转Excel ===
2025-07-04 17:52:57,050 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-04 17:52:57,050 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-04 17:52:57,050 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-04 17:52:57,050 - INFO - 找到 5 个Excel文件
2025-07-04 17:52:57,050 - INFO - 获取第一个Excel文件的第一行样式
2025-07-04 17:52:57,050 - INFO - 正在获取 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx 的第一行样式
2025-07-04 17:52:57,192 - INFO - 已成功获取第一行样式，共 50 个单元格
2025-07-04 17:52:57,193 - INFO - 成功获取第一行样式，将应用到所有输出文件
2025-07-04 17:52:57,193 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:52:57,530 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-04 17:52:57,531 - INFO - 找到匹配的文件: coreflddq_inp_admission_record.txt
2025-07-04 17:52:57,531 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:57,565 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 17:52:57,566 - INFO - 删除后行数: 0
2025-07-04 17:52:57,567 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:52:57,607 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-04 17:52:57,623 - INFO - 已应用表头样式到工作表 【住院患者入院记录表】inp_admission_record
2025-07-04 17:52:57,625 - INFO - 已自动调整工作表 【住院患者入院记录表】inp_admission_record 的列宽
2025-07-04 17:52:57,625 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 23
2025-07-04 17:52:57,625 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-04 17:52:57,625 - INFO - 找到匹配的文件: coreflddq_inp_discharge_record.txt
2025-07-04 17:52:57,625 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:57,657 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 17:52:57,658 - INFO - 删除后行数: 0
2025-07-04 17:52:57,659 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:52:57,703 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-04 17:52:57,722 - INFO - 已应用表头样式到工作表 【住院患者出院记录表】inp_discharge_record
2025-07-04 17:52:57,724 - INFO - 已自动调整工作表 【住院患者出院记录表】inp_discharge_record 的列宽
2025-07-04 17:52:57,724 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 23
2025-07-04 17:52:57,724 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-04 17:52:57,724 - INFO - 找到匹配的文件: coreflddq_inp_diagnosis_record.txt
2025-07-04 17:52:57,724 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:57,778 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:52:57,779 - INFO - 删除后行数: 0
2025-07-04 17:52:57,780 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:52:57,814 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-04 17:52:57,826 - INFO - 已应用表头样式到工作表 【住院诊断记录表】inp_diagnosis_record
2025-07-04 17:52:57,827 - INFO - 已自动调整工作表 【住院诊断记录表】inp_diagnosis_record 的列宽
2025-07-04 17:52:57,827 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 24
2025-07-04 17:52:57,827 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-04 17:52:57,827 - INFO - 找到匹配的文件: coreflddq_inp_order_detail.txt
2025-07-04 17:52:57,827 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:57,859 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-04 17:52:57,860 - INFO - 删除后行数: 0
2025-07-04 17:52:57,861 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 17:52:57,898 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-04 17:52:57,913 - INFO - 已应用表头样式到工作表 【住院医嘱明细表】inp_order_detail
2025-07-04 17:52:57,915 - INFO - 已自动调整工作表 【住院医嘱明细表】inp_order_detail 的列宽
2025-07-04 17:52:57,915 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 22
2025-07-04 17:52:57,916 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-04 17:52:57,916 - INFO - 找到匹配的文件: coreflddq_inp_charge_detail.txt
2025-07-04 17:52:57,916 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:57,947 - INFO - 删除行政区划不为空的所有行，删除前行数: 34
2025-07-04 17:52:57,948 - INFO - 删除后行数: 0
2025-07-04 17:52:57,949 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:52:57,994 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-04 17:52:58,010 - INFO - 已应用表头样式到工作表 【住院费用明细表】inp_charge_detail
2025-07-04 17:52:58,011 - INFO - 已自动调整工作表 【住院费用明细表】inp_charge_detail 的列宽
2025-07-04 17:52:58,011 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 24
2025-07-04 17:52:58,011 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-04 17:52:58,012 - INFO - 找到匹配的文件: coreflddq_inp_settle_master.txt
2025-07-04 17:52:58,012 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,046 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:52:58,047 - INFO - 删除后行数: 0
2025-07-04 17:52:58,048 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:52:58,099 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-04 17:52:58,120 - INFO - 已应用表头样式到工作表 【住院结算主表】inp_settle_master
2025-07-04 17:52:58,122 - INFO - 已自动调整工作表 【住院结算主表】inp_settle_master 的列宽
2025-07-04 17:52:58,122 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 24
2025-07-04 17:52:58,122 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-04 17:52:58,122 - INFO - 找到匹配的文件: coreflddq_inp_settle_detail.txt
2025-07-04 17:52:58,123 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,172 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-04 17:52:58,173 - INFO - 删除后行数: 0
2025-07-04 17:52:58,173 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:52:58,203 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-04 17:52:58,212 - INFO - 已应用表头样式到工作表 【住院结算明细表】inp_settle_detail
2025-07-04 17:52:58,213 - INFO - 已自动调整工作表 【住院结算明细表】inp_settle_detail 的列宽
2025-07-04 17:52:58,213 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 23
2025-07-04 17:52:58,213 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-04 17:52:58,213 - INFO - 找到匹配的文件: coreflddq_inp_settle_charge_detail.txt
2025-07-04 17:52:58,213 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,244 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-04 17:52:58,245 - INFO - 删除后行数: 23
2025-07-04 17:52:58,246 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:52:58,289 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-04 17:52:58,314 - INFO - 已应用表头样式到工作表 住院结算费用明inp_settle_charge_detail
2025-07-04 17:52:58,317 - INFO - 已自动调整工作表 住院结算费用明inp_settle_charge_detail 的列宽
2025-07-04 17:52:58,317 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 46
2025-07-04 17:52:58,317 - INFO - 正在处理工作表: 住院转科记录表inp_transfer_dept_record
2025-07-04 17:52:58,318 - INFO - 找到匹配的文件: coreflddq_inp_transfer_dept_record.txt
2025-07-04 17:52:58,318 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,350 - INFO - 删除行政区划不为空的所有行，删除前行数: 17
2025-07-04 17:52:58,351 - INFO - 删除后行数: 0
2025-07-04 17:52:58,352 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-04 17:52:58,367 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-04 17:52:58,376 - INFO - 已应用表头样式到工作表 住院转科记录表inp_transfer_dept_record
2025-07-04 17:52:58,377 - INFO - 已自动调整工作表 住院转科记录表inp_transfer_dept_record 的列宽
2025-07-04 17:52:58,377 - INFO - 工作表 住院转科记录表inp_transfer_dept_record 处理成功，总行数: 11
2025-07-04 17:52:58,549 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:52:58,550 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:52:58,727 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-04 17:52:58,727 - INFO - 找到匹配的文件: coreflddq_lis_request_form.txt
2025-07-04 17:52:58,727 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,762 - INFO - 删除行政区划不为空的所有行，删除前行数: 23
2025-07-04 17:52:58,763 - INFO - 删除后行数: 0
2025-07-04 17:52:58,764 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-04 17:52:58,789 - INFO - 已添加 17 条医院数据，共更新 595 个单元格
2025-07-04 17:52:58,801 - INFO - 已应用表头样式到工作表 【检验申请表】 lis_request_form
2025-07-04 17:52:58,802 - INFO - 已自动调整工作表 【检验申请表】 lis_request_form 的列宽
2025-07-04 17:52:58,802 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 17
2025-07-04 17:52:58,802 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-04 17:52:58,802 - INFO - 找到匹配的文件: coreflddq_lis_report_master.txt
2025-07-04 17:52:58,803 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,864 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 17:52:58,865 - INFO - 删除后行数: 0
2025-07-04 17:52:58,866 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 17:52:58,904 - INFO - 已添加 22 条医院数据，共更新 968 个单元格
2025-07-04 17:52:58,921 - INFO - 已应用表头样式到工作表 【检验报告主表】 lis_report_master
2025-07-04 17:52:58,923 - INFO - 已自动调整工作表 【检验报告主表】 lis_report_master 的列宽
2025-07-04 17:52:58,923 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 22
2025-07-04 17:52:58,923 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-04 17:52:58,923 - INFO - 找到匹配的文件: coreflddq_lis_report_detail.txt
2025-07-04 17:52:58,923 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:58,958 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-04 17:52:58,959 - INFO - 删除后行数: 0
2025-07-04 17:52:58,960 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 17:52:58,993 - INFO - 已添加 22 条医院数据，共更新 792 个单元格
2025-07-04 17:52:59,007 - INFO - 已应用表头样式到工作表 【检验报告细表】 lis_report_detail
2025-07-04 17:52:59,008 - INFO - 已自动调整工作表 【检验报告细表】 lis_report_detail 的列宽
2025-07-04 17:52:59,008 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 22
2025-07-04 17:52:59,008 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-04 17:52:59,009 - INFO - 找到匹配的文件: coreflddq_lis_bacteria_result.txt
2025-07-04 17:52:59,009 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,043 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-04 17:52:59,044 - INFO - 删除后行数: 0
2025-07-04 17:52:59,044 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-04 17:52:59,061 - INFO - 已添加 12 条医院数据，共更新 348 个单元格
2025-07-04 17:52:59,071 - INFO - 已应用表头样式到工作表 【检验细菌结果表】 lis_bacteria_result
2025-07-04 17:52:59,072 - INFO - 已自动调整工作表 【检验细菌结果表】 lis_bacteria_result 的列宽
2025-07-04 17:52:59,072 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 12
2025-07-04 17:52:59,072 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-04 17:52:59,073 - INFO - 找到匹配的文件: coreflddq_lis_drug_sensitivity.txt
2025-07-04 17:52:59,073 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,108 - INFO - 删除行政区划不为空的所有行，删除前行数: 14
2025-07-04 17:52:59,108 - INFO - 删除后行数: 0
2025-07-04 17:52:59,109 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-04 17:52:59,120 - INFO - 已添加 8 条医院数据，共更新 248 个单元格
2025-07-04 17:52:59,130 - INFO - 已应用表头样式到工作表 【检验药敏结果表】 lis_drug_sensitivity
2025-07-04 17:52:59,131 - INFO - 已自动调整工作表 【检验药敏结果表】 lis_drug_sensitivity 的列宽
2025-07-04 17:52:59,131 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 8
2025-07-04 17:52:59,131 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-04 17:52:59,132 - INFO - 找到匹配的文件: coreflddq_exam_request_form.txt
2025-07-04 17:52:59,132 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,164 - INFO - 删除行政区划不为空的所有行，删除前行数: 13
2025-07-04 17:52:59,165 - INFO - 删除后行数: 0
2025-07-04 17:52:59,166 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-04 17:52:59,176 - INFO - 已添加 7 条医院数据，共更新 231 个单元格
2025-07-04 17:52:59,185 - INFO - 已应用表头样式到工作表 【检查申请表】 exam_request_form
2025-07-04 17:52:59,187 - INFO - 已自动调整工作表 【检查申请表】 exam_request_form 的列宽
2025-07-04 17:52:59,187 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 7
2025-07-04 17:52:59,187 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-04 17:52:59,187 - INFO - 找到匹配的文件: coreflddq_exam_report_master.txt
2025-07-04 17:52:59,188 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,247 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-04 17:52:59,248 - INFO - 删除后行数: 0
2025-07-04 17:52:59,249 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-04 17:52:59,280 - INFO - 已添加 17 条医院数据，共更新 850 个单元格
2025-07-04 17:52:59,296 - INFO - 已应用表头样式到工作表 【检查报告主表】 exam_report_master
2025-07-04 17:52:59,297 - INFO - 已自动调整工作表 【检查报告主表】 exam_report_master 的列宽
2025-07-04 17:52:59,298 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 17
2025-07-04 17:52:59,298 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-04 17:52:59,298 - INFO - 找到匹配的文件: coreflddq_exam_report_detail.txt
2025-07-04 17:52:59,298 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,332 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-04 17:52:59,334 - INFO - 删除后行数: 0
2025-07-04 17:52:59,334 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-04 17:52:59,358 - INFO - 已添加 18 条医院数据，共更新 450 个单元格
2025-07-04 17:52:59,369 - INFO - 已应用表头样式到工作表 【检查报告细表】 exam_report_detail
2025-07-04 17:52:59,370 - INFO - 已自动调整工作表 【检查报告细表】 exam_report_detail 的列宽
2025-07-04 17:52:59,370 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 18
2025-07-04 17:52:59,370 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-04 17:52:59,370 - INFO - 找到匹配的文件: coreflddq_exam_report_part.txt
2025-07-04 17:52:59,370 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,403 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-04 17:52:59,404 - INFO - 删除后行数: 0
2025-07-04 17:52:59,405 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-04 17:52:59,420 - INFO - 已添加 12 条医院数据，共更新 252 个单元格
2025-07-04 17:52:59,429 - INFO - 已应用表头样式到工作表 【检查报告部位表】 exam_report_part
2025-07-04 17:52:59,429 - INFO - 已自动调整工作表 【检查报告部位表】 exam_report_part 的列宽
2025-07-04 17:52:59,430 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 12
2025-07-04 17:52:59,430 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-04 17:52:59,430 - INFO - 找到匹配的文件: coreflddq_exam_sample_record.txt
2025-07-04 17:52:59,430 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,511 - INFO - 删除行政区划不为空的所有行，删除前行数: 12
2025-07-04 17:52:59,512 - INFO - 删除后行数: 0
2025-07-04 17:52:59,513 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-04 17:52:59,522 - INFO - 已添加 6 条医院数据，共更新 186 个单元格
2025-07-04 17:52:59,532 - INFO - 已应用表头样式到工作表 【病理标本记录表】 exam_sample_record
2025-07-04 17:52:59,532 - INFO - 已自动调整工作表 【病理标本记录表】 exam_sample_record 的列宽
2025-07-04 17:52:59,533 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 6
2025-07-04 17:52:59,695 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:52:59,695 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:52:59,774 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-04 17:52:59,775 - INFO - 找到匹配的文件: coreflddq_case_base_info.txt
2025-07-04 17:52:59,775 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,813 - INFO - 删除行政区划不为空的所有行，删除前行数: 27
2025-07-04 17:52:59,814 - INFO - 删除后行数: 0
2025-07-04 17:52:59,815 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-04 17:52:59,888 - INFO - 已添加 21 条医院数据，共更新 2058 个单元格
2025-07-04 17:52:59,912 - INFO - 已应用表头样式到工作表 【病案首页基本信息】 case_base_info
2025-07-04 17:52:59,915 - INFO - 已自动调整工作表 【病案首页基本信息】 case_base_info 的列宽
2025-07-04 17:52:59,915 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 21
2025-07-04 17:52:59,915 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-04 17:52:59,915 - INFO - 找到匹配的文件: coreflddq_case_diagnosis_record.txt
2025-07-04 17:52:59,916 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:52:59,943 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:52:59,944 - INFO - 删除后行数: 0
2025-07-04 17:52:59,944 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 17:52:59,974 - INFO - 已添加 22 条医院数据，共更新 550 个单元格
2025-07-04 17:52:59,985 - INFO - 已应用表头样式到工作表 【病案诊断记录表】 case_diagnosis_record
2025-07-04 17:52:59,986 - INFO - 已自动调整工作表 【病案诊断记录表】 case_diagnosis_record 的列宽
2025-07-04 17:52:59,986 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 22
2025-07-04 17:52:59,986 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-04 17:52:59,986 - INFO - 找到匹配的文件: coreflddq_case_operate_record.txt
2025-07-04 17:52:59,986 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:00,013 - INFO - 删除行政区划不为空的所有行，删除前行数: 22
2025-07-04 17:53:00,014 - INFO - 删除后行数: 0
2025-07-04 17:53:00,014 - INFO - 从JSON中提取了 14 条有效医院数据
2025-07-04 17:53:00,036 - INFO - 已添加 14 条医院数据，共更新 406 个单元格
2025-07-04 17:53:00,047 - INFO - 已应用表头样式到工作表 【病案手术记录表】 case_operate_record
2025-07-04 17:53:00,048 - INFO - 已自动调整工作表 【病案手术记录表】 case_operate_record 的列宽
2025-07-04 17:53:00,048 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 14
2025-07-04 17:53:00,048 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-04 17:53:00,048 - INFO - 找到匹配的文件: coreflddq_case_fee_record.txt
2025-07-04 17:53:00,048 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:00,106 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:53:00,106 - INFO - 删除后行数: 0
2025-07-04 17:53:00,107 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 17:53:00,144 - INFO - 已添加 22 条医院数据，共更新 1012 个单元格
2025-07-04 17:53:00,160 - INFO - 已应用表头样式到工作表 【病案费用记录表】 case_fee_record
2025-07-04 17:53:00,162 - INFO - 已自动调整工作表 【病案费用记录表】 case_fee_record 的列宽
2025-07-04 17:53:00,162 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 22
2025-07-04 17:53:00,262 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:53:00,262 - INFO - 正在处理Excel文件: 福建-医院质控问题清单模板.xlsx
2025-07-04 17:53:01,357 - INFO - 正在处理工作表: 医院列表
2025-07-04 17:53:01,357 - WARNING - 无法从sheet名称 '医院列表' 中提取英文部分
2025-07-04 17:53:01,357 - WARNING - 未能从 '医院列表' 提取英文名称，跳过此工作表
2025-07-04 17:53:01,379 - INFO - 正在处理工作表: 省属
2025-07-04 17:53:01,379 - WARNING - 无法从sheet名称 '省属' 中提取英文部分
2025-07-04 17:53:01,379 - WARNING - 未能从 '省属' 提取英文名称，跳过此工作表
2025-07-04 17:53:01,678 - INFO - 正在处理工作表: 福州市
2025-07-04 17:53:01,678 - WARNING - 无法从sheet名称 '福州市' 中提取英文部分
2025-07-04 17:53:01,678 - WARNING - 未能从 '福州市' 提取英文名称，跳过此工作表
2025-07-04 17:53:01,960 - INFO - 正在处理工作表: 厦门市
2025-07-04 17:53:01,961 - WARNING - 无法从sheet名称 '厦门市' 中提取英文部分
2025-07-04 17:53:01,961 - WARNING - 未能从 '厦门市' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,327 - INFO - 正在处理工作表: 莆田市
2025-07-04 17:53:02,328 - WARNING - 无法从sheet名称 '莆田市' 中提取英文部分
2025-07-04 17:53:02,328 - WARNING - 未能从 '莆田市' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,416 - INFO - 正在处理工作表: 泉州市
2025-07-04 17:53:02,416 - WARNING - 无法从sheet名称 '泉州市' 中提取英文部分
2025-07-04 17:53:02,416 - WARNING - 未能从 '泉州市' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,577 - INFO - 正在处理工作表: 漳州
2025-07-04 17:53:02,577 - WARNING - 无法从sheet名称 '漳州' 中提取英文部分
2025-07-04 17:53:02,577 - WARNING - 未能从 '漳州' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,665 - INFO - 正在处理工作表: 三明市
2025-07-04 17:53:02,665 - WARNING - 无法从sheet名称 '三明市' 中提取英文部分
2025-07-04 17:53:02,666 - WARNING - 未能从 '三明市' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,756 - INFO - 正在处理工作表: 龙岩市
2025-07-04 17:53:02,756 - WARNING - 无法从sheet名称 '龙岩市' 中提取英文部分
2025-07-04 17:53:02,756 - WARNING - 未能从 '龙岩市' 提取英文名称，跳过此工作表
2025-07-04 17:53:02,935 - INFO - 正在处理工作表: 宁德市
2025-07-04 17:53:02,935 - WARNING - 无法从sheet名称 '宁德市' 中提取英文部分
2025-07-04 17:53:02,935 - WARNING - 未能从 '宁德市' 提取英文名称，跳过此工作表
2025-07-04 17:53:03,154 - INFO - 正在处理工作表: 南平市
2025-07-04 17:53:03,155 - WARNING - 无法从sheet名称 '南平市' 中提取英文部分
2025-07-04 17:53:03,155 - WARNING - 未能从 '南平市' 提取英文名称，跳过此工作表
2025-07-04 17:53:03,262 - INFO - 正在处理工作表: Sheet12
2025-07-04 17:53:03,262 - WARNING - 未找到匹配的文件: coreflddq_Sheet12.txt
2025-07-04 17:53:03,263 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet12
2025-07-04 17:53:04,255 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_福建-医院质控问题清单模板.xlsx
2025-07-04 17:53:04,256 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:53:04,489 - INFO - 正在处理工作表: Sheet1
2025-07-04 17:53:04,489 - WARNING - 未找到匹配的文件: coreflddq_Sheet1.txt
2025-07-04 17:53:04,489 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-04 17:53:04,532 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-04 17:53:04,532 - INFO - 找到匹配的文件: coreflddq_outp_register_record.txt
2025-07-04 17:53:04,532 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:04,569 - INFO - 删除行政区划不为空的所有行，删除前行数: 32
2025-07-04 17:53:04,570 - INFO - 删除后行数: 0
2025-07-04 17:53:04,571 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 17:53:04,608 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-04 17:53:04,621 - INFO - 已应用表头样式到工作表 【门急诊挂号记录表】outp_register_record
2025-07-04 17:53:04,622 - INFO - 已自动调整工作表 【门急诊挂号记录表】outp_register_record 的列宽
2025-07-04 17:53:04,622 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 25
2025-07-04 17:53:04,622 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-04 17:53:04,623 - INFO - 找到匹配的文件: coreflddq_outp_visit_record.txt
2025-07-04 17:53:04,623 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:04,668 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-04 17:53:04,668 - INFO - 删除后行数: 0
2025-07-04 17:53:04,669 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 17:53:04,716 - INFO - 已添加 25 条医院数据，共更新 1000 个单元格
2025-07-04 17:53:04,734 - INFO - 已应用表头样式到工作表 【门急诊就诊记录表】outp_visit_record
2025-07-04 17:53:04,735 - INFO - 已自动调整工作表 【门急诊就诊记录表】outp_visit_record 的列宽
2025-07-04 17:53:04,736 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 25
2025-07-04 17:53:04,736 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-04 17:53:04,736 - INFO - 找到匹配的文件: coreflddq_outp_diagnosis_record.txt
2025-07-04 17:53:04,736 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:04,775 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-04 17:53:04,775 - INFO - 删除后行数: 0
2025-07-04 17:53:04,776 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 17:53:04,814 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-04 17:53:04,829 - INFO - 已应用表头样式到工作表 【门急诊断记录表】outp_diagnosis_record
2025-07-04 17:53:04,830 - INFO - 已自动调整工作表 【门急诊断记录表】outp_diagnosis_record 的列宽
2025-07-04 17:53:04,830 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 25
2025-07-04 17:53:04,830 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-04 17:53:04,831 - INFO - 找到匹配的文件: coreflddq_outp_order_master.txt
2025-07-04 17:53:04,831 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:04,900 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:53:04,901 - INFO - 删除后行数: 0
2025-07-04 17:53:04,902 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:53:04,939 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-04 17:53:04,952 - INFO - 已应用表头样式到工作表 【门诊医嘱主表】outp_order_master
2025-07-04 17:53:04,953 - INFO - 已自动调整工作表 【门诊医嘱主表】outp_order_master 的列宽
2025-07-04 17:53:04,954 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 24
2025-07-04 17:53:04,954 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-04 17:53:04,954 - INFO - 找到匹配的文件: coreflddq_outp_order_detail.txt
2025-07-04 17:53:04,954 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:04,999 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 17:53:05,000 - INFO - 删除后行数: 0
2025-07-04 17:53:05,001 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:53:05,046 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-04 17:53:05,067 - INFO - 已应用表头样式到工作表 【门诊医嘱明细表】outp_order_detail
2025-07-04 17:53:05,069 - INFO - 已自动调整工作表 【门诊医嘱明细表】outp_order_detail 的列宽
2025-07-04 17:53:05,069 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 23
2025-07-04 17:53:05,069 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-04 17:53:05,069 - INFO - 找到匹配的文件: coreflddq_outp_charge_detail.txt
2025-07-04 17:53:05,069 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:05,120 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:53:05,121 - INFO - 删除后行数: 0
2025-07-04 17:53:05,122 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:53:05,172 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-04 17:53:05,192 - INFO - 已应用表头样式到工作表 【门诊费用明细表】outp_charge_detail
2025-07-04 17:53:05,193 - INFO - 已自动调整工作表 【门诊费用明细表】outp_charge_detail 的列宽
2025-07-04 17:53:05,193 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 24
2025-07-04 17:53:05,194 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-04 17:53:05,194 - INFO - 找到匹配的文件: coreflddq_outp_settle_master.txt
2025-07-04 17:53:05,194 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:05,247 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 17:53:05,247 - INFO - 删除后行数: 0
2025-07-04 17:53:05,248 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 17:53:05,319 - INFO - 已添加 24 条医院数据，共更新 1536 个单元格
2025-07-04 17:53:05,350 - INFO - 已应用表头样式到工作表 【门诊结算主表】outp_settle_master
2025-07-04 17:53:05,352 - INFO - 已自动调整工作表 【门诊结算主表】outp_settle_master 的列宽
2025-07-04 17:53:05,352 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 24
2025-07-04 17:53:05,353 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-04 17:53:05,353 - INFO - 找到匹配的文件: coreflddq_outp_settle_detail.txt
2025-07-04 17:53:05,353 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 17:53:05,399 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 17:53:05,400 - INFO - 删除后行数: 0
2025-07-04 17:53:05,400 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 17:53:05,441 - INFO - 已添加 23 条医院数据，共更新 437 个单元格
2025-07-04 17:53:05,457 - INFO - 已应用表头样式到工作表 【门诊结算明细表】outp_settle_detail
2025-07-04 17:53:05,459 - INFO - 已自动调整工作表 【门诊结算明细表】outp_settle_detail 的列宽
2025-07-04 17:53:05,459 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 23
2025-07-04 17:53:05,677 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 17:53:05,677 - INFO - 处理完成，成功处理 5/5 个文件
2025-07-04 17:53:05,677 - INFO - 总耗时: 0:00:08.627855
2025-07-04 17:53:05,677 - INFO - === 处理结束 ===
