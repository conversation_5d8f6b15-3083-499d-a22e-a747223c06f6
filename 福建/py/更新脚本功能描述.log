2025-07-11 10:53:20,495 - INFO - 开始扫描Python脚本并更新文档...
2025-07-11 10:53:20,496 - INFO - 找到 57 个Python脚本
2025-07-11 10:53:20,504 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准word生成.md
2025-07-11 10:53:20,506 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准处理rid.md
2025-07-11 10:53:20,509 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准添加公共列.md
2025-07-11 10:53:20,510 - INFO - 已更新文档: D:\work\demo\福建\MD\check_code_name.md
2025-07-11 10:53:20,511 - INFO - 已更新文档: D:\work\demo\福建\MD\convert_to_excel.md
2025-07-11 10:53:20,514 - INFO - 已更新文档: D:\work\demo\福建\MD\excel生成.md
2025-07-11 10:53:20,516 - INFO - 已更新文档: D:\work\demo\福建\MD\excel生成31分册.md
2025-07-11 10:53:20,518 - INFO - 已更新文档: D:\work\demo\福建\MD\final_test.md
2025-07-11 10:53:20,521 - INFO - 已更新文档: D:\work\demo\福建\MD\generate_wide_table.md
2025-07-11 10:53:20,532 - INFO - 已更新文档: D:\work\demo\福建\MD\mysql转其他库.md
2025-07-11 10:53:20,534 - INFO - 已更新文档: D:\work\demo\福建\MD\oracle字段长度变更.md
2025-07-11 10:53:20,536 - INFO - 已更新文档: D:\work\demo\福建\MD\oracle更新sql.md
2025-07-11 10:53:20,537 - INFO - 已更新文档: D:\work\demo\福建\MD\test.md
2025-07-11 10:53:20,538 - INFO - 已更新文档: D:\work\demo\福建\MD\test_complex_regex.md
2025-07-11 10:53:20,540 - INFO - 已更新文档: D:\work\demo\福建\MD\test_date_conversion.md
2025-07-11 10:53:20,541 - INFO - 已更新文档: D:\work\demo\福建\MD\test_gaussdb.md
2025-07-11 10:53:20,542 - INFO - 已更新文档: D:\work\demo\福建\MD\test_process_single_sql.md
2025-07-11 10:53:20,543 - INFO - 已更新文档: D:\work\demo\福建\MD\test_simple.md
2025-07-11 10:53:20,544 - INFO - 已更新文档: D:\work\demo\福建\MD\test_user_example.md
2025-07-11 10:53:20,545 - INFO - 已更新文档: D:\work\demo\福建\MD\test_user_sql.md
2025-07-11 10:53:20,546 - INFO - 已更新文档: D:\work\demo\福建\MD\word生成.md
2025-07-11 10:53:20,548 - INFO - 已更新文档: D:\work\demo\福建\MD\word生成批注.md
2025-07-11 10:53:20,550 - INFO - 已更新文档: D:\work\demo\福建\MD\三医项目_数据采集标准规范20250421提取.md
2025-07-11 10:53:20,552 - INFO - 已更新文档: D:\work\demo\福建\MD\中文名转换英文名.md
2025-07-11 10:53:20,553 - INFO - 已更新文档: D:\work\demo\福建\MD\制作模板.md
2025-07-11 10:53:20,555 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_guass5.0.md
2025-07-11 10:53:20,557 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_mysql.md
2025-07-11 10:53:20,559 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_oracle11g.md
2025-07-11 10:53:20,561 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_sqlserver2017.md
2025-07-11 10:53:20,563 - INFO - 已更新文档: D:\work\demo\福建\MD\基卫平台数据结构提取.md
2025-07-11 10:53:20,565 - INFO - 已更新文档: D:\work\demo\福建\MD\处理rid.md
2025-07-11 10:53:20,567 - INFO - 已更新文档: D:\work\demo\福建\MD\字典抽取-岳群.md
2025-07-11 10:53:20,569 - INFO - 已更新文档: D:\work\demo\福建\MD\更新word.md
2025-07-11 10:53:20,570 - WARNING - 未找到对应的Markdown文档: D:\work\demo\福建\MD\更新脚本功能描述.md
2025-07-11 10:53:20,572 - INFO - 已更新文档: D:\work\demo\福建\MD\最小采集方案excel生成.md
2025-07-11 10:53:20,574 - INFO - 已更新文档: D:\work\demo\福建\MD\检测mysql字段长度.md
2025-07-11 10:53:20,576 - INFO - 已更新文档: D:\work\demo\福建\MD\添加公共列-new.md
2025-07-11 10:53:20,577 - INFO - 已更新文档: D:\work\demo\福建\MD\添加公共列.md
2025-07-11 10:53:20,579 - INFO - 已更新文档: D:\work\demo\福建\MD\添加行.md
2025-07-11 10:53:20,581 - INFO - 已更新文档: D:\work\demo\福建\MD\生成databend建表语句.md
2025-07-11 10:53:20,583 - INFO - 已更新文档: D:\work\demo\福建\MD\生成mysql建表语句.md
2025-07-11 10:53:20,586 - INFO - 已更新文档: D:\work\demo\福建\MD\生成oracle建表语句.md
2025-07-11 10:53:20,588 - INFO - 已更新文档: D:\work\demo\福建\MD\生成sqlserver建表语句.md
2025-07-11 10:53:20,590 - INFO - 已更新文档: D:\work\demo\福建\MD\生成全部建表sql.md
2025-07-11 10:53:20,595 - INFO - 已更新文档: D:\work\demo\福建\MD\生成四类alert脚本.md
2025-07-11 10:53:20,598 - INFO - 已更新文档: D:\work\demo\福建\MD\生成脚本说明文档.md
2025-07-11 10:53:20,601 - INFO - 已更新文档: D:\work\demo\福建\MD\生成达梦建表语句.md
2025-07-11 10:53:20,602 - INFO - 已更新文档: D:\work\demo\福建\MD\生成高斯数据库建表语句-精简版.md
2025-07-11 10:53:20,603 - INFO - 已更新文档: D:\work\demo\福建\MD\生成高斯数据库建表语句.md
2025-07-11 10:53:20,606 - INFO - 已更新文档: D:\work\demo\福建\MD\获取表名和字段名.md
2025-07-11 10:53:20,608 - ERROR - 更新文档 D:\work\demo\福建\MD\质控json转excel.md 时出错: bad escape \w at position 55 (line 4, column 26)
2025-07-11 10:53:20,613 - ERROR - 更新文档 D:\work\demo\福建\MD\质控问题清单模板json转excel.md 时出错: bad escape \w at position 64 (line 4, column 25)
2025-07-11 10:53:20,614 - ERROR - 更新文档 D:\work\demo\福建\MD\转换对照生成数转sql-处理字典关联-基卫.md 时出错: bad escape \w at position 76 (line 4, column 63)
2025-07-11 10:53:20,616 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成建表sql-医保.md
2025-07-11 10:53:20,619 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成建表sql-基卫.md
2025-07-11 10:53:20,622 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成转换sql-医保.md
2025-07-11 10:53:20,624 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成转换sql-基卫.md
2025-07-11 10:53:20,624 - INFO - 文档更新完成!
2025-07-11 10:54:33,849 - INFO - 开始扫描Python脚本并更新文档...
2025-07-11 10:54:33,850 - INFO - 找到 57 个Python脚本
2025-07-11 10:54:33,857 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准word生成.md
2025-07-11 10:54:33,859 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准处理rid.md
2025-07-11 10:54:33,862 - INFO - 已更新文档: D:\work\demo\福建\MD\31个数据标准添加公共列.md
2025-07-11 10:54:33,863 - INFO - 已更新文档: D:\work\demo\福建\MD\check_code_name.md
2025-07-11 10:54:33,864 - INFO - 已更新文档: D:\work\demo\福建\MD\convert_to_excel.md
2025-07-11 10:54:33,866 - INFO - 已更新文档: D:\work\demo\福建\MD\excel生成.md
2025-07-11 10:54:33,869 - INFO - 已更新文档: D:\work\demo\福建\MD\excel生成31分册.md
2025-07-11 10:54:33,871 - INFO - 已更新文档: D:\work\demo\福建\MD\final_test.md
2025-07-11 10:54:33,873 - INFO - 已更新文档: D:\work\demo\福建\MD\generate_wide_table.md
2025-07-11 10:54:33,884 - INFO - 已更新文档: D:\work\demo\福建\MD\mysql转其他库.md
2025-07-11 10:54:33,886 - INFO - 已更新文档: D:\work\demo\福建\MD\oracle字段长度变更.md
2025-07-11 10:54:33,888 - INFO - 已更新文档: D:\work\demo\福建\MD\oracle更新sql.md
2025-07-11 10:54:33,888 - INFO - 已更新文档: D:\work\demo\福建\MD\test.md
2025-07-11 10:54:33,890 - INFO - 已更新文档: D:\work\demo\福建\MD\test_complex_regex.md
2025-07-11 10:54:33,891 - INFO - 已更新文档: D:\work\demo\福建\MD\test_date_conversion.md
2025-07-11 10:54:33,892 - INFO - 已更新文档: D:\work\demo\福建\MD\test_gaussdb.md
2025-07-11 10:54:33,893 - INFO - 已更新文档: D:\work\demo\福建\MD\test_process_single_sql.md
2025-07-11 10:54:33,894 - INFO - 已更新文档: D:\work\demo\福建\MD\test_simple.md
2025-07-11 10:54:33,895 - INFO - 已更新文档: D:\work\demo\福建\MD\test_user_example.md
2025-07-11 10:54:33,896 - INFO - 已更新文档: D:\work\demo\福建\MD\test_user_sql.md
2025-07-11 10:54:33,897 - INFO - 已更新文档: D:\work\demo\福建\MD\word生成.md
2025-07-11 10:54:33,899 - INFO - 已更新文档: D:\work\demo\福建\MD\word生成批注.md
2025-07-11 10:54:33,901 - INFO - 已更新文档: D:\work\demo\福建\MD\三医项目_数据采集标准规范20250421提取.md
2025-07-11 10:54:33,903 - INFO - 已更新文档: D:\work\demo\福建\MD\中文名转换英文名.md
2025-07-11 10:54:33,904 - INFO - 已更新文档: D:\work\demo\福建\MD\制作模板.md
2025-07-11 10:54:33,906 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_guass5.0.md
2025-07-11 10:54:33,907 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_mysql.md
2025-07-11 10:54:33,909 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_oracle11g.md
2025-07-11 10:54:33,910 - INFO - 已更新文档: D:\work\demo\福建\MD\医院184质控sql添加包装_sqlserver2017.md
2025-07-11 10:54:33,913 - INFO - 已更新文档: D:\work\demo\福建\MD\基卫平台数据结构提取.md
2025-07-11 10:54:33,914 - INFO - 已更新文档: D:\work\demo\福建\MD\处理rid.md
2025-07-11 10:54:33,916 - INFO - 已更新文档: D:\work\demo\福建\MD\字典抽取-岳群.md
2025-07-11 10:54:33,918 - INFO - 已更新文档: D:\work\demo\福建\MD\更新word.md
2025-07-11 10:54:33,918 - WARNING - 未找到对应的Markdown文档: D:\work\demo\福建\MD\更新脚本功能描述.md
2025-07-11 10:54:33,921 - INFO - 已更新文档: D:\work\demo\福建\MD\最小采集方案excel生成.md
2025-07-11 10:54:33,922 - INFO - 已更新文档: D:\work\demo\福建\MD\检测mysql字段长度.md
2025-07-11 10:54:33,924 - INFO - 已更新文档: D:\work\demo\福建\MD\添加公共列-new.md
2025-07-11 10:54:33,926 - INFO - 已更新文档: D:\work\demo\福建\MD\添加公共列.md
2025-07-11 10:54:33,927 - INFO - 已更新文档: D:\work\demo\福建\MD\添加行.md
2025-07-11 10:54:33,929 - INFO - 已更新文档: D:\work\demo\福建\MD\生成databend建表语句.md
2025-07-11 10:54:33,932 - INFO - 已更新文档: D:\work\demo\福建\MD\生成mysql建表语句.md
2025-07-11 10:54:33,934 - INFO - 已更新文档: D:\work\demo\福建\MD\生成oracle建表语句.md
2025-07-11 10:54:33,937 - INFO - 已更新文档: D:\work\demo\福建\MD\生成sqlserver建表语句.md
2025-07-11 10:54:33,938 - INFO - 已更新文档: D:\work\demo\福建\MD\生成全部建表sql.md
2025-07-11 10:54:33,943 - INFO - 已更新文档: D:\work\demo\福建\MD\生成四类alert脚本.md
2025-07-11 10:54:33,945 - INFO - 已更新文档: D:\work\demo\福建\MD\生成脚本说明文档.md
2025-07-11 10:54:33,947 - INFO - 已更新文档: D:\work\demo\福建\MD\生成达梦建表语句.md
2025-07-11 10:54:33,949 - INFO - 已更新文档: D:\work\demo\福建\MD\生成高斯数据库建表语句-精简版.md
2025-07-11 10:54:33,950 - INFO - 已更新文档: D:\work\demo\福建\MD\生成高斯数据库建表语句.md
2025-07-11 10:54:33,952 - INFO - 已更新文档: D:\work\demo\福建\MD\获取表名和字段名.md
2025-07-11 10:54:33,955 - INFO - 已更新文档: D:\work\demo\福建\MD\质控json转excel.md
2025-07-11 10:54:33,960 - INFO - 已更新文档: D:\work\demo\福建\MD\质控问题清单模板json转excel.md
2025-07-11 10:54:33,962 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-处理字典关联-基卫.md
2025-07-11 10:54:33,964 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成建表sql-医保.md
2025-07-11 10:54:33,966 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成建表sql-基卫.md
2025-07-11 10:54:33,968 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成转换sql-医保.md
2025-07-11 10:54:33,970 - INFO - 已更新文档: D:\work\demo\福建\MD\转换对照生成数转sql-生成转换sql-基卫.md
2025-07-11 10:54:33,970 - INFO - 文档更新完成!
