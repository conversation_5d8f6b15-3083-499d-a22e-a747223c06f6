import pandas as pd
import os
from collections import defaultdict

def read_excel_data(file_path):
    """读取Excel文件数据"""
    df = pd.read_excel(file_path, sheet_name='制作模板')
    relation_df = pd.read_excel(file_path, sheet_name='关联关系')
    return df, relation_df

def process_table_data(df, relation_df):
    """处理表格数据，按表名分组"""
    tables_by_name = defaultdict(list)
    current_table = None
    table_data = []

    # 遍历所有行
    for _, row in df.iterrows():
        # 如果遇到新的表名
        if pd.notna(row['表名']):
            # 保存之前的表数据
            if current_table and table_data:
                tables_by_name[current_table].append({
                    'table_name': current_table,
                    'table_cn_name': table_data[0]['表中文名'],
                    'fields': table_data
                })
            # 开始新的表
            current_table = row['表名']
            table_data = []

        # 如果该行有字段信息
        if pd.notna(row['字段名']):
            table_data.append({
                '表中文名': row['表中文名'],
                '表名': row['表名'],
                '字段名': row['字段名'],
                '数据项': row['数据项'],
                '数据类型': row['数据类型'],
                '说明': row['说明'],
                '是否主键': row['是否主键']
            })

    # 添加最后一个表
    if current_table and table_data:
        tables_by_name[current_table].append({
            'table_name': current_table,
            'table_cn_name': table_data[0]['表中文名'],
            'fields': table_data
        })

    return tables_by_name, relation_df

def generate_sql(tables_by_name, relation_df):
    """生成SQL语句"""
    # 获取所有表名
    all_tables = list(tables_by_name.keys())
    #获取tables_by_name的第一个表的中文名
    main_table_cn_name = tables_by_name[all_tables[0]][0]['table_cn_name']
    if not all_tables:
        return []

    # 确定主表（第一个表）
    main_table_name = all_tables[0]
    wide_table_name = f"fct0_{main_table_name}"

    # 构建SELECT语句
    select_fields = []
    field_comments = []
    used_fields = set()

    # 从制作模板中获取所有表的字段
    field_list = []

    for table_name in all_tables:
        if table_name in tables_by_name:
            for table in tables_by_name[table_name]:
                for field in table['fields']:
                    field_name = field['字段名']
                    if field_name not in used_fields:
                        field_chinese_name = field['数据项']
                        comment = f"{field_chinese_name} {field['说明']}" if pd.notna(field['说明']) else field_chinese_name
                        # 去除注释中的换行符
                        comment = comment.replace('\n', ' ').replace('\r', ' ').strip()
                        field_str = f"    {table_name}.{field_name}"
                        field_list.append((field_str, comment))
                        used_fields.add(field_name)

    # 格式化字段列表
    total_unique_fields = len(field_list)
    for idx, (field_str, comment) in enumerate(field_list):
        is_last = idx == total_unique_fields - 1
        select_fields.append(f"{field_str}{', ' if not is_last else ''} -- {comment}")

    # 构建JOIN语句
    join_statements = []
    processed_tables = {main_table_name}

    # 处理关联关系
    while len(processed_tables) < len(all_tables):
        for _, relation in relation_df.iterrows():
            table_name = relation['表名']
            join_table_name = relation['关联表名']

            if table_name in processed_tables and join_table_name not in processed_tables:
                # 构建关联条件
                join_condition = f"{table_name}.{relation['字段名']} = {join_table_name}.{relation['关联字段名']}"
                join_statements.append(f"    LEFT JOIN {join_table_name} ON {join_condition}")
                processed_tables.add(join_table_name)
                break

    # 生成SELECT语句部分
    select_clause = "SELECT\n" + "\n".join(select_fields)

    # 生成FROM和JOIN部分
    from_clause = f"\nFROM\n    {main_table_name}"
    if join_statements:
        from_clause += "\n" + "\n".join(join_statements)

    # 生成CREATE TABLE语句
    sql_statements = []
    sql_statements.append(f"-- 删除已存在的表")
    sql_statements.append(f"DROP TABLE IF EXISTS {wide_table_name};")
    sql_statements.append(f"\n-- 生成宽表-{main_table_cn_name}")
    sql_statements.append(f"CREATE TABLE {wide_table_name} AS")
    sql_statements.append(f"{select_clause}{from_clause};")

    full_sql = "\n".join(sql_statements)

    return [full_sql, main_table_name]

def main():
    input_file = "../宽表设计.xlsx"

    # 读取Excel数据
    df, relation_df = read_excel_data(input_file)

    # 打印读取到的数据
    print("读取到的表数据：")
    print(df)
    print("\n读取到的关联关系：")
    print(relation_df)

    # 处理数据
    tables_by_name, relation_df = process_table_data(df, relation_df)

    # 打印处理后的表结构
    print("\n处理后的表结构：")
    for table_name, tables in tables_by_name.items():
        print(f"\n表名: {table_name}")
        for table in tables:
            print(f"表中文名: {table['table_cn_name']}")
            print("字段列表:")
            for field in table['fields']:
                print(f"  - {field['字段名']}")

    # 生成SQL
    sql_statements, main_table_name = generate_sql(tables_by_name, relation_df)
    output_file = f"fct0_{main_table_name}.sql"

    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(sql_statements)

    print(f"\nSQL文件已生成: {output_file}")

if __name__ == "__main__":
    main()
