import os
from docx import Document
import pandas as pd
import re

def clean_title(title):
    """清理表名，去除章节编号和特殊字符"""
    # 去掉标题开头的数字和点，并清理空白
    cleaned = re.sub(r'^\d+\.?\d*\s*', '', title)
    # 移除多余的空白字符
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 移除特殊字符
    cleaned = re.sub(r'[【】\[\]（）\(\)]', '', cleaned)
    # 移除表格、图等词
    cleaned = re.sub(r'^(表|图)\s*\d*\s*', '', cleaned)
    return cleaned.strip()

def process_word_file(file_path):
    # 跳过临时文件
    if os.path.basename(file_path).startswith('~$'):
        print(f"跳过临时文件: {file_path}")
        return []

    print(f"开始处理文件: {file_path}")
    doc = Document(file_path)
    results = []

    # 初始化变量
    current_table_name = None
    last_heading = None

    # 遍历文档中的所有元素，按顺序收集表名和处理表格
    for element in doc.element.body.iter():
        if element.tag.endswith('p'):  # 段落
            # 获取段落对象
            for para in doc.paragraphs:
                if para._element == element:
                    # 检查是否是标题样式
                    if hasattr(para, 'style') and para.style:
                        style_name = para.style.name
                        if ('Heading' in style_name or '标题' in style_name or 'toc' in style_name):
                            text = para.text.strip()
                            if text:
                                last_heading = clean_title(text)
                                print(f"找到标题: {last_heading}")
                    # 如果不是标题样式但有内容，也可能是表格标题
                    elif para.text.strip():
                        last_heading = clean_title(para.text)
                    break

        elif element.tag.endswith('tbl'):  # 表格
            # 找到对应的表格对象
            for table in doc.tables:
                if table._element == element:
                    if len(table.rows) < 2:  # 跳过少于2行的表格
                        print("表格行数少于2行，跳过")
                        continue

                    # 获取表头
                    headers = [cell.text.strip() for cell in table.rows[0].cells]
                    print(f"表头: {headers}")

                    # 检查是否是目标表格格式
                    if not (
                        set(headers) == set(['序号', '字段英文名', '字段中文名', '字段注释', '必填', '字段类型'])
                    ):
                        print("表头格式不匹配，跳过")
                        continue

                    # 使用最近的标题作为表格标题
                    current_table_name = last_heading
                    print(f"使用标题: {current_table_name}")

                    if not current_table_name:
                        # 如果没有找到标题，使用上一个表格的标题（如果有的话）
                        if results:
                            current_table_name = results[-1]['表全名']
                            print(f"使用上一个表格的标题: {current_table_name}")
                        else:
                            print("未获取到表格标题，跳过")
                            continue

                    # 确定列的映射
                    if '名称' in headers:
                        name_idx = headers.index('名称')
                        code_idx = headers.index('代码')
                        type_idx = headers.index('数值类型')
                        required_idx = headers.index('是否为空')
                        desc_idx = headers.index('注释' if '注释' in headers else '备注')
                    else:
                        name_idx = headers.index('字段中文名')
                        code_idx = headers.index('字段英文名')
                        type_idx = headers.index('字段类型')
                        required_idx = headers.index('必填')
                        desc_idx = headers.index('字段注释')

                    # 处理每一行数据
                    for row in table.rows[1:]:
                        cells = [cell.text.strip() for cell in row.cells]
                        if all(not cell for cell in cells):  # 跳过空行
                            continue

                        results.append({
                            '表全名': current_table_name,
                            '表中文名': current_table_name,
                            '表名': '',  # 留空
                            '数据项': cells[name_idx],
                            '字段名': cells[code_idx],
                            '数据类型': cells[type_idx],
                            '长度': '',  # 留空
                            '填报要求': cells[desc_idx],
                            '是否主键': '',  # 留空
                            '说明': cells[required_idx]
                        })
                    break

    print(f"从文件中提取到 {len(results)} 条记录")
    return results

def main():
    input_dir = r'D:\work\demo\福建\待提取'
    output_file = r'D:\work\demo\福建\三医项目_数据采集标准规范20250421(公卫数据).xlsx'

    all_results = []

    # 处理目录下所有的Word文件
    word_files = [f for f in os.listdir(input_dir) if f.endswith('.docx')]
    print(f"找到 {len(word_files)} 个Word文件")

    for filename in word_files:
        file_path = os.path.join(input_dir, filename)
        try:
            results = process_word_file(file_path)
            all_results.extend(results)
            print(f"成功处理文件: {filename}")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

    if all_results:
        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(all_results)
        df.to_excel(output_file, index=False)
        print(f"数据已成功导出到: {output_file}")
        print(f"总共提取了 {len(all_results)} 条记录")
    else:
        print("未找到符合条件的表格数据")

if __name__ == '__main__':
    main()
