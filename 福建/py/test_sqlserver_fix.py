# -*- coding: utf-8 -*-
"""
测试SQL Server子查询列名修复
"""

from 质控mysql转其他库 import translate_sql

def test_sqlserver_subquery_fix():
    """测试SQL Server子查询列名修复"""
    print("===== 测试SQL Server子查询列名修复 =====")
    
    # 用户提供的问题SQL
    problem_sql = """SELECT
	'patient_allergy_record' AS tabname,
	'YJ01202506192001' AS rule_no,
	'数据不满足5年和月连续性' AS rule_dscr,
	DATEDIFF( MONTH, '2020-01-01 00:00:00', DATEADD( MONTH, - 1, CAST ( GETDATE( ) AS DATE ) ) ) - COUNT ( 1 ) AS biz_info 
FROM
	( SELECT 1 FROM patient_allergy_record A WHERE A.rec_time >= CONVERT ( DATETIME, '2020-01-01 00:00:00', 120 ) GROUP BY FORMAT ( A.rec_time, 'YMM%' ) ) T;"""
    
    print("问题SQL:")
    print(problem_sql)
    print()
    
    # 测试各种包含 SELECT 1 的情况
    test_cases = [
        {
            'name': '简单的SELECT 1子查询',
            'sql': 'SELECT COUNT(*) FROM (SELECT 1 FROM table1) T'
        },
        {
            'name': '复杂的SELECT 1子查询',
            'sql': 'SELECT a, b FROM (SELECT 1 FROM table1 WHERE condition) T'
        },
        {
            'name': '多个SELECT 1',
            'sql': 'SELECT * FROM (SELECT 1 FROM t1) T1 UNION SELECT * FROM (SELECT 1 FROM t2) T2'
        },
        {
            'name': '嵌套的SELECT 1',
            'sql': 'SELECT * FROM (SELECT COUNT(*) FROM (SELECT 1 FROM inner_table) IT) OT'
        },
        {
            'name': '用户的实际问题SQL',
            'sql': problem_sql
        }
    ]
    
    for case in test_cases:
        print(f"--- {case['name']} ---")
        print(f"原始SQL: {case['sql']}")
        
        # 转换为SQL Server
        sqlserver_result = translate_sql(case['sql'], 'sqlserver')
        print(f"SQL Server转换: {sqlserver_result}")
        
        # 检查修复结果
        if 'SELECT 1 FROM' in sqlserver_result:
            print("❌ 仍然存在 'SELECT 1 FROM' 问题")
        elif 'SELECT 1 as dummy_col FROM' in sqlserver_result:
            print("✅ 已修复：'SELECT 1' 已添加列名")
        else:
            print("ℹ️  没有发现 'SELECT 1' 模式")
        
        # 检查其他潜在问题
        issues = []
        
        # 检查是否有未命名的列
        if ' 1 FROM' in sqlserver_result and 'SELECT 1 as' not in sqlserver_result:
            issues.append("可能存在未命名的列")
        
        # 检查DATEADD语法
        if 'DATEADD(' in sqlserver_result and ', -, ' in sqlserver_result:
            issues.append("DATEADD语法错误")
        
        if issues:
            print(f"⚠️  其他问题: {issues}")
        else:
            print("✅ 未发现其他语法问题")
        
        print()

def test_edge_cases():
    """测试边缘情况"""
    print("===== 测试边缘情况 =====")
    
    edge_cases = [
        {
            'name': 'SELECT 1 在不同位置',
            'sql': 'select 1 from table1 union select 1 from table2'
        },
        {
            'name': 'SELECT 1 with spaces',
            'sql': 'SELECT   1   FROM table1'
        },
        {
            'name': 'select 1 小写',
            'sql': 'select 1 from table1'
        },
        {
            'name': 'SELECT 1 已有别名',
            'sql': 'SELECT 1 as existing_alias FROM table1'
        },
        {
            'name': 'SELECT count(1)',
            'sql': 'SELECT count(1) FROM table1'
        }
    ]
    
    for case in edge_cases:
        print(f"--- {case['name']} ---")
        print(f"原始: {case['sql']}")
        
        result = translate_sql(case['sql'], 'sqlserver')
        print(f"转换: {result}")
        
        # 分析结果
        if 'SELECT 1 FROM' in result.upper():
            print("❌ 仍有未命名的SELECT 1")
        elif 'SELECT 1 AS DUMMY_COL FROM' in result.upper():
            print("✅ 正确添加了列别名")
        elif 'SELECT 1 AS EXISTING_ALIAS FROM' in result.upper():
            print("✅ 保持了原有别名")
        elif 'COUNT(1)' in result.upper():
            print("✅ COUNT(1)格式正确")
        else:
            print("ℹ️  其他情况")
        
        print()

if __name__ == "__main__":
    test_sqlserver_subquery_fix()
    test_edge_cases()
