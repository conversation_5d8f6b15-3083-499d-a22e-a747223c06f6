# -*- coding: utf-8 -*-
"""
Created on Thu Jan 16 11:48:15 2025

@author: chenlu
"""

# -*- coding: utf-8 -*-
"""
Created on Fri Jun  5 11:45:52 2020

@author: Administrator

"""

from datetime import date, datetime

import docx
from docx.enum.table import WD_TABLE_ALIGNMENT,WD_CELL_VERTICAL_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH,WD_PARAGRAPH_ALIGNMENT

from docx import Document

import pandas as pd

from docx.oxml.ns import qn
from docx.shared import Pt,Cm,Inches
from copy import deepcopy
import lxml
from docx.oxml import OxmlElement
from docx.oxml.shared import OxmlElement
from docx.opc.constants import RELATIONSHIP_TYPE as RT

file_path= r'/福建'

excel_name='三医数据采集标准梳理.xlsx'

data=pd.read_excel(file_path+"""\\"""+excel_name)
data=data.fillna('')
data=data[data['数据项']!='']

file_names = list(data['文件名称'].drop_duplicates())

def create_element(name):
    """创建XML元素"""
    return OxmlElement(name)

def add_comment(document, paragraph, text, author='neusoft'):
    """为文档添加批注"""
    # 确保文档有comments part
    if not hasattr(document.part, '_comments_part'):
        from docx.opc.part import XmlPart
        from docx.opc.constants import CONTENT_TYPE as CT
        from docx.opc.packuri import PackURI

        # 创建comments part
        partname = PackURI('/word/comments.xml')
        content_type = CT.WML_COMMENTS
        comments_xml = '<w:comments xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"/>'

        comments_part = XmlPart.load(
            partname,
            content_type,
            comments_xml.encode('utf-8'),
            document.part.package
        )

        document.part._comments_part = comments_part
        document.part.rels.add_relationship(
            RT.COMMENTS,
            comments_part,
            rId='rId0'
        )

    try:
        comments_part = document.part._comments_part._element
        comments = comments_part.findall('.//{%s}comment' % qn('w:'))
        next_id = len(comments) if comments else 0

        comment = create_element('w:comment')
        comment.set(qn('w:id'), str(next_id))
        comment.set(qn('w:author'), author)
        comment.set(qn('w:date'), datetime.now().isoformat())
        comment.set(qn('w:initials'), author[:2])

        p = create_element('w:p')
        r = create_element('w:r')
        t = create_element('w:t')
        t.text = text
        r.append(t)
        p.append(r)
        comment.append(p)

        comments_part.append(comment)

        comment_reference = create_element('w:commentReference')
        comment_reference.set(qn('w:id'), str(next_id))

        comment_range_start = create_element('w:commentRangeStart')
        comment_range_start.set(qn('w:id'), str(next_id))

        comment_range_end = create_element('w:commentRangeEnd')
        comment_range_end.set(qn('w:id'), str(next_id))

        return comment_reference, comment_range_start, comment_range_end
    except Exception as e:
        print(f"添加批注时发生错误: {str(e)}")
        return None, None, None

def add_revision(cell, original_value, local_value, document):
    """添加修订（删除原文本，插入新文本）"""
    try:
        paragraph = cell.paragraphs[0]

        # 清除现有内容
        for child in paragraph._p[:]:
            paragraph._p.remove(child)

        # 创建删除修订
        del_run = paragraph.add_run(original_value)
        del_run._r.set(qn('w:author'), 'neusoft')
        del_run._r.set(qn('w:date'), datetime.now().isoformat())
        del_element = create_element('w:del')
        del_element.append(del_run._r)
        paragraph._p.append(del_element)

        # 创建插入修订
        ins_run = paragraph.add_run(local_value)
        ins_run.font.size = Pt(9)
        ins_run.font.name = u'宋体'
        ins_run._r.set(qn('w:author'), 'neusoft')
        ins_run._r.set(qn('w:date'), datetime.now().isoformat())
        ins_element = create_element('w:ins')
        ins_element.append(ins_run._r)
        paragraph._p.append(ins_element)

    except Exception as e:
        print(f"处理修订时发生错误: {str(e)}")

def add_comment_if_different(cell, original_value, local_value, document):
    """如果原始值和本地化值不同，添加批注"""
    if local_value and original_value != local_value:
        try:
            # 清除现有段落内容
            paragraph = cell.paragraphs[0]
            paragraph._p.clear_content()

            # 创建新的 run
            run = paragraph.add_run(original_value)
            run.font.size = Pt(9)
            run.font.name = u'宋体'

            # 获取批注元素
            comment_ref, range_start, range_end = add_comment(document, paragraph, local_value)

            if comment_ref is not None and range_start is not None and range_end is not None:
                # 将批注标记添加到正确的位置
                paragraph._p.insert(0, range_start)  # 在段落开始处添加范围开始标记
                run._r.append(comment_ref)  # 在run中添加引用
                paragraph._p.append(range_end)  # 在段落末尾添加范围结束标记

        except Exception as e:
            print(f"处理批注时发生错误: {str(e)}")

# 定义需要对比的字段对
field_pairs = [
    ('数据项', '数据项本地化'),
    ('字段名', '字段名本地化'),
    ('数据类型', '数据类型本地化'),
    ('长度', '长度本地化')
]

# 在处理数据前，检查并添加缺失的列
required_columns = ['数据项', '字段名', '数据类型', '长度', '填报要求', '是否主键', '说明']
localized_columns = ['数据项本地化', '字段名本地化', '数据类型本地化', '长度本地化']

# 确保所有需要的列都存在
for col in required_columns + localized_columns:
    if col not in data.columns:
        print(f"警告: 缺少列 {col}，将添加空值列")
        data[col] = ''

for file_name in file_names:
    data_in = data[data['文件名称']==file_name].copy()
    uniques = list(data_in['表名'].drop_duplicates())

    document = docx.Document(file_path+"""\\模板\\"""+file_name)#打开固定文档进行操作

    # 获取所有表格
    all_tables = document.tables
    # 保留最后一个表格作为模板
    tables = [all_tables[-1]] if all_tables else []

    for i in range(len(uniques)):
        tb_name = uniques[i]
        if tb_name=='':
            print('warning---------------表名有空')
            continue

        # 获取完整的数据，包括本地化字段
        tb = data_in[data_in['表名']==tb_name].copy()

        # 获取用于显示的列
        display_columns = ['数据项', '字段名', '数据类型', '长度','填报要求', '是否主键','说明']

        # 添加表名作为标题
        para_heading = document.add_heading('', level=2)  # 返回2级标题段落对象
        para_heading.add_run(tb_name)  # 这个使用的是"Heading 2" 的样式标题是2级目录

        print('开始写入',file_name,'----------',tb_name,'-----------',i,'/',len(uniques),'字段个数',len(tb))

        # 复制固定模板表格
        table_copy = deepcopy(tables[0])
        document.paragraphs[-1]._p.addnext(table_copy._tbl)

        for row_id in range(len(tb)):
            row = table_copy.add_row()
            for col in range(7):
                cell = row.cells[col]
                base_value = str(tb.iloc[row_id][display_columns[col]])
                current_field = display_columns[col]

                # 检查是否需要添加批注
                needs_comment = False
                local_value = None

                # 查找对应的本地化字段
                for orig_field, local_field in field_pairs:
                    if orig_field == current_field and local_field in tb.columns:
                        local_value = str(tb.iloc[row_id][local_field])
                        if local_value and base_value != local_value:
                            needs_comment = True
                            break

                # 根据是否需要批注分别处理
                if needs_comment:
                    add_comment_if_different(cell, base_value, local_value, document)
                else:
                    cell.text = base_value
                    paragraph = cell.paragraphs[0]
                    run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                    run.font.size = Pt(9)
                    run.font.name = u'宋体'

                # 设置格式
                cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
                paragraph = cell.paragraphs[0]
                if col in [4,5]:
                    paragraph.paragraph_format.alignment = WD_TABLE_ALIGNMENT.CENTER
                else:
                    paragraph.paragraph_format.alignment = WD_TABLE_ALIGNMENT.LEFT

    t = tables[0]._element # 获取模板表格元素
    t.getparent().remove(t) # 删除模板表格

    #以下自动更新目录
    namespace = "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}"
    element_updatefields = lxml.etree.SubElement(document.settings.element, namespace+"updateFields")
    element_updatefields.set(namespace+"val", "true")

    outputpath = file_path+r"\生成的word"
    document.save(outputpath+"""\\"""+file_name[:-5]+' - '+str(date.today()).replace('-','')+'.docx')   # 保存文档






