import pandas as pd

def process_excel():
    # 读取Excel文件
    file_path = '../三医数据采集标准梳理.xlsx'
    df = pd.read_excel(file_path)

    # 找到"系统建设厂商名称"所在的行索引
    target_rows = df[df['数据项'] == '系统建设厂商名称'].index

    # 对每个找到的位置进行处理
    for row_idx in sorted(target_rows, reverse=True):  # 从后向前处理，避免索引变化
        # 复制当前行的数据
        new_row = df.iloc[row_idx].copy()
        # 修改新行的"数据项"列的值
        new_row['数据项'] = '系统建设厂商代码'

        # 在当前行之前插入新行
        df = pd.concat([
            df.iloc[:row_idx],
            pd.DataFrame([new_row]),
            df.iloc[row_idx:]
        ]).reset_index(drop=True)

    # 保存修改后的文件
    output_file = '三医数据采集标准梳理_已修改.xlsx'
    df.to_excel(output_file, index=False)
    print(f'文件已保存为: {output_file}')

if __name__ == '__main__':
    try:
        process_excel()
        print('处理完成！')
    except Exception as e:
        print(f'发生错误: {str(e)}')
