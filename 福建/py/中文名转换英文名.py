import pandas as pd
import jieba
import re


def load_abbreviations(excel_path):
    """加载缩略语对照表"""
    df = pd.read_excel(excel_path, sheet_name='列表')
    # 创建中文到英文缩写的映射字典
    abbr_dict = dict(zip(df['中文名称'], df['英文缩写']))
    return abbr_dict


def preprocess_chinese_name(text):
    """预处理中文名称：
    1. 去掉所有中英文括号及其中的内容
    2. 去掉所有'-'符号
    3. 去掉所有空格
    4. 去掉所有中英文冒号和顿号
    """
    # 去掉英文小括号()及其中的内容
    text = re.sub(r'\([^)]*\)', '', text)
    # 去掉英文中括号[]及其中的内容
    text = re.sub(r'\[[^\]]*\]', '', text)
    # 去掉英文大括号{}及其中的内容
    text = re.sub(r'\{[^}]*\}', '', text)
    # 去掉中文小括号（）及其中的内容
    text = re.sub(r'（[^）]*）', '', text)
    # 去掉中文中括号【】及其中的内容
    text = re.sub(r'【[^】]*】', '', text)
    # 去掉所有'-'符号
    text = text.replace('-', '')
    # 去掉所有空格
    text = text.replace(' ', '')
    # 去掉中英文冒号
    text = text.replace(':', '')
    text = text.replace('：', '')
    # 去掉顿号
    text = text.replace('、', '')
    text = text.replace('/', '')
    text = text.replace('_', '')
    text = text.replace('－', '')
    return text


def convert_chinese_to_english(text, abbr_dict):
    """将中文转换为英文，并返回未找到的中文词"""
    # 预处理文本
    text = preprocess_chinese_name(text)
    
    # 使用jieba进行分词
    words = list(jieba.cut(text))

    # 转换每个词
    converted_words = []
    not_found_words = []
    for word in words:
        # 如果词在缩略语字典中，使用对应的英文缩写
        if word in abbr_dict:
            converted_words.append(abbr_dict[word])
        else:
            # 否则保持原样，并记录未找到的中文词
            converted_words.append(word)
            # 只记录非空白字符的中文词
            if word.strip() and re.search(r'[\u4e00-\u9fff]', word):
                not_found_words.append(word)

    # 用下划线连接所有词并转换为小写
    result = '_'.join(converted_words).lower()
    return result, not_found_words


def main():
    # 文件路径
    abbr_excel_path = r'D:\work\demo\福建\数据组-缩略语-20250414165127.xlsx'
    input_file_path = r'D:\work\demo\福建\中文名转换英文名.txt'
    output_excel_path = r'D:\work\demo\福建\中文名转换英文名结果.xlsx'

    # 加载缩略语对照表
    abbr_dict = load_abbreviations(abbr_excel_path)

    # 读取待转换的中文名
    with open(input_file_path, 'r', encoding='utf-8') as f:
        chinese_names = [line.strip() for line in f if line.strip()]

    # 转换每个中文名
    results = []
    all_not_found_words = set()  # 使用集合去重

    for name in chinese_names:
        english_name, not_found_words = convert_chinese_to_english(name, abbr_dict)
        results.append({
            '中文名': name,
            '英文名': english_name
        })
        # 添加未找到的中文词到集合中
        all_not_found_words.update(not_found_words)

    # 创建DataFrame并保存到Excel
    df_results = pd.DataFrame(results)

    # 创建未找到缩略语的中文词DataFrame
    df_not_found = pd.DataFrame({'未找到缩略语的中文词': list(all_not_found_words)})

    # 使用ExcelWriter保存多个sheet
    with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
        df_results.to_excel(writer, sheet_name='转换结果', index=False)
        df_not_found.to_excel(writer, sheet_name='未找到缩略语', index=False)

    print(f"转换完成，结果已保存到: {output_excel_path}")
    print(f"共有 {len(all_not_found_words)} 个中文词未找到对应缩略语")


if __name__ == "__main__":
    main()