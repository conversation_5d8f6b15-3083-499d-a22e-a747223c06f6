import docx
import pandas as pd
import os
from datetime import date
import re

def clean_table_name(text: str) -> str:
    """清理表名，去除章节编号"""
    # 定义特殊表名映射
    special_tables = {
        '24h内入出院记录': '24h内入出院记录（emr_inout_rec_in24h）',
        '24h内入院死亡记录': '24h内入院死亡记录（emr_inhosp_die_in24h）',
        '8条目痴呆筛查问卷': '8条目痴呆筛查问卷（sdd_ad_ad8）'
    }

    # 检查原始文本是否包含特殊表名
    text = text.strip()
    for special_name, full_name in special_tables.items():
        if special_name in text:
            return full_name

    # 如果不是特殊表名，则按原来的逻辑处理
    cleaned = re.sub(r'^[\d\.、]+\s*', '', text)
    return cleaned

def extract_tables_from_word(doc_path: str, file_name: str) -> pd.DataFrame:
    """
    从Word文档中提取所有表格数据，从第二章开始，跳过每个文件的前两个表格
    """
    doc = docx.Document(doc_path)
    tables_data = []
    current_table_name = None
    last_heading = None
    found_chapter_2 = False
    table_count = 0  # 表格总计数器
    valid_table_count = 0  # 有效表格计数器（用于跳过前两个）

    # 要忽略的标题列表（精确匹配）
    ignore_titles = {'附录', '索引', '目录'}  # 使用集合提高查找效率

    # 添加调试信息
    print(f"\n处理文件: {file_name}")
    print("检查文档中的段落样式：")
    for para in doc.paragraphs:
        if para.text.strip():  # 只打印非空段落
            style_name = para.style.name if hasattr(para, 'style') and para.style else "无样式"
            # print(f"文本: '{para.text.strip()}' -> 样式: '{style_name}'")

    # 修改标题识别逻辑
    for para in doc.paragraphs:
        if hasattr(para, 'style') and para.style:
            style_name = para.style.name
            text = para.text.strip()

            # 检查样式名称和文本内容
            if ('Heading' in style_name or '标题' in style_name or 'toc' in style_name):
                # print(f"发现标题样式: '{style_name}' -> 文本: '{text}'")

                if '第2章' in text or '第二章' in text or '二、' in text  or '病历管理' in text:
                    found_chapter_2 = True
                    print(f"找到第2章: {text}")
                    break

    if not found_chapter_2:
        print("未找到第2章，跳过文件")

        return pd.DataFrame()

    # 遍历文档中的所有元素，按顺序收集表名和处理表格
    for element in doc.element.body.iter():
        if element.tag.endswith('p'):  # 段落
            # 获取段落对象
            for para in doc.paragraphs:
                if para._element == element:
                    # 检查是否是标题
                    if hasattr(para, 'style') and para.style:
                        style_name = para.style.name
                        if style_name in ['Heading 2', 'Heading 3', 'Heading 4', 'Heading 5'] or '标题' in style_name:
                            text = para.text.strip()
                            # 检查是否是要忽略的标题（精确匹配）
                            if text and text not in ignore_titles:
                                # 清理表名，去除章节编号
                                last_heading = clean_table_name(text)
                                print(f"找到标题: {last_heading}")
                    break

        elif element.tag.endswith('tbl'):  # 表格
            # 只在有有效表名时处理表格，且表名不在忽略列表中（精确匹配）
            if last_heading and last_heading not in ignore_titles:
                current_table_name = last_heading
                # 找到对应的表格对象
                for table in doc.tables:
                    if table._element == element:
                        if len(table.rows) <= 1:  # 跳过空表格
                            continue

                        # 检查表格的第一行是否包含预期的列名
                        first_row = [cell.text.strip() for cell in table.rows[0].cells]
                        if not any(col in first_row for col in ['数据项', '字段名']):
                            print(f"跳过非数据表格，首行: {first_row}")
                            break

                        table_count += 1  # 增加表格计数

                        # 跳过前两个表格
                        if table_count <= 2:
                            print(f"跳过第 {table_count} 个表格: {current_table_name}")
                            break

                        # 提取表格数据
                        rows_data = []
                        expected_columns = [
                            '数据项', '字段名', '数据类型', '长度',
                            '填报要求', '是否主键', '说明', '是否字典'
                        ]

                        for row in table.rows[1:]:  # 跳过表头行
                            row_data = [cell.text.strip() for cell in row.cells]
                            if any(row_data):  # 只添加非空行
                                while len(row_data) < len(expected_columns):
                                    row_data.append('')
                                row_data = row_data[:len(expected_columns)]
                                rows_data.append(row_data)

                        if rows_data:
                            # 创建DataFrame
                            df = pd.DataFrame(rows_data, columns=expected_columns)

                            # 添加文件名和表名列
                            df.insert(0, '表名', current_table_name)
                            df.insert(0, '文件名称', file_name+'.docx')

                            tables_data.append(df)
                            valid_table_count += 1  # 增加有效表格计数
                            print(f"已提取表格: {current_table_name}, 行数: {len(rows_data)}")
                        break

    print(f"文件 {file_name} 共提取了 {valid_table_count} 个表格（跳过了前2个表格）")  # 更新输出信息

    if tables_data:
        return pd.concat(tables_data, ignore_index=True)
    return pd.DataFrame()

def get_file_number(filename):
    """从文件名中提取顿号前的数字"""
    match = re.match(r'(\d+)[、.]', filename)
    if match:
        return int(match.group(1))
    return float('inf')  # 如果没有数字，放到最后

def word_to_excel():
    """将所有Word文档中的表格按顺序合并到Excel文件中"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    input_path = os.path.join(base_path, '待提取')

    # 确保待提取文件夹存在
    if not os.path.exists(input_path):
        print(f"待提取文件夹不存在: {input_path}")
        return

    # 获取所有docx文件并按数字顺序排序
    docx_files = [f for f in os.listdir(input_path) if f.endswith('.docx')]
    docx_files.sort(key=get_file_number)

    if not docx_files:
        print(f"在待提取文件夹中未找到Word文件")
        return

    # 打印排序后的文件列表
    print("文件处理顺序：")
    for f in docx_files:
        print(f"  {f}")
    print("\n开始处理文件...")

    all_data = []
    total_tables = 0  # 添加总表格计数器

    # 按顺序处理每个文件
    for docx_file in docx_files:
        try:
            print(f"\n开始处理文件: {docx_file}")
            doc_path = os.path.join(input_path, docx_file)
            file_name = docx_file.replace('.docx', '')

            # 提取当前文件的所有表格数据
            df = extract_tables_from_word(doc_path, file_name)
            if not df.empty:
                all_data.append(df)
                table_count = len(df['表名'].unique())  # 获取当前文件的表格数
                total_tables += table_count  # 累加总表格数
                # print(f"成功提取 {file_name} 的数据，包含 {table_count} 个表格")

        except Exception as e:
            print(f"处理文件 {docx_file} 时发生错误: {str(e)}")
            continue

    if all_data:
        # 合并所有数据
        final_df = pd.concat(all_data, ignore_index=True)

        # 清理数据
        final_df = final_df.dropna(how='all')
        final_df = final_df[final_df['数据项'] != '']

        # 确保列顺序正确
        columns = [
            '文件名称', '表名', '数据项', '字段名', '数据类型',
            '长度', '填报要求', '是否主键', '说明', '是否字典'
        ]
        final_df = final_df[columns]

        # 保存为Excel文件
        excel_path = os.path.join(base_path, '提取.xlsx')
        final_df.to_excel(excel_path, index=False)
        print(f"\n已生成Excel文件: 提取.xlsx")
        print(f"总行数: {len(final_df)}")
        print(f"总表格数: {total_tables}")
    else:
        print("未找到任何有效的表格数据")

if __name__ == "__main__":
    word_to_excel()
