import pandas as pd
import os
import datetime
import re
import traceback

# 在'D:\work\demo\福建\py'路径下下一个脚本'生成四类alert脚本.py'目的是读取'D:\work\demo\福建\alertsql模板.xlsx' 的第一个sheet页  按要求生成 mysql5.7，sqlserver2017，oracle11g，guass5.0四类数据库的alert语句；
# alertsql模板.xlsx中有以下列
# 文件名称	表中文名	表名	字段名	数据类型	长度	长度修改意见	修改目的	填报要求	注释
# 文件名称和表中文名用来注释，生成sql的时候按照文件名称和表中文名进行分组
# 修改目的 有三种：长度 类型 修改注释
# 长度修改意见有两种：修改为500  修改为浮点20,4
# 如果修改目的是"修改注释"，则只修改该字段注释，内容从"注释"列获取
# 请按照各个数据库的语法按照长度修改意见和修改目的生成对应的alert的sql，每个数据库生成一个文件，文件名为'数据库类型+当前日期.sql',生成文件到'D:\work\demo\福建\建表sql\alertsql'


def get_mysql_alter_sql(table_name, field_name, data_type, length, modify_purpose, length_modify_suggestion, comment=None):
    """
    生成MySQL的ALTER语句
    
    Args:
        table_name: 表名
        field_name: 字段名
        data_type: 数据类型
        length: 原长度
        modify_purpose: 修改目的（长度/类型/注释）
        length_modify_suggestion: 长度修改建议
        comment: 字段注释
        
    Returns:
        str: MySQL的ALTER语句
    """
    print(f"DEBUG - MySQL函数: table_name={table_name}, field_name={field_name}, data_type={data_type}, length={length}, modify_purpose={modify_purpose}, comment={comment}")
    
    if pd.isna(modify_purpose):
        print("DEBUG - MySQL函数: modify_purpose为空，返回空字符串")
        return ""
        
    modify_purpose = modify_purpose.strip()
    
    # 处理修改注释的情况
    if modify_purpose == '修改注释':
        print(f"DEBUG - MySQL函数: 处理注释类型，comment={comment}")
        if pd.isna(comment) or comment.strip() == "":
            print("DEBUG - MySQL函数: comment为空，返回空字符串")
            return ""
        # 使用原始数据类型和长度，只修改注释
        if not pd.isna(data_type) and not pd.isna(length):
            print(f"DEBUG - MySQL函数: 使用原始数据类型和长度，data_type={data_type}, length={length}")
            # 根据数据类型生成不同的SQL
            if '字符' in data_type or '文本' in data_type:
                sql = f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` VARCHAR({length}) COMMENT '{comment.strip()}';\n"
                print(f"DEBUG - MySQL函数: 生成字符类型SQL: {sql}")
                return sql
            elif '数值' in data_type or '浮点' in data_type:
                # 假设数值类型默认为DECIMAL(20,4)
                sql = f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` DECIMAL(20,4) COMMENT '{comment.strip()}';\n"
                print(f"DEBUG - MySQL函数: 生成数值类型SQL: {sql}")
                return sql
            else:
                # 如果无法确定具体类型，只修改注释
                sql = f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` COMMENT '{comment.strip()}';\n"
                print(f"DEBUG - MySQL函数: 生成通用SQL: {sql}")
                return sql
        else:
            # 如果没有数据类型和长度信息，只修改注释
            sql = f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` COMMENT '{comment.strip()}';\n"
            print(f"DEBUG - MySQL函数: 无数据类型和长度，生成SQL: {sql}")
            return sql
        
    # 处理长度和类型修改的情况
    if pd.isna(length_modify_suggestion):
        print("DEBUG - MySQL函数: length_modify_suggestion为空，返回空字符串")
        return ""
        
    length_modify_suggestion = length_modify_suggestion.strip()
    
    if modify_purpose == '长度':
        if length_modify_suggestion == '修改为500':
            return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` VARCHAR(500);\n"
        elif length_modify_suggestion == '修改为浮点20,4':
            return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` DECIMAL(20,4);\n"
        else:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match:
                if match.group(2):  # 有小数部分
                    return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` DECIMAL({match.group(1)},{match.group(2)});\n"
                else:  # 只有整数部分
                    return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` VARCHAR({match.group(1)});\n"
    elif modify_purpose == '类型':
        # 根据长度修改建议确定新的数据类型
        if '浮点' in length_modify_suggestion or '数值' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match and match.group(2):
                return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` DECIMAL({match.group(1)},{match.group(2)});\n"
            else:
                return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` DECIMAL(20,4);\n"
        elif '字符' in length_modify_suggestion or '文本' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)', length_modify_suggestion)
            if match:
                length_val = int(match.group(1))
                if length_val > 500:
                    return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` TEXT;\n"
                else:
                    return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` VARCHAR({length_val});\n"
            else:
                return f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field_name}` VARCHAR(500);\n"
    print("DEBUG - MySQL函数: 没有匹配的修改类型，返回空字符串")
    return ""

def get_sqlserver_alter_sql(table_name, field_name, data_type, length, modify_purpose, length_modify_suggestion, comment=None):
    """
    生成SQL Server的ALTER语句
    
    Args:
        table_name: 表名
        field_name: 字段名
        data_type: 数据类型
        length: 原长度
        modify_purpose: 修改目的（长度/类型/注释）
        length_modify_suggestion: 长度修改建议
        comment: 字段注释
        
    Returns:
        str: SQL Server的ALTER语句
    """
    if pd.isna(modify_purpose):
        return ""
        
    modify_purpose = modify_purpose.strip()
    
    # 处理修改注释的情况
    if modify_purpose == '修改注释':
        if pd.isna(comment) or comment.strip() == "":
            return ""
        # SQL Server使用扩展属性来存储注释
        # 检查是否已存在扩展属性
        check_sql = f"IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('{table_name}') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('{table_name}') AND name = '{field_name}'))\n"
        update_sql = f"EXEC sp_updateextendedproperty 'MS_Description', N'{comment.strip()}', 'SCHEMA', 'dbo', 'TABLE', '{table_name}', 'COLUMN', '{field_name}';\n"
        add_sql = f"EXEC sp_addextendedproperty 'MS_Description', N'{comment.strip()}', 'SCHEMA', 'dbo', 'TABLE', '{table_name}', 'COLUMN', '{field_name}';\n"
        return check_sql + "  " + update_sql + "ELSE\n  " + add_sql
        
    # 处理长度和类型修改的情况
    if pd.isna(length_modify_suggestion):
        return ""
        
    length_modify_suggestion = length_modify_suggestion.strip()
    
    if modify_purpose == '长度':
        if length_modify_suggestion == '修改为500':
            return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] NVARCHAR(500);\n"
        elif length_modify_suggestion == '修改为浮点20,4':
            return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] DECIMAL(20,4);\n"
        else:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match:
                if match.group(2):  # 有小数部分
                    return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] DECIMAL({match.group(1)},{match.group(2)});\n"
                else:  # 只有整数部分
                    return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] NVARCHAR({match.group(1)});\n"
    elif modify_purpose == '类型':
        # 根据长度修改建议确定新的数据类型
        if '浮点' in length_modify_suggestion or '数值' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match and match.group(2):
                return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] DECIMAL({match.group(1)},{match.group(2)});\n"
            else:
                return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] DECIMAL(20,4);\n"
        elif '字符' in length_modify_suggestion or '文本' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)', length_modify_suggestion)
            if match:
                length_val = int(match.group(1))
                if length_val > 4000:
                    return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] NVARCHAR(MAX);\n"
                else:
                    return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] NVARCHAR({length_val});\n"
            else:
                return f"ALTER TABLE [{table_name}] ALTER COLUMN [{field_name}] NVARCHAR(500);\n"
    return ""

def get_oracle_alter_sql(table_name, field_name, data_type, length, modify_purpose, length_modify_suggestion, comment=None):
    """
    生成Oracle的ALTER语句
    
    Args:
        table_name: 表名
        field_name: 字段名
        data_type: 数据类型
        length: 原长度
        modify_purpose: 修改目的（长度/类型/注释）
        length_modify_suggestion: 长度修改建议
        comment: 字段注释
        
    Returns:
        str: Oracle的ALTER语句
    """
    if pd.isna(modify_purpose):
        return ""
        
    modify_purpose = modify_purpose.strip()
    
    # 处理修改注释的情况
    if modify_purpose == '修改注释':
        if pd.isna(comment) or comment.strip() == "":
            return ""
        # Oracle使用COMMENT ON COLUMN语句来修改列注释
        return f"COMMENT ON COLUMN {table_name}.{field_name} IS '{comment.strip()}';\n"
        
    # 处理长度和类型修改的情况
    if pd.isna(length_modify_suggestion):
        return ""
        
    length_modify_suggestion = length_modify_suggestion.strip()
    
    if modify_purpose == '长度':
        if length_modify_suggestion == '修改为500':
            return f"ALTER TABLE {table_name} MODIFY {field_name} VARCHAR2(500);\n"
        elif length_modify_suggestion == '修改为浮点20,4':
            return f"ALTER TABLE {table_name} MODIFY {field_name} NUMBER(20,4);\n"
        else:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match:
                if match.group(2):  # 有小数部分
                    return f"ALTER TABLE {table_name} MODIFY {field_name} NUMBER({match.group(1)},{match.group(2)});\n"
                else:  # 只有整数部分
                    return f"ALTER TABLE {table_name} MODIFY {field_name} VARCHAR2({match.group(1)});\n"
    elif modify_purpose == '类型':
        # 根据长度修改建议确定新的数据类型
        if '浮点' in length_modify_suggestion or '数值' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match and match.group(2):
                return f"ALTER TABLE {table_name} MODIFY {field_name} NUMBER({match.group(1)},{match.group(2)});\n"
            else:
                return f"ALTER TABLE {table_name} MODIFY {field_name} NUMBER(20,4);\n"
        elif '字符' in length_modify_suggestion or '文本' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)', length_modify_suggestion)
            if match:
                length_val = int(match.group(1))
                if length_val > 4000:
                    return f"ALTER TABLE {table_name} MODIFY {field_name} CLOB;\n"
                else:
                    return f"ALTER TABLE {table_name} MODIFY {field_name} VARCHAR2({length_val});\n"
            else:
                return f"ALTER TABLE {table_name} MODIFY {field_name} VARCHAR2(500);\n"
    return ""

def get_gauss_alter_sql(table_name, field_name, data_type, length, modify_purpose, length_modify_suggestion, comment=None):
    """
    生成高斯数据库的ALTER语句
    
    Args:
        table_name: 表名
        field_name: 字段名
        data_type: 数据类型
        length: 原长度
        modify_purpose: 修改目的（长度/类型/注释）
        length_modify_suggestion: 长度修改建议
        comment: 字段注释
        
    Returns:
        str: 高斯数据库的ALTER语句
    """
    if pd.isna(modify_purpose):
        return ""
        
    modify_purpose = modify_purpose.strip()
    
    # 处理修改注释的情况
    if modify_purpose == '修改注释':
        if pd.isna(comment) or comment.strip() == "":
            return ""
        # 高斯数据库使用COMMENT ON COLUMN语句来修改列注释
        return f'COMMENT ON COLUMN "{table_name}"."{field_name}" IS \'{comment.strip()}\';\n'
        
    # 处理长度和类型修改的情况
    if pd.isna(length_modify_suggestion):
        return ""
        
    length_modify_suggestion = length_modify_suggestion.strip()
    
    if modify_purpose == '长度':
        if length_modify_suggestion == '修改为500':
            return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE VARCHAR(500);\n'
        elif length_modify_suggestion == '修改为浮点20,4':
            return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE NUMERIC(20,4);\n'
        else:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match:
                if match.group(2):  # 有小数部分
                    return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE NUMERIC({match.group(1)},{match.group(2)});\n'
                else:  # 只有整数部分
                    return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE VARCHAR({match.group(1)});\n'
    elif modify_purpose == '类型':
        # 根据长度修改建议确定新的数据类型
        if '浮点' in length_modify_suggestion or '数值' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)(?:,(\d+))?', length_modify_suggestion)
            if match and match.group(2):
                return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE NUMERIC({match.group(1)},{match.group(2)});\n'
            else:
                return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE NUMERIC(20,4);\n'
        elif '字符' in length_modify_suggestion or '文本' in length_modify_suggestion:
            # 尝试从修改建议中提取数字
            match = re.search(r'(\d+)', length_modify_suggestion)
            if match:
                length_val = int(match.group(1))
                if length_val > 500:
                    return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE CLOB;\n'
                else:
                    return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE VARCHAR({length_val});\n'
            else:
                return f'ALTER TABLE "{table_name}" ALTER COLUMN "{field_name}" TYPE VARCHAR(500);\n'
    return ""

def generate_alter_sql(df, db_type):
    """
    根据数据库类型生成ALTER语句
    
    Args:
        df: DataFrame
        db_type: 数据库类型（mysql/sqlserver/oracle/gauss）
        
    Returns:
        str: 生成的SQL语句
    """
    sql = ""
    
    # 检查必要的列是否存在
    required_columns = ['文件名称', '表中文名', '表名', '字段名', '数据类型', '长度', '长度修改意见', '修改目的', '注释']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        return f"-- 错误：Excel文件缺少以下必要列：{', '.join(missing_columns)}\n"
    
    print(f"DataFrame列：{df.columns.tolist()}")
    print(f"DataFrame行数：{len(df)}")
    
    # 按文件名称和表中文名分组
    try:
        grouped = df.groupby(['文件名称', '表中文名'])
        print(f"分组后的组数：{len(grouped)}")
    except Exception as e:
        print(f"分组数据时出错：{str(e)}")
        return f"-- 错误：分组数据时出错：{str(e)}\n"
    
    for (file_name, table_comment), group in grouped:
        print(f"处理分组：{file_name} - {table_comment}, 行数：{len(group)}")
        
        if pd.isna(file_name) or pd.isna(table_comment):
            print(f"跳过空文件名或表中文名")
            continue
            
        sql += f"-- {file_name} - {table_comment}\n"
        
        try:
            # 获取该组的第一行表名
            table_name = group.iloc[0]['表名'].strip() if not pd.isna(group.iloc[0]['表名']) else ""
            if not table_name:
                print(f"表名为空，跳过")
                sql += f"-- 错误：表名为空\n\n"
                continue
                
            print(f"处理表：{table_name}")
                
            for _, row in group.iterrows():
                field_name = row['字段名'].strip() if not pd.isna(row['字段名']) else ""
                if not field_name:
                    print(f"字段名为空，跳过")
                    continue
                    
                data_type = str(row['数据类型']).strip() if not pd.isna(row['数据类型']) else ""
                length = str(row['长度']).strip() if not pd.isna(row['长度']) else ""
                modify_purpose = str(row['修改目的']).strip() if not pd.isna(row['修改目的']) else ""
                length_modify_suggestion = str(row['长度修改意见']).strip() if not pd.isna(row['长度修改意见']) else ""
                comment = str(row['注释']).strip() if not pd.isna(row['注释']) else None
                
                print(f"处理字段：{field_name}, 修改目的：{modify_purpose}, 长度修改建议：{length_modify_suggestion}")
                
                # 根据数据库类型生成不同的ALTER语句
                alter_sql = ""
                if db_type == 'mysql':
                    alter_sql = get_mysql_alter_sql(table_name, field_name, data_type, length, 
                                                modify_purpose, length_modify_suggestion, comment)
                elif db_type == 'sqlserver':
                    alter_sql = get_sqlserver_alter_sql(table_name, field_name, data_type, length, 
                                                    modify_purpose, length_modify_suggestion, comment)
                elif db_type == 'oracle':
                    alter_sql = get_oracle_alter_sql(table_name, field_name, data_type, length, 
                                                modify_purpose, length_modify_suggestion, comment)
                elif db_type == 'gauss':
                    alter_sql = get_gauss_alter_sql(table_name, field_name, data_type, length, 
                                                modify_purpose, length_modify_suggestion, comment)
                
                if alter_sql:
                    print(f"生成的ALTER语句：{alter_sql.strip()}")
                else:
                    print(f"没有生成ALTER语句")
                
                sql += alter_sql
        except Exception as e:
            print(f"处理表 {table_name} 时出错：{str(e)}")
            print(traceback.format_exc())
            sql += f"-- 错误：处理表 {table_name} 时出错：{str(e)}\n"
        
        sql += "\n"
    
    return sql

def main():
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, 'alertsql模板.xlsx')
    output_dir = os.path.join(base_path, '建表sql', 'alertsql')
    
    print(f"Excel文件路径: {excel_path}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    print(f"已确保输出目录存在")
    
    # 获取当前日期
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    
    try:
        # 读取Excel文件的第一个sheet页
        print(f"正在读取Excel文件: {excel_path}")
        if not os.path.exists(excel_path):
            print(f"错误：找不到文件 {excel_path}")
            return
            
        df = pd.read_excel(excel_path, sheet_name=0)
        print(f"成功读取Excel文件，数据行数: {len(df)}")
        
        # 检查DataFrame是否为空
        if df.empty:
            print("错误：Excel文件为空或没有数据")
            return
            
        # 生成并保存四种数据库的ALTER语句
        for db_type, db_name in [
            ('mysql', 'mysql5.7'), 
            ('sqlserver', 'sqlserver2017'), 
            ('oracle', 'oracle11g'), 
            ('gauss', 'guass5.0')
        ]:
            # 生成SQL语句
            print(f"正在生成{db_name}的ALTER语句...")
            sql = generate_alter_sql(df, db_type)
            
            # 保存到文件
            output_path = os.path.join(output_dir, f"{db_name}_{current_date}.sql")
            with open(output_path, 'w', encoding='utf-8') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f'-- 生成时间：{current_time}\n')
                f.write(f'-- {db_name}数据库ALTER语句\n\n')
                f.write(sql)
            
            print(f"成功生成{db_name}的ALTER语句: {output_path}")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {excel_path}")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")
        print(traceback.format_exc())

if __name__ == "__main__":
    main() 