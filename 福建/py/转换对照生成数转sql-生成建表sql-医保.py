import pandas as pd
import os
import datetime
import re

# 数据类型映射，涵盖常见类型及二进制blob
# 可根据实际Excel内容扩展

def get_mysql_type(data_type: str, length: str) -> str:
    data_type = str(data_type).lower().strip()
    length = str(length).strip()
    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR(255)'
    # 整数
    if '整数' in data_type or 'int' in data_type:
        return 'INT'
    # 浮点/小数
    if '数值' in data_type or 'float' in data_type or 'double' in data_type or 'decimal' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'DECIMAL({total_len},{decimal_len})'
                return f'DECIMAL({length})'
            except ValueError:
                return 'DECIMAL(10,2)'
        return 'DECIMAL(10,2)'
    # 字符串
    if '字符' in data_type or 'varchar' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                if length_val > 500:
                    return 'TEXT'
                return f'VARCHAR({length})'
            except ValueError:
                return 'VARCHAR(255)'
        return 'VARCHAR(255)'
    # 文本
    if '文本' in data_type or 'text' in data_type:
        return 'TEXT'
    # 日期时间
    if '日期' in data_type and '时间' in data_type:
        return 'DATETIME'
    if '日期' in data_type:
        return 'DATE'
    if '时间' in data_type:
        return 'DATETIME'
    # 二进制
    if '二进制' in data_type or 'blob' in data_type or 'binary' in data_type:
        return 'BLOB'
    # 布尔
    if '布尔' in data_type or 'bool' in data_type:
        return 'TINYINT(1)'
    # 默认
    return 'VARCHAR(255)'

def clean_comment(*args) -> str:
    # 拼接并清洗注释内容，去除特殊符号
    comment = ' '.join([str(a) for a in args if pd.notna(a) and str(a).strip() != 'nan'])
    # 去除换行、制表符、特殊标点
    comment = re.sub(r'[\r\n\t]+', ' ', comment)
    comment = re.sub(r'["\'\\/<>|:;，。！？、【】（）\[\]{}…—\-]', ' ', comment)
    comment = re.sub(r'\s+', ' ', comment).strip()
    return comment

def main():
    # 路径设置
    excel_path = r'D:\work\demo\福建\转换对照生成数转sql模板 -医保.xlsx'
    base_path = r'D:\work\demo\福建\建表sql'
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path, f"医保mysql建表语句{current_date}.sql")

    try:
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path, sheet_name=0)
        # 检查必要列
        required_cols = ['业务大类','文件名称','表中文名','表名','数据项','字段名','数据类型','长度','是否主键','填报要求','说明','是否字典']
        for col in required_cols:
            if col not in df.columns:
                raise Exception(f"缺少必要列: {col}")
        # 保持表顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)
        # 生成SQL
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
            for table_name in table_order:
                group = df[df['表名'] == table_name]
                table_comment = str(group.iloc[0]['表中文名']).strip()
                sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` (\n"
                primary_keys = []
                for _, row in group.iterrows():
                    field_name = str(row['字段名']).strip()
                    data_type = get_mysql_type(row['数据类型'], row['长度'])
                    is_primary = str(row['是否主键']).strip().upper()
                    comment = clean_comment(row['数据项'], row['说明'])
                    nullable = '不能为空' in str(row['填报要求'])
                    sql += f"    `{field_name}` {data_type}"
                    if nullable:
                        sql += " NOT NULL"
                    if comment:
                        sql += f" COMMENT '{comment}'"
                    sql += ",\n"
                    if is_primary in ['是','Y','1']:
                        primary_keys.append(field_name)
                if primary_keys:
                    sql += f"    PRIMARY KEY ({', '.join([f'`{pk}`' for pk in primary_keys])})\n"
                else:
                    sql = sql.rstrip(",\n") + "\n"
                sql += f") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='{table_comment}';\n\n"
                f.write(sql)
        print(f"SQL文件已生成: {output_path}")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main() 