import pandas as pd
import os
from datetime import datetime

# 设置Excel文件路径
excel_path = os.path.join('..', '转换对照生成数转sql模板 -医保.xlsx')

# 读取第一个sheet
try:
    df = pd.read_excel(excel_path, sheet_name=0, dtype=str)
    print('Excel文件读取成功，数据行数:', len(df))
except Exception as e:
    print('Excel文件读取失败:', e)
    exit(1)

# 保持原始顺序分组
if '表名' not in df.columns:
    print('未找到"表名"列，请检查Excel文件格式。')
    exit(1)

grouped = df.groupby('表名', sort=False)
print('按表名分组完成，表数量:', len(grouped))

sql_list = []

for table_name, group in grouped:
    # 1. 确定主表名（主副表关联过滤为where 1=1的行的引用业务表）
    main_table_row = group[group['主副表关联过滤'] == 'where 1=1']
    if not main_table_row.empty:
        main_table = main_table_row.iloc[0]['引用业务表']
    else:
        main_table = ''

    # 2. 获取所有主键字段，拼接rid
    pk_fields = group[group['是否主键'] == '是']['字段名'].tolist()
    rid_expr = f"concat({','.join(pk_fields)})" if pk_fields else "''"

    # 3. 生成select字段（按规则处理引用业务表、数据加工方式等）
    select_lines = []
    for idx, row in group.iterrows():
        col_name = row['字段名']
        if col_name == 'rid':
            continue  # 跳过rid字段，避免重复
        ref_table = str(row['引用业务表']) if not pd.isna(row['引用业务表']) else ''
        ref_col = str(row['引用业务字段名']) if not pd.isna(row['引用业务字段名']) else ''
        ref_item = str(row['引用业务数据项']) if not pd.isna(row['引用业务数据项']) else ''
        data_proc = str(row['数据加工方式']) if not pd.isna(row['数据加工方式']) else ''
        # 新增规则：字典值名称/字典值
        if ref_item in ['字典值名称', '字典值']:
            select_line = f"    {ref_table}.{ref_item} as {col_name}"
        elif data_proc:
            select_line = f"    {data_proc} as {col_name}"
        elif ref_table == '':
            select_line = f"    null as {col_name}"
        elif ref_table == '待定':
            select_line = f"    '待定' as {col_name}"
        elif ref_table == '常量':
            select_line = f"    '{ref_item}' as {col_name}"
        else:
            select_line = f"    {ref_table}.{ref_col} as {col_name}"
        select_lines.append(select_line)
    # select最后一项不加逗号
    select_body = ',\n'.join(select_lines)

    # 4. 处理主副表关联过滤，去重后换行拼接，where 1=1放最后
    filter_list = group['主副表关联过滤'].dropna().unique().tolist()
    filter_list_no_where = [f for f in filter_list if f.strip() != 'where 1=1']
    filter_list_final = filter_list_no_where + ['where 1=1'] if 'where 1=1' in filter_list else filter_list_no_where
    filter_str = '\n'.join(filter_list_final)

    # 5. 组装SQL，严格按要求格式
    sql = f'''insert overwrite table {table_name} partition(dt)
select 
    tab.*
    ,date_format(crte_time,'%Y-%m-%d') as dt
from 
(
select  
{select_body}
from src_data.{main_table} as t
{filter_str}
) as tab
'''
    sql_list.append(sql)

print('SQL生成完成，表数量:', len(sql_list))

# 输出SQL到文件
output_dir = r'D:\work\demo\福建\建表sql'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 获取当前日期并拼接文件名
current_date = datetime.now().strftime('%Y%m%d')
output_file = os.path.join(output_dir, f"医保mysql转换语句{current_date}.sql")

# 将所有SQL语句写入一个文件
with open(output_file, 'w', encoding='utf-8') as f:
    for sql in sql_list:
        f.write(sql + '\n-- ================================================\n')

print(f'所有SQL语句已输出到文件: {output_file}')