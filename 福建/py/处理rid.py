import pandas as pd
import os

def process_rid_description(df):
    """处理每个表中rid字段的说明"""
    # 要排除的字段名列表
    exclude_fields = [
        'rid',
        'sys_prdr_name',  # 系统建设厂商名称
        'uscid' ,    # 医疗机构统一社会信用代码
        'sys_prdr_code',  # 系统建设厂商代码
    ]

    # 按表名分组处理
    for table_name, table_df in df.groupby('表名'):
        # 找出该表中所有主键，排除特定字段
        primary_keys = table_df[
            (table_df['是否主键'] == '是') &
            (~table_df['字段名'].str.lower().isin([f.lower() for f in exclude_fields]))
        ]

        if primary_keys.empty:
            print(f"警告: 表 {table_name} 没有找到主键(排除特定字段)")
            continue

        # 构建主键拼接字符串
        primary_key_str = '+'.join([
            f"{row['数据项']}{row['字段名']}"
            for _, row in primary_keys.iterrows()
        ])

        # 找到该表中字段名为rid的行
        rid_rows = table_df[table_df['字段名'].str.lower() == 'rid']
        if rid_rows.empty:
            print(f"警告: 表 {table_name} 没有找到rid字段")
            continue

        # 构建说明内容
        description = (
            "唯一索引，生成规则：医疗机构统一社会信用代码（18位）+"
            f"系统建设厂商代码sys_prdr_code+{primary_key_str}"
        )

        # 更新rid行的说明列
        df.loc[rid_rows.index, '说明'] = description
        print(f"已更新表 {table_name} 的rid字段说明")

    return df

def main():
    # 设置文件路径
    file_path = r'/福建'
    excel_name = '处理rid.xlsx'
    excel_path = os.path.join(file_path, excel_name)

    try:
        # 读取Excel文件
        print(f"正在读取文件: {excel_name}")
        df = pd.read_excel(excel_path)

        # 清理数据
        df = df.fillna('')
        df = df[df['数据项'] != '']

        # 处理rid字段说明
        df = process_rid_description(df)

        # 保存修改后的文件
        output_name = '已处理rid.xlsx'
        output_path = os.path.join(file_path, output_name)
        df.to_excel(output_path, index=False)
        print(f"\n处理完成，已保存到: {output_name}")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
