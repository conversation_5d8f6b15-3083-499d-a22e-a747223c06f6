# -*- coding: utf-8 -*-
"""
测试复杂SQL语句转换
"""

from 质控mysql转其他库 import translate_sql, process_single_sql

def test_complex_timestampdiff():
    """测试复杂的TIMESTAMPDIFF函数转换"""
    print("===== 测试复杂的TIMESTAMPDIFF函数转换 =====")
    
    # 用户提供的复杂SQL语句
    complex_sql = "timestampdiff ( month, '2020-01-01 00:00:00', date_add ( current_date, interval - 1 month ) ) - count( 1 ) AS biz_info"
    
    print(f"原始SQL: {complex_sql}")
    print()
    
    # 转换为各种数据库方言
    oracle_result = translate_sql(complex_sql, 'oracle')
    sqlserver_result = translate_sql(complex_sql, 'sqlserver')
    gaussdb_result = translate_sql(complex_sql, 'gaussdb')
    
    print("Oracle 转换结果:")
    print(oracle_result)
    print()
    
    print("SQL Server 转换结果:")
    print(sqlserver_result)
    print()
    
    print("GaussDB 转换结果:")
    print(gaussdb_result)
    print()
    
    # 分析转换结果
    print("===== 转换分析 =====")
    
    # 检查Oracle转换
    if "MONTHS_BETWEEN" in oracle_result and "TRUNC(SYSDATE)" in oracle_result:
        print("✓ Oracle转换正确：使用了MONTHS_BETWEEN函数和TRUNC(SYSDATE)")
    else:
        print("✗ Oracle转换可能有问题")
    
    # 检查SQL Server转换
    if "DATEDIFF" in sqlserver_result and "DATEADD" in sqlserver_result and "CAST(GETDATE() AS DATE)" in sqlserver_result:
        print("✓ SQL Server转换正确：使用了DATEDIFF和DATEADD函数")
    else:
        print("✗ SQL Server转换可能有问题")
    
    # 检查GaussDB转换
    if "CURRENT_DATE" in gaussdb_result and "INTERVAL" in gaussdb_result:
        print("✓ GaussDB转换正确：使用了CURRENT_DATE和INTERVAL")
    else:
        print("✗ GaussDB转换可能有问题")

def test_other_complex_cases():
    """测试其他复杂情况"""
    print("\n===== 测试其他复杂SQL情况 =====")
    
    test_cases = [
        {
            'name': '嵌套函数调用',
            'sql': "SELECT timestampdiff(day, date_add(current_date, interval -7 day), now()) as days_diff"
        },
        {
            'name': '多个date_add函数',
            'sql': "SELECT date_add(date_add(current_date, interval 1 month), interval -1 day) as last_day_of_next_month"
        },
        {
            'name': '复杂的WHERE条件',
            'sql': "SELECT * FROM table WHERE created_date >= date_add(current_date, interval -1 month) AND updated_date <= now()"
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        print(f"原始SQL: {case['sql']}")
        
        oracle_result = translate_sql(case['sql'], 'oracle')
        sqlserver_result = translate_sql(case['sql'], 'sqlserver')
        gaussdb_result = translate_sql(case['sql'], 'gaussdb')
        
        print(f"Oracle: {oracle_result}")
        print(f"SQL Server: {sqlserver_result}")
        print(f"GaussDB: {gaussdb_result}")

if __name__ == "__main__":
    test_complex_timestampdiff()
    test_other_complex_cases()
