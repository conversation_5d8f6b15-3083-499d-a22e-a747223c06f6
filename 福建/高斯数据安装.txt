https://opengauss.org/zh/download/archive/
https://docs.opengauss.org/zh/docs/5.0.0-lite/docs/InstallationGuide/%E5%AE%89%E8%A3%85openGauss.html

cp /mnt/c/Users/<USER>/Downloads/openGauss-5.0.0-openEuler-64bit.tar.bz2 /home/<USER>/openguess/

sudo chown -R mars:mars /opt/software
tar -jxf openGauss-5.0.0-openEuler-64bit.tar.bz2 -C /opt/software/openGauss


bash  install.sh  -w "Aa12345678" &&source ~/.bashrc

[complete successfully]: You can start or stop the database server using:
    gs_ctl start|stop|restart -D $GAUSSHOME/data/single_node -Z single_node


gs_ctl start|stop|restart -D $GAUSSHOME/data/single_node -Z single_node

gs_ctl start -D $GAUSSHOME/data/single_node -Z single_node


/opt/software/openGauss/bin/gs_ctl start -D /opt/software/openGauss/data/single_node -Z single_node


rm -rf /opt/software/openGauss/data/single_node
cd /opt/software/openGauss/simpleInstall
bash install.sh -w "Aa12345678" && source ~/.bashrc


sudo chown mars:mars /opt/software/openGauss/etc/gscgroup_mars.cfg


echo "openGauss 5.0.0" > /opt/software/openGauss/data/single_node/gaussdb.version
chown mars:mars /opt/software/openGauss/data/single_node/gaussdb.version


chown mars:mars /opt/software/openGauss/etc/gscgroup_mars.cfg

gs_cgroup -U mars -D /opt/software/openGauss/data/single_node -c


sudo chown omm:dbgrp 


cp -r /mnt/c/Users/<USER>/Downloads/openGauss-Lite-5.0.0-openEuler-x86_64.tar.gz /home/
cp -r /mnt/c/Users/<USER>/Downloads/init_chenlu.sh /home/


/opt/software/openGauss


tar -zxf openGauss-Lite-5.0.0-openEuler-x86_64.tar.gz -C /opt/software/openGauss


启动数据库
su omm   密码123456
cd /opt/software/openGauss
echo "Aa123456" | sh ./install.sh --mode single -D ~/openGauss/data -R ~/openGauss/install --start


/home/<USER>/openGauss/install/bin/gsql -d postgres -p 5432 -U omm -W Aa123456
/home/<USER>/openGauss/install/bin/gsql -d postgres -p 5432 -U test -W NewPass@123

vim /home/<USER>/openGauss/data/postgresql.conf
vim /home/<USER>/openGauss/data/pg_hba.conf

/home/<USER>/openGauss/install/bin/gs_ctl reload -D /home/<USER>/openGauss/data

/home/<USER>/openGauss/install/bin/gs_ctl restart -D ${GAUSSDATA} 

CREATE USER test WITH PASSWORD 'Aa123456';
GRANT ALL PRIVILEGES ON DATABASE postgres TO test;
ALTER USER test WITH LOGIN;

grep listen_addresses /home/<USER>/openGauss/data/postgresql.conf


GRANT ALL PRIVILEGES ON DATABASE postgres TO test;