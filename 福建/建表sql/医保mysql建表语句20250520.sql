-- 生成时间：2025-05-20 15:27:02
CREATE TABLE IF NOT EXISTS `insu_emp_info_b` (
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `prnt_emp_no` VARCHAR(40) COMMENT '上级单位编号',
    `asoc_legent_flag` VARCHAR(6) COMMENT '关联法人标志 0否；1是',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `reg_name` VARCHAR(500) COMMENT '注册名称',
    `loc_admdvs` VARCHAR(6) COMMENT '所属医保区划',
    `coner_name` VARCHAR(50) COMMENT '联系人姓名',
    `coner_email` VARCHAR(100) COMMENT '联系人电子邮箱',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `fax_no` VARCHAR(50) COMMENT '传真号码',
    `tax_reg_no` VARCHAR(50) COMMENT '税务登记号',
    `orgcode` VARCHAR(20) COMMENT '组织机构代码',
    `regno` VARCHAR(30) COMMENT '注册号',
    `regno_cert_type` VARCHAR(6) COMMENT '注册号证件类型',
    `emp_addr` VARCHAR(200) COMMENT '单位地址',
    `poscode` VARCHAR(6) COMMENT '邮政编码',
    `aprv_esta_dept` VARCHAR(100) COMMENT '批准成立部门',
    `aprv_esta_date` DATE COMMENT '批准成立日期',
    `aprv_esta_docno` VARCHAR(100) COMMENT '批准成立文号',
    `prnt_admdvs` VARCHAR(6) COMMENT '上级医保区划',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `org_vali_stas` VARCHAR(3) COMMENT '组织有效状态 0无效；1有效',
    `legrep_name` VARCHAR(255) COMMENT '法定代表人姓名',
    `legrep_cert_type` VARCHAR(3) COMMENT '法定代表人证件类型 身份证件类别代码表',
    `legrep_certno` VARCHAR(50) COMMENT '法定代表人证件号码',
    `orgcode_issu_emp` VARCHAR(100) COMMENT '组织机构代码证颁发单位',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `ver` VARCHAR(50) COMMENT '版本号',
    `sync_prnt_flag` VARCHAR(3) COMMENT '同步上级标志 0否；1是',
    `memo` TEXT COMMENT '备注',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `entp_spec_flag` VARCHAR(3) COMMENT '企业规格标志 0否；1是',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `emp_entt_codg` VARCHAR(40) COMMENT '单位实体编码',
    `legrep_tel` VARCHAR(50) COMMENT '法定代表人电话号码',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参保单位信息';

CREATE TABLE IF NOT EXISTS `psn_insu_d` (
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `crt_insu_date` DATE COMMENT '本次参保日期',
    `paus_insu_date` DATE COMMENT '暂停参保日期',
    `psn_insu_stas` VARCHAR(6) COMMENT '人员参保状态 人员参保状态(psn_insu_stas)',
    `insutype_retr_flag` VARCHAR(6) COMMENT '险种离退休标志 0在职；1退休；2离退休',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `clct_way` VARCHAR(6) COMMENT '征收方式 征收方式字典',
    `emp_fom` VARCHAR(6) COMMENT '用工形式 用工形式字典',
    `max_acctprd` VARCHAR(6) COMMENT '最大做账期号 最后缴费年月',
    `acct_crtn_ym` VARCHAR(6) COMMENT '账户建立年月',
    `fst_insu_ym` VARCHAR(6) COMMENT '首次参保年月',
    `psn_insu_date` DATE COMMENT '本系统首次参保日期',
    `clct_rule_type_codg` VARCHAR(50) COMMENT '征缴规则类型编码',
    `clctstd_crtf_rule_codg` VARCHAR(50) COMMENT '基数核定规则类型编码',
    `hi_type` VARCHAR(3) COMMENT '医保类型 医保类型 hi_type',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `opt_chnl` VARCHAR(3) COMMENT '经办渠道 经办渠道字典',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `psn_insu_mgt_eid` VARCHAR(20) COMMENT '参保人员管理事件ID',
    `retr_trt_enjymnt_flag` VARCHAR(6) COMMENT '退休待遇享受标志',
    `retr_acct_enjymnt_flag` VARCHAR(3) COMMENT '退休账户享受标志',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `opt_time` DATETIME COMMENT '经办时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `quts_type` VARCHAR(6) COMMENT '编制类型 编制类型字典',
    `retr_trt_begn_date` DATE COMMENT '退休待遇开始日期',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员参保信息';

CREATE TABLE IF NOT EXISTS `psn_clctstd_d` (
    `may_psn_clctstd` DECIMAL(20,4) COMMENT '5月人员缴费基数',
    `jan_clctstd_rule_codg` VARCHAR(50) COMMENT '1月基数规则编码',
    `nov_wag` DECIMAL(20,4) COMMENT '11月工资',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `dec_wag` DECIMAL(20,4) COMMENT '12月工资',
    `may_wag` DECIMAL(20,4) COMMENT '5月工资',
    `july_wag` DECIMAL(20,4) COMMENT '7月工资',
    `sept_clctstd_rule_codg` VARCHAR(50) COMMENT '9月基数规则编码',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `psn_clctstd_sn` VARCHAR(20) COMMENT '人员缴费基数流水号 复合主键',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `year` VARCHAR(4) COMMENT '年度',
    `insutype_retr_flag` VARCHAR(6) COMMENT '险种离退休标志 0在职；1退休；2离退休',
    `jan_wag` DECIMAL(20,4) COMMENT '1月工资',
    `jan_psn_clctstd` DECIMAL(20,4) COMMENT '1月人员缴费基数',
    `feb_wag` DECIMAL(20,4) COMMENT '2月工资',
    `feb_clctstd_rule_codg` VARCHAR(50) COMMENT '2月基数规则编码',
    `feb_psn_clctstd` DECIMAL(20,4) COMMENT '2月人员缴费基数',
    `mar_wag` DECIMAL(20,4) COMMENT '3月工资',
    `mar_clctstd_rule_codg` VARCHAR(50) COMMENT '3月基数规则编码',
    `mar_psn_clctstd` DECIMAL(20,4) COMMENT '3月人员缴费基数',
    `apr_wag` DECIMAL(20,4) COMMENT '4月工资',
    `apr_clctstd_rule_codg` VARCHAR(50) COMMENT '4月基数规则编码',
    `apr_psn_clctstd` DECIMAL(20,4) COMMENT '4月人员缴费基数',
    `may_clctstd_rule_codg` VARCHAR(50) COMMENT '5月基数规则编码',
    `june_wag` DECIMAL(20,4) COMMENT '6月工资',
    `june_clctstd_rule_codg` VARCHAR(50) COMMENT '6月基数规则编码',
    `june_psn_clctstd` DECIMAL(20,4) COMMENT '6月人员缴费基数',
    `july_clctstd_rule_codg` VARCHAR(50) COMMENT '7月基数规则编码',
    `july_psn_clctstd` DECIMAL(20,4) COMMENT '7月人员缴费基数',
    `aug_wag` DECIMAL(20,4) COMMENT '8月工资',
    `aug_clctstd_rule_codg` VARCHAR(50) COMMENT '8月基数规则编码',
    `aug_psn_clctstd` DECIMAL(20,4) COMMENT '8月人员缴费基数',
    `sept_wag` DECIMAL(20,4) COMMENT '9月工资',
    `sept_psn_clctstd` DECIMAL(20,4) COMMENT '9月人员缴费基数',
    `oct_wag` DECIMAL(20,4) COMMENT '10月工资',
    `oct_clctstd_rule_codg` VARCHAR(50) COMMENT '10月基数规则编码',
    `oct_psn_clctstd` DECIMAL(20,4) COMMENT '10月人员缴费基数',
    `nov_clctstd_rule_codg` VARCHAR(50) COMMENT '11月基数规则编码',
    `nov_psn_clctstd` DECIMAL(20,4) COMMENT '11月人员缴费基数',
    `dec_clctstd_rule_codg` VARCHAR(50) COMMENT '12月基数规则编码',
    `dec_psn_clctstd` DECIMAL(20,4) COMMENT '12月人员缴费基数',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `opt_chnl` VARCHAR(3) COMMENT '经办渠道 经办渠道字典',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `wag_dcla_mgt_eid` VARCHAR(20) COMMENT '工资申报管理事件ID',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员缴费基数';

CREATE TABLE IF NOT EXISTS `psn_info_b` (
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_mgtcode` VARCHAR(30) COMMENT '人员管理码',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `brdy` DATE COMMENT '出生日期',
    `file_brdy` DATE COMMENT '档案出生日期',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` TEXT COMMENT '证件号码',
    `hsecfc` VARCHAR(100) COMMENT '电子凭证号',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `mob` VARCHAR(50) COMMENT '手机号码',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `email` VARCHAR(100) COMMENT '电子邮箱',
    `resd_natu` VARCHAR(3) COMMENT '户口性质 见户口性质字典',
    `hsreg_addr_poscode` VARCHAR(6) COMMENT '户籍地址邮政编码',
    `live_admdvs` VARCHAR(6) COMMENT '居住地医保区划',
    `live_addr` VARCHAR(200) COMMENT '居住地址',
    `live_addr_poscode` VARCHAR(6) COMMENT '居住地邮政编码',
    `memo` TEXT COMMENT '备注',
    `surv_stas` VARCHAR(3) COMMENT '生存状态 1 正常；2 死亡；3 被判刑收监或劳动教养；4 失踪；9 状态不明',
    `mul_prov_mnt_flag` VARCHAR(3) COMMENT '多省维护标志',
    `retr_type` VARCHAR(3) COMMENT '离退休类型 1 离休；2 正常退休；3 退职；4 因病退休；5 特殊工种退休；6 原工伤退休；7 政策性提前退休；8 经认定可提前退休；9 其他；99 一次性退休',
    `grad_schl` VARCHAR(50) COMMENT '毕业院校',
    `educ` VARCHAR(3) COMMENT '学历 学历代码GB 4658 2006',
    `pro_tech_duty_lv` VARCHAR(3) COMMENT '专业技术职务等级 GB T 8561 2001专业技术职务代码',
    `nat_prfs_qua_lv` VARCHAR(3) COMMENT '国家职业资格等级 GB T12407 2008职务级别代码',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `ver` VARCHAR(50) COMMENT '版本号',
    `cpr_flag` VARCHAR(3) COMMENT '对比标志',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `chk_time` DATETIME COMMENT '核查时间',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `nat_regn_code` VARCHAR(3) COMMENT '国家地区代码',
    `resdbook_no` VARCHAR(20) COMMENT '户口簿编号',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `polstas` VARCHAR(3) COMMENT '政治面貌',
    `mrg_stas` VARCHAR(3) COMMENT '婚姻状态',
    `fst_patc_job_date` DATE COMMENT '首次参加工作日期',
    `hlcon` VARCHAR(3) COMMENT '健康状况 1 健康或良好；2 一般或较弱；3 有慢性病；6 残疾',
    `admdut` VARCHAR(50) COMMENT '行政职务',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `alis` VARCHAR(100) COMMENT '别名',
    `hsreg_addr` VARCHAR(200) COMMENT '户籍地址',
    `chk_chnl` VARCHAR(30) COMMENT '核查渠道',
    `resd_loc_admdvs` VARCHAR(6) COMMENT '户口所在地医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员基本信息';

CREATE TABLE IF NOT EXISTS `rsdt_psn_clct_detl_d` (
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `opt_time` DATETIME COMMENT '经办时间',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `rsdt_clct_detl_id` VARCHAR(20) COMMENT '居民缴费明细ID 主键',
    `clct_bill_id` VARCHAR(20) COMMENT '征集通知单ID',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `insu_idet` VARCHAR(6) COMMENT '参保身份',
    `psn_idet_type` VARCHAR(30) COMMENT '人员身份类别 人员身份类别(psn_idet_type)',
    `elec_taxrpt_no` VARCHAR(20) COMMENT '电子税票号码',
    `cashym` VARCHAR(6) COMMENT '费款所属期 费款所属年月',
    `accrym_begn` VARCHAR(6) COMMENT '对应费款所属期起始 账目开始年月',
    `accrym_end` VARCHAR(6) COMMENT '对应费款所属期结束 账目截止年月',
    `psn_clct_amt` DECIMAL(20,4) COMMENT '个人缴费金额',
    `revs_flag` VARCHAR(3) COMMENT '核销标志 1未核销；2已核销',
    `clct_flag` VARCHAR(3) COMMENT '缴费标志 0否；1是',
    `clct_type` VARCHAR(6) COMMENT '缴费类型 缴费类型字典',
    `clct_time` DATETIME COMMENT '缴费时间',
    `quot_clct_flag` VARCHAR(3) COMMENT '定额征缴标志 0非定额1定额',
    `intsury_time` DATETIME COMMENT '入国库时间',
    `ursn_time` DATETIME COMMENT '上解时间',
    `dcla_prd` VARCHAR(3) COMMENT '申报周期 1月；2年',
    `insutype_retr_flag` VARCHAR(6) COMMENT '险种离退休标志 0在职；1退休；2离退休',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `taxdept_code` VARCHAR(16) COMMENT '主管税务部门代码',
    `init_rsdt_clct_detl_id` VARCHAR(20) COMMENT '原居民缴费明细ID',
    `plan_bchno` VARCHAR(20) COMMENT '计划执行批次号',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `finsubs_amt` DECIMAL(20,4) COMMENT '财政补助金额',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `oth_clct_amt` DECIMAL(20,4) COMMENT '其他缴费金额',
    `clct_sumamt` DECIMAL(20,4) COMMENT '缴费总金额',
    `send_flag` VARCHAR(3) COMMENT '发送标志',
    `clct_rule_type_codg` VARCHAR(50) COMMENT '征缴规则类型编码',
    `psn_clct_detl_id` VARCHAR(20) COMMENT '个人缴费明细ID 主键',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='居民人员缴费明细';

CREATE TABLE IF NOT EXISTS `staf_psn_clct_detl_d` (
    `emp_clct_detl_id` VARCHAR(100) COMMENT '单位缴费明细ID',
    `clct_bill_id` VARCHAR(20) COMMENT '征集通知单ID',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `cashym` VARCHAR(6) COMMENT '费款所属期 费款所属年月',
    `accrym_begn` VARCHAR(6) COMMENT '对应费款所属期起始 账目开始年月',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `psn_idet_type` VARCHAR(30) COMMENT '人员身份类别 人员身份类别(psn_idet_type)',
    `insu_idet` VARCHAR(6) COMMENT '参保身份',
    `emp_clctstd` DECIMAL(20,4) COMMENT '单位缴费基数',
    `psn_clctstd` DECIMAL(20,4) COMMENT '人员缴费基数',
    `wag` DECIMAL(20,4) COMMENT '工资',
    `quot_clct_flag` VARCHAR(3) COMMENT '定额征缴标志 0非定额1定额',
    `emp_clct_paraval` DECIMAL(20,4) COMMENT '单位缴费比例或定额标准',
    `emp_clct_amt` DECIMAL(20,4) COMMENT '单位缴费金额',
    `emp_clct_into_acct_amt` DECIMAL(20,4) COMMENT '单位缴费划入个人账户金额',
    `psn_clct_paraval` DECIMAL(20,4) COMMENT '个人缴费比例或定额标准',
    `psn_into_paraval` DECIMAL(20,4) COMMENT '个人缴费划入个人账户比例或定额标准',
    `psn_clct_amt` DECIMAL(20,4) COMMENT '个人缴费金额',
    `finsubs_traf_amt` DECIMAL(20,4) COMMENT '财政补助划拨金额',
    `oth_clct_traf_amt` DECIMAL(20,4) COMMENT '其他缴费划拨金额',
    `oth_clct_amt` DECIMAL(20,4) COMMENT '其他缴费金额',
    `finsubs_amt` DECIMAL(20,4) COMMENT '财政补助金额',
    `traf_sumamt` DECIMAL(20,4) COMMENT '划拨总金额',
    `clct_sumamt` DECIMAL(20,4) COMMENT '缴费总金额',
    `inte` DECIMAL(20,4) COMMENT '利息',
    `latefee` DECIMAL(20,4) COMMENT '滞纳金',
    `clct_flag` VARCHAR(3) COMMENT '缴费标志 0否；1是',
    `clct_time` DATETIME COMMENT '缴费时间',
    `arvler` VARCHAR(50) COMMENT '到账经办人',
    `arvl_bchno` VARCHAR(20) COMMENT '到账批次号',
    `revs_flag` VARCHAR(3) COMMENT '核销标志 1未核销；2已核销',
    `trafer` VARCHAR(100) COMMENT '划账人',
    `intsury_time` DATETIME COMMENT '入国库时间',
    `ursn_time` DATETIME COMMENT '上解时间',
    `dcla_prd` VARCHAR(3) COMMENT '申报周期 1月；2年',
    `elec_taxrpt_no` VARCHAR(20) COMMENT '电子税票号码',
    `peawkr_flag` VARCHAR(3) COMMENT '农民工标志 0非农名工；1农名工',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `insutype_retr_flag` VARCHAR(6) COMMENT '险种离退休标志 0在职；1退休；2离退休',
    `taxdept_code` VARCHAR(16) COMMENT '主管税务部门代码',
    `plan_bchno` VARCHAR(20) COMMENT '计划执行批次号',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `insu_clct_mons` DECIMAL(20,4) COMMENT '参保缴费月数',
    `init_psn_no` VARCHAR(30) COMMENT '原人员编号',
    `accter` VARCHAR(50) COMMENT '建账经办人',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `bill_flag` VARCHAR(3) COMMENT '征集标志',
    `send_flag` VARCHAR(3) COMMENT '发送标志',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `clct_type` VARCHAR(6) COMMENT '缴费类型 缴费类型字典',
    `clctstd_crtf_rule_codg` VARCHAR(50) COMMENT '基数核定规则类型编码',
    `psn_clct_into_acct_amt` DECIMAL(20,4) COMMENT '个人缴费划入个人账户金额',
    `clct_rule_type_codg` VARCHAR(50) COMMENT '征缴规则类型编码',
    `accrym_end` VARCHAR(6) COMMENT '对应费款所属期结束 账目截止年月',
    `traf_time` DATETIME COMMENT '划拨时间',
    `traf_flag` VARCHAR(3) COMMENT '划拨标志',
    `emp_into_paraval` DECIMAL(20,4) COMMENT '单位缴费划入个人账户比例或定额标准',
    `init_psn_clct_detl_id` VARCHAR(20) COMMENT '原个人缴费明细ID',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职工人员缴费明细';

CREATE TABLE IF NOT EXISTS `setl_diag_list_d` (
    `diag_name` VARCHAR(500) COMMENT '诊断名称',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `evt_inst_id` VARCHAR(40) COMMENT '事件实例ID',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `diag_info_id` VARCHAR(30) COMMENT '诊断信息ID',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `inout_diag_type` VARCHAR(6) COMMENT '出入院诊断类别',
    `diag_type` VARCHAR(3) COMMENT '诊断类别 诊断类别 diag_type',
    `maindiag_flag` VARCHAR(3) COMMENT '主诊断标志 0 否；1 是',
    `diag_srt_no` DECIMAL(20,4) COMMENT '诊断排序号',
    `diag_code` VARCHAR(30) COMMENT '诊断代码 医保疾病诊断与分类代码',
    `adm_cond` VARCHAR(500) COMMENT '入院病情',
    `diag_dept` VARCHAR(50) COMMENT '诊断科室',
    `diag_dr_code` VARCHAR(30) COMMENT '诊断医师代码 医保医师代码',
    `diag_dr_name` VARCHAR(50) COMMENT '诊断医师姓名',
    `diag_time` DATETIME COMMENT '诊断时间',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='处方诊断信息';

CREATE TABLE IF NOT EXISTS `rx_setl_info_d` (
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `hi_rxno` VARCHAR(30) COMMENT '医保处方号',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊编号',
    `prsc_fixmedins_code` VARCHAR(20) COMMENT '开方定点医药机构编号',
    `fixmedins_code` VARCHAR(20) COMMENT '结算定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '结算定点医药机构名称',
    `hsecfc` VARCHAR(100) COMMENT '电子凭证号',
    `setl_time` DATETIME COMMENT '结算时间',
    `hi_feesetl_flag` VARCHAR(3) COMMENT '医保结算标志',
    `mmp_flag` VARCHAR(3) COMMENT '医保移动支付标志位',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '总费用 本次需要结算的医疗费用总额 不包括处方药品费用',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `inscp_scp_amt` DECIMAL(20,4) COMMENT '符合政策范围金额',
    `act_pay_dedc` DECIMAL(20,4) COMMENT '实际支付起付线',
    `pool_prop_selfpay` DECIMAL(20,4) COMMENT '基本医疗保险统筹支付比例',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金基金支出',
    `hifes_pay` DECIMAL(20,4) COMMENT '企业补充医疗保险基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '居民大病补充医疗保险基金资金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '职工大额医疗费用补助基金支出',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `oth_pay` DECIMAL(20,4) COMMENT '其他支出',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `psn_cash_pay` DECIMAL(20,4) COMMENT '现金支付费用',
    `hi_sumfee` DECIMAL(20,4) COMMENT '医保费用总额',
    `psn_part_amt` DECIMAL(20,4) COMMENT '个人负担总金额',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号处方中心',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `rx_setl_info_id` VARCHAR(40) COMMENT '主键 复合主键',
    `hifp_pay` DECIMAL(20,4) COMMENT '基本医疗保险统筹基金支出',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `refd_setl_flag` VARCHAR(3) COMMENT '退款结算标志位 0 否；1 是',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支付',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `insu_plc_no` VARCHAR(6) COMMENT '参保地',
    `feedetl_sn` VARCHAR(30) COMMENT '结算费用明细流水号 复合主键',
    `fixmedins_poolarea_no` VARCHAR(6) COMMENT '定点机构统筹区',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `rx_setl_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='处方明细信息';

CREATE TABLE IF NOT EXISTS `rx_setl_drug_d` (
    `med_list_codg` VARCHAR(50) COMMENT '医疗目录编码',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `rx_setl_info_id` VARCHAR(40) COMMENT '处方结算信息表主键 复合主键',
    `medins_list_codg` VARCHAR(50) COMMENT '医药机构药品编号',
    `prsc_med_list_codg` VARCHAR(50) COMMENT '开方药品医疗目录编码',
    `hi_rxno` VARCHAR(30) COMMENT '医保处方号',
    `fixmedins_name` VARCHAR(255) COMMENT '结算定点医药机构名称',
    `drug_genname` VARCHAR(100) COMMENT '药品通用名',
    `drug_spec` VARCHAR(500) COMMENT '药品规格',
    `drug_dosform` VARCHAR(200) COMMENT '药品剂型',
    `manu_lotnum` VARCHAR(30) COMMENT '生产批号',
    `prdr_name` TEXT COMMENT '生厂厂家',
    `bchno` VARCHAR(30) COMMENT '批次号',
    `cnt` DECIMAL(20,4) COMMENT '药品总数量',
    `drug_emp` VARCHAR(6) COMMENT '药品单位',
    `det_item_fee_sumamt` DECIMAL(20,4) COMMENT '明细总额',
    `vali_flag` VARCHAR(3) COMMENT '有效标志位 0无效；1有效',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `rx_setl_drug_id` VARCHAR(40) COMMENT '处方结算药品信息编号 复合主键',
    `pric` DECIMAL(20,4) COMMENT '单价',
    `aprvno` VARCHAR(100) COMMENT '批准文号',
    `drug_prodname` VARCHAR(100) COMMENT '药品商品名',
    `fixmedins_code` VARCHAR(50) COMMENT '结算定点医药机构编号',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    PRIMARY KEY (`biz_date`, `rx_setl_drug_id`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='结算药品明细信息';

CREATE TABLE IF NOT EXISTS `rx_indx_info_b` (
    `rid` VARCHAR(40) COMMENT '唯一记录号',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `hi_rxno` VARCHAR(30) COMMENT '医保处方号',
    `hsecfc` VARCHAR(100) COMMENT '电子凭证号',
    `rx_trace_code` VARCHAR(30) COMMENT '处方追溯码',
    `cert_type` VARCHAR(3) COMMENT '证件类型 身份证件类别代码表',
    `psn_name` VARCHAR(256) COMMENT '人员姓名',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `rx_stas_codg` VARCHAR(3) COMMENT '处方状态编号 0 待上传1 有效2 已失效3 已撤回4 未上传',
    `rx_stas_name` VARCHAR(20) COMMENT '处方状态名称',
    `rx_used_stas_codg` VARCHAR(3) COMMENT '处方使用状态编号 1 未使用2 已使用',
    `rx_used_stas_name` VARCHAR(20) COMMENT '处方使用状态名称',
    `rx_chk_stas_codg` VARCHAR(10) COMMENT '处方审核状态代码 处方审核状态 rx_chk_stas_codg',
    `rx_chk_stas_name` VARCHAR(10) COMMENT '处方审核状态名称',
    `fixmedins_code` VARCHAR(20) COMMENT '定点医疗机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医疗机构名称',
    `chk_fixmedins_code` VARCHAR(20) COMMENT '审核定点医疗机构编号',
    `chk_fixmedins_name` VARCHAR(255) COMMENT '审核定点医疗机构名称',
    `setl_fixmedins_code` VARCHAR(20) COMMENT '结算定点医疗机构编号',
    `setl_fixmedins_name` VARCHAR(255) COMMENT '结算定点医疗机构名称',
    `prsc_dept_name` VARCHAR(50) COMMENT '开方科室名称',
    `prsc_dept_code` VARCHAR(30) COMMENT '开方科室编号 科室字典表',
    `prsc_dr_name` VARCHAR(256) COMMENT '开方医生姓名',
    `maindiag_code` VARCHAR(30) COMMENT '主诊断代码 医保疾病诊断与分类代码',
    `maindiag_name` VARCHAR(100) COMMENT '主诊断名称',
    `mdtrt_time` DATETIME COMMENT '就诊时间',
    `prsc_time` DATETIME COMMENT '开方时间',
    `rx_used_time` DATETIME COMMENT '处方使用时间',
    `rx_chk_time` DATETIME COMMENT '处方审核时间',
    `insu_plc_no` VARCHAR(6) COMMENT '参保地编号',
    `phac_poolarea_no` VARCHAR(6) COMMENT '零售药店统筹区编号',
    `setl_insuplc_no` VARCHAR(6) COMMENT '结算参保地编号',
    `souc_poolarea_no` VARCHAR(6) COMMENT '来源统筹区编号',
    `crte_time` DATETIME COMMENT '创建时间',
    `updt_time` DATETIME COMMENT '更新时间',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `rx_indx_info_id` VARCHAR(40) COMMENT '索引信息编号',
    `out_rx_flag` VARCHAR(3) COMMENT '异地处方标志位 0否；1是',
    `vali_end_time` DATETIME COMMENT '有效结束时间',
    `certno` VARCHAR(256) COMMENT '中心编号',
    `rx_hsecfc` VARCHAR(100) COMMENT '处方电子凭证',
    `hosp_poolarea_no` VARCHAR(6) COMMENT '医疗机构统筹区编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `rx_indx_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='处方信息';

CREATE TABLE IF NOT EXISTS `dip_fund_setl_ext_d` (
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `fund_setl_info_id` VARCHAR(40) COMMENT '基金结算信息编号',
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `fixmedins_lv` VARCHAR(4) COMMENT '定点医疗机构等级',
    `grp_diag_code` VARCHAR(255) COMMENT '分组诊断代码',
    `grp_diag_name` VARCHAR(255) COMMENT '分组诊断名称',
    `schm_detl_id` VARCHAR(40) COMMENT '方案详情编号',
    `est_schm_detl_list_id` VARCHAR(40) COMMENT '测算方案明细目录编号',
    `grp_dise_sco` DECIMAL(20,4) COMMENT '分组病种分值',
    `act_setl_sco` DECIMAL(20,4) COMMENT '实际结算分值',
    `pre_setl_pt_val` DECIMAL(20,4) COMMENT '预结算点值',
    `pay_std` DECIMAL(20,4) COMMENT '支付标准',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `dip_hifp_pay` DECIMAL(20,4) COMMENT 'DIP统筹基金支出',
    `dip_hifmi_pay` DECIMAL(20,4) COMMENT 'DIP大病保险基金支出',
    `dip_cvlserv_pay` DECIMAL(20,4) COMMENT 'DIP公务员支出',
    `dip_maf_pay` DECIMAL(20,4) COMMENT 'DIP医疗救助基金支出',
    `psn_selfpay` DECIMAL(20,4) COMMENT '个人自付',
    `dedc_std` DECIMAL(20,4) COMMENT '起付标准',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `setl_end_date` DATETIME COMMENT '结算结束日期',
    `grp_flag` VARCHAR(3) COMMENT '人群标志',
    `norm_flag` VARCHAR(2) COMMENT '正常标志',
    `dise_list_type` VARCHAR(3) COMMENT '疾病目录类别',
    `dise_sco` DECIMAL(20,4) COMMENT '病种分值',
    `drug_sco` DECIMAL(20,4) COMMENT '药品分值',
    `mcs_sco` DECIMAL(20,4) COMMENT '耗材分值',
    `oprn_grp_code` TEXT COMMENT '手术分组代码',
    `oprn_grp_name` TEXT COMMENT '手术分组名称',
    `sco_schm_id` VARCHAR(40) COMMENT '分值方案编号',
    `dept_cof` DECIMAL(20,4) COMMENT '科室系数',
    `dise_lv_cof` DECIMAL(20,4) COMMENT '病种等级系数',
    `fixmedins_cof` DECIMAL(20,4) COMMENT '定点医疗机构系数',
    `updt_cof` DECIMAL(20,4) COMMENT '更新系数',
    `oprn_lv` VARCHAR(10) COMMENT '手术等级',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `remarks` VARCHAR(255) COMMENT '备注信息',
    `dyna_remarks` TEXT COMMENT '动态备注信息',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `chk_type` VARCHAR(3) COMMENT '审核类型',
    `dip_dise_grp_code` VARCHAR(200) COMMENT '按病种分值付费病种组代码',
    `dip_dise_grp_name` VARCHAR(500) COMMENT '按病种分值付费病种组名称',
    `tcm_adt_dise_used_flag` VARCHAR(3) COMMENT '中医优势病种使用标志',
    `tcm_adt_dise_name` VARCHAR(255) COMMENT '中医优势病种名称',
    `tcm_adt_dise_cof` DECIMAL(20,4) COMMENT '中医优势病种系数',
    `sco_cof` DECIMAL(20,4) COMMENT '分值系数',
    `sco_stas` VARCHAR(6) COMMENT '分值状态',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `sp_markup_sco` DECIMAL(20,4) COMMENT '特殊加成分值',
    `sp_item_markup` VARCHAR(500) COMMENT '特殊项目加成',
    `asst_list_flag` VARCHAR(6) COMMENT '辅助目录标志',
    `asst_list_detl` TEXT COMMENT '辅助目录详情',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `clr_optins` VARCHAR(6) COMMENT '清算经办机构',
    `oprn_cof` DECIMAL(20,4) COMMENT '手术系数',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `dise_list_name` VARCHAR(40) COMMENT '病种目录名称',
    `tcm_dise_flag` VARCHAR(3) COMMENT '中医病种标志',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `nuld_flag` VARCHAR(3) COMMENT '未上传标志',
    `act_clr_ym` VARCHAR(6) COMMENT '实际清算月份',
    `dise_list_code` VARCHAR(60) COMMENT '病种目录代码',
    `tcm_dise_cof` DECIMAL(20,4) COMMENT '中医病种系数',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `dip_hifob_pay` DECIMAL(20,4) COMMENT 'DIP大额救助医疗基金支出',
    `mon_pre_pay_info_id` VARCHAR(40) COMMENT '月度预付款信息编号',
    `dip_hifdm_pay` DECIMAL(20,4) COMMENT 'DIP伤残军人补助基金支出',
    `dip_hifes_pay` DECIMAL(20,4) COMMENT 'DIP补充医疗保险基金支出',
    `tcm_adt_dise_code` VARCHAR(30) COMMENT '中医优势病种编码',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    PRIMARY KEY (`psn_no`, `fund_setl_info_id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DIP基金结算信息';

CREATE TABLE IF NOT EXISTS `bydise_setl_reg_d` (
    `appy_date` DATE COMMENT '申请日期',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `bydise_setl_dise_name` VARCHAR(500) COMMENT '按病种结算病种名称',
    `memo` TEXT COMMENT '备注',
    `opt_time` DATETIME COMMENT '经办时间',
    `biz_date` DATE COMMENT '业务日期',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `dcla_souc` VARCHAR(6) COMMENT '申报来源 申报来源(dcla_souc)',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` TEXT COMMENT '证件号码',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `bydise_setl_list_code` VARCHAR(30) COMMENT '按病种结算病种目录代码',
    `oprn_oprt_code` VARCHAR(200) COMMENT '手术操作代码 医保版手术操作与分类代码',
    `oprn_oprt_name` VARCHAR(500) COMMENT '手术操作名称',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `appy_rea` TEXT COMMENT '申请理由',
    `agnter_name` VARCHAR(50) COMMENT '代办人姓名',
    `agnter_cert_type` VARCHAR(6) COMMENT '代办人证件类型 身份证件类别代码表',
    `agnter_certno` VARCHAR(50) COMMENT '代办人证件号码',
    `agnter_tel` VARCHAR(30) COMMENT '代办人联系方式',
    `agnter_addr` VARCHAR(200) COMMENT '代办人联系地址',
    `agnter_rlts` VARCHAR(3) COMMENT '代办人关系 代办人关系(agnter_rlts)',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`trt_dcla_detl_sn`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='按病种结算登记信息';

CREATE TABLE IF NOT EXISTS `setl_d` (
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `medins_setl_id` VARCHAR(30) COMMENT '医药机构结算ID',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `manl_reim_rea` VARCHAR(6) COMMENT '零星报销原因',
    `clr_optins` VARCHAR(6) COMMENT '清算经办机构',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `mdtrt_cert_type` VARCHAR(3) COMMENT '就诊凭证类型 就诊凭证类型(mdtrt_cert_type)',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `setl_type` VARCHAR(6) COMMENT '结算类别 结算类别字典表',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `refd_setl_flag` VARCHAR(3) COMMENT '退费结算标志 0 否；1 是',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `init_setl_id` VARCHAR(30) COMMENT '原结算ID',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `age` DECIMAL(20,4) COMMENT '年龄',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `cvlserv_lv` VARCHAR(6) COMMENT '公务员等级 公务员等级(cvlserv_lv)',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `sp_psn_type_lv` VARCHAR(3) COMMENT '特殊人员类型等级',
    `clct_grde` VARCHAR(3) COMMENT '缴费档次 缴费档次 clctGrde',
    `flxempe_flag` VARCHAR(3) COMMENT '灵活就业标志 0 否；1 是',
    `nwb_flag` VARCHAR(3) COMMENT '新生儿标志 0否；1是',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `lmtpric_hosp_lv` VARCHAR(6) COMMENT '限价医院等级 限价医院等级字典表',
    `setl_time` DATETIME COMMENT '结算时间',
    `dedc_hosp_lv` VARCHAR(6) COMMENT '起付线医院等级 起付线医院等级字典表',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `mdtrt_cert_no` VARCHAR(50) COMMENT '就诊凭证编号',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `psn_setlway` VARCHAR(6) COMMENT '个人结算方式 个人结算方式 psn_setlway',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `dedc_std` DECIMAL(20,4) COMMENT '起付标准',
    `crt_dedc` DECIMAL(20,4) COMMENT '本次起付线',
    `act_pay_dedc` DECIMAL(20,4) COMMENT '实际支付起付线',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `pool_prop_selfpay` DECIMAL(20,4) COMMENT '基本医疗统筹支付比例',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额',
    `hifes_pay` DECIMAL(20,4) COMMENT '补充医疗保险基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `hifdm_pay` DECIMAL(20,4) COMMENT '伤残人员医疗保障基金支出',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `psn_pay` DECIMAL(20,4) COMMENT '个人支付金额',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `cash_payamt` DECIMAL(20,4) COMMENT '现金支付金额',
    `ownpay_hosp_part` DECIMAL(20,4) COMMENT '自费中医院负担部分',
    `balc` DECIMAL(20,4) COMMENT '余额',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `cal_ipt_cnt` VARCHAR(3) COMMENT '计算住院次数标志 0 否；1 是',
    `setl_cashpay_way` VARCHAR(6) COMMENT '结算现金支付方式',
    `year` VARCHAR(4) COMMENT '年度',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `invono` TEXT COMMENT '发票号',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `memo` VARCHAR(500) COMMENT '备注',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `mid_setl_flag` VARCHAR(3) COMMENT '中途结算标志',
    `acct_used_flag` VARCHAR(3) COMMENT '账户使用标志',
    `quts_type` VARCHAR(6) COMMENT '编制类型 编制类型字典',
    `bydise_setl_payamt` DECIMAL(20,4) COMMENT '按病种结算支付金额',
    `exct_item_fund_payamt` DECIMAL(20,4) COMMENT '除外项目基金支付金额',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    PRIMARY KEY (`subsys_codg`, `rid`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='结算信息';

CREATE TABLE IF NOT EXISTS `mdtrt_d` (
    `age` DECIMAL(20,4) COMMENT '年龄',
    `lmtpric_hosp_lv` VARCHAR(6) COMMENT '限价医院等级 限价医院等级字典表',
    `geso_val` DECIMAL(20,4) COMMENT '孕周数',
    `birctrl_type` VARCHAR(6) COMMENT '计划生育手术类别 计划生育手术类别(birctrl_type)',
    `quts_type` VARCHAR(6) COMMENT '编制类型 编制类型字典',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `pre_pay_flag` VARCHAR(3) COMMENT '先行支付标志 0 否；1 是',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `inhosp_stas` VARCHAR(3) COMMENT '在院状态 0出院；1在院',
    `opt_time` DATETIME COMMENT '经办时间',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `brdy` DATE COMMENT '出生日期',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `oprn_oprt_name` VARCHAR(500) COMMENT '手术操作名称',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `medins_setl_id` VARCHAR(30) COMMENT '医药机构结算ID',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `coner_name` VARCHAR(50) COMMENT '联系人姓名',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `cvlserv_lv` VARCHAR(6) COMMENT '公务员等级 公务员等级(cvlserv_lv)',
    `sp_psn_type_lv` VARCHAR(3) COMMENT '特殊人员类型等级',
    `clct_grde` VARCHAR(3) COMMENT '缴费档次 缴费档次 clctGrde',
    `flxempe_flag` VARCHAR(3) COMMENT '灵活就业标志 0 否；1 是',
    `nwb_flag` VARCHAR(3) COMMENT '新生儿标志 0否；1是',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `mdtrt_cert_type` VARCHAR(3) COMMENT '就诊凭证类型 就诊凭证类型(mdtrt_cert_type)',
    `dedc_hosp_lv` VARCHAR(6) COMMENT '起付线医院等级 起付线医院等级字典表',
    `begntime` DATETIME COMMENT '开始时间',
    `endtime` DATETIME COMMENT '结束时间',
    `mdtrt_cert_no` VARCHAR(50) COMMENT '就诊凭证编号',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `rloc_type` VARCHAR(6) COMMENT '异地安置类别',
    `ars_year_ipt_flag` VARCHAR(3) COMMENT '跨年度住院标志 0 否；1 是',
    `year` VARCHAR(4) COMMENT '年度',
    `refl_old_mdtrt_id` VARCHAR(30) COMMENT '转诊前就诊ID',
    `ipt_otp_no` VARCHAR(30) COMMENT '住院门诊号',
    `medrcdno` VARCHAR(30) COMMENT '病历号',
    `chfpdr_code` VARCHAR(30) COMMENT '主诊医师代码 医保医师代码',
    `adm_diag_dscr` VARCHAR(200) COMMENT '入院诊断描述',
    `adm_dept_codg` VARCHAR(30) COMMENT '入院科室编码',
    `adm_dept_name` VARCHAR(100) COMMENT '入院科室名称',
    `adm_bed` VARCHAR(50) COMMENT '入院床位',
    `wardarea_bed` VARCHAR(50) COMMENT '病区床位',
    `traf_dept_flag` VARCHAR(6) COMMENT '转科室标志 0 否；1 是',
    `dscg_maindiag_code` VARCHAR(30) COMMENT '住院主诊断代码 医保疾病诊断与分类代码',
    `dscg_dept_codg` VARCHAR(30) COMMENT '出院科室编码',
    `dscg_dept_name` VARCHAR(100) COMMENT '出院科室名称',
    `dscg_bed` VARCHAR(30) COMMENT '出院床位',
    `dscg_way` VARCHAR(3) COMMENT '离院方式 编码 1.医嘱离院2.医嘱转院 拟接收医疗机构名称3.医嘱转社区卫生服务机构 乡镇卫生院 拟接收医疗机构名称4.非医嘱离院5.死亡9.其他',
    `main_cond_dscr` TEXT COMMENT '主要病情描述',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `oprn_oprt_code` VARCHAR(200) COMMENT '手术操作代码 医保版手术操作与分类代码',
    `otp_diag_info` VARCHAR(200) COMMENT '门诊诊断信息',
    `die_date` DATE COMMENT '死亡日期',
    `ipt_days` DECIMAL(20,4) COMMENT '住院天数',
    `fetts` DECIMAL(20,4) COMMENT '胎次',
    `fetus_cnt` DECIMAL(20,4) COMMENT '胎儿数',
    `matn_type` VARCHAR(6) COMMENT '生育类别 生育类别(matn_type)',
    `prey_time` DATETIME COMMENT '妊娠时间',
    `latechb_flag` VARCHAR(3) COMMENT '晚育标志 0 否；1 是',
    `pret_flag` VARCHAR(3) COMMENT '早产标志 0 否；1 是',
    `fpsc_no` VARCHAR(50) COMMENT '计划生育服务证号',
    `birctrl_matn_date` DATETIME COMMENT '计划生育手术或生育日期',
    `cop_flag` VARCHAR(3) COMMENT '伴有并发症标志 0 否；1 是',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `memo` VARCHAR(500) COMMENT '备注',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `chfpdr_name` VARCHAR(50) COMMENT '主诊医师姓名',
    `dscg_maindiag_name` VARCHAR(300) COMMENT '住院主诊断名称',
    `adm_caty` VARCHAR(10) COMMENT '入院科别 填写院内科室代码',
    `dscg_caty` VARCHAR(10) COMMENT '出院科别 填写院内科室代码',
    `ttp_pay_flag` VARCHAR(3) COMMENT '第三方赔付标志',
    `ttp_pay_prop` DECIMAL(20,4) COMMENT '第三方赔付比例',
    `dise_type_code` VARCHAR(30) COMMENT '病种类型代码 病种类型字典',
    `same_dise_adm_flag` VARCHAR(3) COMMENT '同病种入院标志',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    PRIMARY KEY (`subsys_codg`, `rid`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='就诊信息';

CREATE TABLE IF NOT EXISTS `opsp_reg_d` (
    `appy_date` DATE COMMENT '申请日期',
    `appy_rea` TEXT COMMENT '申请理由',
    `agnter_name` VARCHAR(50) COMMENT '代办人姓名',
    `agnter_cert_type` VARCHAR(6) COMMENT '代办人证件类型 身份证件类别代码表',
    `agnter_certno` VARCHAR(50) COMMENT '代办人证件号码',
    `agnter_tel` VARCHAR(30) COMMENT '代办人联系方式',
    `agnter_addr` VARCHAR(200) COMMENT '代办人联系地址',
    `agnter_rlts` VARCHAR(3) COMMENT '代办人关系 代办人关系(agnter_rlts)',
    `enddate` DATE COMMENT '结束日期',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opt_time` DATETIME COMMENT '经办时间',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `selfdef_splm_flag` VARCHAR(20) COMMENT '自定义补充标志',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `opsp_dise_code` VARCHAR(30) COMMENT '门慢门特病种目录代码',
    `begndate` DATE COMMENT '开始日期',
    `dcla_souc` VARCHAR(6) COMMENT '申报来源 申报来源(dcla_souc)',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `dise_type_code` VARCHAR(30) COMMENT '病种类型代码 病种类型字典',
    `opsp_dise_name` VARCHAR(300) COMMENT '门慢门特病种名称',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `ide_fixmedins_no` VARCHAR(30) COMMENT '鉴定定点医药机构编号',
    `ide_fixmedins_name` VARCHAR(200) COMMENT '鉴定定点医药机构名称',
    `hosp_ide_date` DATE COMMENT '医院鉴定日期',
    `diag_dr_name` VARCHAR(50) COMMENT '诊断医师姓名',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `brdy` DATE COMMENT '出生日期',
    `diag_dr_code` VARCHAR(30) COMMENT '诊断医师代码 医保医师代码',
    `memo` VARCHAR(500) COMMENT '备注',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门慢门特登记信息';

CREATE TABLE IF NOT EXISTS `trum_chk_reg_d` (
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `certno` TEXT COMMENT '证件号码',
    `biz_used_flag` VARCHAR(3) COMMENT '业务使用标志 0否；1是',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `agnter_addr` VARCHAR(200) COMMENT '代办人联系地址',
    `chk_pay_flag` VARCHAR(3) COMMENT '审核支付标志 01可予支付；02不予支付',
    `dcla_souc` VARCHAR(6) COMMENT '申报来源 申报来源(dcla_souc)',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `trum_part` VARCHAR(50) COMMENT '伤害部位',
    `trum_time` DATETIME COMMENT '受伤时间',
    `trum_site` VARCHAR(200) COMMENT '受伤地点',
    `trum_rea` VARCHAR(200) COMMENT '致伤原因',
    `adm_mtd` VARCHAR(3) COMMENT '入院方式',
    `adm_time` DATETIME COMMENT '入院时间',
    `adm_diag_dscr` VARCHAR(200) COMMENT '入院诊断描述',
    `agnter_name` VARCHAR(50) COMMENT '代办人姓名',
    `agnter_cert_type` VARCHAR(6) COMMENT '代办人证件类型 身份证件类别代码表',
    `agnter_certno` VARCHAR(50) COMMENT '代办人证件号码',
    `agnter_tel` VARCHAR(30) COMMENT '代办人联系方式',
    `agnter_rlts` VARCHAR(3) COMMENT '代办人关系 代办人关系(agnter_rlts)',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `memo` TEXT COMMENT '备注',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `ttp_pay_prop` DECIMAL(20,4) COMMENT '第三方赔付比例',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='外伤审核登记信息';

CREATE TABLE IF NOT EXISTS `refl_appy_d` (
    `tel` VARCHAR(50) COMMENT '联系电话',
    `agnter_name` VARCHAR(50) COMMENT '代办人姓名',
    `agnter_addr` VARCHAR(200) COMMENT '代办人联系地址',
    `certno` TEXT COMMENT '证件号码',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `refl_rea` VARCHAR(100) COMMENT '转诊转院原因',
    `dcla_souc` VARCHAR(6) COMMENT '申报来源 申报来源(dcla_souc)',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `diag_name` VARCHAR(255) COMMENT '诊断名称',
    `mdtrtarea_admdvs` VARCHAR(6) COMMENT '就医地医保区划',
    `refl_opnn` VARCHAR(200) COMMENT '转院意见',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `brdy` DATE COMMENT '出生日期',
    `refl_setl_flag` VARCHAR(3) COMMENT '转院结算标志 0否；1是',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `diag_code` VARCHAR(30) COMMENT '诊断代码 医保疾病诊断与分类代码',
    `drord` TEXT COMMENT '医嘱',
    `dise_cond_dscr` TEXT COMMENT '疾病病情描述',
    `reflin_medins_no` VARCHAR(30) COMMENT '转往定点医药机构编号',
    `reflin_medins_name` VARCHAR(200) COMMENT '转往医院名称',
    `out_flag` VARCHAR(3) COMMENT '异地标志 0否；1是',
    `refl_date` DATE COMMENT '转院日期',
    `agnter_cert_type` VARCHAR(6) COMMENT '代办人证件类型 身份证件类别代码表',
    `agnter_certno` VARCHAR(50) COMMENT '代办人证件号码',
    `agnter_tel` VARCHAR(30) COMMENT '代办人联系方式',
    `agnter_rlts` VARCHAR(3) COMMENT '代办人关系 代办人关系(agnter_rlts)',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `refl_old_mdtrt_id` VARCHAR(30) COMMENT '转诊前就诊ID',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `hosp_agre_refl_flag` VARCHAR(3) COMMENT '医院同意转院标志 0否；1是',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `memo` TEXT COMMENT '备注',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `refl_fil_type` VARCHAR(6) COMMENT '转院备案类别',
    `allo_setl_cnt` DECIMAL(20,4) COMMENT '允许结算次数',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='转院申请信息';

CREATE TABLE IF NOT EXISTS `eps_setl_acct_info_b` (
    `acct_sn` VARCHAR(30) COMMENT '唯一标识',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `orgname` VARCHAR(200) COMMENT '机构名称',
    `insutype_code` VARCHAR(30) COMMENT '险种编号',
    `bankacct` VARCHAR(32) COMMENT '银行账号',
    `bank` VARCHAR(100) COMMENT '开户银行',
    `acctname` VARCHAR(100) COMMENT '开户户名',
    `setl_cent_no` VARCHAR(50) COMMENT '结算中心编号',
    `acct_rpot_addr` VARCHAR(500) COMMENT '账户报告地址',
    `same_bank_flag` VARCHAR(1) COMMENT '是否同行YN',
    `samecity_flag` VARCHAR(1) COMMENT '是否同城YN',
    `bankcode` VARCHAR(20) COMMENT '银行行号',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `vali_flag` VARCHAR(3) COMMENT '有效标识 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `orgcode` VARCHAR(50) COMMENT '机构编号',
    `phone` VARCHAR(30) COMMENT '电话号码',
    `acct_blng_place` VARCHAR(100) COMMENT '账户归属地',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `insutype_name` VARCHAR(50) COMMENT '险种名称',
    `setl_cent_name` VARCHAR(50) COMMENT '结算中心名称',
    PRIMARY KEY (`acct_sn`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机构结算账户基本信息';

CREATE TABLE IF NOT EXISTS `trt_setl_fund_sbit_d` (
    `biz_date` DATETIME COMMENT '业务日期',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `clr_type_lv2` VARCHAR(6) COMMENT '二级清算类别 二级清算类别 clr_type_lv2',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系编号 主键',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `fund_pay_type` VARCHAR(10) COMMENT '基金支付类型 基金支付类型(fund_pay_type)',
    `fund_payamt` DECIMAL(20,4) COMMENT '基金支付金额',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊编号',
    `year` VARCHAR(4) COMMENT '年度',
    `ym` VARCHAR(6) COMMENT '年月',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `crt_payb_lmt_amt` DECIMAL(20,4) COMMENT '本次可支付限额金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `setl_proc_info` TEXT COMMENT '结算过程信息',
    `poolarea_fund_pay_type` VARCHAR(10) COMMENT '统筹区基金支付类型',
    `poolarea_fund_pay_name` VARCHAR(200) COMMENT '统筹区基金支付类型名称',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `pay_prop` DECIMAL(20,4) COMMENT '支付比例',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `psn_no`, `fund_pay_type`, `setl_id`, `poolarea_fund_pay_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金结算清单基金支付信息';

CREATE TABLE IF NOT EXISTS `setl_detl_d` (
    `exra_serv_flag` VARCHAR(2) COMMENT '是否需要伴随服务 0否；1是',
    `sprt_reate` VARCHAR(3) COMMENT '是否支持翻新 0否；1是',
    `comb_flag` VARCHAR(2) COMMENT '是否组套 0否；1是',
    `adjust_flag` VARCHAR(2) COMMENT '是否调价 0否；1是',
    `chker_begin_time` DATETIME COMMENT '本次流程到达管理端时间',
    `prcs_stas` VARCHAR(2) COMMENT '流程状态',
    `used` VARCHAR(100) COMMENT '用途',
    `invo_supplement_flag` VARCHAR(3) COMMENT '票据补充标志',
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `mcs_comb_prod_detail_id` VARCHAR(40) COMMENT '组套产品明细编号',
    `mcs_comb_id` VARCHAR(40) COMMENT '组套产品编号',
    `comb_ver` VARCHAR(50) COMMENT '组套版本号',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `init_para_id` VARCHAR(40) COMMENT '结算初始参数编号',
    `setl_pric` DECIMAL(20,4) COMMENT '结算单价',
    `setl_invo_apply_id` VARCHAR(40) COMMENT '结算发票申请单编号',
    `setl_ym` VARCHAR(6) COMMENT '结算年月',
    `adjm_setl_sumpric` DECIMAL(20,4) COMMENT '结算总价调整后',
    `setlway` VARCHAR(3) COMMENT '结算方式',
    `setl_node_stas` VARCHAR(3) COMMENT '结算环节状态',
    `ord_code` VARCHAR(40) COMMENT '订单代码',
    `ord_souc` VARCHAR(3) COMMENT '订单来源 0：新平台下单1 平台下单2 凯特数据迁移4 三明数据迁移10 接口下单',
    `ot_npay_stas` VARCHAR(3) COMMENT '超时未支付状态',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `settle_submit_time` DATETIME COMMENT '配送企业提交结算时间',
    `delventp_stmt_stas` VARCHAR(3) COMMENT '配送对账状态',
    `comb_cnt` DECIMAL(20,4) COMMENT '采购组套套数',
    `pre_chk_flag` VARCHAR(3) COMMENT '预审标识',
    `item_codg` VARCHAR(50) COMMENT '项目编码',
    `pre_chk_time` DATETIME COMMENT '预审时间',
    `biz_date` DATETIME COMMENT '业务日期',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `manu_lotnum` VARCHAR(200) COMMENT '数量',
    `prnt_medins_code` VARCHAR(40) COMMENT '上级医疗机构代码',
    `prnt_medins_name` VARCHAR(200) COMMENT '上级医疗机构名称',
    `biz_id` VARCHAR(40) COMMENT '业务编号',
    `seld_id` VARCHAR(20) COMMENT '中选产品标识',
    `main_parts` VARCHAR(3) COMMENT '主部件 0否；1是',
    `detl_cpr_fail_fld` VARCHAR(200) COMMENT '二票明细对比不通过的字段逗号分割',
    `detl_cpr_stas` VARCHAR(3) COMMENT '二票明细对比状态',
    `snap_id` VARCHAR(40) COMMENT '交易目录快照编号',
    `prod_name` VARCHAR(200) COMMENT '产品名称',
    `prod_type` VARCHAR(3) COMMENT '产品类型 1 药品2 医用耗材',
    `prod_id` VARCHAR(40) COMMENT '产品编号',
    `exra_serv_pric` DECIMAL(20,4) COMMENT '伴随服务价格',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `fchker` VARCHAR(30) COMMENT '初审审核人',
    `chk_opnn` VARCHAR(255) COMMENT '初审意见',
    `chk_time` DATETIME COMMENT '初审时间',
    `fchk_time` DATETIME COMMENT '核对时间',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `setl_detl_medins_stas` VARCHAR(3) COMMENT '医疗机构明细状态',
    `medins_setldoc_id` VARCHAR(40) COMMENT '医疗机构结算单编号',
    `hosp_list_id` VARCHAR(40) COMMENT '医院目录编号',
    `ori_setl_detl_id` VARCHAR(40) COMMENT '原结算明细编号',
    `invo_cplt_flag` VARCHAR(3) COMMENT '发票完整标志',
    `hosp_bidprcu_item_id` VARCHAR(40) COMMENT '院端招采项目编号',
    `invo_date` DATETIME COMMENT '发票日期',
    `rchk_opnn` VARCHAR(255) COMMENT '复查意见',
    `memo` VARCHAR(500) COMMENT '备注',
    `rchker` VARCHAR(30) COMMENT '复查审核人',
    `review_failed` VARCHAR(3) COMMENT '复核不通过时标记为',
    `chker` VARCHAR(50) COMMENT '审核人',
    `chktime` DATETIME COMMENT '审核时间',
    `chk_org` VARCHAR(20) COMMENT '审核组织机构',
    `stmt_id` VARCHAR(40) COMMENT '对账编号',
    `stroom_id` VARCHAR(30) COMMENT '库房',
    `fchker_id` VARCHAR(50) COMMENT '当前流程初审人员编号',
    `chker_id` VARCHAR(50) COMMENT '当前流程复审人员编号',
    `rchker_id` VARCHAR(50) COMMENT '当前流程复查人员编号',
    `wait_chker_name` VARCHAR(100) COMMENT '待初审姓名',
    `wait_chker_id` VARCHAR(100) COMMENT '待初审编号',
    `wait_rchk_id` VARCHAR(100) COMMENT '待复审编号',
    `check_bchno` VARCHAR(20) COMMENT '批次编号抽检',
    `fail_chk_rea` TEXT COMMENT '拒绝审核原因',
    `pubonln_stas` VARCHAR(3) COMMENT '挂网状态 挂网状态字典表',
    `paydoc_id` VARCHAR(30) COMMENT '支付单编号',
    `payins_code` VARCHAR(50) COMMENT '支付方机构代码',
    `payins_name` VARCHAR(200) COMMENT '支付机构名称',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crte_flag` VARCHAR(3) COMMENT '数据生成标志',
    `invalid_data_last_chk_rea` VARCHAR(200) COMMENT '无效数据流程上次审核原因',
    `invalid_data_fchker` VARCHAR(25) COMMENT '无效数据流程初审人',
    `invalid_data_fchk_opnn` VARCHAR(200) COMMENT '无效数据流程初审意见',
    `invalid_data_fchk_time` DATETIME COMMENT '无效数据流程初审时间',
    `invalid_data_prcs_stas` VARCHAR(3) COMMENT '无效数据流程初审流程',
    `invalid_data_chker` VARCHAR(25) COMMENT '无效数据流程复审人',
    `invalid_data_chk_opnn` VARCHAR(200) COMMENT '无效数据流程复审意见',
    `invalid_data_chk_time` DATETIME COMMENT '无效数据流程复审时间',
    `invalid_data_rchk_opnn` VARCHAR(200) COMMENT '无效数据流程复查意见',
    `invalid_data_fail_chk_rea` VARCHAR(200) COMMENT '无效数据流程拒绝审核原因',
    `invalid_data_submitter` VARCHAR(25) COMMENT '无效数据流程提交人',
    `invalid_data_submit_time` DATETIME COMMENT '无效数据流程提交时间',
    `invalid_data_declare` VARCHAR(500) COMMENT '无效数据申报说明',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `adm_re_check_opinion` VARCHAR(500) COMMENT '管理单位复审意见',
    `setl_detl_adm_stas` VARCHAR(3) COMMENT '管理机构明细状态',
    `setl_detl_biz_type` VARCHAR(3) COMMENT '结算明细业务类型',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `medins_stmt_stas` VARCHAR(3) COMMENT '医疗对账状态',
    `wait_rchk_name` VARCHAR(100) COMMENT '待复审姓名',
    `shpp_retn_time` DATETIME COMMENT '收退货时间',
    `expy_end_date` DATETIME COMMENT '有效期',
    `setl_sumpric` DECIMAL(20,4) COMMENT '结算总价',
    `setl_cnt` DECIMAL(20,4) COMMENT '结算数量',
    `invo_amt` DECIMAL(20,4) COMMENT '发票金额',
    `invalid_data_setl_node_stas` VARCHAR(3) COMMENT '无效数据环节状态',
    `setl_detl_id` VARCHAR(40) COMMENT '结算明细编号',
    `splm_flag` VARCHAR(3) COMMENT '标志 0否；1是',
    `itemname` VARCHAR(100) COMMENT '项目名称',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `last_chk_rea` TEXT COMMENT '上次审核原因',
    `prod_code` VARCHAR(50) COMMENT '产品代码',
    `invalid_data_rchk_time` DATETIME COMMENT '无效数据流程复查时间',
    `setldoc_id` VARCHAR(40) COMMENT '结算单编号',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `samp_flag` VARCHAR(20) COMMENT '取样标志',
    `rchk_time` DATETIME COMMENT '复查时间',
    `invalid_data_rchker` VARCHAR(25) COMMENT '无效数据流程复查人',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `setl_detl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金结算明细信息';

CREATE TABLE IF NOT EXISTS `mdcs_fund_setl_list_chrgitm_d` (
    `setl_list_chrgitm_id` VARCHAR(30) COMMENT '结算清单收费项目编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `item_sumamt` DECIMAL(20,4) COMMENT '项目总金额',
    `item_claa_amt` DECIMAL(20,4) COMMENT '项目甲类金额',
    `item_clab_amt` DECIMAL(20,4) COMMENT '项目乙类金额',
    `item_ownpay_amt` DECIMAL(20,4) COMMENT '项目自费金额',
    `item_oth_amt` DECIMAL(20,4) COMMENT '项目其他金额',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `sindise_code_name` VARCHAR(100) COMMENT '单病种代码名称',
    `daysrg_code_name` VARCHAR(100) COMMENT '日间手术代码名称',
    `sync_time` DATETIME COMMENT '同步时间',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `med_chrgitm_type` VARCHAR(6) COMMENT '医疗收费项目类别',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊编号',
    PRIMARY KEY (`setl_list_chrgitm_id`, `psn_no`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金结算清单收费项目信息';

CREATE TABLE IF NOT EXISTS `mdcs_fund_setl_list_diag_d` (
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `maindiag_flag` VARCHAR(3) COMMENT '主诊断标志 0 否；1 是',
    `diag_code` VARCHAR(30) COMMENT '诊断代码 医保疾病诊断与分类代码',
    `adm_cond_type` VARCHAR(3) COMMENT '入院病情类型 1.有 2.临床未确定 3.情况不明 4.无',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `sync_time` DATETIME COMMENT '同步时间',
    `setl_list_diag_id` VARCHAR(30) COMMENT '结算清单诊断编号',
    `diag_type` VARCHAR(3) COMMENT '诊断类别 诊断类别 diag_type',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `diag_name` VARCHAR(500) COMMENT '诊断名称',
    PRIMARY KEY (`psn_no`, `biz_date`, `subsys_codg`, `setl_list_diag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金结算清单住院诊断信息';

CREATE TABLE IF NOT EXISTS `hf_setl_list_info` (
    `rid` VARCHAR(40) COMMENT '结算清单唯一标识',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `fixmedins_code` VARCHAR(12) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `hi_setl_lv` VARCHAR(3) COMMENT '医保结算等级 1一级2二级3三级',
    `hi_no` VARCHAR(30) COMMENT '医保编号 参保人在医保系统中的唯一身份代码',
    `medcasno` VARCHAR(40) COMMENT '病案号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `brdy` DATE COMMENT '出生日期',
    `age` DECIMAL(20,4) COMMENT '年龄',
    `nwb_age` DECIMAL(20,4) COMMENT '新生儿年龄(天)',
    `patn_cert_type` VARCHAR(3) COMMENT '患者证件类别',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `adm_time` DATETIME COMMENT '入院时间',
    `dscg_time` DATETIME COMMENT '出院时间',
    `adm_caty` VARCHAR(6) COMMENT '入院科别 填写院内科室代码',
    `refldept_dept` VARCHAR(200) COMMENT '转科科别 填写院内科室代码 如果超过一次以上的转科 用“→”转接表示',
    `dscg_caty` VARCHAR(6) COMMENT '出院科别 填写院内科室代码',
    `act_ipt_days` DECIMAL(20,4) COMMENT '实际住院天数',
    `diag_code_cnt` DECIMAL(20,4) COMMENT '诊断代码计数',
    `oprn_oprt_code_cnt` DECIMAL(20,4) COMMENT '手术操作代码计数',
    `dscg_way` VARCHAR(3) COMMENT '离院方式 编码 1.医嘱离院2.医嘱转院 拟接收医疗机构名称3.医嘱转社区卫生服务机构 乡镇卫生院 拟接收医疗机构名称4.非医嘱离院5.死亡9.其他',
    `days_rinp_flag_31` VARCHAR(3) COMMENT '出院31天内再住院计划标志',
    `chfpdr_code` VARCHAR(30) COMMENT '主诊医师代码 医保医师代码',
    `chfpdr_name` VARCHAR(50) COMMENT '主诊医师姓名',
    `psn_selfpay` DECIMAL(20,4) COMMENT '个人自付',
    `psn_ownpay` DECIMAL(20,4) COMMENT '个人自费',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `psn_cashpay` DECIMAL(20,4) COMMENT '个人现金支付',
    `hi_type` VARCHAR(3) COMMENT '医保类型 医保类型 hi_type',
    `main_oprn_code` VARCHAR(30) COMMENT '主手术操作代码',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `main_diag_code` VARCHAR(20) COMMENT '主诊断代码',
    `main_diag_name` VARCHAR(100) COMMENT '主诊断名称',
    `main_oprn_name` VARCHAR(500) COMMENT '主手术操作名称',
    `bed_fee` DECIMAL(20,4) COMMENT '床位费(元)',
    `examine_fee` DECIMAL(20,4) COMMENT '诊察费(元)',
    `inspect_fee` DECIMAL(20,4) COMMENT '检查费(元)',
    `assay_fee` DECIMAL(20,4) COMMENT '化验费(元)',
    `trt_fee` DECIMAL(20,4) COMMENT '治疗费(元)',
    `rgtrt_fee` DECIMAL(20,4) COMMENT '手术费(元)',
    `nurs_fee` DECIMAL(20,4) COMMENT '护理费(元)',
    `med_mat_fee` DECIMAL(20,4) COMMENT '卫生材料费(元)',
    `ordn_diag_trt_fee` DECIMAL(20,4) COMMENT '一般诊疗费(元)',
    `rgst_serv_fee` DECIMAL(20,4) COMMENT '挂号费(元)',
    `oth_fee` DECIMAL(20,4) COMMENT '其他费(元)',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额(元) 本次需要结算的医疗费用总额 不包括处方药品费用',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额(元)',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额(元)',
    `case_type` VARCHAR(6) COMMENT '病案类型',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `coefficient` DECIMAL(20,4) COMMENT '机构调节系数',
    `quality_grade` VARCHAR(20) COMMENT '质控等级',
    `sub_cat_code` VARCHAR(20) COMMENT '诊断亚目代码',
    `sub_cat_name` VARCHAR(100) COMMENT '诊断亚目名称',
    `concat_oprn_code` VARCHAR(300) COMMENT '手术操作编码(+号拼接)',
    `concat_oprn_name` TEXT COMMENT '手术操作名称(+号拼接)',
    `dise_id` VARCHAR(30) COMMENT '病种id',
    `dise_type` VARCHAR(6) COMMENT '病种类型',
    `group_flag` VARCHAR(6) COMMENT '入组标志',
    `classify_reason` VARCHAR(500) COMMENT '分组原因',
    `cell` DECIMAL(20,4) COMMENT 'DIP点值或DRG费率',
    `rw` DECIMAL(20,4) COMMENT 'DIP分值或DRG权重',
    `price` DECIMAL(20,4) COMMENT '结算价(元)',
    `bed_type` VARCHAR(3) COMMENT '床日类型',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `setl_begn_date` DATE COMMENT '结算开始日期',
    `setl_end_date` DATE COMMENT '结算结束日期',
    `setl_stas` VARCHAR(3) COMMENT '结算状态',
    `setl_time` DATE COMMENT '结算时间',
    `clr_time` DATE COMMENT '清算时间',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `delete_flag` DECIMAL(20,4) COMMENT '删除标志',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `hi_paymtd` VARCHAR(3) COMMENT '医保支付方式 医保支付方式(hi_paymtd)',
    `tcmherb_fee` DECIMAL(20,4) COMMENT '中药饮片费(元)',
    `clr_stas` VARCHAR(3) COMMENT '清算状态 1 申报未清算 2 已清算',
    `wm_fee` DECIMAL(20,4) COMMENT '西药费(元)',
    `tcmpat_fee` DECIMAL(20,4) COMMENT '中成药费(元)',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `id` VARCHAR(40) COMMENT 'ID 主键',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金结算清单主体信息';

CREATE TABLE IF NOT EXISTS `ma_setl_info_ext_d` (
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊编号',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系编号 主键',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(200) COMMENT '定点医药机构名称',
    `polin_psn_pay` DECIMAL(20,4) COMMENT '政策范围内个人自付金额',
    `ma_admdvs` VARCHAR(6) COMMENT '医疗救助认定区划',
    `mat_idet_type` VARCHAR(30) COMMENT '救助对象身份类型',
    `mat_idet_code` VARCHAR(30) COMMENT '救助对象身份',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `addr` VARCHAR(200) COMMENT '地址',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `refd_setl_flag` VARCHAR(3) COMMENT '退费结算标志 0 否；1 是',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `setl_type` VARCHAR(6) COMMENT '结算类别 结算类别字典表',
    `psn_setlway` VARCHAR(6) COMMENT '个人结算方式 个人结算方式 psn_setlway',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `hifes_pay` DECIMAL(20,4) COMMENT '补充医疗保险基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `hifdm_pay` DECIMAL(20,4) COMMENT '伤残人员医疗保障基金支出',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `psn_pay` DECIMAL(20,4) COMMENT '个人支付金额',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `cash_payamt` DECIMAL(20,4) COMMENT '现金支付金额',
    `setl_time` DATETIME COMMENT '结算时间',
    `year` VARCHAR(4) COMMENT '年度',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `memo` VARCHAR(500) COMMENT '备注',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `biz_date` DATETIME COMMENT '业务日期',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `setl_id` VARCHAR(30) COMMENT '结算编号 主键',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `setl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗保障基金救助结算明细信息';

CREATE TABLE IF NOT EXISTS `ag_gs_complaint` (
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `id` DECIMAL(20,4) COMMENT '主键ID 主键',
    `zfzt` VARCHAR(255) COMMENT '执法主体',
    `tsdh` VARCHAR(20) COMMENT '投诉电话',
    `tsdz` VARCHAR(255) COMMENT '投诉地址',
    `qtfs` VARCHAR(255) COMMENT '其他方式',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `cret_date` DATETIME COMMENT '创建时间',
    `updt_user` VARCHAR(20) COMMENT '修改人',
    `updt_date` DATETIME COMMENT '修改时间',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='举报投诉主要信息';

CREATE TABLE IF NOT EXISTS `trns_entp_cred_lv_d` (
    `biz_date` DATETIME COMMENT '业务日期',
    `evaluate_time` VARCHAR(20) COMMENT '评定时间',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `item_type` VARCHAR(3) COMMENT '业务类型',
    `cred_lv` VARCHAR(3) COMMENT '信用等级',
    `rep_time` VARCHAR(20) COMMENT '修复时间',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `oprt_name` VARCHAR(50) COMMENT '操作人姓名',
    `oprt_id` VARCHAR(20) COMMENT '操作人编号',
    `oprt_account` VARCHAR(20) COMMENT '操作人账号',
    `oprt_time` DATETIME COMMENT '操作时间',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `entp_cred_lv_id` VARCHAR(40) COMMENT '企业信用管理编号',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `opt_time` DATETIME COMMENT '经办时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `uscc` VARCHAR(40) COMMENT 'USCC',
    `dcla_entp_name` VARCHAR(40) COMMENT '申报企业名称',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `entp_cred_lv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机构信用分析信息';

CREATE TABLE IF NOT EXISTS `report` (
    `report_country` VARCHAR(1) COMMENT '是否上报国家 0否；1是',
    `biz_date` DATE COMMENT '业务日期',
    `name` VARCHAR(255) COMMENT '举报人姓名',
    `handle_person` VARCHAR(255) COMMENT '处理人',
    `handle_event` VARCHAR(255) COMMENT '处理事件',
    `has_report` VARCHAR(2) COMMENT '是否被投诉过 0否；1是',
    `turn_user_admdvs` VARCHAR(255) COMMENT '转发用户区划',
    `sign_val` TEXT COMMENT '签名',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `turn_back` VARCHAR(255) COMMENT '上級转发',
    `handle_type` VARCHAR(2) COMMENT '处理类型',
    `is_agree` VARCHAR(2) COMMENT '举报人是否同意处理结果 0否；1是',
    `id` VARCHAR(255) COMMENT '举报ID 主键',
    `code` VARCHAR(255) COMMENT '举报编码',
    `idcard` VARCHAR(255) COMMENT '举报人身份证',
    `openid` VARCHAR(255) COMMENT '终端的唯一标识',
    `address` VARCHAR(255) COMMENT '联系地址',
    `mail` VARCHAR(255) COMMENT '邮箱',
    `phone` VARCHAR(255) COMMENT '举报人手机号',
    `ipaddress` VARCHAR(10) COMMENT 'ip地址',
    `report_type` VARCHAR(2) COMMENT '举报对象类型 信用主体类型字典',
    `report_addr` VARCHAR(255) COMMENT '骗保行为地址',
    `report_obj_code` VARCHAR(255) COMMENT '举报对象编码',
    `report_obj` VARCHAR(255) COMMENT '举报对象名称',
    `report_name` VARCHAR(255) COMMENT '被举报人的姓名',
    `report_event` VARCHAR(255) COMMENT '举报事件',
    `report_event_text` TEXT COMMENT '举报事件文本',
    `report_title` VARCHAR(255) COMMENT '举报信息标题',
    `report_memo` TEXT COMMENT '举报问题描述',
    `is_anonym` VARCHAR(2) COMMENT '是否匿名 0否；1是',
    `channel` VARCHAR(4) COMMENT '投诉渠道',
    `source` VARCHAR(10) COMMENT '来源 1无；2市级；3省级；4国家级',
    `region` VARCHAR(10) COMMENT '来源地市',
    `terminal_type` VARCHAR(2) COMMENT '终端类型 终端类型字典',
    `equipment_type` VARCHAR(2) COMMENT '设备类型 设备类型字典',
    `electronic_archives_no` TEXT COMMENT '附件id',
    `turn_status` VARCHAR(2) COMMENT '转发状态 1 已上报；2 已转办；3 已移送；4 对方回退',
    `biz_status` VARCHAR(2) COMMENT '业务状态 1 未处理；2 处理中；3 待受理确认；4 已处理；5 不予处理；6 已回退',
    `report_date` DATETIME COMMENT '投诉时间',
    `reward` DECIMAL(20,4) COMMENT '奖励金额',
    `recovery` DECIMAL(20,4) COMMENT '回收金额',
    `chargeback` DECIMAL(20,4) COMMENT '拒付金额',
    `penalize` DECIMAL(20,4) COMMENT '处罚金额',
    `reward_content` VARCHAR(255) COMMENT '奖励依据',
    `turn_time` VARCHAR(10) COMMENT '转发时间',
    `turn_mechanism_name` VARCHAR(255) COMMENT '转发机构名称',
    `turn_mechanism` VARCHAR(255) COMMENT '转发机构',
    `turn_outside_name` VARCHAR(255) COMMENT '移送外部机构名称',
    `turn_region_name` VARCHAR(255) COMMENT '地市名称',
    `turn_region` VARCHAR(255) COMMENT '转发地市',
    `turn_handle_time` DATE COMMENT '开始处理时间',
    `turn_end_time` DATE COMMENT '处理完成时间',
    `turn_user_id` VARCHAR(255) COMMENT '转发用户ID',
    `turn_user_name` VARCHAR(255) COMMENT '转发用户名称',
    `turn_user_phone` VARCHAR(255) COMMENT '转发用户手机号',
    `event_type` VARCHAR(255) COMMENT '操作类型 1举报2投诉',
    `handle_result` TEXT COMMENT '处理结果',
    `handle_verified` VARCHAR(2) COMMENT '是否查实 0否；1是',
    `handle_reason` TEXT COMMENT '处理理由',
    `handle_person_id` VARCHAR(255) COMMENT '处理人ID',
    `handle_person_admdvs` VARCHAR(255) COMMENT '处理人区划',
    `memo` VARCHAR(255) COMMENT '附件说明',
    `opinions` VARCHAR(255) COMMENT '办理意见',
    `time_limit` VARCHAR(255) COMMENT '办理时限',
    `appeal_org` VARCHAR(255) COMMENT '诉求单位',
    `is_delay` VARCHAR(2) COMMENT '是否延期 0否；1是',
    `is_notified` VARCHAR(2) COMMENT '结果是否告知举报人 0否；1是',
    `notified_date` VARCHAR(10) COMMENT '处理结果告知时间',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `updt_user` VARCHAR(20) COMMENT '更新人',
    `cret_date` DATETIME COMMENT '创建时间',
    `updt_date` DATETIME COMMENT '更新时间',
    `cret_admdvs` VARCHAR(20) COMMENT '创建医保区划',
    `admdvs_path` VARCHAR(400) COMMENT '医保区划变更路径',
    `is_delete` VARCHAR(2) COMMENT '删除标志 0否；1是',
    `remake` VARCHAR(255) COMMENT '备注',
    `secret` VARCHAR(255) COMMENT '密文',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='举报投诉事件类型信息';

CREATE TABLE IF NOT EXISTS `ag_ad_punish_hearing_report` (
    `report_way` VARCHAR(10) COMMENT '听证方式0 公开1 不公开',
    `hear_officer` VARCHAR(255) COMMENT '听证员',
    `agent` VARCHAR(255) COMMENT '当事人委托代理人',
    `third_party` VARCHAR(255) COMMENT '第三人',
    `deleted_time` DATE COMMENT '数据删除时间',
    `report_place` VARCHAR(255) COMMENT '听证地点',
    `cret_date` DATETIME COMMENT '创建时间',
    `handle_person_name` VARCHAR(255) COMMENT '经办人姓名',
    `report_id` VARCHAR(50) COMMENT '主键',
    `accept_id` VARCHAR(50) COMMENT '受理ID',
    `report_time` VARCHAR(50) COMMENT '听证时间',
    `report_host` VARCHAR(255) COMMENT '听证主持人',
    `recorder` VARCHAR(255) COMMENT '记录员',
    `interpreter` VARCHAR(255) COMMENT '翻译人员',
    `unit_charge_person` VARCHAR(255) COMMENT '单位法定代表人或负责人',
    `handle_advice` TEXT COMMENT '处理意见及建议',
    `other_people` VARCHAR(255) COMMENT '其他参加人员',
    `basic_information` TEXT COMMENT '听证基本情况',
    `other_matters` TEXT COMMENT '其他事项',
    `is_delete` VARCHAR(2) COMMENT '逻辑删除 0否；1是',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `party_person` VARCHAR(255) COMMENT '当事人',
    `examiner_id` VARCHAR(64) COMMENT '审批人id',
    `examiner_name` VARCHAR(255) COMMENT '审批人姓名',
    `handle_person_id` VARCHAR(64) COMMENT '经办人id',
    `updt_user` VARCHAR(50) COMMENT '修改人',
    `updt_date` DATETIME COMMENT '修改时间',
    `biz_date` DATE COMMENT '业务日期',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`report_id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行政处罚-听证报告信息';

CREATE TABLE IF NOT EXISTS `ag_ad_check_report` (
    `complate_time` VARCHAR(255) COMMENT '完成时间',
    `check_record` TEXT COMMENT '现场检查工作基本情况',
    `biz_date` DATE COMMENT '业务日期',
    `advice_content` TEXT COMMENT '建议内容',
    `report_id` VARCHAR(50) COMMENT '报告ID',
    `accept_id` VARCHAR(50) COMMENT '受理ID',
    `check_project` TEXT COMMENT '检查项目',
    `check_time` VARCHAR(255) COMMENT '检查时间',
    `check_appraise` TEXT COMMENT '被查单位工作基本情况及总体评价',
    `check_problem` TEXT COMMENT '检查中发现的问题',
    `advice_code` VARCHAR(255) COMMENT '拟办建议选项',
    `transfer_unit` VARCHAR(255) COMMENT '移送单位',
    `clause_array` TEXT COMMENT '处理依据',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `cret_date` DATETIME COMMENT '创建时间',
    `updt_user` VARCHAR(20) COMMENT '修改人',
    `updt_date` DATETIME COMMENT '修改时间',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`biz_date`, `report_id`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行政检查-检查报告信息';

CREATE TABLE IF NOT EXISTS `ag_ad_accept` (
    `updt_user` VARCHAR(255) COMMENT '修改人',
    `cret_user_id` VARCHAR(64) COMMENT '创建人id',
    `unit_person_name` VARCHAR(255) COMMENT '承办单位负责人名称',
    `party_id` VARCHAR(255) COMMENT '当事人id',
    `cret_user` VARCHAR(255) COMMENT '创建人',
    `cret_date` DATETIME COMMENT '创建时间',
    `unit_person` VARCHAR(255) COMMENT '承办单位负责人',
    `check_captain` VARCHAR(255) COMMENT '检查组队长',
    `check_captain_name` VARCHAR(255) COMMENT '检查组队长名称',
    `is_delete` VARCHAR(2) COMMENT '逻辑删除 0否；1是',
    `submit_time` DATETIME COMMENT '提交时间',
    `updt_date` DATETIME COMMENT '修改时间',
    `workflow` VARCHAR(255) COMMENT '案件类型',
    `state` VARCHAR(255) COMMENT '案件状态0 &gt 草稿1 &gt 进行中2 &gt 已结束 编码 0：正常 1：撤销；',
    `end_date` DATETIME COMMENT '结束时间',
    `edoc_info_id` VARCHAR(64) COMMENT '电子档案id',
    `check_type` VARCHAR(255) COMMENT '检查方式',
    `cret_admdvs` VARCHAR(64) COMMENT '创建医保区划',
    `clue_no` VARCHAR(255) COMMENT '线索编号',
    `enforce_law_item_array` TEXT COMMENT '行政执法事项',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `accept_id` VARCHAR(255) COMMENT '受理ID',
    `case_code` VARCHAR(255) COMMENT '案件编号',
    `case_source` VARCHAR(255) COMMENT '案件来源',
    `check_time` VARCHAR(255) COMMENT '检查时间',
    `content_target` TEXT COMMENT '检查内容',
    `clause_array` TEXT COMMENT '检查依据',
    `unit` VARCHAR(255) COMMENT '承办单位',
    `unit_name` VARCHAR(255) COMMENT '承办单位名称',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `accept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行政检查-检查登记信息';

CREATE TABLE IF NOT EXISTS `ag_ad_check_packet` (
    `updt_date` DATETIME COMMENT '修改时间',
    `packet_id` VARCHAR(255) COMMENT '结案ID 主键',
    `accept_id` VARCHAR(255) COMMENT '受理ID',
    `recovery_funds` DECIMAL(20,4) COMMENT '追回金额',
    `punish_money` DECIMAL(20,4) COMMENT '处罚金额',
    `protest_money` DECIMAL(20,4) COMMENT '拒付金额',
    `reward_money` DECIMAL(20,4) COMMENT '奖励金额',
    `brief_content` TEXT COMMENT '简要案情及查处经过',
    `packet_result` VARCHAR(255) COMMENT '是否结件',
    `packet_content` TEXT COMMENT '结件建议',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `cret_date` DATETIME COMMENT '创建时间',
    `updt_user` VARCHAR(20) COMMENT '修改人',
    `typical_flag` VARCHAR(2) COMMENT '是否典型',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`packet_id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行政处罚-结案归档信息';

CREATE TABLE IF NOT EXISTS `ag_ad_check_handle` (
    `handle_id` VARCHAR(255) COMMENT '处理ID 主键',
    `accept_id` VARCHAR(255) COMMENT '受理ID',
    `evidence_array` TEXT COMMENT '主要证据',
    `party_reply_code` VARCHAR(255) COMMENT '当事人反馈类型',
    `party_reply_content` TEXT COMMENT '当事人意见',
    `delivery_way` TEXT COMMENT '征求意见方式',
    `extenuate_reason` TEXT COMMENT '从轻减轻处罚的理由',
    `transfer_unit` VARCHAR(255) COMMENT '移送单位',
    `handle_type` VARCHAR(255) COMMENT '处理方式',
    `handle_content` TEXT COMMENT '处理意见',
    `clause_array` TEXT COMMENT '处理依据',
    `order_change` TEXT COMMENT '责令改正违法行为',
    `cret_user` VARCHAR(20) COMMENT '创建人',
    `cret_date` DATETIME COMMENT '创建时间',
    `updt_user` VARCHAR(20) COMMENT '修改人',
    `updt_date` DATETIME COMMENT '修改时间',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `illegal_act` TEXT COMMENT '违法行为',
    `delivery_time` TEXT COMMENT '征求意见时间',
    PRIMARY KEY (`handle_id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行政检查处理决定信息';

CREATE TABLE IF NOT EXISTS `trns_ord_c` (
    `mcs_comb_id` VARCHAR(40) COMMENT '组套ID',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `ext_code` VARCHAR(100) COMMENT '外部编码',
    `ord_id` VARCHAR(40) COMMENT '订单ID 主键',
    `prt_flag` VARCHAR(3) COMMENT '打印状态 0未打印1已打印',
    `ord_code` VARCHAR(40) COMMENT '订单代码',
    `ord_sumamt` DECIMAL(20,4) COMMENT '订单总金额',
    `addr_id` VARCHAR(40) COMMENT '地址ID',
    `pubonln_delv_ext_id` VARCHAR(40) COMMENT '挂网目录配送扩展ID',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `read_flag` VARCHAR(3) COMMENT '已读标志 0未读；1已读',
    `read_time` DATETIME COMMENT '阅读时间',
    `send_time` DATETIME COMMENT '发送时间',
    `shp_time` DATETIME COMMENT '发货时间',
    `splm_inpt_flag` VARCHAR(3) COMMENT '补充录入标志 0否；1是',
    `shp_stas` VARCHAR(3) COMMENT '发货状态 发货状态字典',
    `shpp_stas` VARCHAR(3) COMMENT '收货状态 收货状态字典',
    `purc_plan_id` VARCHAR(40) COMMENT '采购计划ID 主键',
    `purc_plan_code` VARCHAR(40) COMMENT '采购计划代码',
    `itemname` VARCHAR(100) COMMENT '项目名称',
    `invottl` VARCHAR(100) COMMENT '发票抬头',
    `docmker` VARCHAR(50) COMMENT '制单人',
    `memo` VARCHAR(500) COMMENT '备注',
    `cncl_time` DATETIME COMMENT '作废时间',
    `shpp_time` DATETIME COMMENT '收货时间',
    `splm_inpt_retn_time` DATETIME COMMENT '补录订单不通过时间',
    `plan_detl_memo` TEXT COMMENT '计划详情备注',
    `prod_type` VARCHAR(3) COMMENT '产品类型 1 药品2 医用耗材',
    `delventp_cnfm_shpp_time` DATETIME COMMENT '配送企业确认收货时间',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `opt_time` DATETIME COMMENT '经办时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `shp_end_time` DATETIME COMMENT '发货截止时间',
    `shpp_end_time` DATETIME COMMENT '收货截止时间',
    `eval_flag` VARCHAR(3) COMMENT '是否已评价标志 0待评价1已评价',
    `his_ord_no` VARCHAR(50) COMMENT '医院his系统订单编号',
    `splm_inpt_retn_reason` VARCHAR(200) COMMENT '下单时间',
    `comb_flag` VARCHAR(2) COMMENT '是否组套 0否；1是',
    `exra_serv_flag` VARCHAR(2) COMMENT '是否需要伴随服务 0否；1是',
    `exra_serv_pric` DECIMAL(20,4) COMMENT '伴随服务价格',
    `comb_purc_cnt` DECIMAL(20,4) COMMENT '采购组套数量',
    `comb_shp_cnt` DECIMAL(20,4) COMMENT '组套发货数量',
    `comb_shpp_cnt` DECIMAL(20,4) COMMENT '组套收货数量',
    `comb_retn_cnt` DECIMAL(20,4) COMMENT '组套退货数量',
    `comb_rtnb_cnt` DECIMAL(20,4) COMMENT '组套可退货数量',
    `acp_flag` VARCHAR(3) COMMENT '接单状态 0未接单1已接单',
    `acp_time` DATETIME COMMENT '接单时间',
    `ord_souc` VARCHAR(3) COMMENT '订单来源 0：新平台下单1 平台下单2 凯特数据迁移4 三明数据迁移10 接口下单',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易订单信息';

CREATE TABLE IF NOT EXISTS `trns_purc_plan_detl_c` (
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `purc_pric` DECIMAL(20,4) COMMENT '采购价格',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `purc_cnt` DECIMAL(20,4) COMMENT '采购数量',
    `shpp_time` DATETIME COMMENT '收货时间',
    `opt_time` DATETIME COMMENT '经办时间',
    `purc_plan_det_id` VARCHAR(40) COMMENT '采购计划明细ID 复合主键',
    `purc_plan_id` VARCHAR(40) COMMENT '采购计划ID 主键',
    `prod_name` VARCHAR(200) COMMENT '产品名称',
    `itemname` VARCHAR(100) COMMENT '项目名称',
    `pubonln_pric` DECIMAL(20,4) COMMENT '挂网价格',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `prod_id` VARCHAR(40) COMMENT '产品ID',
    `hosp_bidprcu_item_id` VARCHAR(40) COMMENT '院端招采项目ID',
    `hosp_list_id` VARCHAR(40) COMMENT '医院目录ID',
    `pubonln_delv_ext_id` VARCHAR(40) COMMENT '挂网目录配送扩展ID',
    `plan_detl_memo` TEXT COMMENT '计划详情备注',
    `trns_data_souc` VARCHAR(3) COMMENT '交易数据来源',
    `shp_time` DATETIME COMMENT '发货时间',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`purc_plan_det_id`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易采购计划明细信息';

CREATE TABLE IF NOT EXISTS `trns_purc_plan_c` (
    `prnt_entp_code` VARCHAR(20) COMMENT '上级机构代码',
    `plan_shpp_time` DATETIME COMMENT '计划到货时间',
    `send_time` DATETIME COMMENT '发送时间',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `purc_plan_id` VARCHAR(40) COMMENT '采购计划ID 主键',
    `purc_type` VARCHAR(3) COMMENT '采购计划类型 采购类型字典表',
    `plan_sumamt` DECIMAL(20,4) COMMENT '计划总金额',
    `purc_plan_code` VARCHAR(40) COMMENT '采购计划代码',
    `invottl` VARCHAR(100) COMMENT '发票抬头',
    `purc_plan_addr_id` VARCHAR(40) COMMENT '配送地址ID',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `purc_plan_name` VARCHAR(100) COMMENT '购置计划名称',
    `plandoc_type` VARCHAR(20) COMMENT '计划单类型',
    `chk_stas` VARCHAR(3) COMMENT '审核状态 审核状态字典',
    `plan_type` VARCHAR(3) COMMENT '计划类型',
    `plan_time` DATETIME COMMENT '计划时间',
    `sender` VARCHAR(50) COMMENT '发送人',
    `plan_memo` TEXT COMMENT '计划备注',
    `splm_flag` VARCHAR(3) COMMENT '补录标志 0否；1是',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `opt_time` DATETIME COMMENT '经办时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    PRIMARY KEY (`subsys_codg`, `purc_plan_id`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易采购计划信息';

CREATE TABLE IF NOT EXISTS `trns_prod_shp_c` (
    `hosp_bidprcu_item_id` VARCHAR(40) COMMENT '院端招采项目ID',
    `pubonln_stas` VARCHAR(3) COMMENT '挂网状态 挂网状态字典表',
    `invo_amt` DECIMAL(20,4) COMMENT '发票金额',
    `shp_stas` VARCHAR(3) COMMENT '发货状态 发货状态字典',
    `manu_lotnum` VARCHAR(200) COMMENT '批次号',
    `outsto_time` DATETIME COMMENT '缺货时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `ext_updt_time` DATETIME COMMENT '原始数据更新时间',
    `shpp_cnt` DECIMAL(20,4) COMMENT '收货数量',
    `cncl_type` VARCHAR(3) COMMENT '作废类型',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `snap_id` VARCHAR(40) COMMENT '交易目录快照ID',
    `splt_code` VARCHAR(40) COMMENT '分词代码',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `shp_id` VARCHAR(40) COMMENT '发货ID 主键',
    `shp_code` VARCHAR(40) COMMENT '发货代码',
    `ord_id` VARCHAR(40) COMMENT '订单ID 主键',
    `ord_code` VARCHAR(40) COMMENT '订单代码',
    `pubonln_delv_ext_id` VARCHAR(40) COMMENT '挂网目录配送扩展ID',
    `ord_detl_id` VARCHAR(40) COMMENT '订单明细ID 复合主键',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `prod_id` VARCHAR(40) COMMENT '产品ID',
    `prod_name` VARCHAR(200) COMMENT '产品名称',
    `purc_cnt` DECIMAL(20,4) COMMENT '采购数量',
    `purcpric` DECIMAL(20,4) COMMENT '采购价',
    `shp_cnt` DECIMAL(20,4) COMMENT '发货数量',
    `shp_pric` DECIMAL(20,4) COMMENT '发货价格',
    `shp_time` DATETIME COMMENT '发货时间',
    `shpp_time` DATETIME COMMENT '收货时间',
    `send_time` DATETIME COMMENT '发送时间',
    `rtnb_cnt` DECIMAL(20,4) COMMENT '可退货数量',
    `retn_cnt` DECIMAL(20,4) COMMENT '退货数量',
    `avl_shp_cnt` DECIMAL(20,4) COMMENT '可用发货数量',
    `shp_amt` DECIMAL(20,4) COMMENT '发货金额',
    `shpp_amt` DECIMAL(20,4) COMMENT '收货金额',
    `expy_endtime` DATETIME COMMENT '有效期结束时间',
    `purc_amt` DECIMAL(20,4) COMMENT '采购金额',
    `ord_sumamt` DECIMAL(20,4) COMMENT '订单总金额',
    `cncl_time` DATETIME COMMENT '作废时间',
    `prod_type` VARCHAR(3) COMMENT '产品类型 1 药品2 医用耗材',
    `hosp_list_id` VARCHAR(40) COMMENT '医院目录ID',
    `itemname` VARCHAR(100) COMMENT '项目名称',
    `delventp_cnfm_shpp_stas` VARCHAR(3) COMMENT '配送企业确认收货状态',
    `delventp_cnfm_shpp_time` DATETIME COMMENT '配送企业确认收货时间',
    `plan_detl_memo` TEXT COMMENT '计划详情备注',
    `shp_memo` TEXT COMMENT '发货备注',
    `read_time` DATETIME COMMENT '阅读时间',
    `read_flag` VARCHAR(3) COMMENT '已读标志 0未读；1已读',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `splm_flag` VARCHAR(3) COMMENT '补录标志 0否；1是',
    `opt_time` DATETIME COMMENT '经办时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户id',
    `avl_shpp_cnt` DECIMAL(20,4) COMMENT '可用收货数量',
    `erp_shp_id` VARCHAR(50) COMMENT 'ERP发货ID',
    `deal_drug_snap_id` VARCHAR(40) COMMENT '交易产品快照ID',
    `ext_code` VARCHAR(100) COMMENT '外部编码',
    `place_ord_time` DATETIME COMMENT '下单时间',
    `his_ord_no` VARCHAR(50) COMMENT '医院his系统订单编号',
    `his_ord_detl_no` VARCHAR(50) COMMENT '医院his系统订单明细编号',
    `reject_time` DATETIME COMMENT '发货明细被退回时间',
    `reject_reason` VARCHAR(200) COMMENT '发货明细被退回原因',
    `stroom_id` VARCHAR(30) COMMENT '库房',
    `seld_id` VARCHAR(20) COMMENT '中选产品标识',
    `comb_flag` VARCHAR(2) COMMENT '是否组套 0否；1是',
    `mcs_comb_id` VARCHAR(40) COMMENT '组套ID',
    `expy_end_date` DATETIME COMMENT '有效期',
    `invo_date` DATETIME COMMENT '发票日期',
    `adjust_flag` VARCHAR(2) COMMENT '是否调价 0否；1是',
    `ord_souc` VARCHAR(3) COMMENT '订单来源 0：新平台下单1 平台下单2 凯特数据迁移4 三明数据迁移10 接口下单',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易发货信息';

CREATE TABLE IF NOT EXISTS `trns_prod_retn_c` (
    `hosp_bidprcu_item_id` VARCHAR(40) COMMENT '院端招采项目ID',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `admorg_code` VARCHAR(50) COMMENT '管理单位代码',
    `retn_cnt` DECIMAL(20,4) COMMENT '退货数量',
    `rtnb_cnt` DECIMAL(20,4) COMMENT '可退货数量',
    `admorg_name` VARCHAR(200) COMMENT '管理单位名称',
    `medins_retn_time` DATETIME COMMENT '医疗机构退货时间',
    `adjust_flag` VARCHAR(2) COMMENT '是否调价 0否；1是',
    `cncl_time` DATETIME COMMENT '作废时间',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `ord_detl_id` VARCHAR(40) COMMENT '订单明细ID 复合主键',
    `retn_id` VARCHAR(40) COMMENT '退货ID 主键',
    `retn_code` VARCHAR(40) COMMENT '退货代码',
    `ord_code` VARCHAR(40) COMMENT '订单代码',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `shp_code` VARCHAR(40) COMMENT '发货代码',
    `shp_id` VARCHAR(40) COMMENT '发货ID 主键',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `prod_id` VARCHAR(40) COMMENT '产品ID',
    `prod_name` VARCHAR(200) COMMENT '产品名称',
    `shp_cnt` DECIMAL(20,4) COMMENT '发货数量',
    `shp_pric` DECIMAL(20,4) COMMENT '发货价格',
    `shpp_cnt` DECIMAL(20,4) COMMENT '收货数量',
    `hosp_list_id` VARCHAR(40) COMMENT '医院目录ID',
    `retn_chk_stas` VARCHAR(3) COMMENT '退货审核状态 退货审核状态字典表',
    `medins_retn_rea` VARCHAR(200) COMMENT '医疗机构退货原因',
    `delventp_fail_rea` TEXT COMMENT '配送机构不通过原因',
    `delventp_pass_time` DATETIME COMMENT '配送企业通过时间',
    `delventp_fail_time` DATETIME COMMENT '配送企业不通过时间',
    `manu_lotnum` VARCHAR(200) COMMENT '生产批号',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `prod_type` VARCHAR(3) COMMENT '产品类型 1 药品2 医用耗材',
    `opt_time` DATETIME COMMENT '经办时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `tenant_id` VARCHAR(50) COMMENT '租户id',
    `deal_drug_snap_id` VARCHAR(40) COMMENT '交易产品快照ID',
    `ext_code` VARCHAR(100) COMMENT '外部编码',
    `ext_updt_time` DATETIME COMMENT '原始数据更新时间',
    `snap_id` VARCHAR(40) COMMENT '交易目录快照ID',
    `seld_id` VARCHAR(20) COMMENT '中选产品标识',
    `comb_flag` VARCHAR(2) COMMENT '是否组套 0否；1是',
    `stroom_id` VARCHAR(30) COMMENT '库房',
    `mcs_comb_id` VARCHAR(40) COMMENT '组套ID',
    `comb_ver` VARCHAR(50) COMMENT '组套版本号',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易退货信息';

CREATE TABLE IF NOT EXISTS `trns_ord_detl_c` (
    `acp_time` DATETIME COMMENT '接单时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `tenant_id` VARCHAR(50) COMMENT '租户id',
    `ext_code` VARCHAR(100) COMMENT '外部编码',
    `main_parts` VARCHAR(3) COMMENT '主部件 0否；1是',
    `area_code` VARCHAR(20) COMMENT '片区',
    `acp_flag` VARCHAR(3) COMMENT '接单状态 0未接单1已接单',
    `ord_detl_stas` VARCHAR(3) COMMENT '订单明细状态 订单明细状态',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `itemname` VARCHAR(100) COMMENT '项目名称',
    `biz_date` DATE COMMENT '业务日期',
    `hosp_bidprcu_item_id` VARCHAR(40) COMMENT '院端招采项目ID',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `item_codg` VARCHAR(50) COMMENT '项目编码',
    `exra_serv_pric` DECIMAL(20,4) COMMENT '伴随服务价格',
    `seld_id` VARCHAR(20) COMMENT '中选产品标识',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `read_time` DATETIME COMMENT '阅读时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `opt_time` TEXT COMMENT '经办时间',
    `delventp_code` VARCHAR(50) COMMENT '配送企业代码',
    `prod_type` VARCHAR(3) COMMENT '产品类型 1 药品2 医用耗材',
    `snap_id` VARCHAR(40) COMMENT '交易目录快照ID',
    `pubonln_stas` VARCHAR(3) COMMENT '挂网状态 挂网状态字典表',
    `outsto_cnt` DECIMAL(20,4) COMMENT '缺货数量',
    `deal_drug_snap_id` VARCHAR(40) COMMENT '交易产品快照ID',
    `ext_updt_time` DATETIME COMMENT '原始数据更新时间',
    `cncl_time` DATETIME COMMENT '作废时间',
    `cncl_type` VARCHAR(3) COMMENT '作废类型',
    `purc_plan_det_id` VARCHAR(40) COMMENT '采购计划明细ID 复合主键',
    `purc_plan_id` VARCHAR(40) COMMENT '采购计划ID 主键',
    `list_attr_code` VARCHAR(3) COMMENT '目录属性代码 目录类别代码',
    `prod_tag` VARCHAR(3) COMMENT '产品标签 根据需要配置皖南区 皖北区 皖中区',
    `nego_tag` VARCHAR(3) COMMENT '耗材谈判标识 1国家带量；2省谈议价目录',
    `prov_amt_tag` VARCHAR(3) COMMENT '省级带量采购标识 0否；1是',
    `his_ord_detl_no` VARCHAR(50) COMMENT '医院his系统订单明细编号',
    `shp_time` DATETIME COMMENT '发货时间',
    `shpp_time` DATETIME COMMENT '收货时间',
    `comb_flag` VARCHAR(2) COMMENT '是否组套 0否；1是',
    `mcs_comb_id` VARCHAR(40) COMMENT '组套产品ID',
    `exra_serv_flag` VARCHAR(2) COMMENT '是否需要伴随服务 0否；1是',
    `mcs_comb_prod_detail_id` VARCHAR(40) COMMENT '组套产品明细ID',
    `comb_cnt` DECIMAL(20,4) COMMENT '采购组套套数',
    `sprt_reate` VARCHAR(3) COMMENT '是否支持翻新 0否；1是',
    `used` VARCHAR(100) COMMENT '用途',
    `stroom_id` VARCHAR(200) COMMENT '库房',
    `place_ord_time` DATETIME COMMENT '下单时间',
    `un_pubonln_rea` VARCHAR(500) COMMENT '未挂网原因',
    `detl_info` VARCHAR(100) COMMENT '明细情况',
    `delv_outsto_liable` VARCHAR(3) COMMENT '配送企业填写缺货责任方',
    `delv_outsto_rea` VARCHAR(200) COMMENT '配送企业填写缺货原因',
    `deleted_time` DATE COMMENT '数据删除时间',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `ord_detl_id` VARCHAR(40) COMMENT '订单明细ID 复合主键',
    `ord_id` VARCHAR(40) COMMENT '订单ID 主键',
    `ord_code` VARCHAR(40) COMMENT '订单代码',
    `prod_name` VARCHAR(200) COMMENT '产品名称',
    `purc_cnt` DECIMAL(20,4) COMMENT '采购数量',
    `purc_pric` DECIMAL(20,4) COMMENT '采购价格',
    `prod_id` VARCHAR(40) COMMENT '产品ID',
    `read_flag` VARCHAR(3) COMMENT '已读标志 0未读；1已读',
    `hosp_list_id` VARCHAR(40) COMMENT '医院目录ID',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `delventp_name` VARCHAR(100) COMMENT '配送企业名称',
    `shp_cnt` DECIMAL(20,4) COMMENT '发货数量',
    `shpp_cnt` DECIMAL(20,4) COMMENT '收货数量',
    `retn_cnt` DECIMAL(20,4) COMMENT '退货数量',
    `plan_detl_memo` TEXT COMMENT '计划详情备注',
    `send_time` DATETIME COMMENT '发送时间',
    `outsto_time` DATETIME COMMENT '缺货时间',
    PRIMARY KEY (`biz_date`, `subsys_codg`, `rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='交易订单明细信息';

CREATE TABLE IF NOT EXISTS `mcs_pubonln_trade_d` (
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `mcs_regno` VARCHAR(100) COMMENT '耗材注册证编号',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `pubonln_rslt_id` VARCHAR(40) COMMENT '编号 主键',
    `crte_time` DATETIME COMMENT '创建时间',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `biz_date` DATETIME COMMENT '业务日期',
    PRIMARY KEY (`subsys_codg`, `pubonln_rslt_id`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医用耗材挂网信息';

CREATE TABLE IF NOT EXISTS `drug_pubonln_rslt_d` (
    `rute_code` VARCHAR(10) COMMENT '给药途径代码 CV06.00.104用药途径代码',
    `prod_souc_name` VARCHAR(10) COMMENT '产品来源名称',
    `ypid` VARCHAR(40) COMMENT '药品ID',
    `prod_name` VARCHAR(200) COMMENT '商品名称',
    `rute_name` VARCHAR(50) COMMENT '给药途径名称',
    `cal_spec_code` VARCHAR(20) COMMENT '计算规格代码',
    `cal_spec_name` VARCHAR(300) COMMENT '计算用规格',
    `dayuse` VARCHAR(10) COMMENT '日均使用量',
    `convrat` VARCHAR(10) COMMENT '转换比',
    `market_permit_holder` VARCHAR(200) COMMENT '上市许可持有人',
    `min_rpup_prc` DECIMAL(20,4) COMMENT '产品现行全国最低带量采购价',
    `diffpric_dosform_code` VARCHAR(20) COMMENT '差比价剂型代码',
    `diffpric_dosform` VARCHAR(20) COMMENT '差比价剂型',
    `big_pac` VARCHAR(18) COMMENT '大包装',
    `big_pac_unt` VARCHAR(20) COMMENT '大包装单位',
    `lowpricmed_flag` VARCHAR(3) COMMENT '低价药标志 0否；1是',
    `essdrug_seq` VARCHAR(50) COMMENT '基药序号',
    `med_code` VARCHAR(100) COMMENT '医保编码',
    `county_product_code` VARCHAR(100) COMMENT '国药药品编码',
    `pubonln_stas` VARCHAR(3) COMMENT '挂网状态 挂网状态字典表',
    `npubonln_type` VARCHAR(3) COMMENT '未挂网分类',
    `npubonln_rea` VARCHAR(500) COMMENT '未挂网原因',
    `cancel_pubonln_time` TEXT COMMENT '取消挂网时间',
    `tenditm_id` VARCHAR(40) COMMENT '招标项目ID',
    `tenditm_name` VARCHAR(100) COMMENT '招标项目名称',
    `blng_item` VARCHAR(6) COMMENT '归属项目',
    `drug_identification` VARCHAR(64) COMMENT '药品标识',
    `compete_type` VARCHAR(2) COMMENT '竞争性非竞争性类型',
    `consistency_evaluation_flag` VARCHAR(2) COMMENT '是否一致性评价 0否；1是',
    `druglist_code` VARCHAR(20) COMMENT '招采药品目录代码',
    `grpno` VARCHAR(50) COMMENT '分组号',
    `tpclprod` VARCHAR(100) COMMENT '代表品',
    `latest_pubonln_pric` DECIMAL(20,4) COMMENT '更新前报价',
    `pubonln_pric` DECIMAL(20,4) COMMENT '挂网价格',
    `max_lmtpric` DECIMAL(20,4) COMMENT '最高销售限价',
    `lmtpric` DECIMAL(20,4) COMMENT '限价',
    `max_lmtpric_type` VARCHAR(3) COMMENT '最高销售限价分类 01一级及以下限价；02二级限价；03三级限价',
    `toplmtp_remark` VARCHAR(200) COMMENT '最高销售限价备注',
    `price_comparison` VARCHAR(2) COMMENT '挂网价是否超过最高销售限价 0否；1是',
    `max_lmtpric_parity` DECIMAL(20,4) COMMENT '最高销售限价比价',
    `max_lmtpric_parity_type` VARCHAR(3) COMMENT '最高销售限价比价分类',
    `aprvno_expy_begntime` DATETIME COMMENT '批准文号有效期开始时间',
    `aprvno_expy_endtime` DATETIME COMMENT '批准文号有效期结束时间',
    `aprvno_att` VARCHAR(500) COMMENT '批准文号附件',
    `regno` VARCHAR(50) COMMENT '注册号',
    `regcert_expy_begntime` DATETIME COMMENT '注册证有效期开始时间',
    `regcert_expy_endtime` DATETIME COMMENT '注册证有效期结束时间',
    `regcert_att` VARCHAR(500) COMMENT '注册证附件',
    `prod_manl_att` TEXT COMMENT '产品说明书附件',
    `prod_img_att` VARCHAR(500) COMMENT '产品图片附件',
    `trt_attr_code` VARCHAR(20) COMMENT '治疗属性编码 治疗属性字典',
    `trt_attr_att` TEXT COMMENT '治疗属性附件',
    `orrs_flag` VARCHAR(3) COMMENT '原研标志 0否；1是',
    `orrs_type` VARCHAR(50) COMMENT '原研类型',
    `orrs_attr_att` TEXT COMMENT '原研属性附件',
    `fda_flag` VARCHAR(3) COMMENT 'FDA标志 0否；1是',
    `fda_attr_att` TEXT COMMENT 'FDA属性附件',
    `cons_eval_flag` VARCHAR(3) COMMENT '一致性评价标志 0否；1是',
    `cons_eval_att` TEXT COMMENT '一致性评价附件',
    `druglist_att` VARCHAR(500) COMMENT '药品目录附件',
    `ref_prep_att` VARCHAR(500) COMMENT '参比制剂附件',
    `non_cptn_flag` VARCHAR(3) COMMENT '非竞争属性标志',
    `non_cptn_attr` VARCHAR(20) COMMENT '非竞争属性',
    `non_cptn_attr_att` TEXT COMMENT '非竞争属性附件',
    `sp_drug_type` VARCHAR(3) COMMENT '特殊药品类型',
    `sp_drug_att` TEXT COMMENT '特殊药品附件',
    `insn_flag` VARCHAR(3) COMMENT '胰岛素标志 0否；1是',
    `osm_pre` VARCHAR(128) COMMENT '渗透压',
    `lowga_flag` VARCHAR(3) COMMENT '低钙标志 0否；1是',
    `amac_type` VARCHAR(3) COMMENT '氨基酸类型',
    `poa_att` VARCHAR(200) COMMENT '授权委托书附件',
    `poa_expy_begntime` DATETIME COMMENT '授权委托有效期开始时间',
    `poa_expy_endtime` DATETIME COMMENT '授权委托有效期结束时间',
    `gmp_cont` TEXT COMMENT 'GMP内容',
    `qua_ext_cont` TEXT COMMENT '资质扩展内容',
    `att` TEXT COMMENT '附件',
    `uscc` VARCHAR(50) COMMENT '申报企业代码',
    `dcla_entp_name` VARCHAR(200) COMMENT '申报企业名称',
    `prxy_entp_code` VARCHAR(50) COMMENT '代理企业代码',
    `prxy_entp_name` VARCHAR(200) COMMENT '代理企业名称',
    `changed_time` DATETIME COMMENT '更新时间',
    `changed_content` VARCHAR(200) COMMENT '更新内容',
    `memo` TEXT COMMENT '备注',
    `efcc_type` VARCHAR(3) COMMENT '功能类型 1谈判；2竞价',
    `pubonln_type` VARCHAR(3) COMMENT '挂网类型 挂网类型字典',
    `seld_stas` VARCHAR(3) COMMENT '入选状态 0未入选；1已入选；2放弃',
    `adjm_chk_stas` VARCHAR(3) COMMENT '动态调整状态',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `admdvs_name` VARCHAR(100) COMMENT '医保区划名称',
    `item_drug_ful_info_id` VARCHAR(40) COMMENT '项目药品全量信息ID',
    `purc_totlcnt` DECIMAL(20,4) COMMENT '采购总数量',
    `drug_expy` VARCHAR(255) COMMENT '药品有效期',
    `bidprcu_datatype` VARCHAR(3) COMMENT '招采数据类型',
    `hi_pay_std` DECIMAL(20,4) COMMENT '医保支付标准',
    `bkls_flag` VARCHAR(3) COMMENT '黑名单标志 0否；1是',
    `bkls_begntime` DATETIME COMMENT '黑名单开始时间',
    `bkls_endtime` DATETIME COMMENT '黑名单结束时间',
    `isu_prov_code` VARCHAR(10) COMMENT '下发省份代码',
    `selc_stas` VARCHAR(3) COMMENT '遴选状态 1 符合挂网条件 2 超1.8倍 符合挂网条件 3 不符合挂网条件 4 待定',
    `pric_sort` VARCHAR(20) COMMENT '价格排序',
    `match_stas` VARCHAR(3) COMMENT '是否对码 0否；1是',
    `ext_goods_id` VARCHAR(50) COMMENT '老系统挂网id',
    `ext_prod_id` VARCHAR(50) COMMENT '老系统生产企业id',
    `ext_prxy_id` VARCHAR(50) COMMENT '老系统代理企业id',
    `nat_prov_min_pubonln_pric` DECIMAL(20,4) COMMENT '全国省级最低挂网价',
    `prcunt_name` VARCHAR(1) COMMENT '计价单位名称',
    `fil_no` VARCHAR(20) COMMENT '备案号',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `fil_time` DATETIME COMMENT '备案时间',
    `outsto_remark` VARCHAR(300) COMMENT '缺货备注',
    `tenant_id` VARCHAR(50) COMMENT '租户id',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `invd_flag` VARCHAR(3) COMMENT '无效标志 0有效；1无效',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `pubonln_rslt_id` VARCHAR(19) COMMENT '挂网结果ID 主键',
    `ext_code` VARCHAR(100) COMMENT '包装ID',
    `adjm_type` VARCHAR(3) COMMENT '调整类型',
    `drug_code` VARCHAR(50) COMMENT '招采药品代码',
    `old_sys_prod_id` VARCHAR(40) COMMENT '老系统的产品ID',
    `drugstdcode` VARCHAR(50) COMMENT '药品本位码',
    `nat_drug_no` VARCHAR(50) COMMENT '国家药品编号',
    `drug_name` VARCHAR(200) COMMENT '药品名称',
    `drug_name_pinyin` VARCHAR(500) COMMENT '药品名称拼音',
    `genname_code` VARCHAR(50) COMMENT '通用名代码',
    `genname` VARCHAR(100) COMMENT '通用名',
    `dosform_code` VARCHAR(10) COMMENT '剂型代码',
    `dosform_name` VARCHAR(200) COMMENT '剂型名称',
    `spec_name` VARCHAR(300) COMMENT '规格名称',
    `minunt_name` VARCHAR(10) COMMENT '最小使用单位名称',
    `minpacunt_code` VARCHAR(10) COMMENT '最小包装单位代码 最小包装单位字典',
    `pac` VARCHAR(500) COMMENT '包装',
    `pacmatl_code` VARCHAR(10) COMMENT '包装材质代码 包装材质(pacmatl)',
    `pacmatl_name` VARCHAR(500) COMMENT '包装材质名称',
    `pacmatl` VARCHAR(500) COMMENT '包装材质',
    `bas_medn_flag` VARCHAR(3) COMMENT '基本药物标志 0 否；1 是',
    `acdbas_code` VARCHAR(10) COMMENT '酸根盐基代码',
    `acdbas_name` VARCHAR(100) COMMENT '酸根盐基名称',
    `otc_attr_code` VARCHAR(10) COMMENT 'OTC属性代码',
    `otc_attr_name` VARCHAR(10) COMMENT 'OTC属性名称',
    `prodentp_code` VARCHAR(50) COMMENT '生产企业代码',
    `prodentp_name` VARCHAR(200) COMMENT '生产企业名称',
    `subpck_entp_name` VARCHAR(200) COMMENT '分包装企业名称',
    `drug_attr_name` VARCHAR(50) COMMENT '药品属性名称',
    `prod_souc_code` VARCHAR(3) COMMENT '产品来源代码 药品产品来源代码',
    `vita_type` VARCHAR(3) COMMENT '维生素类型',
    `minpacunt_name` VARCHAR(50) COMMENT '最小包装单位名称',
    `subpck_entp_code` VARCHAR(50) COMMENT '分包装企业代码',
    `druglist_name` VARCHAR(100) COMMENT '药品目录名称',
    `grp_id` VARCHAR(40) COMMENT '分组ID',
    `tpclprod_modi_flag` VARCHAR(3) COMMENT '代表品修改标志 0否；1是',
    `latest_lmtpric` DECIMAL(20,4) COMMENT '更新前最高销售限价',
    `aprvno` VARCHAR(100) COMMENT '批准文号',
    `nsugar_flag` VARCHAR(3) COMMENT '无糖标志 0否；1是',
    `syn_flag` VARCHAR(3) COMMENT '是否要同步交易 0否；1是',
    `spec_code` VARCHAR(20) COMMENT '规格代码',
    `drug_public_inquiry` VARCHAR(64) COMMENT '药品公开询价说明',
    `sp_drug_flag` VARCHAR(3) COMMENT '特殊药品标志 0否；1是',
    `bez_musk_type` VARCHAR(3) COMMENT '牛黄或麝香的类型',
    `minunt_code` VARCHAR(10) COMMENT '最小使用单位代码 最小使用单位(min_useunt)',
    `minprc` DECIMAL(20,4) COMMENT '产品现行全国最低采购价',
    `entp_quo_parity` DECIMAL(20,4) COMMENT '企业报价比价',
    `druglist_flag` VARCHAR(3) COMMENT '药品目录标志',
    `ref_prep_flag` VARCHAR(3) COMMENT '参比制剂标志',
    `latest_modify_time` DATETIME COMMENT '最后更新时间',
    `opt_time` DATETIME COMMENT '经办时间',
    `qlv_srt` VARCHAR(10) COMMENT '质量层次排序',
    `prov_lmtpric` DECIMAL(20,4) COMMENT '省级集采限价',
    `ext_product_id` DECIMAL(20,4) COMMENT '老系统产品id',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='药品挂网信息';

CREATE TABLE IF NOT EXISTS `tran_admdvs_open_a` (
    `admdvs_name` VARCHAR(100) COMMENT '医保区划名称',
    `memo` TEXT COMMENT '备注',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `ver` VARCHAR(20) COMMENT '版本号',
    `deleted_time` DATE COMMENT '数据删除时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `insu_open_sign` VARCHAR(3) COMMENT '参保地开通标志 0否；1是',
    `fix_open_sign` VARCHAR(3) COMMENT '就医地开通标志 0否；1是',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `coner` VARCHAR(50) COMMENT '联系人',
    `mbb_tel` VARCHAR(50) COMMENT '移动电话',
    `inhos_open_sign` VARCHAR(3) COMMENT '住院开通标志 0否；1是',
    `outpa_open_sign` VARCHAR(3) COMMENT '门诊开通标志 0否；1是',
    `spout_open_sign` VARCHAR(3) COMMENT '特门开通标志 0否；1是',
    `vali_flag` VARCHAR(3) COMMENT '当前有效标识 0无效；1有效',
    `chg_begntime` VARCHAR(8) COMMENT '变更生效日期',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `biz_date` DATE COMMENT '业务日期',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    PRIMARY KEY (`subsys_codg`, `rid`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='统筹区开通信息';

CREATE TABLE IF NOT EXISTS `outmed_setl_d` (
    `flxempe_flag` VARCHAR(3) COMMENT '灵活就业标志 0 否；1 是',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `setl_time` DATETIME COMMENT '结算时间',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `biz_date` DATE COMMENT '业务日期',
    `init_setl_id` VARCHAR(30) COMMENT '原结算ID',
    `balc` DECIMAL(20,4) COMMENT '余额',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `pool_prop_selfpay` DECIMAL(20,4) COMMENT '基本医疗统筹支付比例',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `mdtrt_cert_no` VARCHAR(50) COMMENT '就诊凭证编号',
    `refd_setl_flag` VARCHAR(3) COMMENT '退费结算标志 0 否；1 是',
    `memo` VARCHAR(500) COMMENT '备注',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `clr_optins` VARCHAR(6) COMMENT '清算经办机构',
    `medins_setl_id` VARCHAR(30) COMMENT '医药机构结算ID',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `age` DECIMAL(20,4) COMMENT '年龄',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `cvlserv_lv` VARCHAR(6) COMMENT '公务员等级 公务员等级(cvlserv_lv)',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `sp_psn_type_lv` VARCHAR(3) COMMENT '特殊人员类型等级',
    `clct_grde` VARCHAR(3) COMMENT '缴费档次 缴费档次 clctGrde',
    `nwb_flag` VARCHAR(3) COMMENT '新生儿标志 0否；1是',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `lmtpric_hosp_lv` VARCHAR(6) COMMENT '限价医院等级 限价医院等级字典表',
    `dedc_hosp_lv` VARCHAR(6) COMMENT '起付线医院等级 起付线医院等级字典表',
    `begndate` DATE COMMENT '开始日期',
    `enddate` DATE COMMENT '结束日期',
    `mdtrt_cert_type` VARCHAR(3) COMMENT '就诊凭证类型 就诊凭证类型(mdtrt_cert_type)',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `setl_type` VARCHAR(6) COMMENT '结算类别 结算类别字典表',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `psn_setlway` VARCHAR(6) COMMENT '个人结算方式 个人结算方式 psn_setlway',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `dedc_std` DECIMAL(20,4) COMMENT '起付标准',
    `crt_dedc` DECIMAL(20,4) COMMENT '本次起付线',
    `act_pay_dedc` DECIMAL(20,4) COMMENT '实际支付起付线',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `hifes_pay` DECIMAL(20,4) COMMENT '补充医疗保险基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `hifdm_pay` DECIMAL(20,4) COMMENT '伤残人员医疗保障基金支出',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `psn_pay` DECIMAL(20,4) COMMENT '个人支付金额',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `cash_payamt` DECIMAL(20,4) COMMENT '现金支付金额',
    `ownpay_hosp_part` DECIMAL(20,4) COMMENT '自费中医院负担部分',
    `cal_ipt_cnt` VARCHAR(3) COMMENT '计算住院次数标志 0 否；1 是',
    `setl_cashpay_way` VARCHAR(6) COMMENT '结算现金支付方式',
    `year` VARCHAR(4) COMMENT '年度',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `invono` TEXT COMMENT '发票号',
    `manl_reim_rea` VARCHAR(6) COMMENT '零星报销原因',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`biz_date`, `rid`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='异地就医结算信息';

CREATE TABLE IF NOT EXISTS `outmed_mdtrt_d` (
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `medins_setl_id` VARCHAR(30) COMMENT '医药机构结算ID',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `coner_name` VARCHAR(50) COMMENT '联系人姓名',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `cvlserv_lv` VARCHAR(6) COMMENT '公务员等级 公务员等级(cvlserv_lv)',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `sp_psn_type_lv` VARCHAR(3) COMMENT '特殊人员类型等级',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `flxempe_flag` VARCHAR(3) COMMENT '灵活就业标志 0 否；1 是',
    `nwb_flag` VARCHAR(3) COMMENT '新生儿标志 0否；1是',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `lmtpric_hosp_lv` VARCHAR(6) COMMENT '限价医院等级 限价医院等级字典表',
    `dedc_hosp_lv` VARCHAR(6) COMMENT '起付线医院等级 起付线医院等级字典表',
    `begntime` DATETIME COMMENT '开始时间',
    `endtime` DATETIME COMMENT '结束时间',
    `mdtrt_cert_type` VARCHAR(3) COMMENT '就诊凭证类型 就诊凭证类型(mdtrt_cert_type)',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `rloc_type` VARCHAR(6) COMMENT '异地安置类别',
    `pre_pay_flag` VARCHAR(3) COMMENT '先行支付标志 0 否；1 是',
    `year` VARCHAR(4) COMMENT '年度',
    `ipt_otp_no` VARCHAR(30) COMMENT '住院门诊号',
    `medrcdno` VARCHAR(30) COMMENT '病历号',
    `chfpdr_code` VARCHAR(30) COMMENT '主诊医师代码 医保医师代码',
    `adm_diag_dscr` VARCHAR(200) COMMENT '入院诊断描述',
    `adm_dept_codg` VARCHAR(30) COMMENT '入院科室编码',
    `adm_dept_name` VARCHAR(100) COMMENT '入院科室名称',
    `adm_bed` VARCHAR(50) COMMENT '入院床位',
    `wardarea_bed` VARCHAR(50) COMMENT '病区床位',
    `traf_dept_flag` VARCHAR(6) COMMENT '转科室标志 0 否；1 是',
    `dscg_maindiag_code` VARCHAR(30) COMMENT '住院主诊断代码 医保疾病诊断与分类代码',
    `dscg_dept_codg` VARCHAR(30) COMMENT '出院科室编码',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `oprn_oprt_name` VARCHAR(500) COMMENT '手术操作名称',
    `otp_diag_info` VARCHAR(200) COMMENT '门诊诊断信息',
    `die_date` DATE COMMENT '死亡日期',
    `ipt_days` DECIMAL(20,4) COMMENT '住院天数',
    `fpsc_no` VARCHAR(50) COMMENT '计划生育服务证号',
    `matn_type` VARCHAR(6) COMMENT '生育类别 生育类别(matn_type)',
    `birctrl_type` VARCHAR(6) COMMENT '计划生育手术类别 计划生育手术类别(birctrl_type)',
    `latechb_flag` VARCHAR(3) COMMENT '晚育标志 0 否；1 是',
    `geso_val` DECIMAL(20,4) COMMENT '孕周数',
    `fetts` DECIMAL(20,4) COMMENT '胎次',
    `fetus_cnt` DECIMAL(20,4) COMMENT '胎儿数',
    `pret_flag` VARCHAR(3) COMMENT '早产标志 0 否；1 是',
    `prey_time` DATETIME COMMENT '妊娠时间',
    `birctrl_matn_date` DATETIME COMMENT '计划生育手术或生育日期',
    `cop_flag` VARCHAR(3) COMMENT '伴有并发症标志 0 否；1 是',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `chfpdr_name` VARCHAR(50) COMMENT '主诊医师姓名',
    `dscg_maindiag_name` VARCHAR(300) COMMENT '住院主诊断名称',
    `adm_caty` VARCHAR(10) COMMENT '入院科别 填写院内科室代码',
    `dscg_caty` VARCHAR(10) COMMENT '出院科别 填写院内科室代码',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `clct_grde` VARCHAR(3) COMMENT '缴费档次 缴费档次 clctGrde',
    `refl_old_mdtrt_id` VARCHAR(30) COMMENT '转诊前就诊ID',
    `oprn_oprt_code` VARCHAR(200) COMMENT '手术操作代码 医保版手术操作与分类代码',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `ars_year_ipt_flag` VARCHAR(3) COMMENT '跨年度住院标志 0 否；1 是',
    `age` DECIMAL(20,4) COMMENT '年龄',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `mdtrt_cert_no` VARCHAR(50) COMMENT '就诊凭证编号',
    `dscg_dept_name` VARCHAR(100) COMMENT '出院科室名称',
    `memo` VARCHAR(500) COMMENT '备注',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `dscg_bed` VARCHAR(30) COMMENT '出院床位',
    `dscg_way` VARCHAR(3) COMMENT '离院方式 编码 1.医嘱离院2.医嘱转院 拟接收医疗机构名称3.医嘱转社区卫生服务机构 乡镇卫生院 拟接收医疗机构名称4.非医嘱离院5.死亡9.其他',
    `main_cond_dscr` TEXT COMMENT '主要病情描述',
    `inhosp_stas` VARCHAR(3) COMMENT '在院状态 0出院；1在院',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='异地就医门诊信息';

CREATE TABLE IF NOT EXISTS `tran_outmed_mmmt_d` (
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `brdy` VARCHAR(8) COMMENT '出生日期',
    `insuplc_admdvs` VARCHAR(6) COMMENT '参保人所在地的医保区划 复合主键',
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `cert_type` VARCHAR(3) COMMENT '证件类型 身份证件类别代码表',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `begndate` VARCHAR(8) COMMENT '开始日期',
    `enddate` VARCHAR(8) COMMENT '结束日期',
    `opsp_dise` VARCHAR(6) COMMENT '门慢门特病种',
    `vali_flag` VARCHAR(3) COMMENT '当前有效标志 0无效；1有效',
    `opsp_crtf_qua_no` VARCHAR(30) COMMENT '门慢门特认定资格编号',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门慢门特认定资格信息数据';

CREATE TABLE IF NOT EXISTS `trans_clr_setl_d` (
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `dedc_std` DECIMAL(20,4) COMMENT '起付标准',
    `nwb_flag` VARCHAR(3) COMMENT '新生儿标志 0否；1是',
    `enddate` DATE COMMENT '结束日期',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `invono` TEXT COMMENT '发票号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `hifes_pay` DECIMAL(20,4) COMMENT '补充医疗保险基金支出',
    `hifdm_pay` DECIMAL(20,4) COMMENT '伤残人员医疗保障基金支出',
    `manl_reim_rea` VARCHAR(6) COMMENT '零星报销原因',
    `memo` VARCHAR(500) COMMENT '备注',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_setlway` VARCHAR(6) COMMENT '个人结算方式 个人结算方式 psn_setlway',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `setl_id` VARCHAR(30) COMMENT '结算ID 主键',
    `clr_optins` VARCHAR(6) COMMENT '清算经办机构',
    `medins_setl_id` VARCHAR(30) COMMENT '医药机构结算ID',
    `init_setl_id` VARCHAR(30) COMMENT '原结算ID',
    `mdtrt_id` VARCHAR(30) COMMENT '就诊ID',
    `clr_appy_evt_id` VARCHAR(30) COMMENT '机构清算申请事件ID',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` VARCHAR(50) COMMENT '证件号码',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `age` DECIMAL(20,4) COMMENT '年龄',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `cvlserv_flag` VARCHAR(3) COMMENT '公务员标志 0 否；1 是',
    `cvlserv_lv` VARCHAR(6) COMMENT '公务员等级 公务员等级(cvlserv_lv)',
    `sp_psn_type` VARCHAR(6) COMMENT '特殊人员类型 特殊人员类型 sp_psn_type',
    `sp_psn_type_lv` VARCHAR(3) COMMENT '特殊人员类型等级',
    `clct_grde` VARCHAR(3) COMMENT '缴费档次 缴费档次 clctGrde',
    `flxempe_flag` VARCHAR(3) COMMENT '灵活就业标志 0 否；1 是',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `emp_type` VARCHAR(6) COMMENT '单位类型 单位类型字典',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `afil_indu` VARCHAR(6) COMMENT '所属行业 所属行业字典表',
    `emp_mgt_type` VARCHAR(6) COMMENT '单位管理类型 单位管理类型字典',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `lmtpric_hosp_lv` VARCHAR(6) COMMENT '限价医院等级 限价医院等级字典表',
    `dedc_hosp_lv` VARCHAR(6) COMMENT '起付线医院等级 起付线医院等级字典表',
    `begndate` DATE COMMENT '开始日期',
    `setl_time` DATETIME COMMENT '结算时间',
    `mdtrt_cert_type` VARCHAR(3) COMMENT '就诊凭证类型 就诊凭证类型(mdtrt_cert_type)',
    `mdtrt_cert_no` VARCHAR(50) COMMENT '就诊凭证编号',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `setl_type` VARCHAR(6) COMMENT '结算类别 结算类别字典表',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `crt_dedc` DECIMAL(20,4) COMMENT '本次起付线',
    `act_pay_dedc` DECIMAL(20,4) COMMENT '实际支付起付线',
    `pool_prop_selfpay` DECIMAL(20,4) COMMENT '基本医疗统筹支付比例',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `psn_pay` DECIMAL(20,4) COMMENT '个人支付金额',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `cash_payamt` DECIMAL(20,4) COMMENT '现金支付金额',
    `ownpay_hosp_part` DECIMAL(20,4) COMMENT '自费中医院负担部分',
    `balc` DECIMAL(20,4) COMMENT '余额',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `refd_setl_flag` VARCHAR(3) COMMENT '退费结算标志 0 否；1 是',
    `cal_ipt_cnt` VARCHAR(3) COMMENT '计算住院次数标志 0 否；1 是',
    `setl_cashpay_way` VARCHAR(6) COMMENT '结算现金支付方式',
    `year` VARCHAR(4) COMMENT '年度',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `medins_stmt_flag` VARCHAR(6) COMMENT '医疗机构对账标志',
    `quts_type` VARCHAR(6) COMMENT '编制类型 编制类型字典',
    `bydise_setl_payamt` DECIMAL(20,4) COMMENT '按病种结算支付金额',
    `exct_item_fund_payamt` DECIMAL(20,4) COMMENT '除外项目基金支付金额',
    `clr_ym` VARCHAR(6) COMMENT '清算年月',
    `n_msgid` VARCHAR(30) COMMENT '国家发送方报文ID',
    `setl_sn` VARCHAR(40) COMMENT '国家结算流水号',
    `mdtrt_seq` VARCHAR(30) COMMENT '国家就诊顺序号',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    PRIMARY KEY (`subsys_codg`, `rid`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='已清算结算信息';

CREATE TABLE IF NOT EXISTS `out_appy_d` (
    `psn_no` VARCHAR(30) COMMENT '人员编号 主键',
    `trt_dcla_detl_sn` VARCHAR(30) COMMENT '待遇申报明细流水号',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_insu_rlts_id` VARCHAR(20) COMMENT '人员参保关系ID 主键',
    `psn_cert_type` VARCHAR(6) COMMENT '人员证件类型 身份证件类别代码表',
    `certno` TEXT COMMENT '证件号码',
    `psn_name` VARCHAR(50) COMMENT '人员姓名',
    `gend` VARCHAR(6) COMMENT '性别 GB T 2261.1 2003',
    `naty` VARCHAR(3) COMMENT '民族 GB T 3304 1991民族代码',
    `brdy` DATE COMMENT '出生日期',
    `tel` VARCHAR(50) COMMENT '联系电话',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `emp_no` VARCHAR(40) COMMENT '单位编号',
    `emp_name` VARCHAR(200) COMMENT '单位名称',
    `rloc_admdvs` VARCHAR(6) COMMENT '安置地所属行政区代码',
    `rloc_coty_type` VARCHAR(6) COMMENT '安置区类型 10省内；20省外',
    `rloc_hsorg_name` VARCHAR(200) COMMENT '安置地医保机构名称',
    `rloc_hsorg_coner` VARCHAR(100) COMMENT '安置地医保机构联系人',
    `out_onln_way` VARCHAR(6) COMMENT '异地联网方式 异地联网方式字典表',
    `rloc_rea` VARCHAR(6) COMMENT '异地安置原因',
    `resout_addr` VARCHAR(200) COMMENT '居外地址',
    `memo` TEXT COMMENT '备注',
    `agnter_name` VARCHAR(50) COMMENT '代办人姓名',
    `agnter_certno` VARCHAR(50) COMMENT '代办人证件号码',
    `agnter_tel` VARCHAR(30) COMMENT '代办人联系方式',
    `agnter_addr` VARCHAR(200) COMMENT '代办人联系地址',
    `agnter_rlts` VARCHAR(3) COMMENT '代办人关系 代办人关系(agnter_rlts)',
    `enddate` DATE COMMENT '结束日期',
    `out_fil_upld_stas` VARCHAR(3) COMMENT '异地备案上报状态 异地备案上报状态字典表',
    `att_cnt` DECIMAL(20,4) COMMENT '附件数量',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `trafout_fixmedins_code` VARCHAR(30) COMMENT '转出地定点医药机构编号',
    `trafout_fixmedins_name` VARCHAR(200) COMMENT '转出地定点医药机构名称',
    `bankcode` VARCHAR(50) COMMENT '银行行号',
    `bank_type_code` VARCHAR(6) COMMENT '银行行别代码',
    `acctname` VARCHAR(200) COMMENT '银行卡户名',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `begndate` DATE COMMENT '开始日期',
    `retn_flag` VARCHAR(3) COMMENT '回退标志',
    `dcla_souc` VARCHAR(6) COMMENT '申报来源 申报来源(dcla_souc)',
    `rloc_hsorg_tel` VARCHAR(50) COMMENT '安置地医保机构联系电话',
    `agnter_cert_type` VARCHAR(6) COMMENT '代办人证件类型 身份证件类别代码表',
    `bankacct` VARCHAR(50) COMMENT '银行账号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `bank_samecity_out_flag` VARCHAR(6) COMMENT '银行同城异地标志',
    `biz_date` DATE COMMENT '业务日期',
    PRIMARY KEY (`rid`, `subsys_codg`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='异地申请信息';

CREATE TABLE IF NOT EXISTS `opsp_dise_list_b` (
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `tram_data_id` VARCHAR(40) COMMENT '传输数据ID',
    `dise_cont` TEXT COMMENT '病种内涵',
    `deleted_time` DATE COMMENT '数据删除时间',
    `opsp_dise_code` VARCHAR(30) COMMENT '门慢门特病种目录代码',
    `opsp_dise_majcls_name` VARCHAR(500) COMMENT '门慢门特病种大类名称',
    `opsp_dise_subd_clss_name` VARCHAR(500) COMMENT '门慢门特病种细分类名称',
    `memo` VARCHAR(500) COMMENT '备注',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `ver` VARCHAR(50) COMMENT '版本号',
    `ver_name` VARCHAR(30) COMMENT '版本名称',
    `trt_guide_pagen` VARCHAR(255) COMMENT '诊疗指南页码',
    `trt_guide_elecacs` BLOB COMMENT '诊疗指南电子档案',
    `opsp_dise_name` VARCHAR(300) COMMENT '门慢门特病种名称',
    `opsp_dise_majcls_code` VARCHAR(50) COMMENT '门慢门特病种大类代码',
    `invd_time` DATETIME COMMENT '失效时间',
    `isu_flag` VARCHAR(3) COMMENT '下发标志',
    `efft_time` DATETIME COMMENT '生效时间',
    `dif_dscr` TEXT COMMENT '差异说明',
    `biz_date` DATE COMMENT '业务日期',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='国家门慢门特病种';

CREATE TABLE IF NOT EXISTS `medins_info_b` (
    `medins_code` VARCHAR(40) COMMENT '医疗机构代码 医保定点医疗机构代码',
    `uscc` VARCHAR(50) COMMENT '统一社会信用代码',
    `medins_name` VARCHAR(255) COMMENT '医疗机构名称',
    `medins_abbr` VARCHAR(50) COMMENT '医疗机构简称',
    `fax_no` VARCHAR(50) COMMENT '传真号码',
    `main_resper` VARCHAR(50) COMMENT '主要负责人',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `addr` VARCHAR(500) COMMENT '联系地址',
    `medins_type` VARCHAR(6) COMMENT '医疗服务机构类型',
    `medins_type_name` VARCHAR(50) COMMENT '医疗服务机构类型名称',
    `fixmedins_type` VARCHAR(6) COMMENT '定点医疗服务机构类型',
    `reg_stas` VARCHAR(10) COMMENT '登记状态',
    `enrd_staf_psncnt` DECIMAL(20,4) COMMENT '在编职工数',
    `hosp_dept_cnt` DECIMAL(20,4) COMMENT '医院科室数',
    `hosp_key_dept_cnt` DECIMAL(20,4) COMMENT '医院重点科室数',
    `senr_profttl_psncnt` DECIMAL(20,4) COMMENT '正高职称人数',
    `mid_profttl_psncnt` DECIMAL(20,4) COMMENT '中级职称人数',
    `pro_techstf_psncnt` DECIMAL(20,4) COMMENT '专业技术人员人数',
    `depsenr_profttl_psncnt` DECIMAL(20,4) COMMENT '副高职称人数',
    `aprv_bed_cnt` DECIMAL(20,4) COMMENT '批准床位数量',
    `biz_psncnt` DECIMAL(20,4) COMMENT '营业人数',
    `oth_psncnt` DECIMAL(20,4) COMMENT '其他人数',
    `biz_area` VARCHAR(100) COMMENT '经营面积',
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `lnt` VARCHAR(100) COMMENT '经度',
    `lat` VARCHAR(100) COMMENT '纬度',
    `endtime` DATETIME COMMENT '结束时间',
    `memo` TEXT COMMENT '备注',
    `grst_hosp_flag` VARCHAR(3) COMMENT '基层医院标志',
    `cred_lv` VARCHAR(3) COMMENT '信用等级',
    `medins_natu` VARCHAR(3) COMMENT '机构性质',
    `cred_lv_name` VARCHAR(50) COMMENT '信用等级名称',
    `prnt_medins_code` VARCHAR(40) COMMENT '上级医疗机构代码',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `medins_natu_name` VARCHAR(50) COMMENT '机构性质名称',
    `npmo_flag` VARCHAR(3) COMMENT '非盈利机构标志',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `crter_id` VARCHAR(20) COMMENT '创建人ID',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人ID',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `ver` VARCHAR(50) COMMENT '版本号',
    `sync_prnt_flag` VARCHAR(3) COMMENT '同步上级标志 0否；1是',
    `medins_grade` VARCHAR(3) COMMENT '医疗机构等次',
    `ver_rid` VARCHAR(40) COMMENT '版本唯一编号',
    `ver_name` VARCHAR(30) COMMENT '版本名称',
    `medins_info_id` VARCHAR(40) COMMENT '医疗机构信息ID',
    `legent_name` VARCHAR(500) COMMENT '法人名称',
    `legrep_name` VARCHAR(255) COMMENT '法定代表人姓名',
    `medins_prac_lic_regno` VARCHAR(50) COMMENT '医疗机构执业许可证登记号',
    `biznat` VARCHAR(3) COMMENT '经营性质',
    `afil_rlts` VARCHAR(6) COMMENT '隶属关系 隶属关系字典表',
    `trtitem` TEXT COMMENT '诊疗项目',
    `medins_prac_lic_expy` DATETIME COMMENT '医疗机构执业许可证有效期限',
    `bank_name` VARCHAR(100) COMMENT '开户银行名称',
    `bankacct` VARCHAR(50) COMMENT '银行账号',
    `bank` VARCHAR(200) COMMENT '开户银行',
    `inchg_hosp_resper_name` VARCHAR(36) COMMENT '分管医疗机构负责人姓名',
    `hi_resper_name` VARCHAR(50) COMMENT '医保办负责人姓名',
    `hi_resper_tel` VARCHAR(50) COMMENT '医保办负责人联系电话',
    `hi_tel` VARCHAR(36) COMMENT '医保办电话',
    `hi_email` VARCHAR(36) COMMENT '医保办邮箱',
    `medins_cert_elec_doc` BLOB COMMENT '医疗机构证照电子文档',
    `medins_prac_lic_elec_doc` VARCHAR(255) COMMENT '医疗机构执业许可证电子文档',
    `asgcode_dr_cnt` DECIMAL(20,4) COMMENT '已赋码医生数量',
    `asgcode_nurs_cnt` DECIMAL(20,4) COMMENT '已赋码护士数量',
    `act_addr_info` TEXT COMMENT '实际地址信息',
    `ec_open_flag` VARCHAR(3) COMMENT '电子凭证开通标志',
    `dif_dscr` TEXT COMMENT '差异说明',
    `biz_date` DATE COMMENT '业务日期',
    `deleted_time` DATE COMMENT '数据删除时间',
    `deleted` VARCHAR(5) COMMENT '数据删除状态',
    `exch_updt_time` DATE COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `econ_type` VARCHAR(6) COMMENT '经济类型 GB T 12402 2000经济类型分类与代码',
    `inchg_hosp_resper_tel` VARCHAR(36) COMMENT '分管医疗机构负责人电话',
    `act_addr_code` VARCHAR(100) COMMENT '实际地址编码',
    `medinslv` VARCHAR(3) COMMENT '医疗机构等级 医院等级(hosp_lv)',
    `begntime` DATETIME COMMENT '开始时间',
    PRIMARY KEY (`rid`, `biz_date`, `subsys_codg`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='医疗机构开通信息';

CREATE TABLE IF NOT EXISTS `clred_setl_funds_sum_d` (
    `hosp_lv` VARCHAR(6) COMMENT '医院等级 医院等级(hosp_lv)',
    `fixmedins_pay_type` VARCHAR(6) COMMENT '省属 1省属 2市属 3县域 9其他',
    `dise_no` VARCHAR(30) COMMENT '病种编号',
    `dise_name` VARCHAR(500) COMMENT '病种名称',
    `union_id` VARCHAR(40) COMMENT '联盟编号',
    `union_name` VARCHAR(100) COMMENT '联盟名称',
    `fixmedins_attr` VARCHAR(6) COMMENT '机构属性 1公立2民营',
    `chk_det_amt` DECIMAL(20,4) COMMENT '审核扣款金额',
    `setl_supy_amt` DECIMAL(20,4) COMMENT '医保结算补差金额',
    `fund_payamt_310201` DECIMAL(20,4) COMMENT '城镇职工基本医疗保险个人账户基金',
    `fund_payamt_310205` DECIMAL(20,4) COMMENT '健康账户基金',
    `fund_payamt_310301` DECIMAL(20,4) COMMENT '城镇职工大病医疗保险基金',
    `fund_payamt_320101` DECIMAL(20,4) COMMENT '公务员医疗补助基金',
    `fund_payamt_330101` DECIMAL(20,4) COMMENT '大额医疗费用补助基金',
    `fund_payamt_390201` DECIMAL(20,4) COMMENT '城乡居民大病医疗保险基金',
    `fund_payamt_340101` DECIMAL(20,4) COMMENT '离休人员医疗保障基金',
    `fund_payamt_350101` DECIMAL(20,4) COMMENT '一至六级残疾军人医疗补助基金',
    `fund_payamt_390101` DECIMAL(20,4) COMMENT '城乡居民基本医疗保险基金',
    `fund_payamt_510101` DECIMAL(20,4) COMMENT '生育基金',
    `fund_payamt_99999601` DECIMAL(20,4) COMMENT '医院垫付基金',
    `fund_payamt_999997` DECIMAL(20,4) COMMENT '其他基金',
    `fund_payamt_99999721` DECIMAL(20,4) COMMENT '正常分娩财政补助',
    `fund_payamt_99999722` DECIMAL(20,4) COMMENT '保健财政补助',
    `fund_payamt_99999723` DECIMAL(20,4) COMMENT '省级第一道扶贫补助',
    `fund_payamt_99999725` DECIMAL(20,4) COMMENT '市级扶贫补助',
    `fund_payamt_99999726` DECIMAL(20,4) COMMENT '县级扶贫补助',
    `fund_payamt_99999764` DECIMAL(20,4) COMMENT '生育DNA缺陷基金补助',
    `fund_payamt_99999728` DECIMAL(20,4) COMMENT '处级干部补助',
    `fund_payamt_99999729` DECIMAL(20,4) COMMENT '医疗救助补充保险基金',
    `fund_payamt_99999730` DECIMAL(20,4) COMMENT '产前检查补助基金',
    `fund_payamt_99999761` DECIMAL(20,4) COMMENT '省级第三道扶贫补助',
    `fund_payamt_99999755` DECIMAL(20,4) COMMENT '医疗救助基金重特大疾病再救助',
    `fund_payamt_99999762` DECIMAL(20,4) COMMENT '强制精神病住院补助',
    `fund_payamt_99999763` DECIMAL(20,4) COMMENT '民政一次性补助',
    `fund_payamt_999998` DECIMAL(20,4) COMMENT '现金支出',
    `fund_payamt_999999` DECIMAL(20,4) COMMENT '个人支出部分个人账户现金支出',
    `fund_payamt_31020402` DECIMAL(20,4) COMMENT '异地家庭共济账户支付',
    `fund_payamt_370102` DECIMAL(20,4) COMMENT '二次补充医疗保险基金',
    `fund_payamt_99999754` DECIMAL(20,4) COMMENT '公务员补助账户支付',
    `clr_type` VARCHAR(6) COMMENT '清算类别 清算类别(clr_type)',
    `clr_way` VARCHAR(6) COMMENT '清算方式 清算方式(clr_way)',
    `clr_type_lv2` VARCHAR(6) COMMENT '二级清算类别 二级清算类别 clr_type_lv2',
    `clr_appy_evt_id` VARCHAR(30) COMMENT '机构清算申请事件编号',
    `clr_stas` VARCHAR(3) COMMENT '月结状态 1 申报未清算 2 已清算',
    `fee_clr_ext_id` VARCHAR(30) COMMENT '编号',
    `dpst_sumamt` DECIMAL(20,4) COMMENT '预留保证金总额',
    `ds_mon_settle_id` VARCHAR(32) COMMENT '结算单编号',
    `deleted_time` DATETIME COMMENT '数据删除时间',
    `deleted` VARCHAR(3) COMMENT '数据删除状态',
    `exch_updt_time` DATETIME COMMENT '交换库更新时间',
    `subsys_codg` VARCHAR(40) COMMENT '子系统代码',
    `pay_loc` VARCHAR(6) COMMENT '支付地点类别 支付地点类别字典表',
    `insu_admdvs` VARCHAR(6) COMMENT '参保所属医保区划',
    `fixmedins_code` VARCHAR(30) COMMENT '定点医药机构编号',
    `fixmedins_name` VARCHAR(255) COMMENT '定点医药机构名称',
    `fix_blng_admdvs` VARCHAR(6) COMMENT '定点归属医保区划',
    `med_type` VARCHAR(6) COMMENT '医疗类别 医疗类别(med_type)',
    `med_type_name` VARCHAR(30) COMMENT '医疗类别名称 医疗类别(med_type)',
    `setl_ym` VARCHAR(6) COMMENT '结算年月',
    `medfee_sumamt` DECIMAL(20,4) COMMENT '医疗费总额 本次需要结算的医疗费用总额 不包括处方药品费用',
    `fulamt_ownpay_amt` DECIMAL(20,4) COMMENT '全自费金额',
    `preselfpay_amt` DECIMAL(20,4) COMMENT '先行自付金额',
    `inscp_amt` DECIMAL(20,4) COMMENT '符合范围金额',
    `hifp_pay` DECIMAL(20,4) COMMENT '统筹基金支出',
    `hi_agre_sumfee` DECIMAL(20,4) COMMENT '医保认可费用总额',
    `cvlserv_pay` DECIMAL(20,4) COMMENT '公务员医疗补助资金支出',
    `hifes_pay` DECIMAL(20,4) COMMENT '补充医疗保险基金支出',
    `hifmi_pay` DECIMAL(20,4) COMMENT '大病补充医疗保险基金支出',
    `hifob_pay` DECIMAL(20,4) COMMENT '大额医疗补助基金支出',
    `hifdm_pay` DECIMAL(20,4) COMMENT '伤残人员医疗保障基金支出',
    `maf_pay` DECIMAL(20,4) COMMENT '医疗救助基金支出',
    `othfund_pay` DECIMAL(20,4) COMMENT '其它基金支付',
    `psn_pay` DECIMAL(20,4) COMMENT '个人支付金额',
    `acct_pay` DECIMAL(20,4) COMMENT '个人账户支出',
    `cash_payamt` DECIMAL(20,4) COMMENT '现金支付金额',
    `ownpay_hosp_part` DECIMAL(20,4) COMMENT '自费中医院负担部分',
    `acct_mulaid_pay` DECIMAL(20,4) COMMENT '账户共济支付金额',
    `rid` VARCHAR(40) COMMENT '数据唯一记录号',
    `updt_time` DATETIME COMMENT '数据更新时间',
    `crter_id` VARCHAR(20) COMMENT '创建人编号',
    `crter_name` VARCHAR(50) COMMENT '创建人姓名',
    `crte_time` DATETIME COMMENT '数据创建时间',
    `crte_optins_no` VARCHAR(20) COMMENT '创建机构编号',
    `opter_id` VARCHAR(20) COMMENT '经办人编号',
    `opter_name` VARCHAR(50) COMMENT '经办人姓名',
    `opt_time` DATETIME COMMENT '经办时间',
    `optins_no` VARCHAR(20) COMMENT '经办机构编号',
    `poolarea_no` VARCHAR(6) COMMENT '统筹区编号',
    `exct_item_fund_payamt` DECIMAL(20,4) COMMENT '除外项目基金支付金额',
    `vali_flag` VARCHAR(3) COMMENT '有效标志 0无效；1有效',
    `clr_setl_fund_id` VARCHAR(50) COMMENT '清算结算编号 主键',
    `biz_date` DATETIME COMMENT '业务日期',
    `overlmt_selfpay` DECIMAL(20,4) COMMENT '超限价自费费用',
    `fund_pay_sumamt` DECIMAL(20,4) COMMENT '基金支付总额',
    `bydise_setl_payamt` DECIMAL(20,4) COMMENT '按病种结算支付金额',
    `ipt_days` DECIMAL(20,4) COMMENT '住院天数',
    `fee_type` VARCHAR(3) COMMENT '费用类型 1省内医疗费用 2稽核扣款费用 3跨省医疗费用 4结算补差',
    `insutype` VARCHAR(6) COMMENT '险种类型 险种类型(insutype)',
    `psn_type` VARCHAR(6) COMMENT '人员类别 人员类别(psn_type)',
    `clr_ym` VARCHAR(6) COMMENT '清算年月',
    `fund_payamt_610101` DECIMAL(20,4) COMMENT '医疗救助基金',
    `fund_payamt_99999724` DECIMAL(20,4) COMMENT '省级第二道扶贫补助',
    `setl_type` VARCHAR(6) COMMENT '结算类别 结算类别字典表',
    `fund_payamt_310101` DECIMAL(20,4) COMMENT '城镇职工基本医疗保险统筹基金',
    `fund_payamt_320201` DECIMAL(20,4) COMMENT '厅级干部补助基金',
    `fund_payamt_999719` DECIMAL(20,4) COMMENT '工会互助基金',
    `bus_supy_amt` DECIMAL(20,4) COMMENT '商保结算补差金额',
    `admdvs` VARCHAR(6) COMMENT '医保区划',
    `mdtrt_cnt` DECIMAL(20,4) COMMENT '就诊次数',
    `fund_payamt_31020401` DECIMAL(20,4) COMMENT '本地家庭共济账户支付',
    PRIMARY KEY (`subsys_codg`, `fixmedins_code`, `clr_setl_fund_id`, `biz_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='已结算基金信息汇总';

