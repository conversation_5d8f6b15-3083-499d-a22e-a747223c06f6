-- 质控执行脚本 - 自动生成 (SQL Server 2017版)
-- 生成时间: 2025-07-25 20:32:58

-- 删除可能存在的日志表和存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[quality_control_log]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[quality_control_log]
END;

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[execute_quality_control_queries]') AND type in (N'P', N'PC'))
BEGIN
    DROP PROCEDURE [dbo].[execute_quality_control_queries]
END;

-- 创建日志表
CREATE TABLE [dbo].[quality_control_log] (
    [id] INT IDENTITY(1,1) PRIMARY KEY,
    [rule_no] NVARCHAR(50),
    [tabname] NVARCHAR(100),
    [rule_desc] NVARCHAR(MAX),
    [dty_count] INT,
    [error_desc] NVARCHAR(MAX),
    [execution_timestamp] DATETIME DEFAULT GETDATE()
);

GO

-- 创建存储过程，用于执行质控查询
CREATE PROCEDURE [dbo].[execute_quality_control_queries]
AS
BEGIN
    -- 设置错误处理选项
    SET NOCOUNT ON;
    
    -- 声明变量
    DECLARE @v_tabname NVARCHAR(100);
    DECLARE @v_rule_no NVARCHAR(50);
    DECLARE @v_rule_desc NVARCHAR(MAX);
    DECLARE @v_dty_count INT;
    DECLARE @v_error_message NVARCHAR(4000);
    DECLARE @v_error_number INT;
    DECLARE @v_error_severity INT;
    DECLARE @v_error_state INT;
    DECLARE @v_error_line INT;
    DECLARE @v_error_proc NVARCHAR(200);
    

    -- 执行第 1 条质控规则-sqlserver2017
    SET @v_tabname = N'cis_lh_summary';
    SET @v_rule_no = N'YJ05202507162141';
    SET @v_rule_desc = N'查询 #1 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_1') IS NOT NULL
            DROP TABLE #temp_result_1;
            
        CREATE TABLE #temp_result_1 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_1
        select max(org_name) as org_name,max(uscid) as uscid,'cis_lh_summary' as tabname ,'YJ05202507162141' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from cis_lh_summary A where A.dscg_time >= '2020-01-01 00:00:00' group by FORMAT (A.dscg_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_1;
        
        -- 删除临时表
        DROP TABLE #temp_result_1;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 2 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_adm_rec';
    SET @v_rule_no = N'YJ05202507162142';
    SET @v_rule_desc = N'查询 #2 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_2') IS NOT NULL
            DROP TABLE #temp_result_2;
            
        CREATE TABLE #temp_result_2 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_2
        select max(org_name) as org_name,max(uscid) as uscid,'emr_adm_rec' as tabname ,'YJ05202507162142' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_adm_rec A where A.adm_time >= '2020-01-01 00:00:00' group by FORMAT (A.adm_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_2;
        
        -- 删除临时表
        DROP TABLE #temp_result_2;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 3 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_aft_anst';
    SET @v_rule_no = N'YJ05202507162143';
    SET @v_rule_desc = N'查询 #3 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_3') IS NOT NULL
            DROP TABLE #temp_result_3;
            
        CREATE TABLE #temp_result_3 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_3
        select max(org_name) as org_name,max(uscid) as uscid,'emr_aft_anst' as tabname ,'YJ05202507162143' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_aft_anst A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_3;
        
        -- 删除临时表
        DROP TABLE #temp_result_3;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 4 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_anst_informed';
    SET @v_rule_no = N'YJ05202507162144';
    SET @v_rule_desc = N'查询 #4 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_4') IS NOT NULL
            DROP TABLE #temp_result_4;
            
        CREATE TABLE #temp_result_4 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_4
        select max(org_name) as org_name,max(uscid) as uscid,'emr_anst_informed' as tabname ,'YJ05202507162144' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_anst_informed A where A.plan_oprt_time >= '2020-01-01 00:00:00' group by FORMAT (A.plan_oprt_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_4;
        
        -- 删除临时表
        DROP TABLE #temp_result_4;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 5 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_blood_informed';
    SET @v_rule_no = N'YJ05202507162145';
    SET @v_rule_desc = N'查询 #5 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_5') IS NOT NULL
            DROP TABLE #temp_result_5;
            
        CREATE TABLE #temp_result_5 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_5
        select max(org_name) as org_name,max(uscid) as uscid,'emr_blood_informed' as tabname ,'YJ05202507162145' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_blood_informed A where A.plan_transfuse_time >= '2020-01-01 00:00:00' group by FORMAT (A.plan_transfuse_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_5;
        
        -- 删除临时表
        DROP TABLE #temp_result_5;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 6 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_detail';
    SET @v_rule_no = N'YJ05202507162146';
    SET @v_rule_desc = N'查询 #6 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_6') IS NOT NULL
            DROP TABLE #temp_result_6;
            
        CREATE TABLE #temp_result_6 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_6
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_detail' as tabname ,'YJ05202507162146' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_consult_detail A where A.consult_time >= '2020-01-01 00:00:00' group by FORMAT (A.consult_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_6;
        
        -- 删除临时表
        DROP TABLE #temp_result_6;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 7 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_info';
    SET @v_rule_no = N'YJ05202507162147';
    SET @v_rule_desc = N'查询 #7 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_7') IS NOT NULL
            DROP TABLE #temp_result_7;
            
        CREATE TABLE #temp_result_7 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_7
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_info' as tabname ,'YJ05202507162147' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_consult_info A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_7;
        
        -- 删除临时表
        DROP TABLE #temp_result_7;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 8 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_daily_dis_course';
    SET @v_rule_no = N'YJ05202507162148';
    SET @v_rule_desc = N'查询 #8 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_8') IS NOT NULL
            DROP TABLE #temp_result_8;
            
        CREATE TABLE #temp_result_8 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_8
        select max(org_name) as org_name,max(uscid) as uscid,'emr_daily_dis_course' as tabname ,'YJ05202507162148' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_daily_dis_course A where A.sign_time >= '2020-01-01 00:00:00' group by FORMAT (A.sign_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_8;
        
        -- 删除临时表
        DROP TABLE #temp_result_8;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 9 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_case_discu';
    SET @v_rule_no = N'YJ05202507162149';
    SET @v_rule_desc = N'查询 #9 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_9') IS NOT NULL
            DROP TABLE #temp_result_9;
            
        CREATE TABLE #temp_result_9 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_9
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_case_discu' as tabname ,'YJ05202507162149' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_death_case_discu A where A.discu_time >= '2020-01-01 00:00:00' group by FORMAT (A.discu_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_9;
        
        -- 删除临时表
        DROP TABLE #temp_result_9;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 10 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_record';
    SET @v_rule_no = N'YJ05202507162150';
    SET @v_rule_desc = N'查询 #10 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_10') IS NOT NULL
            DROP TABLE #temp_result_10;
            
        CREATE TABLE #temp_result_10 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_10
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_record' as tabname ,'YJ05202507162150' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_death_record A where A.death_time >= '2020-01-01 00:00:00' group by FORMAT (A.death_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_10;
        
        -- 删除临时表
        DROP TABLE #temp_result_10;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 11 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_detail';
    SET @v_rule_no = N'YJ05202507162151';
    SET @v_rule_desc = N'查询 #11 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_11') IS NOT NULL
            DROP TABLE #temp_result_11;
            
        CREATE TABLE #temp_result_11 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_11
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_detail' as tabname ,'YJ05202507162151' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_doc_detail A where A.file_create_time >= '2020-01-01 00:00:00' group by FORMAT (A.file_create_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_11;
        
        -- 删除临时表
        DROP TABLE #temp_result_11;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 12 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_rec';
    SET @v_rule_no = N'YJ05202507162152';
    SET @v_rule_desc = N'查询 #12 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_12') IS NOT NULL
            DROP TABLE #temp_result_12;
            
        CREATE TABLE #temp_result_12 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_12
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_rec' as tabname ,'YJ05202507162152' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_doc_rec A where A.doc_create_time >= '2020-01-01 00:00:00' group by FORMAT (A.doc_create_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_12;
        
        -- 删除临时表
        DROP TABLE #temp_result_12;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 13 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_first_postop_course';
    SET @v_rule_no = N'YJ05202507162154';
    SET @v_rule_desc = N'查询 #13 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_13') IS NOT NULL
            DROP TABLE #temp_result_13;
            
        CREATE TABLE #temp_result_13 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_13
        select max(org_name) as org_name,max(uscid) as uscid,'emr_first_postop_course' as tabname ,'YJ05202507162154' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_first_postop_course A where A.oprn_time >= '2020-01-01 00:00:00' group by FORMAT (A.oprn_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_13;
        
        -- 删除临时表
        DROP TABLE #temp_result_13;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 14 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_hard_case_discu';
    SET @v_rule_no = N'YJ05202507162155';
    SET @v_rule_desc = N'查询 #14 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_14') IS NOT NULL
            DROP TABLE #temp_result_14;
            
        CREATE TABLE #temp_result_14 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_14
        select max(org_name) as org_name,max(uscid) as uscid,'emr_hard_case_discu' as tabname ,'YJ05202507162155' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_hard_case_discu A where A.discu_time >= '2020-01-01 00:00:00' group by FORMAT (A.discu_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_14;
        
        -- 删除临时表
        DROP TABLE #temp_result_14;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 15 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_informed';
    SET @v_rule_no = N'YJ05202507162156';
    SET @v_rule_desc = N'查询 #15 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_15') IS NOT NULL
            DROP TABLE #temp_result_15;
            
        CREATE TABLE #temp_result_15 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_15
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_informed' as tabname ,'YJ05202507162156' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_heavy_informed A where A.dying_inform_time >= '2020-01-01 00:00:00' group by FORMAT (A.dying_inform_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_15;
        
        -- 删除临时表
        DROP TABLE #temp_result_15;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 16 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_nurse';
    SET @v_rule_no = N'YJ05202507162157';
    SET @v_rule_desc = N'查询 #16 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_16') IS NOT NULL
            DROP TABLE #temp_result_16;
            
        CREATE TABLE #temp_result_16 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_16
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_nurse' as tabname ,'YJ05202507162157' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_heavy_nurse A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_16;
        
        -- 删除临时表
        DROP TABLE #temp_result_16;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 17 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_assess';
    SET @v_rule_no = N'YJ05202507162158';
    SET @v_rule_desc = N'查询 #17 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_17') IS NOT NULL
            DROP TABLE #temp_result_17;
            
        CREATE TABLE #temp_result_17 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_17
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_assess' as tabname ,'YJ05202507162158' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_inhosp_assess A where A.eval_time >= '2020-01-01 00:00:00' group by FORMAT (A.eval_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_17;
        
        -- 删除临时表
        DROP TABLE #temp_result_17;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 18 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_die_in24h';
    SET @v_rule_no = N'YJ05202507162159';
    SET @v_rule_desc = N'查询 #18 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_18') IS NOT NULL
            DROP TABLE #temp_result_18;
            
        CREATE TABLE #temp_result_18 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_18
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_die_in24h' as tabname ,'YJ05202507162159' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_inhosp_die_in24h A where A.adm_time >= '2020-01-01 00:00:00' group by FORMAT (A.adm_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_18;
        
        -- 删除临时表
        DROP TABLE #temp_result_18;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 19 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec';
    SET @v_rule_no = N'YJ05202507162160';
    SET @v_rule_desc = N'查询 #19 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_19') IS NOT NULL
            DROP TABLE #temp_result_19;
            
        CREATE TABLE #temp_result_19 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_19
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec' as tabname ,'YJ05202507162160' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_inout_rec A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_19;
        
        -- 删除临时表
        DROP TABLE #temp_result_19;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 20 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec_in24h';
    SET @v_rule_no = N'YJ05202507162161';
    SET @v_rule_desc = N'查询 #20 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_20') IS NOT NULL
            DROP TABLE #temp_result_20;
            
        CREATE TABLE #temp_result_20 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_20
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec_in24h' as tabname ,'YJ05202507162161' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_inout_rec_in24h A where A.adm_time >= '2020-01-01 00:00:00' group by FORMAT (A.adm_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_20;
        
        -- 删除临时表
        DROP TABLE #temp_result_20;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 21 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_usedrug';
    SET @v_rule_no = N'YJ05202507162162';
    SET @v_rule_desc = N'查询 #21 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_21') IS NOT NULL
            DROP TABLE #temp_result_21;
            
        CREATE TABLE #temp_result_21 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_21
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_usedrug' as tabname ,'YJ05202507162162' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_inout_usedrug A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_21;
        
        -- 删除临时表
        DROP TABLE #temp_result_21;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 22 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_labor';
    SET @v_rule_no = N'YJ05202507162163';
    SET @v_rule_desc = N'查询 #22 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_22') IS NOT NULL
            DROP TABLE #temp_result_22;
            
        CREATE TABLE #temp_result_22 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_22
        select max(org_name) as org_name,max(uscid) as uscid,'emr_labor' as tabname ,'YJ05202507162163' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_labor A where A.labor_rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.labor_rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_22;
        
        -- 删除临时表
        DROP TABLE #temp_result_22;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 23 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse';
    SET @v_rule_no = N'YJ05202507162164';
    SET @v_rule_desc = N'查询 #23 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_23') IS NOT NULL
            DROP TABLE #temp_result_23;
            
        CREATE TABLE #temp_result_23 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_23
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse' as tabname ,'YJ05202507162164' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_nurse A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_23;
        
        -- 删除临时表
        DROP TABLE #temp_result_23;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 24 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse_plan';
    SET @v_rule_no = N'YJ05202507162165';
    SET @v_rule_desc = N'查询 #24 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_24') IS NOT NULL
            DROP TABLE #temp_result_24;
            
        CREATE TABLE #temp_result_24 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_24
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse_plan' as tabname ,'YJ05202507162165' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_nurse_plan A where A.sign_time >= '2020-01-01 00:00:00' group by FORMAT (A.sign_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_24;
        
        -- 删除临时表
        DROP TABLE #temp_result_24;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 25 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observmedi';
    SET @v_rule_no = N'YJ05202507162166';
    SET @v_rule_desc = N'查询 #25 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_25') IS NOT NULL
            DROP TABLE #temp_result_25;
            
        CREATE TABLE #temp_result_25 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_25
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observmedi' as tabname ,'YJ05202507162166' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_observmedi A where A.observe_room_time >= '2020-01-01 00:00:00' group by FORMAT (A.observe_room_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_25;
        
        -- 删除临时表
        DROP TABLE #temp_result_25;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 26 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observoprn';
    SET @v_rule_no = N'YJ05202507162167';
    SET @v_rule_desc = N'查询 #26 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_26') IS NOT NULL
            DROP TABLE #temp_result_26;
            
        CREATE TABLE #temp_result_26 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_26
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observoprn' as tabname ,'YJ05202507162167' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_observoprn A where A.oprn_oprt_time >= '2020-01-01 00:00:00' group by FORMAT (A.oprn_oprt_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_26;
        
        -- 删除临时表
        DROP TABLE #temp_result_26;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 27 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_informed';
    SET @v_rule_no = N'YJ05202507162168';
    SET @v_rule_desc = N'查询 #27 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_27') IS NOT NULL
            DROP TABLE #temp_result_27;
            
        CREATE TABLE #temp_result_27 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_27
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_informed' as tabname ,'YJ05202507162168' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_opr_informed A where A.plan_oprt_time >= '2020-01-01 00:00:00' group by FORMAT (A.plan_oprt_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_27;
        
        -- 删除临时表
        DROP TABLE #temp_result_27;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 28 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_nurse';
    SET @v_rule_no = N'YJ05202507162169';
    SET @v_rule_desc = N'查询 #28 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_28') IS NOT NULL
            DROP TABLE #temp_result_28;
            
        CREATE TABLE #temp_result_28 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_28
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_nurse' as tabname ,'YJ05202507162169' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_opr_nurse A where A.oprn_begin_time >= '2020-01-01 00:00:00' group by FORMAT (A.oprn_begin_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_28;
        
        -- 删除临时表
        DROP TABLE #temp_result_28;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 29 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_rec';
    SET @v_rule_no = N'YJ05202507162170';
    SET @v_rule_desc = N'查询 #29 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_29') IS NOT NULL
            DROP TABLE #temp_result_29;
            
        CREATE TABLE #temp_result_29 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_29
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_rec' as tabname ,'YJ05202507162170' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_opr_rec A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_29;
        
        -- 删除临时表
        DROP TABLE #temp_result_29;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 30 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_oth_informed';
    SET @v_rule_no = N'YJ05202507162171';
    SET @v_rule_desc = N'查询 #30 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_30') IS NOT NULL
            DROP TABLE #temp_result_30;
            
        CREATE TABLE #temp_result_30 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_30
        select max(org_name) as org_name,max(uscid) as uscid,'emr_oth_informed' as tabname ,'YJ05202507162171' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_oth_informed A where A.doc_sign_time >= '2020-01-01 00:00:00' group by FORMAT (A.doc_sign_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_30;
        
        -- 删除临时表
        DROP TABLE #temp_result_30;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 31 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_otpmedi';
    SET @v_rule_no = N'YJ05202507162172';
    SET @v_rule_desc = N'查询 #31 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_31') IS NOT NULL
            DROP TABLE #temp_result_31;
            
        CREATE TABLE #temp_result_31 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_31
        select max(org_name) as org_name,max(uscid) as uscid,'emr_otpmedi' as tabname ,'YJ05202507162172' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_otpmedi A where A.mdtrt_time >= '2020-01-01 00:00:00' group by FORMAT (A.mdtrt_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_31;
        
        -- 删除临时表
        DROP TABLE #temp_result_31;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 32 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp';
    SET @v_rule_no = N'YJ05202507162173';
    SET @v_rule_desc = N'查询 #32 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_32') IS NOT NULL
            DROP TABLE #temp_result_32;
            
        CREATE TABLE #temp_result_32 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_32
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp' as tabname ,'YJ05202507162173' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_outhosp A where A.adm_time >= '2020-01-01 00:00:00' group by FORMAT (A.adm_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_32;
        
        -- 删除临时表
        DROP TABLE #temp_result_32;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 33 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp_assess';
    SET @v_rule_no = N'YJ05202507162174';
    SET @v_rule_desc = N'查询 #33 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_33') IS NOT NULL
            DROP TABLE #temp_result_33;
            
        CREATE TABLE #temp_result_33 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_33
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp_assess' as tabname ,'YJ05202507162174' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_outhosp_assess A where A.dscg_time >= '2020-01-01 00:00:00' group by FORMAT (A.dscg_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_33;
        
        -- 删除临时表
        DROP TABLE #temp_result_33;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 34 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_periodic_sum';
    SET @v_rule_no = N'YJ05202507162175';
    SET @v_rule_desc = N'查询 #34 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_34') IS NOT NULL
            DROP TABLE #temp_result_34;
            
        CREATE TABLE #temp_result_34 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_34
        select max(org_name) as org_name,max(uscid) as uscid,'emr_periodic_sum' as tabname ,'YJ05202507162175' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_periodic_sum A where A.sum_time >= '2020-01-01 00:00:00' group by FORMAT (A.sum_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_34;
        
        -- 删除临时表
        DROP TABLE #temp_result_34;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 35 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pre_anst';
    SET @v_rule_no = N'YJ05202507162176';
    SET @v_rule_desc = N'查询 #35 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_35') IS NOT NULL
            DROP TABLE #temp_result_35;
            
        CREATE TABLE #temp_result_35 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_35
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pre_anst' as tabname ,'YJ05202507162176' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_pre_anst A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_35;
        
        -- 删除临时表
        DROP TABLE #temp_result_35;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 36 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_predelivery';
    SET @v_rule_no = N'YJ05202507162177';
    SET @v_rule_desc = N'查询 #36 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_36') IS NOT NULL
            DROP TABLE #temp_result_36;
            
        CREATE TABLE #temp_result_36 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_36
        select max(org_name) as org_name,max(uscid) as uscid,'emr_predelivery' as tabname ,'YJ05202507162177' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_predelivery A where A.expectant_time >= '2020-01-01 00:00:00' group by FORMAT (A.expectant_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_36;
        
        -- 删除临时表
        DROP TABLE #temp_result_36;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 37 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy';
    SET @v_rule_no = N'YJ05202507162178';
    SET @v_rule_desc = N'查询 #37 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_37') IS NOT NULL
            DROP TABLE #temp_result_37;
            
        CREATE TABLE #temp_result_37 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_37
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy' as tabname ,'YJ05202507162178' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_pregnancy A where A.oprn_end_time >= '2020-01-01 00:00:00' group by FORMAT (A.oprn_end_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_37;
        
        -- 删除临时表
        DROP TABLE #temp_result_37;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 38 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_nwb';
    SET @v_rule_no = N'YJ05202507162179';
    SET @v_rule_desc = N'查询 #38 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_38') IS NOT NULL
            DROP TABLE #temp_result_38;
            
        CREATE TABLE #temp_result_38 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_38
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_nwb' as tabname ,'YJ05202507162179' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_pregnancy_nwb A where A.nwb_brdy_time >= '2020-01-01 00:00:00' group by FORMAT (A.nwb_brdy_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_38;
        
        -- 删除临时表
        DROP TABLE #temp_result_38;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 39 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_observ';
    SET @v_rule_no = N'YJ05202507162180';
    SET @v_rule_desc = N'查询 #39 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_39') IS NOT NULL
            DROP TABLE #temp_result_39;
            
        CREATE TABLE #temp_result_39 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_39
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_observ' as tabname ,'YJ05202507162180' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_pregnancy_observ A where A.postpar_observ_time >= '2020-01-01 00:00:00' group by FORMAT (A.postpar_observ_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_39;
        
        -- 删除临时表
        DROP TABLE #temp_result_39;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 40 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_discu';
    SET @v_rule_no = N'YJ05202507162181';
    SET @v_rule_desc = N'查询 #40 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_40') IS NOT NULL
            DROP TABLE #temp_result_40;
            
        CREATE TABLE #temp_result_40 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_40
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_discu' as tabname ,'YJ05202507162181' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_preop_discu A where A.discu_time >= '2020-01-01 00:00:00' group by FORMAT (A.discu_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_40;
        
        -- 删除临时表
        DROP TABLE #temp_result_40;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 41 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_sum';
    SET @v_rule_no = N'YJ05202507162182';
    SET @v_rule_desc = N'查询 #41 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_41') IS NOT NULL
            DROP TABLE #temp_result_41;
            
        CREATE TABLE #temp_result_41 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_41
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_sum' as tabname ,'YJ05202507162182' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_preop_sum A where A.sum_time >= '2020-01-01 00:00:00' group by FORMAT (A.sum_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_41;
        
        -- 删除临时表
        DROP TABLE #temp_result_41;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 42 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_resc_rec';
    SET @v_rule_no = N'YJ05202507162183';
    SET @v_rule_desc = N'查询 #42 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_42') IS NOT NULL
            DROP TABLE #temp_result_42;
            
        CREATE TABLE #temp_result_42 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_42
        select max(org_name) as org_name,max(uscid) as uscid,'emr_resc_rec' as tabname ,'YJ05202507162183' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_resc_rec A where A.resc_endtime >= '2020-01-01 00:00:00' group by FORMAT (A.resc_endtime,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_42;
        
        -- 删除临时表
        DROP TABLE #temp_result_42;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 43 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rescue';
    SET @v_rule_no = N'YJ05202507162184';
    SET @v_rule_desc = N'查询 #43 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_43') IS NOT NULL
            DROP TABLE #temp_result_43;
            
        CREATE TABLE #temp_result_43 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_43
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rescue' as tabname ,'YJ05202507162184' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_rescue A where A.sign_time >= '2020-01-01 00:00:00' group by FORMAT (A.sign_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_43;
        
        -- 删除临时表
        DROP TABLE #temp_result_43;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 44 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rreat_drug';
    SET @v_rule_no = N'YJ05202507162185';
    SET @v_rule_desc = N'查询 #44 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_44') IS NOT NULL
            DROP TABLE #temp_result_44;
            
        CREATE TABLE #temp_result_44 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_44
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rreat_drug' as tabname ,'YJ05202507162185' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_rreat_drug A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_44;
        
        -- 删除临时表
        DROP TABLE #temp_result_44;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 45 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_shift_change';
    SET @v_rule_no = N'YJ05202507162186';
    SET @v_rule_desc = N'查询 #45 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_45') IS NOT NULL
            DROP TABLE #temp_result_45;
            
        CREATE TABLE #temp_result_45 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_45
        select max(org_name) as org_name,max(uscid) as uscid,'emr_shift_change' as tabname ,'YJ05202507162186' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_shift_change A where A.shift_time >= '2020-01-01 00:00:00' group by FORMAT (A.shift_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_45;
        
        -- 删除临时表
        DROP TABLE #temp_result_45;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 46 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_spe_informed';
    SET @v_rule_no = N'YJ05202507162187';
    SET @v_rule_desc = N'查询 #46 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_46') IS NOT NULL
            DROP TABLE #temp_result_46;
            
        CREATE TABLE #temp_result_46 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_46
        select max(org_name) as org_name,max(uscid) as uscid,'emr_spe_informed' as tabname ,'YJ05202507162187' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_spe_informed A where A.doc_sign_time >= '2020-01-01 00:00:00' group by FORMAT (A.doc_sign_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_46;
        
        -- 删除临时表
        DROP TABLE #temp_result_46;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 47 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_super_doct_check';
    SET @v_rule_no = N'YJ05202507162188';
    SET @v_rule_desc = N'查询 #47 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_47') IS NOT NULL
            DROP TABLE #temp_result_47;
            
        CREATE TABLE #temp_result_47 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_47
        select max(org_name) as org_name,max(uscid) as uscid,'emr_super_doct_check' as tabname ,'YJ05202507162188' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_super_doct_check A where A.check_room_time >= '2020-01-01 00:00:00' group by FORMAT (A.check_room_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_47;
        
        -- 删除临时表
        DROP TABLE #temp_result_47;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 48 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_sympt_rec';
    SET @v_rule_no = N'YJ05202507162189';
    SET @v_rule_desc = N'查询 #48 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_48') IS NOT NULL
            DROP TABLE #temp_result_48;
            
        CREATE TABLE #temp_result_48 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_48
        select max(org_name) as org_name,max(uscid) as uscid,'emr_sympt_rec' as tabname ,'YJ05202507162189' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_sympt_rec A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_48;
        
        -- 删除临时表
        DROP TABLE #temp_result_48;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 49 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_treat_rec';
    SET @v_rule_no = N'YJ05202507162191';
    SET @v_rule_desc = N'查询 #49 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_49') IS NOT NULL
            DROP TABLE #temp_result_49;
            
        CREATE TABLE #temp_result_49 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_49
        select max(org_name) as org_name,max(uscid) as uscid,'emr_treat_rec' as tabname ,'YJ05202507162191' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_treat_rec A where A.rec_time >= '2020-01-01 00:00:00' group by FORMAT (A.rec_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_49;
        
        -- 删除临时表
        DROP TABLE #temp_result_49;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 50 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver';
    SET @v_rule_no = N'YJ05202507162192';
    SET @v_rule_desc = N'查询 #50 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_50') IS NOT NULL
            DROP TABLE #temp_result_50;
            
        CREATE TABLE #temp_result_50 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_50
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver' as tabname ,'YJ05202507162192' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_vagina_deliver A where A.expect_deliver_date >= '2020-01-01 00:00:00' group by FORMAT (A.expect_deliver_date,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_50;
        
        -- 删除临时表
        DROP TABLE #temp_result_50;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 51 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_nwb';
    SET @v_rule_no = N'YJ05202507162193';
    SET @v_rule_desc = N'查询 #51 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_51') IS NOT NULL
            DROP TABLE #temp_result_51;
            
        CREATE TABLE #temp_result_51 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_51
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_nwb' as tabname ,'YJ05202507162193' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_vagina_deliver_nwb A where A.nwb_brdy_time >= '2020-01-01 00:00:00' group by FORMAT (A.nwb_brdy_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_51;
        
        -- 删除临时表
        DROP TABLE #temp_result_51;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 52 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_observ';
    SET @v_rule_no = N'YJ05202507162194';
    SET @v_rule_desc = N'查询 #52 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_52') IS NOT NULL
            DROP TABLE #temp_result_52;
            
        CREATE TABLE #temp_result_52 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_52
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_observ' as tabname ,'YJ05202507162194' as rule_no,'数据不满足5年和月连续性(缺失月数)' as rule_dscr, DATEDIFF(month,'2020-01-01 00:00:00',GETDATE()) - count(1) as biz_info from (select max(org_name) as org_name,max(uscid) as uscid from emr_vagina_deliver_observ A where A.postpar_observ_time >= '2020-01-01 00:00:00' group by FORMAT (A.postpar_observ_time,'Y%m%')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_52;
        
        -- 删除临时表
        DROP TABLE #temp_result_52;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 53 条质控规则-sqlserver2017
    SET @v_tabname = N'cis_lh_summary';
    SET @v_rule_no = N'YJ05202507162206';
    SET @v_rule_desc = N'查询 #53 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_53') IS NOT NULL
            DROP TABLE #temp_result_53;
            
        CREATE TABLE #temp_result_53 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_53
        select max(org_name) as org_name,max(uscid) as uscid,'cis_lh_summary' as tabname ,'YJ05202507162206' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from cis_lh_summary A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_53;
        
        -- 删除临时表
        DROP TABLE #temp_result_53;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 54 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_adm_rec';
    SET @v_rule_no = N'YJ05202507162207';
    SET @v_rule_desc = N'查询 #54 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_54') IS NOT NULL
            DROP TABLE #temp_result_54;
            
        CREATE TABLE #temp_result_54 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_54
        select max(org_name) as org_name,max(uscid) as uscid,'emr_adm_rec' as tabname ,'YJ05202507162207' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_adm_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_54;
        
        -- 删除临时表
        DROP TABLE #temp_result_54;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 55 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_aft_anst';
    SET @v_rule_no = N'YJ05202507162208';
    SET @v_rule_desc = N'查询 #55 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_55') IS NOT NULL
            DROP TABLE #temp_result_55;
            
        CREATE TABLE #temp_result_55 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_55
        select max(org_name) as org_name,max(uscid) as uscid,'emr_aft_anst' as tabname ,'YJ05202507162208' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_aft_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_55;
        
        -- 删除临时表
        DROP TABLE #temp_result_55;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 56 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_anst_informed';
    SET @v_rule_no = N'YJ05202507162209';
    SET @v_rule_desc = N'查询 #56 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_56') IS NOT NULL
            DROP TABLE #temp_result_56;
            
        CREATE TABLE #temp_result_56 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_56
        select max(org_name) as org_name,max(uscid) as uscid,'emr_anst_informed' as tabname ,'YJ05202507162209' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_anst_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_56;
        
        -- 删除临时表
        DROP TABLE #temp_result_56;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 57 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_blood_informed';
    SET @v_rule_no = N'YJ05202507162210';
    SET @v_rule_desc = N'查询 #57 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_57') IS NOT NULL
            DROP TABLE #temp_result_57;
            
        CREATE TABLE #temp_result_57 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_57
        select max(org_name) as org_name,max(uscid) as uscid,'emr_blood_informed' as tabname ,'YJ05202507162210' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_blood_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_57;
        
        -- 删除临时表
        DROP TABLE #temp_result_57;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 58 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_detail';
    SET @v_rule_no = N'YJ05202507162211';
    SET @v_rule_desc = N'查询 #58 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_58') IS NOT NULL
            DROP TABLE #temp_result_58;
            
        CREATE TABLE #temp_result_58 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_58
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_detail' as tabname ,'YJ05202507162211' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_consult_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_58;
        
        -- 删除临时表
        DROP TABLE #temp_result_58;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 59 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_info';
    SET @v_rule_no = N'YJ05202507162212';
    SET @v_rule_desc = N'查询 #59 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_59') IS NOT NULL
            DROP TABLE #temp_result_59;
            
        CREATE TABLE #temp_result_59 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_59
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_info' as tabname ,'YJ05202507162212' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_consult_info A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_59;
        
        -- 删除临时表
        DROP TABLE #temp_result_59;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 60 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_daily_dis_course';
    SET @v_rule_no = N'YJ05202507162213';
    SET @v_rule_desc = N'查询 #60 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_60') IS NOT NULL
            DROP TABLE #temp_result_60;
            
        CREATE TABLE #temp_result_60 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_60
        select max(org_name) as org_name,max(uscid) as uscid,'emr_daily_dis_course' as tabname ,'YJ05202507162213' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_daily_dis_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_60;
        
        -- 删除临时表
        DROP TABLE #temp_result_60;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 61 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_case_discu';
    SET @v_rule_no = N'YJ05202507162214';
    SET @v_rule_desc = N'查询 #61 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_61') IS NOT NULL
            DROP TABLE #temp_result_61;
            
        CREATE TABLE #temp_result_61 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_61
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_case_discu' as tabname ,'YJ05202507162214' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_death_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_61;
        
        -- 删除临时表
        DROP TABLE #temp_result_61;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 62 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_record';
    SET @v_rule_no = N'YJ05202507162215';
    SET @v_rule_desc = N'查询 #62 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_62') IS NOT NULL
            DROP TABLE #temp_result_62;
            
        CREATE TABLE #temp_result_62 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_62
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_record' as tabname ,'YJ05202507162215' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_death_record A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_62;
        
        -- 删除临时表
        DROP TABLE #temp_result_62;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 63 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_detail';
    SET @v_rule_no = N'YJ05202507162216';
    SET @v_rule_desc = N'查询 #63 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_63') IS NOT NULL
            DROP TABLE #temp_result_63;
            
        CREATE TABLE #temp_result_63 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_63
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_detail' as tabname ,'YJ05202507162216' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_doc_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_63;
        
        -- 删除临时表
        DROP TABLE #temp_result_63;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 64 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_rec';
    SET @v_rule_no = N'YJ05202507162217';
    SET @v_rule_desc = N'查询 #64 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_64') IS NOT NULL
            DROP TABLE #temp_result_64;
            
        CREATE TABLE #temp_result_64 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_64
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_rec' as tabname ,'YJ05202507162217' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_doc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_64;
        
        -- 删除临时表
        DROP TABLE #temp_result_64;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 65 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_first_dis_course';
    SET @v_rule_no = N'YJ05202507162218';
    SET @v_rule_desc = N'查询 #65 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_65') IS NOT NULL
            DROP TABLE #temp_result_65;
            
        CREATE TABLE #temp_result_65 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_65
        select max(org_name) as org_name,max(uscid) as uscid,'emr_first_dis_course' as tabname ,'YJ05202507162218' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_first_dis_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_65;
        
        -- 删除临时表
        DROP TABLE #temp_result_65;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 66 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_first_postop_course';
    SET @v_rule_no = N'YJ05202507162219';
    SET @v_rule_desc = N'查询 #66 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_66') IS NOT NULL
            DROP TABLE #temp_result_66;
            
        CREATE TABLE #temp_result_66 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_66
        select max(org_name) as org_name,max(uscid) as uscid,'emr_first_postop_course' as tabname ,'YJ05202507162219' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_first_postop_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_66;
        
        -- 删除临时表
        DROP TABLE #temp_result_66;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 67 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_hard_case_discu';
    SET @v_rule_no = N'YJ05202507162220';
    SET @v_rule_desc = N'查询 #67 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_67') IS NOT NULL
            DROP TABLE #temp_result_67;
            
        CREATE TABLE #temp_result_67 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_67
        select max(org_name) as org_name,max(uscid) as uscid,'emr_hard_case_discu' as tabname ,'YJ05202507162220' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_hard_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_67;
        
        -- 删除临时表
        DROP TABLE #temp_result_67;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 68 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_informed';
    SET @v_rule_no = N'YJ05202507162221';
    SET @v_rule_desc = N'查询 #68 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_68') IS NOT NULL
            DROP TABLE #temp_result_68;
            
        CREATE TABLE #temp_result_68 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_68
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_informed' as tabname ,'YJ05202507162221' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_heavy_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_68;
        
        -- 删除临时表
        DROP TABLE #temp_result_68;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 69 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_nurse';
    SET @v_rule_no = N'YJ05202507162222';
    SET @v_rule_desc = N'查询 #69 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_69') IS NOT NULL
            DROP TABLE #temp_result_69;
            
        CREATE TABLE #temp_result_69 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_69
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_nurse' as tabname ,'YJ05202507162222' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_heavy_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_69;
        
        -- 删除临时表
        DROP TABLE #temp_result_69;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 70 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_assess';
    SET @v_rule_no = N'YJ05202507162223';
    SET @v_rule_desc = N'查询 #70 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_70') IS NOT NULL
            DROP TABLE #temp_result_70;
            
        CREATE TABLE #temp_result_70 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_70
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_assess' as tabname ,'YJ05202507162223' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_inhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_70;
        
        -- 删除临时表
        DROP TABLE #temp_result_70;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 71 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_die_in24h';
    SET @v_rule_no = N'YJ05202507162224';
    SET @v_rule_desc = N'查询 #71 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_71') IS NOT NULL
            DROP TABLE #temp_result_71;
            
        CREATE TABLE #temp_result_71 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_71
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_die_in24h' as tabname ,'YJ05202507162224' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_inhosp_die_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_71;
        
        -- 删除临时表
        DROP TABLE #temp_result_71;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 72 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec';
    SET @v_rule_no = N'YJ05202507162225';
    SET @v_rule_desc = N'查询 #72 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_72') IS NOT NULL
            DROP TABLE #temp_result_72;
            
        CREATE TABLE #temp_result_72 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_72
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec' as tabname ,'YJ05202507162225' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_inout_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_72;
        
        -- 删除临时表
        DROP TABLE #temp_result_72;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 73 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec_in24h';
    SET @v_rule_no = N'YJ05202507162226';
    SET @v_rule_desc = N'查询 #73 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_73') IS NOT NULL
            DROP TABLE #temp_result_73;
            
        CREATE TABLE #temp_result_73 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_73
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec_in24h' as tabname ,'YJ05202507162226' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_inout_rec_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_73;
        
        -- 删除临时表
        DROP TABLE #temp_result_73;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 74 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_usedrug';
    SET @v_rule_no = N'YJ05202507162227';
    SET @v_rule_desc = N'查询 #74 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_74') IS NOT NULL
            DROP TABLE #temp_result_74;
            
        CREATE TABLE #temp_result_74 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_74
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_usedrug' as tabname ,'YJ05202507162227' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_inout_usedrug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_74;
        
        -- 删除临时表
        DROP TABLE #temp_result_74;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 75 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_labor';
    SET @v_rule_no = N'YJ05202507162228';
    SET @v_rule_desc = N'查询 #75 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_75') IS NOT NULL
            DROP TABLE #temp_result_75;
            
        CREATE TABLE #temp_result_75 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_75
        select max(org_name) as org_name,max(uscid) as uscid,'emr_labor' as tabname ,'YJ05202507162228' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_labor A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_75;
        
        -- 删除临时表
        DROP TABLE #temp_result_75;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 76 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse';
    SET @v_rule_no = N'YJ05202507162229';
    SET @v_rule_desc = N'查询 #76 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_76') IS NOT NULL
            DROP TABLE #temp_result_76;
            
        CREATE TABLE #temp_result_76 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_76
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse' as tabname ,'YJ05202507162229' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_76;
        
        -- 删除临时表
        DROP TABLE #temp_result_76;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 77 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse_plan';
    SET @v_rule_no = N'YJ05202507162230';
    SET @v_rule_desc = N'查询 #77 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_77') IS NOT NULL
            DROP TABLE #temp_result_77;
            
        CREATE TABLE #temp_result_77 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_77
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse_plan' as tabname ,'YJ05202507162230' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_nurse_plan A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_77;
        
        -- 删除临时表
        DROP TABLE #temp_result_77;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 78 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observmedi';
    SET @v_rule_no = N'YJ05202507162231';
    SET @v_rule_desc = N'查询 #78 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_78') IS NOT NULL
            DROP TABLE #temp_result_78;
            
        CREATE TABLE #temp_result_78 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_78
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observmedi' as tabname ,'YJ05202507162231' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_observmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_78;
        
        -- 删除临时表
        DROP TABLE #temp_result_78;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 79 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observoprn';
    SET @v_rule_no = N'YJ05202507162232';
    SET @v_rule_desc = N'查询 #79 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_79') IS NOT NULL
            DROP TABLE #temp_result_79;
            
        CREATE TABLE #temp_result_79 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_79
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observoprn' as tabname ,'YJ05202507162232' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_observoprn A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_79;
        
        -- 删除临时表
        DROP TABLE #temp_result_79;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 80 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_informed';
    SET @v_rule_no = N'YJ05202507162233';
    SET @v_rule_desc = N'查询 #80 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_80') IS NOT NULL
            DROP TABLE #temp_result_80;
            
        CREATE TABLE #temp_result_80 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_80
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_informed' as tabname ,'YJ05202507162233' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_opr_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_80;
        
        -- 删除临时表
        DROP TABLE #temp_result_80;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 81 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_nurse';
    SET @v_rule_no = N'YJ05202507162234';
    SET @v_rule_desc = N'查询 #81 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_81') IS NOT NULL
            DROP TABLE #temp_result_81;
            
        CREATE TABLE #temp_result_81 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_81
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_nurse' as tabname ,'YJ05202507162234' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_opr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_81;
        
        -- 删除临时表
        DROP TABLE #temp_result_81;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 82 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_rec';
    SET @v_rule_no = N'YJ05202507162235';
    SET @v_rule_desc = N'查询 #82 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_82') IS NOT NULL
            DROP TABLE #temp_result_82;
            
        CREATE TABLE #temp_result_82 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_82
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_rec' as tabname ,'YJ05202507162235' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_opr_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_82;
        
        -- 删除临时表
        DROP TABLE #temp_result_82;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 83 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_oth_informed';
    SET @v_rule_no = N'YJ05202507162236';
    SET @v_rule_desc = N'查询 #83 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_83') IS NOT NULL
            DROP TABLE #temp_result_83;
            
        CREATE TABLE #temp_result_83 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_83
        select max(org_name) as org_name,max(uscid) as uscid,'emr_oth_informed' as tabname ,'YJ05202507162236' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_oth_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_83;
        
        -- 删除临时表
        DROP TABLE #temp_result_83;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 84 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_otpmedi';
    SET @v_rule_no = N'YJ05202507162237';
    SET @v_rule_desc = N'查询 #84 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_84') IS NOT NULL
            DROP TABLE #temp_result_84;
            
        CREATE TABLE #temp_result_84 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_84
        select max(org_name) as org_name,max(uscid) as uscid,'emr_otpmedi' as tabname ,'YJ05202507162237' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_otpmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_84;
        
        -- 删除临时表
        DROP TABLE #temp_result_84;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 85 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp';
    SET @v_rule_no = N'YJ05202507162238';
    SET @v_rule_desc = N'查询 #85 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_85') IS NOT NULL
            DROP TABLE #temp_result_85;
            
        CREATE TABLE #temp_result_85 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_85
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp' as tabname ,'YJ05202507162238' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_outhosp A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_85;
        
        -- 删除临时表
        DROP TABLE #temp_result_85;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 86 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp_assess';
    SET @v_rule_no = N'YJ05202507162239';
    SET @v_rule_desc = N'查询 #86 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_86') IS NOT NULL
            DROP TABLE #temp_result_86;
            
        CREATE TABLE #temp_result_86 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_86
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp_assess' as tabname ,'YJ05202507162239' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_outhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_86;
        
        -- 删除临时表
        DROP TABLE #temp_result_86;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 87 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_periodic_sum';
    SET @v_rule_no = N'YJ05202507162240';
    SET @v_rule_desc = N'查询 #87 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_87') IS NOT NULL
            DROP TABLE #temp_result_87;
            
        CREATE TABLE #temp_result_87 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_87
        select max(org_name) as org_name,max(uscid) as uscid,'emr_periodic_sum' as tabname ,'YJ05202507162240' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_periodic_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_87;
        
        -- 删除临时表
        DROP TABLE #temp_result_87;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 88 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pre_anst';
    SET @v_rule_no = N'YJ05202507162241';
    SET @v_rule_desc = N'查询 #88 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_88') IS NOT NULL
            DROP TABLE #temp_result_88;
            
        CREATE TABLE #temp_result_88 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_88
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pre_anst' as tabname ,'YJ05202507162241' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_pre_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_88;
        
        -- 删除临时表
        DROP TABLE #temp_result_88;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 89 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_predelivery';
    SET @v_rule_no = N'YJ05202507162242';
    SET @v_rule_desc = N'查询 #89 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_89') IS NOT NULL
            DROP TABLE #temp_result_89;
            
        CREATE TABLE #temp_result_89 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_89
        select max(org_name) as org_name,max(uscid) as uscid,'emr_predelivery' as tabname ,'YJ05202507162242' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_predelivery A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_89;
        
        -- 删除临时表
        DROP TABLE #temp_result_89;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 90 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy';
    SET @v_rule_no = N'YJ05202507162243';
    SET @v_rule_desc = N'查询 #90 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_90') IS NOT NULL
            DROP TABLE #temp_result_90;
            
        CREATE TABLE #temp_result_90 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_90
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy' as tabname ,'YJ05202507162243' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_pregnancy A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_90;
        
        -- 删除临时表
        DROP TABLE #temp_result_90;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 91 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_nwb';
    SET @v_rule_no = N'YJ05202507162244';
    SET @v_rule_desc = N'查询 #91 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_91') IS NOT NULL
            DROP TABLE #temp_result_91;
            
        CREATE TABLE #temp_result_91 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_91
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_nwb' as tabname ,'YJ05202507162244' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_pregnancy_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_91;
        
        -- 删除临时表
        DROP TABLE #temp_result_91;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 92 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_observ';
    SET @v_rule_no = N'YJ05202507162245';
    SET @v_rule_desc = N'查询 #92 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_92') IS NOT NULL
            DROP TABLE #temp_result_92;
            
        CREATE TABLE #temp_result_92 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_92
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_observ' as tabname ,'YJ05202507162245' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_pregnancy_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_92;
        
        -- 删除临时表
        DROP TABLE #temp_result_92;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 93 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_discu';
    SET @v_rule_no = N'YJ05202507162246';
    SET @v_rule_desc = N'查询 #93 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_93') IS NOT NULL
            DROP TABLE #temp_result_93;
            
        CREATE TABLE #temp_result_93 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_93
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_discu' as tabname ,'YJ05202507162246' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_preop_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_93;
        
        -- 删除临时表
        DROP TABLE #temp_result_93;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 94 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_sum';
    SET @v_rule_no = N'YJ05202507162247';
    SET @v_rule_desc = N'查询 #94 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_94') IS NOT NULL
            DROP TABLE #temp_result_94;
            
        CREATE TABLE #temp_result_94 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_94
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_sum' as tabname ,'YJ05202507162247' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_preop_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_94;
        
        -- 删除临时表
        DROP TABLE #temp_result_94;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 95 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_resc_rec';
    SET @v_rule_no = N'YJ05202507162248';
    SET @v_rule_desc = N'查询 #95 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_95') IS NOT NULL
            DROP TABLE #temp_result_95;
            
        CREATE TABLE #temp_result_95 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_95
        select max(org_name) as org_name,max(uscid) as uscid,'emr_resc_rec' as tabname ,'YJ05202507162248' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_resc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_95;
        
        -- 删除临时表
        DROP TABLE #temp_result_95;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 96 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rescue';
    SET @v_rule_no = N'YJ05202507162249';
    SET @v_rule_desc = N'查询 #96 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_96') IS NOT NULL
            DROP TABLE #temp_result_96;
            
        CREATE TABLE #temp_result_96 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_96
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rescue' as tabname ,'YJ05202507162249' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_rescue A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_96;
        
        -- 删除临时表
        DROP TABLE #temp_result_96;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 97 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rreat_drug';
    SET @v_rule_no = N'YJ05202507162250';
    SET @v_rule_desc = N'查询 #97 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_97') IS NOT NULL
            DROP TABLE #temp_result_97;
            
        CREATE TABLE #temp_result_97 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_97
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rreat_drug' as tabname ,'YJ05202507162250' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_rreat_drug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_97;
        
        -- 删除临时表
        DROP TABLE #temp_result_97;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 98 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_shift_change';
    SET @v_rule_no = N'YJ05202507162251';
    SET @v_rule_desc = N'查询 #98 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_98') IS NOT NULL
            DROP TABLE #temp_result_98;
            
        CREATE TABLE #temp_result_98 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_98
        select max(org_name) as org_name,max(uscid) as uscid,'emr_shift_change' as tabname ,'YJ05202507162251' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_shift_change A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_98;
        
        -- 删除临时表
        DROP TABLE #temp_result_98;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 99 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_spe_informed';
    SET @v_rule_no = N'YJ05202507162252';
    SET @v_rule_desc = N'查询 #99 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_99') IS NOT NULL
            DROP TABLE #temp_result_99;
            
        CREATE TABLE #temp_result_99 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_99
        select max(org_name) as org_name,max(uscid) as uscid,'emr_spe_informed' as tabname ,'YJ05202507162252' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_spe_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_99;
        
        -- 删除临时表
        DROP TABLE #temp_result_99;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 100 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_super_doct_check';
    SET @v_rule_no = N'YJ05202507162253';
    SET @v_rule_desc = N'查询 #100 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_100') IS NOT NULL
            DROP TABLE #temp_result_100;
            
        CREATE TABLE #temp_result_100 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_100
        select max(org_name) as org_name,max(uscid) as uscid,'emr_super_doct_check' as tabname ,'YJ05202507162253' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_super_doct_check A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_100;
        
        -- 删除临时表
        DROP TABLE #temp_result_100;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 101 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_sympt_rec';
    SET @v_rule_no = N'YJ05202507162254';
    SET @v_rule_desc = N'查询 #101 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_101') IS NOT NULL
            DROP TABLE #temp_result_101;
            
        CREATE TABLE #temp_result_101 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_101
        select max(org_name) as org_name,max(uscid) as uscid,'emr_sympt_rec' as tabname ,'YJ05202507162254' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_sympt_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_101;
        
        -- 删除临时表
        DROP TABLE #temp_result_101;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 102 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_trans_depart';
    SET @v_rule_no = N'YJ05202507162255';
    SET @v_rule_desc = N'查询 #102 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_102') IS NOT NULL
            DROP TABLE #temp_result_102;
            
        CREATE TABLE #temp_result_102 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_102
        select max(org_name) as org_name,max(uscid) as uscid,'emr_trans_depart' as tabname ,'YJ05202507162255' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_trans_depart A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_102;
        
        -- 删除临时表
        DROP TABLE #temp_result_102;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 103 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_treat_rec';
    SET @v_rule_no = N'YJ05202507162256';
    SET @v_rule_desc = N'查询 #103 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_103') IS NOT NULL
            DROP TABLE #temp_result_103;
            
        CREATE TABLE #temp_result_103 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_103
        select max(org_name) as org_name,max(uscid) as uscid,'emr_treat_rec' as tabname ,'YJ05202507162256' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_treat_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_103;
        
        -- 删除临时表
        DROP TABLE #temp_result_103;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 104 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver';
    SET @v_rule_no = N'YJ05202507162257';
    SET @v_rule_desc = N'查询 #104 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_104') IS NOT NULL
            DROP TABLE #temp_result_104;
            
        CREATE TABLE #temp_result_104 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_104
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver' as tabname ,'YJ05202507162257' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_vagina_deliver A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_104;
        
        -- 删除临时表
        DROP TABLE #temp_result_104;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 105 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_nwb';
    SET @v_rule_no = N'YJ05202507162258';
    SET @v_rule_desc = N'查询 #105 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_105') IS NOT NULL
            DROP TABLE #temp_result_105;
            
        CREATE TABLE #temp_result_105 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_105
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_nwb' as tabname ,'YJ05202507162258' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_vagina_deliver_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_105;
        
        -- 删除临时表
        DROP TABLE #temp_result_105;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 106 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_observ';
    SET @v_rule_no = N'YJ05202507162259';
    SET @v_rule_desc = N'查询 #106 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_106') IS NOT NULL
            DROP TABLE #temp_result_106;
            
        CREATE TABLE #temp_result_106 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_106
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_observ' as tabname ,'YJ05202507162259' as rule_no,'表数据量统计' as rule_dscr, count(1) as biz_info from emr_vagina_deliver_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_106;
        
        -- 删除临时表
        DROP TABLE #temp_result_106;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 107 条质控规则-sqlserver2017
    SET @v_tabname = N'cis_lh_summary';
    SET @v_rule_no = N'YJ05202507162271';
    SET @v_rule_desc = N'查询 #107 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_107') IS NOT NULL
            DROP TABLE #temp_result_107;
            
        CREATE TABLE #temp_result_107 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_107
        select max(org_name) as org_name,max(uscid) as uscid,'cis_lh_summary' as tabname ,'YJ05202507162271' as rule_no,'最小业务日期' as rule_dscr, min(A.dscg_time) as biz_info from cis_lh_summary A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_107;
        
        -- 删除临时表
        DROP TABLE #temp_result_107;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 108 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_adm_rec';
    SET @v_rule_no = N'YJ05202507162272';
    SET @v_rule_desc = N'查询 #108 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_108') IS NOT NULL
            DROP TABLE #temp_result_108;
            
        CREATE TABLE #temp_result_108 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_108
        select max(org_name) as org_name,max(uscid) as uscid,'emr_adm_rec' as tabname ,'YJ05202507162272' as rule_no,'最小业务日期' as rule_dscr, min(A.adm_time) as biz_info from emr_adm_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_108;
        
        -- 删除临时表
        DROP TABLE #temp_result_108;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 109 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_aft_anst';
    SET @v_rule_no = N'YJ05202507162273';
    SET @v_rule_desc = N'查询 #109 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_109') IS NOT NULL
            DROP TABLE #temp_result_109;
            
        CREATE TABLE #temp_result_109 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_109
        select max(org_name) as org_name,max(uscid) as uscid,'emr_aft_anst' as tabname ,'YJ05202507162273' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_aft_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_109;
        
        -- 删除临时表
        DROP TABLE #temp_result_109;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 110 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_anst_informed';
    SET @v_rule_no = N'YJ05202507162274';
    SET @v_rule_desc = N'查询 #110 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_110') IS NOT NULL
            DROP TABLE #temp_result_110;
            
        CREATE TABLE #temp_result_110 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_110
        select max(org_name) as org_name,max(uscid) as uscid,'emr_anst_informed' as tabname ,'YJ05202507162274' as rule_no,'最小业务日期' as rule_dscr, min(A.plan_oprt_time) as biz_info from emr_anst_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_110;
        
        -- 删除临时表
        DROP TABLE #temp_result_110;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 111 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_blood_informed';
    SET @v_rule_no = N'YJ05202507162275';
    SET @v_rule_desc = N'查询 #111 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_111') IS NOT NULL
            DROP TABLE #temp_result_111;
            
        CREATE TABLE #temp_result_111 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_111
        select max(org_name) as org_name,max(uscid) as uscid,'emr_blood_informed' as tabname ,'YJ05202507162275' as rule_no,'最小业务日期' as rule_dscr, min(A.plan_transfuse_time) as biz_info from emr_blood_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_111;
        
        -- 删除临时表
        DROP TABLE #temp_result_111;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 112 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_detail';
    SET @v_rule_no = N'YJ05202507162276';
    SET @v_rule_desc = N'查询 #112 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_112') IS NOT NULL
            DROP TABLE #temp_result_112;
            
        CREATE TABLE #temp_result_112 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_112
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_detail' as tabname ,'YJ05202507162276' as rule_no,'最小业务日期' as rule_dscr, min(A.consult_time) as biz_info from emr_consult_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_112;
        
        -- 删除临时表
        DROP TABLE #temp_result_112;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 113 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_info';
    SET @v_rule_no = N'YJ05202507162277';
    SET @v_rule_desc = N'查询 #113 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_113') IS NOT NULL
            DROP TABLE #temp_result_113;
            
        CREATE TABLE #temp_result_113 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_113
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_info' as tabname ,'YJ05202507162277' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_consult_info A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_113;
        
        -- 删除临时表
        DROP TABLE #temp_result_113;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 114 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_daily_dis_course';
    SET @v_rule_no = N'YJ05202507162278';
    SET @v_rule_desc = N'查询 #114 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_114') IS NOT NULL
            DROP TABLE #temp_result_114;
            
        CREATE TABLE #temp_result_114 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_114
        select max(org_name) as org_name,max(uscid) as uscid,'emr_daily_dis_course' as tabname ,'YJ05202507162278' as rule_no,'最小业务日期' as rule_dscr, min(A.sign_time) as biz_info from emr_daily_dis_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_114;
        
        -- 删除临时表
        DROP TABLE #temp_result_114;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 115 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_case_discu';
    SET @v_rule_no = N'YJ05202507162279';
    SET @v_rule_desc = N'查询 #115 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_115') IS NOT NULL
            DROP TABLE #temp_result_115;
            
        CREATE TABLE #temp_result_115 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_115
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_case_discu' as tabname ,'YJ05202507162279' as rule_no,'最小业务日期' as rule_dscr, min(A.discu_time) as biz_info from emr_death_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_115;
        
        -- 删除临时表
        DROP TABLE #temp_result_115;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 116 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_record';
    SET @v_rule_no = N'YJ05202507162280';
    SET @v_rule_desc = N'查询 #116 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_116') IS NOT NULL
            DROP TABLE #temp_result_116;
            
        CREATE TABLE #temp_result_116 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_116
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_record' as tabname ,'YJ05202507162280' as rule_no,'最小业务日期' as rule_dscr, min(A.death_time) as biz_info from emr_death_record A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_116;
        
        -- 删除临时表
        DROP TABLE #temp_result_116;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 117 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_detail';
    SET @v_rule_no = N'YJ05202507162281';
    SET @v_rule_desc = N'查询 #117 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_117') IS NOT NULL
            DROP TABLE #temp_result_117;
            
        CREATE TABLE #temp_result_117 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_117
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_detail' as tabname ,'YJ05202507162281' as rule_no,'最小业务日期' as rule_dscr, min(A.file_create_time) as biz_info from emr_doc_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_117;
        
        -- 删除临时表
        DROP TABLE #temp_result_117;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 118 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_rec';
    SET @v_rule_no = N'YJ05202507162282';
    SET @v_rule_desc = N'查询 #118 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_118') IS NOT NULL
            DROP TABLE #temp_result_118;
            
        CREATE TABLE #temp_result_118 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_118
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_rec' as tabname ,'YJ05202507162282' as rule_no,'最小业务日期' as rule_dscr, min(A.doc_create_time) as biz_info from emr_doc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_118;
        
        -- 删除临时表
        DROP TABLE #temp_result_118;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 119 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_first_postop_course';
    SET @v_rule_no = N'YJ05202507162284';
    SET @v_rule_desc = N'查询 #119 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_119') IS NOT NULL
            DROP TABLE #temp_result_119;
            
        CREATE TABLE #temp_result_119 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_119
        select max(org_name) as org_name,max(uscid) as uscid,'emr_first_postop_course' as tabname ,'YJ05202507162284' as rule_no,'最小业务日期' as rule_dscr, min(A.oprn_time) as biz_info from emr_first_postop_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_119;
        
        -- 删除临时表
        DROP TABLE #temp_result_119;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 120 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_hard_case_discu';
    SET @v_rule_no = N'YJ05202507162285';
    SET @v_rule_desc = N'查询 #120 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_120') IS NOT NULL
            DROP TABLE #temp_result_120;
            
        CREATE TABLE #temp_result_120 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_120
        select max(org_name) as org_name,max(uscid) as uscid,'emr_hard_case_discu' as tabname ,'YJ05202507162285' as rule_no,'最小业务日期' as rule_dscr, min(A.discu_time) as biz_info from emr_hard_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_120;
        
        -- 删除临时表
        DROP TABLE #temp_result_120;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 121 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_informed';
    SET @v_rule_no = N'YJ05202507162286';
    SET @v_rule_desc = N'查询 #121 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_121') IS NOT NULL
            DROP TABLE #temp_result_121;
            
        CREATE TABLE #temp_result_121 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_121
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_informed' as tabname ,'YJ05202507162286' as rule_no,'最小业务日期' as rule_dscr, min(A.dying_inform_time) as biz_info from emr_heavy_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_121;
        
        -- 删除临时表
        DROP TABLE #temp_result_121;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 122 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_nurse';
    SET @v_rule_no = N'YJ05202507162287';
    SET @v_rule_desc = N'查询 #122 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_122') IS NOT NULL
            DROP TABLE #temp_result_122;
            
        CREATE TABLE #temp_result_122 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_122
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_nurse' as tabname ,'YJ05202507162287' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_heavy_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_122;
        
        -- 删除临时表
        DROP TABLE #temp_result_122;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 123 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_assess';
    SET @v_rule_no = N'YJ05202507162288';
    SET @v_rule_desc = N'查询 #123 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_123') IS NOT NULL
            DROP TABLE #temp_result_123;
            
        CREATE TABLE #temp_result_123 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_123
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_assess' as tabname ,'YJ05202507162288' as rule_no,'最小业务日期' as rule_dscr, min(A.eval_time) as biz_info from emr_inhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_123;
        
        -- 删除临时表
        DROP TABLE #temp_result_123;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 124 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_die_in24h';
    SET @v_rule_no = N'YJ05202507162289';
    SET @v_rule_desc = N'查询 #124 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_124') IS NOT NULL
            DROP TABLE #temp_result_124;
            
        CREATE TABLE #temp_result_124 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_124
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_die_in24h' as tabname ,'YJ05202507162289' as rule_no,'最小业务日期' as rule_dscr, min(A.adm_time) as biz_info from emr_inhosp_die_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_124;
        
        -- 删除临时表
        DROP TABLE #temp_result_124;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 125 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec';
    SET @v_rule_no = N'YJ05202507162290';
    SET @v_rule_desc = N'查询 #125 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_125') IS NOT NULL
            DROP TABLE #temp_result_125;
            
        CREATE TABLE #temp_result_125 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_125
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec' as tabname ,'YJ05202507162290' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_inout_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_125;
        
        -- 删除临时表
        DROP TABLE #temp_result_125;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 126 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec_in24h';
    SET @v_rule_no = N'YJ05202507162291';
    SET @v_rule_desc = N'查询 #126 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_126') IS NOT NULL
            DROP TABLE #temp_result_126;
            
        CREATE TABLE #temp_result_126 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_126
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec_in24h' as tabname ,'YJ05202507162291' as rule_no,'最小业务日期' as rule_dscr, min(A.adm_time) as biz_info from emr_inout_rec_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_126;
        
        -- 删除临时表
        DROP TABLE #temp_result_126;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 127 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_usedrug';
    SET @v_rule_no = N'YJ05202507162292';
    SET @v_rule_desc = N'查询 #127 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_127') IS NOT NULL
            DROP TABLE #temp_result_127;
            
        CREATE TABLE #temp_result_127 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_127
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_usedrug' as tabname ,'YJ05202507162292' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_inout_usedrug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_127;
        
        -- 删除临时表
        DROP TABLE #temp_result_127;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 128 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_labor';
    SET @v_rule_no = N'YJ05202507162293';
    SET @v_rule_desc = N'查询 #128 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_128') IS NOT NULL
            DROP TABLE #temp_result_128;
            
        CREATE TABLE #temp_result_128 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_128
        select max(org_name) as org_name,max(uscid) as uscid,'emr_labor' as tabname ,'YJ05202507162293' as rule_no,'最小业务日期' as rule_dscr, min(A.labor_rec_time) as biz_info from emr_labor A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_128;
        
        -- 删除临时表
        DROP TABLE #temp_result_128;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 129 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse';
    SET @v_rule_no = N'YJ05202507162294';
    SET @v_rule_desc = N'查询 #129 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_129') IS NOT NULL
            DROP TABLE #temp_result_129;
            
        CREATE TABLE #temp_result_129 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_129
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse' as tabname ,'YJ05202507162294' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_129;
        
        -- 删除临时表
        DROP TABLE #temp_result_129;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 130 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse_plan';
    SET @v_rule_no = N'YJ05202507162295';
    SET @v_rule_desc = N'查询 #130 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_130') IS NOT NULL
            DROP TABLE #temp_result_130;
            
        CREATE TABLE #temp_result_130 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_130
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse_plan' as tabname ,'YJ05202507162295' as rule_no,'最小业务日期' as rule_dscr, min(A.sign_time) as biz_info from emr_nurse_plan A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_130;
        
        -- 删除临时表
        DROP TABLE #temp_result_130;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 131 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observmedi';
    SET @v_rule_no = N'YJ05202507162296';
    SET @v_rule_desc = N'查询 #131 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_131') IS NOT NULL
            DROP TABLE #temp_result_131;
            
        CREATE TABLE #temp_result_131 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_131
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observmedi' as tabname ,'YJ05202507162296' as rule_no,'最小业务日期' as rule_dscr, min(A.observe_room_time) as biz_info from emr_observmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_131;
        
        -- 删除临时表
        DROP TABLE #temp_result_131;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 132 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observoprn';
    SET @v_rule_no = N'YJ05202507162297';
    SET @v_rule_desc = N'查询 #132 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_132') IS NOT NULL
            DROP TABLE #temp_result_132;
            
        CREATE TABLE #temp_result_132 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_132
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observoprn' as tabname ,'YJ05202507162297' as rule_no,'最小业务日期' as rule_dscr, min(A.oprn_oprt_time) as biz_info from emr_observoprn A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_132;
        
        -- 删除临时表
        DROP TABLE #temp_result_132;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 133 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_informed';
    SET @v_rule_no = N'YJ05202507162298';
    SET @v_rule_desc = N'查询 #133 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_133') IS NOT NULL
            DROP TABLE #temp_result_133;
            
        CREATE TABLE #temp_result_133 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_133
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_informed' as tabname ,'YJ05202507162298' as rule_no,'最小业务日期' as rule_dscr, min(A.plan_oprt_time) as biz_info from emr_opr_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_133;
        
        -- 删除临时表
        DROP TABLE #temp_result_133;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 134 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_nurse';
    SET @v_rule_no = N'YJ05202507162299';
    SET @v_rule_desc = N'查询 #134 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_134') IS NOT NULL
            DROP TABLE #temp_result_134;
            
        CREATE TABLE #temp_result_134 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_134
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_nurse' as tabname ,'YJ05202507162299' as rule_no,'最小业务日期' as rule_dscr, min(A.oprn_begin_time) as biz_info from emr_opr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_134;
        
        -- 删除临时表
        DROP TABLE #temp_result_134;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 135 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_rec';
    SET @v_rule_no = N'YJ05202507162300';
    SET @v_rule_desc = N'查询 #135 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_135') IS NOT NULL
            DROP TABLE #temp_result_135;
            
        CREATE TABLE #temp_result_135 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_135
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_rec' as tabname ,'YJ05202507162300' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_opr_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_135;
        
        -- 删除临时表
        DROP TABLE #temp_result_135;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 136 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_oth_informed';
    SET @v_rule_no = N'YJ05202507162301';
    SET @v_rule_desc = N'查询 #136 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_136') IS NOT NULL
            DROP TABLE #temp_result_136;
            
        CREATE TABLE #temp_result_136 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_136
        select max(org_name) as org_name,max(uscid) as uscid,'emr_oth_informed' as tabname ,'YJ05202507162301' as rule_no,'最小业务日期' as rule_dscr, min(A.doc_sign_time) as biz_info from emr_oth_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_136;
        
        -- 删除临时表
        DROP TABLE #temp_result_136;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 137 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_otpmedi';
    SET @v_rule_no = N'YJ05202507162302';
    SET @v_rule_desc = N'查询 #137 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_137') IS NOT NULL
            DROP TABLE #temp_result_137;
            
        CREATE TABLE #temp_result_137 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_137
        select max(org_name) as org_name,max(uscid) as uscid,'emr_otpmedi' as tabname ,'YJ05202507162302' as rule_no,'最小业务日期' as rule_dscr, min(A.mdtrt_time) as biz_info from emr_otpmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_137;
        
        -- 删除临时表
        DROP TABLE #temp_result_137;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 138 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp';
    SET @v_rule_no = N'YJ05202507162303';
    SET @v_rule_desc = N'查询 #138 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_138') IS NOT NULL
            DROP TABLE #temp_result_138;
            
        CREATE TABLE #temp_result_138 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_138
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp' as tabname ,'YJ05202507162303' as rule_no,'最小业务日期' as rule_dscr, min(A.adm_time) as biz_info from emr_outhosp A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_138;
        
        -- 删除临时表
        DROP TABLE #temp_result_138;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 139 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp_assess';
    SET @v_rule_no = N'YJ05202507162304';
    SET @v_rule_desc = N'查询 #139 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_139') IS NOT NULL
            DROP TABLE #temp_result_139;
            
        CREATE TABLE #temp_result_139 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_139
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp_assess' as tabname ,'YJ05202507162304' as rule_no,'最小业务日期' as rule_dscr, min(A.dscg_time) as biz_info from emr_outhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_139;
        
        -- 删除临时表
        DROP TABLE #temp_result_139;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 140 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_periodic_sum';
    SET @v_rule_no = N'YJ05202507162305';
    SET @v_rule_desc = N'查询 #140 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_140') IS NOT NULL
            DROP TABLE #temp_result_140;
            
        CREATE TABLE #temp_result_140 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_140
        select max(org_name) as org_name,max(uscid) as uscid,'emr_periodic_sum' as tabname ,'YJ05202507162305' as rule_no,'最小业务日期' as rule_dscr, min(A.sum_time) as biz_info from emr_periodic_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_140;
        
        -- 删除临时表
        DROP TABLE #temp_result_140;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 141 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pre_anst';
    SET @v_rule_no = N'YJ05202507162306';
    SET @v_rule_desc = N'查询 #141 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_141') IS NOT NULL
            DROP TABLE #temp_result_141;
            
        CREATE TABLE #temp_result_141 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_141
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pre_anst' as tabname ,'YJ05202507162306' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_pre_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_141;
        
        -- 删除临时表
        DROP TABLE #temp_result_141;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 142 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_predelivery';
    SET @v_rule_no = N'YJ05202507162307';
    SET @v_rule_desc = N'查询 #142 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_142') IS NOT NULL
            DROP TABLE #temp_result_142;
            
        CREATE TABLE #temp_result_142 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_142
        select max(org_name) as org_name,max(uscid) as uscid,'emr_predelivery' as tabname ,'YJ05202507162307' as rule_no,'最小业务日期' as rule_dscr, min(A.expectant_time) as biz_info from emr_predelivery A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_142;
        
        -- 删除临时表
        DROP TABLE #temp_result_142;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 143 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy';
    SET @v_rule_no = N'YJ05202507162308';
    SET @v_rule_desc = N'查询 #143 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_143') IS NOT NULL
            DROP TABLE #temp_result_143;
            
        CREATE TABLE #temp_result_143 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_143
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy' as tabname ,'YJ05202507162308' as rule_no,'最小业务日期' as rule_dscr, min(A.oprn_end_time) as biz_info from emr_pregnancy A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_143;
        
        -- 删除临时表
        DROP TABLE #temp_result_143;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 144 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_nwb';
    SET @v_rule_no = N'YJ05202507162309';
    SET @v_rule_desc = N'查询 #144 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_144') IS NOT NULL
            DROP TABLE #temp_result_144;
            
        CREATE TABLE #temp_result_144 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_144
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_nwb' as tabname ,'YJ05202507162309' as rule_no,'最小业务日期' as rule_dscr, min(A.nwb_brdy_time) as biz_info from emr_pregnancy_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_144;
        
        -- 删除临时表
        DROP TABLE #temp_result_144;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 145 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_observ';
    SET @v_rule_no = N'YJ05202507162310';
    SET @v_rule_desc = N'查询 #145 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_145') IS NOT NULL
            DROP TABLE #temp_result_145;
            
        CREATE TABLE #temp_result_145 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_145
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_observ' as tabname ,'YJ05202507162310' as rule_no,'最小业务日期' as rule_dscr, min(A.postpar_observ_time) as biz_info from emr_pregnancy_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_145;
        
        -- 删除临时表
        DROP TABLE #temp_result_145;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 146 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_discu';
    SET @v_rule_no = N'YJ05202507162311';
    SET @v_rule_desc = N'查询 #146 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_146') IS NOT NULL
            DROP TABLE #temp_result_146;
            
        CREATE TABLE #temp_result_146 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_146
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_discu' as tabname ,'YJ05202507162311' as rule_no,'最小业务日期' as rule_dscr, min(A.discu_time) as biz_info from emr_preop_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_146;
        
        -- 删除临时表
        DROP TABLE #temp_result_146;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 147 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_sum';
    SET @v_rule_no = N'YJ05202507162312';
    SET @v_rule_desc = N'查询 #147 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_147') IS NOT NULL
            DROP TABLE #temp_result_147;
            
        CREATE TABLE #temp_result_147 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_147
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_sum' as tabname ,'YJ05202507162312' as rule_no,'最小业务日期' as rule_dscr, min(A.sum_time) as biz_info from emr_preop_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_147;
        
        -- 删除临时表
        DROP TABLE #temp_result_147;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 148 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_resc_rec';
    SET @v_rule_no = N'YJ05202507162313';
    SET @v_rule_desc = N'查询 #148 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_148') IS NOT NULL
            DROP TABLE #temp_result_148;
            
        CREATE TABLE #temp_result_148 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_148
        select max(org_name) as org_name,max(uscid) as uscid,'emr_resc_rec' as tabname ,'YJ05202507162313' as rule_no,'最小业务日期' as rule_dscr, min(A.resc_endtime) as biz_info from emr_resc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_148;
        
        -- 删除临时表
        DROP TABLE #temp_result_148;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 149 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rescue';
    SET @v_rule_no = N'YJ05202507162314';
    SET @v_rule_desc = N'查询 #149 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_149') IS NOT NULL
            DROP TABLE #temp_result_149;
            
        CREATE TABLE #temp_result_149 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_149
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rescue' as tabname ,'YJ05202507162314' as rule_no,'最小业务日期' as rule_dscr, min(A.sign_time) as biz_info from emr_rescue A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_149;
        
        -- 删除临时表
        DROP TABLE #temp_result_149;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 150 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rreat_drug';
    SET @v_rule_no = N'YJ05202507162315';
    SET @v_rule_desc = N'查询 #150 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_150') IS NOT NULL
            DROP TABLE #temp_result_150;
            
        CREATE TABLE #temp_result_150 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_150
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rreat_drug' as tabname ,'YJ05202507162315' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_rreat_drug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_150;
        
        -- 删除临时表
        DROP TABLE #temp_result_150;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 151 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_shift_change';
    SET @v_rule_no = N'YJ05202507162316';
    SET @v_rule_desc = N'查询 #151 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_151') IS NOT NULL
            DROP TABLE #temp_result_151;
            
        CREATE TABLE #temp_result_151 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_151
        select max(org_name) as org_name,max(uscid) as uscid,'emr_shift_change' as tabname ,'YJ05202507162316' as rule_no,'最小业务日期' as rule_dscr, min(A.shift_time) as biz_info from emr_shift_change A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_151;
        
        -- 删除临时表
        DROP TABLE #temp_result_151;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 152 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_spe_informed';
    SET @v_rule_no = N'YJ05202507162317';
    SET @v_rule_desc = N'查询 #152 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_152') IS NOT NULL
            DROP TABLE #temp_result_152;
            
        CREATE TABLE #temp_result_152 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_152
        select max(org_name) as org_name,max(uscid) as uscid,'emr_spe_informed' as tabname ,'YJ05202507162317' as rule_no,'最小业务日期' as rule_dscr, min(A.doc_sign_time) as biz_info from emr_spe_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_152;
        
        -- 删除临时表
        DROP TABLE #temp_result_152;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 153 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_super_doct_check';
    SET @v_rule_no = N'YJ05202507162318';
    SET @v_rule_desc = N'查询 #153 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_153') IS NOT NULL
            DROP TABLE #temp_result_153;
            
        CREATE TABLE #temp_result_153 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_153
        select max(org_name) as org_name,max(uscid) as uscid,'emr_super_doct_check' as tabname ,'YJ05202507162318' as rule_no,'最小业务日期' as rule_dscr, min(A.check_room_time) as biz_info from emr_super_doct_check A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_153;
        
        -- 删除临时表
        DROP TABLE #temp_result_153;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 154 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_sympt_rec';
    SET @v_rule_no = N'YJ05202507162319';
    SET @v_rule_desc = N'查询 #154 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_154') IS NOT NULL
            DROP TABLE #temp_result_154;
            
        CREATE TABLE #temp_result_154 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_154
        select max(org_name) as org_name,max(uscid) as uscid,'emr_sympt_rec' as tabname ,'YJ05202507162319' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_sympt_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_154;
        
        -- 删除临时表
        DROP TABLE #temp_result_154;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 155 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_trans_depart';
    SET @v_rule_no = N'YJ05202507162320';
    SET @v_rule_desc = N'查询 #155 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_155') IS NOT NULL
            DROP TABLE #temp_result_155;
            
        CREATE TABLE #temp_result_155 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_155
        select max(org_name) as org_name,max(uscid) as uscid,'emr_trans_depart' as tabname ,'YJ05202507162320' as rule_no,'最小业务日期' as rule_dscr, min(A.adm_time) as biz_info from emr_trans_depart A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_155;
        
        -- 删除临时表
        DROP TABLE #temp_result_155;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 156 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_treat_rec';
    SET @v_rule_no = N'YJ05202507162321';
    SET @v_rule_desc = N'查询 #156 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_156') IS NOT NULL
            DROP TABLE #temp_result_156;
            
        CREATE TABLE #temp_result_156 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_156
        select max(org_name) as org_name,max(uscid) as uscid,'emr_treat_rec' as tabname ,'YJ05202507162321' as rule_no,'最小业务日期' as rule_dscr, min(A.rec_time) as biz_info from emr_treat_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_156;
        
        -- 删除临时表
        DROP TABLE #temp_result_156;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 157 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver';
    SET @v_rule_no = N'YJ05202507162322';
    SET @v_rule_desc = N'查询 #157 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_157') IS NOT NULL
            DROP TABLE #temp_result_157;
            
        CREATE TABLE #temp_result_157 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_157
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver' as tabname ,'YJ05202507162322' as rule_no,'最小业务日期' as rule_dscr, min(A.expect_deliver_date) as biz_info from emr_vagina_deliver A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_157;
        
        -- 删除临时表
        DROP TABLE #temp_result_157;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 158 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_nwb';
    SET @v_rule_no = N'YJ05202507162323';
    SET @v_rule_desc = N'查询 #158 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_158') IS NOT NULL
            DROP TABLE #temp_result_158;
            
        CREATE TABLE #temp_result_158 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_158
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_nwb' as tabname ,'YJ05202507162323' as rule_no,'最小业务日期' as rule_dscr, min(A.nwb_brdy_time) as biz_info from emr_vagina_deliver_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_158;
        
        -- 删除临时表
        DROP TABLE #temp_result_158;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 159 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_observ';
    SET @v_rule_no = N'YJ05202507162324';
    SET @v_rule_desc = N'查询 #159 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_159') IS NOT NULL
            DROP TABLE #temp_result_159;
            
        CREATE TABLE #temp_result_159 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_159
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_observ' as tabname ,'YJ05202507162324' as rule_no,'最小业务日期' as rule_dscr, min(A.postpar_observ_time) as biz_info from emr_vagina_deliver_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_159;
        
        -- 删除临时表
        DROP TABLE #temp_result_159;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 160 条质控规则-sqlserver2017
    SET @v_tabname = N'cis_lh_summary';
    SET @v_rule_no = N'YJ05202507162336';
    SET @v_rule_desc = N'查询 #160 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_160') IS NOT NULL
            DROP TABLE #temp_result_160;
            
        CREATE TABLE #temp_result_160 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_160
        select max(org_name) as org_name,max(uscid) as uscid,'cis_lh_summary' as tabname ,'YJ05202507162336' as rule_no,'最大业务日期' as rule_dscr, max(A.dscg_time) as biz_info from cis_lh_summary A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_160;
        
        -- 删除临时表
        DROP TABLE #temp_result_160;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 161 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_adm_rec';
    SET @v_rule_no = N'YJ05202507162337';
    SET @v_rule_desc = N'查询 #161 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_161') IS NOT NULL
            DROP TABLE #temp_result_161;
            
        CREATE TABLE #temp_result_161 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_161
        select max(org_name) as org_name,max(uscid) as uscid,'emr_adm_rec' as tabname ,'YJ05202507162337' as rule_no,'最大业务日期' as rule_dscr, max(A.adm_time) as biz_info from emr_adm_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_161;
        
        -- 删除临时表
        DROP TABLE #temp_result_161;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 162 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_aft_anst';
    SET @v_rule_no = N'YJ05202507162338';
    SET @v_rule_desc = N'查询 #162 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_162') IS NOT NULL
            DROP TABLE #temp_result_162;
            
        CREATE TABLE #temp_result_162 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_162
        select max(org_name) as org_name,max(uscid) as uscid,'emr_aft_anst' as tabname ,'YJ05202507162338' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_aft_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_162;
        
        -- 删除临时表
        DROP TABLE #temp_result_162;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 163 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_anst_informed';
    SET @v_rule_no = N'YJ05202507162339';
    SET @v_rule_desc = N'查询 #163 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_163') IS NOT NULL
            DROP TABLE #temp_result_163;
            
        CREATE TABLE #temp_result_163 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_163
        select max(org_name) as org_name,max(uscid) as uscid,'emr_anst_informed' as tabname ,'YJ05202507162339' as rule_no,'最大业务日期' as rule_dscr, max(A.plan_oprt_time) as biz_info from emr_anst_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_163;
        
        -- 删除临时表
        DROP TABLE #temp_result_163;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 164 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_blood_informed';
    SET @v_rule_no = N'YJ05202507162340';
    SET @v_rule_desc = N'查询 #164 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_164') IS NOT NULL
            DROP TABLE #temp_result_164;
            
        CREATE TABLE #temp_result_164 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_164
        select max(org_name) as org_name,max(uscid) as uscid,'emr_blood_informed' as tabname ,'YJ05202507162340' as rule_no,'最大业务日期' as rule_dscr, max(A.plan_transfuse_time) as biz_info from emr_blood_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_164;
        
        -- 删除临时表
        DROP TABLE #temp_result_164;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 165 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_detail';
    SET @v_rule_no = N'YJ05202507162341';
    SET @v_rule_desc = N'查询 #165 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_165') IS NOT NULL
            DROP TABLE #temp_result_165;
            
        CREATE TABLE #temp_result_165 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_165
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_detail' as tabname ,'YJ05202507162341' as rule_no,'最大业务日期' as rule_dscr, max(A.consult_time) as biz_info from emr_consult_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_165;
        
        -- 删除临时表
        DROP TABLE #temp_result_165;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 166 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_consult_info';
    SET @v_rule_no = N'YJ05202507162342';
    SET @v_rule_desc = N'查询 #166 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_166') IS NOT NULL
            DROP TABLE #temp_result_166;
            
        CREATE TABLE #temp_result_166 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_166
        select max(org_name) as org_name,max(uscid) as uscid,'emr_consult_info' as tabname ,'YJ05202507162342' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_consult_info A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_166;
        
        -- 删除临时表
        DROP TABLE #temp_result_166;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 167 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_daily_dis_course';
    SET @v_rule_no = N'YJ05202507162343';
    SET @v_rule_desc = N'查询 #167 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_167') IS NOT NULL
            DROP TABLE #temp_result_167;
            
        CREATE TABLE #temp_result_167 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_167
        select max(org_name) as org_name,max(uscid) as uscid,'emr_daily_dis_course' as tabname ,'YJ05202507162343' as rule_no,'最大业务日期' as rule_dscr, max(A.sign_time) as biz_info from emr_daily_dis_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_167;
        
        -- 删除临时表
        DROP TABLE #temp_result_167;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 168 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_case_discu';
    SET @v_rule_no = N'YJ05202507162344';
    SET @v_rule_desc = N'查询 #168 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_168') IS NOT NULL
            DROP TABLE #temp_result_168;
            
        CREATE TABLE #temp_result_168 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_168
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_case_discu' as tabname ,'YJ05202507162344' as rule_no,'最大业务日期' as rule_dscr, max(A.discu_time) as biz_info from emr_death_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_168;
        
        -- 删除临时表
        DROP TABLE #temp_result_168;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 169 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_death_record';
    SET @v_rule_no = N'YJ05202507162345';
    SET @v_rule_desc = N'查询 #169 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_169') IS NOT NULL
            DROP TABLE #temp_result_169;
            
        CREATE TABLE #temp_result_169 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_169
        select max(org_name) as org_name,max(uscid) as uscid,'emr_death_record' as tabname ,'YJ05202507162345' as rule_no,'最大业务日期' as rule_dscr, max(A.death_time) as biz_info from emr_death_record A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_169;
        
        -- 删除临时表
        DROP TABLE #temp_result_169;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 170 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_detail';
    SET @v_rule_no = N'YJ05202507162346';
    SET @v_rule_desc = N'查询 #170 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_170') IS NOT NULL
            DROP TABLE #temp_result_170;
            
        CREATE TABLE #temp_result_170 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_170
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_detail' as tabname ,'YJ05202507162346' as rule_no,'最大业务日期' as rule_dscr, max(A.file_create_time) as biz_info from emr_doc_detail A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_170;
        
        -- 删除临时表
        DROP TABLE #temp_result_170;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 171 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_doc_rec';
    SET @v_rule_no = N'YJ05202507162347';
    SET @v_rule_desc = N'查询 #171 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_171') IS NOT NULL
            DROP TABLE #temp_result_171;
            
        CREATE TABLE #temp_result_171 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_171
        select max(org_name) as org_name,max(uscid) as uscid,'emr_doc_rec' as tabname ,'YJ05202507162347' as rule_no,'最大业务日期' as rule_dscr, max(A.doc_create_time) as biz_info from emr_doc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_171;
        
        -- 删除临时表
        DROP TABLE #temp_result_171;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 172 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_first_postop_course';
    SET @v_rule_no = N'YJ05202507162349';
    SET @v_rule_desc = N'查询 #172 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_172') IS NOT NULL
            DROP TABLE #temp_result_172;
            
        CREATE TABLE #temp_result_172 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_172
        select max(org_name) as org_name,max(uscid) as uscid,'emr_first_postop_course' as tabname ,'YJ05202507162349' as rule_no,'最大业务日期' as rule_dscr, max(A.oprn_time) as biz_info from emr_first_postop_course A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_172;
        
        -- 删除临时表
        DROP TABLE #temp_result_172;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 173 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_hard_case_discu';
    SET @v_rule_no = N'YJ05202507162350';
    SET @v_rule_desc = N'查询 #173 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_173') IS NOT NULL
            DROP TABLE #temp_result_173;
            
        CREATE TABLE #temp_result_173 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_173
        select max(org_name) as org_name,max(uscid) as uscid,'emr_hard_case_discu' as tabname ,'YJ05202507162350' as rule_no,'最大业务日期' as rule_dscr, max(A.discu_time) as biz_info from emr_hard_case_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_173;
        
        -- 删除临时表
        DROP TABLE #temp_result_173;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 174 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_informed';
    SET @v_rule_no = N'YJ05202507162351';
    SET @v_rule_desc = N'查询 #174 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_174') IS NOT NULL
            DROP TABLE #temp_result_174;
            
        CREATE TABLE #temp_result_174 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_174
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_informed' as tabname ,'YJ05202507162351' as rule_no,'最大业务日期' as rule_dscr, max(A.dying_inform_time) as biz_info from emr_heavy_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_174;
        
        -- 删除临时表
        DROP TABLE #temp_result_174;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 175 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_heavy_nurse';
    SET @v_rule_no = N'YJ05202507162352';
    SET @v_rule_desc = N'查询 #175 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_175') IS NOT NULL
            DROP TABLE #temp_result_175;
            
        CREATE TABLE #temp_result_175 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_175
        select max(org_name) as org_name,max(uscid) as uscid,'emr_heavy_nurse' as tabname ,'YJ05202507162352' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_heavy_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_175;
        
        -- 删除临时表
        DROP TABLE #temp_result_175;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 176 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_assess';
    SET @v_rule_no = N'YJ05202507162353';
    SET @v_rule_desc = N'查询 #176 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_176') IS NOT NULL
            DROP TABLE #temp_result_176;
            
        CREATE TABLE #temp_result_176 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_176
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_assess' as tabname ,'YJ05202507162353' as rule_no,'最大业务日期' as rule_dscr, max(A.eval_time) as biz_info from emr_inhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_176;
        
        -- 删除临时表
        DROP TABLE #temp_result_176;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 177 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inhosp_die_in24h';
    SET @v_rule_no = N'YJ05202507162354';
    SET @v_rule_desc = N'查询 #177 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_177') IS NOT NULL
            DROP TABLE #temp_result_177;
            
        CREATE TABLE #temp_result_177 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_177
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inhosp_die_in24h' as tabname ,'YJ05202507162354' as rule_no,'最大业务日期' as rule_dscr, max(A.adm_time) as biz_info from emr_inhosp_die_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_177;
        
        -- 删除临时表
        DROP TABLE #temp_result_177;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 178 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec';
    SET @v_rule_no = N'YJ05202507162355';
    SET @v_rule_desc = N'查询 #178 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_178') IS NOT NULL
            DROP TABLE #temp_result_178;
            
        CREATE TABLE #temp_result_178 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_178
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec' as tabname ,'YJ05202507162355' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_inout_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_178;
        
        -- 删除临时表
        DROP TABLE #temp_result_178;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 179 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_rec_in24h';
    SET @v_rule_no = N'YJ05202507162356';
    SET @v_rule_desc = N'查询 #179 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_179') IS NOT NULL
            DROP TABLE #temp_result_179;
            
        CREATE TABLE #temp_result_179 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_179
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_rec_in24h' as tabname ,'YJ05202507162356' as rule_no,'最大业务日期' as rule_dscr, max(A.adm_time) as biz_info from emr_inout_rec_in24h A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_179;
        
        -- 删除临时表
        DROP TABLE #temp_result_179;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 180 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_inout_usedrug';
    SET @v_rule_no = N'YJ05202507162357';
    SET @v_rule_desc = N'查询 #180 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_180') IS NOT NULL
            DROP TABLE #temp_result_180;
            
        CREATE TABLE #temp_result_180 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_180
        select max(org_name) as org_name,max(uscid) as uscid,'emr_inout_usedrug' as tabname ,'YJ05202507162357' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_inout_usedrug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_180;
        
        -- 删除临时表
        DROP TABLE #temp_result_180;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 181 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_labor';
    SET @v_rule_no = N'YJ05202507162358';
    SET @v_rule_desc = N'查询 #181 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_181') IS NOT NULL
            DROP TABLE #temp_result_181;
            
        CREATE TABLE #temp_result_181 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_181
        select max(org_name) as org_name,max(uscid) as uscid,'emr_labor' as tabname ,'YJ05202507162358' as rule_no,'最大业务日期' as rule_dscr, max(A.labor_rec_time) as biz_info from emr_labor A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_181;
        
        -- 删除临时表
        DROP TABLE #temp_result_181;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 182 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse';
    SET @v_rule_no = N'YJ05202507162359';
    SET @v_rule_desc = N'查询 #182 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_182') IS NOT NULL
            DROP TABLE #temp_result_182;
            
        CREATE TABLE #temp_result_182 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_182
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse' as tabname ,'YJ05202507162359' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_182;
        
        -- 删除临时表
        DROP TABLE #temp_result_182;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 183 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_nurse_plan';
    SET @v_rule_no = N'YJ05202507162360';
    SET @v_rule_desc = N'查询 #183 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_183') IS NOT NULL
            DROP TABLE #temp_result_183;
            
        CREATE TABLE #temp_result_183 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_183
        select max(org_name) as org_name,max(uscid) as uscid,'emr_nurse_plan' as tabname ,'YJ05202507162360' as rule_no,'最大业务日期' as rule_dscr, max(A.sign_time) as biz_info from emr_nurse_plan A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_183;
        
        -- 删除临时表
        DROP TABLE #temp_result_183;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 184 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observmedi';
    SET @v_rule_no = N'YJ05202507162361';
    SET @v_rule_desc = N'查询 #184 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_184') IS NOT NULL
            DROP TABLE #temp_result_184;
            
        CREATE TABLE #temp_result_184 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_184
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observmedi' as tabname ,'YJ05202507162361' as rule_no,'最大业务日期' as rule_dscr, max(A.observe_room_time) as biz_info from emr_observmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_184;
        
        -- 删除临时表
        DROP TABLE #temp_result_184;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 185 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_observoprn';
    SET @v_rule_no = N'YJ05202507162362';
    SET @v_rule_desc = N'查询 #185 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_185') IS NOT NULL
            DROP TABLE #temp_result_185;
            
        CREATE TABLE #temp_result_185 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_185
        select max(org_name) as org_name,max(uscid) as uscid,'emr_observoprn' as tabname ,'YJ05202507162362' as rule_no,'最大业务日期' as rule_dscr, max(A.oprn_oprt_time) as biz_info from emr_observoprn A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_185;
        
        -- 删除临时表
        DROP TABLE #temp_result_185;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 186 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_informed';
    SET @v_rule_no = N'YJ05202507162363';
    SET @v_rule_desc = N'查询 #186 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_186') IS NOT NULL
            DROP TABLE #temp_result_186;
            
        CREATE TABLE #temp_result_186 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_186
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_informed' as tabname ,'YJ05202507162363' as rule_no,'最大业务日期' as rule_dscr, max(A.plan_oprt_time) as biz_info from emr_opr_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_186;
        
        -- 删除临时表
        DROP TABLE #temp_result_186;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 187 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_nurse';
    SET @v_rule_no = N'YJ05202507162364';
    SET @v_rule_desc = N'查询 #187 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_187') IS NOT NULL
            DROP TABLE #temp_result_187;
            
        CREATE TABLE #temp_result_187 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_187
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_nurse' as tabname ,'YJ05202507162364' as rule_no,'最大业务日期' as rule_dscr, max(A.oprn_begin_time) as biz_info from emr_opr_nurse A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_187;
        
        -- 删除临时表
        DROP TABLE #temp_result_187;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 188 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_opr_rec';
    SET @v_rule_no = N'YJ05202507162365';
    SET @v_rule_desc = N'查询 #188 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_188') IS NOT NULL
            DROP TABLE #temp_result_188;
            
        CREATE TABLE #temp_result_188 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_188
        select max(org_name) as org_name,max(uscid) as uscid,'emr_opr_rec' as tabname ,'YJ05202507162365' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_opr_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_188;
        
        -- 删除临时表
        DROP TABLE #temp_result_188;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 189 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_oth_informed';
    SET @v_rule_no = N'YJ05202507162366';
    SET @v_rule_desc = N'查询 #189 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_189') IS NOT NULL
            DROP TABLE #temp_result_189;
            
        CREATE TABLE #temp_result_189 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_189
        select max(org_name) as org_name,max(uscid) as uscid,'emr_oth_informed' as tabname ,'YJ05202507162366' as rule_no,'最大业务日期' as rule_dscr, max(A.doc_sign_time) as biz_info from emr_oth_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_189;
        
        -- 删除临时表
        DROP TABLE #temp_result_189;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 190 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_otpmedi';
    SET @v_rule_no = N'YJ05202507162367';
    SET @v_rule_desc = N'查询 #190 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_190') IS NOT NULL
            DROP TABLE #temp_result_190;
            
        CREATE TABLE #temp_result_190 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_190
        select max(org_name) as org_name,max(uscid) as uscid,'emr_otpmedi' as tabname ,'YJ05202507162367' as rule_no,'最大业务日期' as rule_dscr, max(A.mdtrt_time) as biz_info from emr_otpmedi A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_190;
        
        -- 删除临时表
        DROP TABLE #temp_result_190;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 191 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp';
    SET @v_rule_no = N'YJ05202507162368';
    SET @v_rule_desc = N'查询 #191 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_191') IS NOT NULL
            DROP TABLE #temp_result_191;
            
        CREATE TABLE #temp_result_191 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_191
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp' as tabname ,'YJ05202507162368' as rule_no,'最大业务日期' as rule_dscr, max(A.adm_time) as biz_info from emr_outhosp A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_191;
        
        -- 删除临时表
        DROP TABLE #temp_result_191;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 192 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_outhosp_assess';
    SET @v_rule_no = N'YJ05202507162369';
    SET @v_rule_desc = N'查询 #192 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_192') IS NOT NULL
            DROP TABLE #temp_result_192;
            
        CREATE TABLE #temp_result_192 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_192
        select max(org_name) as org_name,max(uscid) as uscid,'emr_outhosp_assess' as tabname ,'YJ05202507162369' as rule_no,'最大业务日期' as rule_dscr, max(A.dscg_time) as biz_info from emr_outhosp_assess A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_192;
        
        -- 删除临时表
        DROP TABLE #temp_result_192;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 193 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_periodic_sum';
    SET @v_rule_no = N'YJ05202507162370';
    SET @v_rule_desc = N'查询 #193 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_193') IS NOT NULL
            DROP TABLE #temp_result_193;
            
        CREATE TABLE #temp_result_193 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_193
        select max(org_name) as org_name,max(uscid) as uscid,'emr_periodic_sum' as tabname ,'YJ05202507162370' as rule_no,'最大业务日期' as rule_dscr, max(A.sum_time) as biz_info from emr_periodic_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_193;
        
        -- 删除临时表
        DROP TABLE #temp_result_193;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 194 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pre_anst';
    SET @v_rule_no = N'YJ05202507162371';
    SET @v_rule_desc = N'查询 #194 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_194') IS NOT NULL
            DROP TABLE #temp_result_194;
            
        CREATE TABLE #temp_result_194 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_194
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pre_anst' as tabname ,'YJ05202507162371' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_pre_anst A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_194;
        
        -- 删除临时表
        DROP TABLE #temp_result_194;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 195 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_predelivery';
    SET @v_rule_no = N'YJ05202507162372';
    SET @v_rule_desc = N'查询 #195 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_195') IS NOT NULL
            DROP TABLE #temp_result_195;
            
        CREATE TABLE #temp_result_195 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_195
        select max(org_name) as org_name,max(uscid) as uscid,'emr_predelivery' as tabname ,'YJ05202507162372' as rule_no,'最大业务日期' as rule_dscr, max(A.expectant_time) as biz_info from emr_predelivery A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_195;
        
        -- 删除临时表
        DROP TABLE #temp_result_195;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 196 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy';
    SET @v_rule_no = N'YJ05202507162373';
    SET @v_rule_desc = N'查询 #196 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_196') IS NOT NULL
            DROP TABLE #temp_result_196;
            
        CREATE TABLE #temp_result_196 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_196
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy' as tabname ,'YJ05202507162373' as rule_no,'最大业务日期' as rule_dscr, max(A.oprn_end_time) as biz_info from emr_pregnancy A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_196;
        
        -- 删除临时表
        DROP TABLE #temp_result_196;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 197 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_nwb';
    SET @v_rule_no = N'YJ05202507162374';
    SET @v_rule_desc = N'查询 #197 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_197') IS NOT NULL
            DROP TABLE #temp_result_197;
            
        CREATE TABLE #temp_result_197 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_197
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_nwb' as tabname ,'YJ05202507162374' as rule_no,'最大业务日期' as rule_dscr, max(A.nwb_brdy_time) as biz_info from emr_pregnancy_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_197;
        
        -- 删除临时表
        DROP TABLE #temp_result_197;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 198 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_pregnancy_observ';
    SET @v_rule_no = N'YJ05202507162375';
    SET @v_rule_desc = N'查询 #198 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_198') IS NOT NULL
            DROP TABLE #temp_result_198;
            
        CREATE TABLE #temp_result_198 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_198
        select max(org_name) as org_name,max(uscid) as uscid,'emr_pregnancy_observ' as tabname ,'YJ05202507162375' as rule_no,'最大业务日期' as rule_dscr, max(A.postpar_observ_time) as biz_info from emr_pregnancy_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_198;
        
        -- 删除临时表
        DROP TABLE #temp_result_198;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 199 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_discu';
    SET @v_rule_no = N'YJ05202507162376';
    SET @v_rule_desc = N'查询 #199 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_199') IS NOT NULL
            DROP TABLE #temp_result_199;
            
        CREATE TABLE #temp_result_199 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_199
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_discu' as tabname ,'YJ05202507162376' as rule_no,'最大业务日期' as rule_dscr, max(A.discu_time) as biz_info from emr_preop_discu A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_199;
        
        -- 删除临时表
        DROP TABLE #temp_result_199;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 200 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_preop_sum';
    SET @v_rule_no = N'YJ05202507162377';
    SET @v_rule_desc = N'查询 #200 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_200') IS NOT NULL
            DROP TABLE #temp_result_200;
            
        CREATE TABLE #temp_result_200 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_200
        select max(org_name) as org_name,max(uscid) as uscid,'emr_preop_sum' as tabname ,'YJ05202507162377' as rule_no,'最大业务日期' as rule_dscr, max(A.sum_time) as biz_info from emr_preop_sum A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_200;
        
        -- 删除临时表
        DROP TABLE #temp_result_200;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 201 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_resc_rec';
    SET @v_rule_no = N'YJ05202507162378';
    SET @v_rule_desc = N'查询 #201 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_201') IS NOT NULL
            DROP TABLE #temp_result_201;
            
        CREATE TABLE #temp_result_201 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_201
        select max(org_name) as org_name,max(uscid) as uscid,'emr_resc_rec' as tabname ,'YJ05202507162378' as rule_no,'最大业务日期' as rule_dscr, max(A.resc_endtime) as biz_info from emr_resc_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_201;
        
        -- 删除临时表
        DROP TABLE #temp_result_201;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 202 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rescue';
    SET @v_rule_no = N'YJ05202507162379';
    SET @v_rule_desc = N'查询 #202 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_202') IS NOT NULL
            DROP TABLE #temp_result_202;
            
        CREATE TABLE #temp_result_202 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_202
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rescue' as tabname ,'YJ05202507162379' as rule_no,'最大业务日期' as rule_dscr, max(A.sign_time) as biz_info from emr_rescue A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_202;
        
        -- 删除临时表
        DROP TABLE #temp_result_202;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 203 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_rreat_drug';
    SET @v_rule_no = N'YJ05202507162380';
    SET @v_rule_desc = N'查询 #203 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_203') IS NOT NULL
            DROP TABLE #temp_result_203;
            
        CREATE TABLE #temp_result_203 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_203
        select max(org_name) as org_name,max(uscid) as uscid,'emr_rreat_drug' as tabname ,'YJ05202507162380' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_rreat_drug A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_203;
        
        -- 删除临时表
        DROP TABLE #temp_result_203;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 204 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_shift_change';
    SET @v_rule_no = N'YJ05202507162381';
    SET @v_rule_desc = N'查询 #204 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_204') IS NOT NULL
            DROP TABLE #temp_result_204;
            
        CREATE TABLE #temp_result_204 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_204
        select max(org_name) as org_name,max(uscid) as uscid,'emr_shift_change' as tabname ,'YJ05202507162381' as rule_no,'最大业务日期' as rule_dscr, max(A.shift_time) as biz_info from emr_shift_change A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_204;
        
        -- 删除临时表
        DROP TABLE #temp_result_204;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 205 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_spe_informed';
    SET @v_rule_no = N'YJ05202507162382';
    SET @v_rule_desc = N'查询 #205 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_205') IS NOT NULL
            DROP TABLE #temp_result_205;
            
        CREATE TABLE #temp_result_205 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_205
        select max(org_name) as org_name,max(uscid) as uscid,'emr_spe_informed' as tabname ,'YJ05202507162382' as rule_no,'最大业务日期' as rule_dscr, max(A.doc_sign_time) as biz_info from emr_spe_informed A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_205;
        
        -- 删除临时表
        DROP TABLE #temp_result_205;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 206 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_super_doct_check';
    SET @v_rule_no = N'YJ05202507162383';
    SET @v_rule_desc = N'查询 #206 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_206') IS NOT NULL
            DROP TABLE #temp_result_206;
            
        CREATE TABLE #temp_result_206 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_206
        select max(org_name) as org_name,max(uscid) as uscid,'emr_super_doct_check' as tabname ,'YJ05202507162383' as rule_no,'最大业务日期' as rule_dscr, max(A.check_room_time) as biz_info from emr_super_doct_check A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_206;
        
        -- 删除临时表
        DROP TABLE #temp_result_206;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 207 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_sympt_rec';
    SET @v_rule_no = N'YJ05202507162384';
    SET @v_rule_desc = N'查询 #207 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_207') IS NOT NULL
            DROP TABLE #temp_result_207;
            
        CREATE TABLE #temp_result_207 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_207
        select max(org_name) as org_name,max(uscid) as uscid,'emr_sympt_rec' as tabname ,'YJ05202507162384' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_sympt_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_207;
        
        -- 删除临时表
        DROP TABLE #temp_result_207;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 208 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_trans_depart';
    SET @v_rule_no = N'YJ05202507162385';
    SET @v_rule_desc = N'查询 #208 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_208') IS NOT NULL
            DROP TABLE #temp_result_208;
            
        CREATE TABLE #temp_result_208 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_208
        select max(org_name) as org_name,max(uscid) as uscid,'emr_trans_depart' as tabname ,'YJ05202507162385' as rule_no,'最大业务日期' as rule_dscr, max(A.adm_time) as biz_info from emr_trans_depart A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_208;
        
        -- 删除临时表
        DROP TABLE #temp_result_208;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 209 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_treat_rec';
    SET @v_rule_no = N'YJ05202507162386';
    SET @v_rule_desc = N'查询 #209 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_209') IS NOT NULL
            DROP TABLE #temp_result_209;
            
        CREATE TABLE #temp_result_209 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_209
        select max(org_name) as org_name,max(uscid) as uscid,'emr_treat_rec' as tabname ,'YJ05202507162386' as rule_no,'最大业务日期' as rule_dscr, max(A.rec_time) as biz_info from emr_treat_rec A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_209;
        
        -- 删除临时表
        DROP TABLE #temp_result_209;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 210 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver';
    SET @v_rule_no = N'YJ05202507162387';
    SET @v_rule_desc = N'查询 #210 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_210') IS NOT NULL
            DROP TABLE #temp_result_210;
            
        CREATE TABLE #temp_result_210 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_210
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver' as tabname ,'YJ05202507162387' as rule_no,'最大业务日期' as rule_dscr, max(A.expect_deliver_date) as biz_info from emr_vagina_deliver A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_210;
        
        -- 删除临时表
        DROP TABLE #temp_result_210;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 211 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_nwb';
    SET @v_rule_no = N'YJ05202507162388';
    SET @v_rule_desc = N'查询 #211 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_211') IS NOT NULL
            DROP TABLE #temp_result_211;
            
        CREATE TABLE #temp_result_211 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_211
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_nwb' as tabname ,'YJ05202507162388' as rule_no,'最大业务日期' as rule_dscr, max(A.nwb_brdy_time) as biz_info from emr_vagina_deliver_nwb A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_211;
        
        -- 删除临时表
        DROP TABLE #temp_result_211;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 212 条质控规则-sqlserver2017
    SET @v_tabname = N'emr_vagina_deliver_observ';
    SET @v_rule_no = N'YJ05202507162389';
    SET @v_rule_desc = N'查询 #212 - 未找到描述';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_212') IS NOT NULL
            DROP TABLE #temp_result_212;
            
        CREATE TABLE #temp_result_212 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_212
        select max(org_name) as org_name,max(uscid) as uscid,'emr_vagina_deliver_observ' as tabname ,'YJ05202507162389' as rule_no,'最大业务日期' as rule_dscr, max(A.postpar_observ_time) as biz_info from emr_vagina_deliver_observ A;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_212;
        
        -- 删除临时表
        DROP TABLE #temp_result_212;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

END;

GO

-- 执行存储过程
EXEC [dbo].[execute_quality_control_queries];

-- 显示结果
SELECT * FROM [dbo].[quality_control_log] ORDER BY [id];
