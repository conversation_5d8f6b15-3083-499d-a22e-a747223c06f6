-- 质控执行脚本 - 自动生成 (GaussDB 5.0版)
-- 生成时间: 2025-07-14 09:41:52

-- 删除可能存在的日志表和函数
DROP TABLE IF EXISTS quality_control_log CASCADE;
DROP FUNCTION IF EXISTS execute_quality_control_queries();

-- 创建日志表
CREATE TABLE quality_control_log (
    id SERIAL PRIMARY KEY,
    rule_no VARCHAR(50),
    tabname VARCHAR(100),
    rule_desc TEXT,
    dty_count INTEGER,
    error_desc TEXT,
    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建函数，用于执行质控查询
CREATE OR REPLACE FUNCTION execute_quality_control_queries() RETURNS VOID AS $$
DECLARE
    v_tabname VARCHAR(100);
    v_rule_no VARCHAR(50);
    v_rule_desc TEXT;
    v_dty_count INTEGER;
    v_error_message TEXT;
    v_error RECORD;
BEGIN

    -- 执行第 1 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177078';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的数据唯一记录号（rid）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177078' AS rule_no,'患者基本信息表（patient_basic_info）中的数据唯一记录号（rid）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.rid) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 2 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177070';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的医疗机构名称（org_name）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177070' AS rule_no,'患者基本信息表（patient_basic_info）中的医疗机构名称（org_name）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.org_name) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 3 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177082';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的医疗机构统一社会信用代码（uscid）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177082' AS rule_no,'患者基本信息表（patient_basic_info）中的医疗机构统一社会信用代码（uscid）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.uscid) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 4 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177081';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的数据上传时间（upload_time）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177081' AS rule_no,'患者基本信息表（patient_basic_info）中的数据上传时间（upload_time）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and A.upload_time is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 5 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177079';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的系统建设厂商代码（sys_prdr_code）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177079' AS rule_no,'患者基本信息表（patient_basic_info）中的系统建设厂商代码（sys_prdr_code）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.sys_prdr_code) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 6 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177080';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的系统建设厂商名称（sys_prdr_name）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177080' AS rule_no,'患者基本信息表（patient_basic_info）中的系统建设厂商名称（sys_prdr_name）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.sys_prdr_name) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 7 条质控规则-guass5.0
    v_tabname := 'patient_basic_info';
    v_rule_no := 'YD01202506177056';
    v_rule_desc := '患者基本信息表（patient_basic_info）中的注册时间（enroll_time）不能为空';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT 'patient_basic_info' AS tabname,'YD01202506177056' AS rule_no,'患者基本信息表（patient_basic_info）中的注册时间（enroll_time）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and A.enroll_time is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

END;
$$ LANGUAGE plpgsql;

-- 执行函数
SELECT execute_quality_control_queries();

-- 显示结果
SELECT * FROM quality_control_log ORDER BY id;
