-- 质控执行脚本 - 自动生成 (SQL Server 2017版)
-- 生成时间: 2025-07-14 10:21:19

-- 删除可能存在的日志表和存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[quality_control_log]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[quality_control_log]
END;

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[execute_quality_control_queries]') AND type in (N'P', N'PC'))
BEGIN
    DROP PROCEDURE [dbo].[execute_quality_control_queries]
END;

-- 创建日志表
CREATE TABLE [dbo].[quality_control_log] (
    [id] INT IDENTITY(1,1) PRIMARY KEY,
    [rule_no] NVARCHAR(50),
    [tabname] NVARCHAR(100),
    [rule_desc] NVARCHAR(MAX),
    [dty_count] INT,
    [error_desc] NVARCHAR(MAX),
    [execution_timestamp] DATETIME DEFAULT GETDATE()
);

GO

-- 创建存储过程，用于执行质控查询
CREATE PROCEDURE [dbo].[execute_quality_control_queries]
AS
BEGIN
    -- 设置错误处理选项
    SET NOCOUNT ON;
    
    -- 声明变量
    DECLARE @v_tabname NVARCHAR(100);
    DECLARE @v_rule_no NVARCHAR(50);
    DECLARE @v_rule_desc NVARCHAR(MAX);
    DECLARE @v_dty_count INT;
    DECLARE @v_error_message NVARCHAR(4000);
    DECLARE @v_error_number INT;
    DECLARE @v_error_severity INT;
    DECLARE @v_error_state INT;
    DECLARE @v_error_line INT;
    DECLARE @v_error_proc NVARCHAR(200);
    

    -- 执行第 1 条质控规则-sqlserver2017
    SET @v_tabname = N'patient_basic_info';
    SET @v_rule_no = N'YD01202506177078';
    SET @v_rule_desc = N'患者基本信息表（patient_basic_info）中的数据唯一记录号（rid）不能为空';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_1') IS NOT NULL
            DROP TABLE #temp_result_1;
            
        CREATE TABLE #temp_result_1 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_1
        SELECT 'patient_basic_info' AS tabname,'YD01202506177078' AS rule_no,'患者基本信息表（patient_basic_info）中的数据唯一记录号（rid）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.rid) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_1;
        
        -- 删除临时表
        DROP TABLE #temp_result_1;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

    -- 执行第 2 条质控规则-sqlserver2017
    SET @v_tabname = N'patient_basic_info';
    SET @v_rule_no = N'YD01202506177070';
    SET @v_rule_desc = N'患者基本信息表（patient_basic_info）中的医疗机构名称（org_name）不能为空';
    
    -- 执行查询，使用TRY-CATCH进行错误处理
    BEGIN TRY
        -- 创建临时表来存储查询结果
        IF OBJECT_ID('tempdb..#temp_result_2') IS NOT NULL
            DROP TABLE #temp_result_2;
            
        CREATE TABLE #temp_result_2 (
            tabname NVARCHAR(100),
            rule_no NVARCHAR(50),
            rule_desc NVARCHAR(MAX),
            dty_count INT
        );
        
        -- 执行查询并将结果插入临时表
        INSERT INTO #temp_result_2
        SELECT 'patient_basic_info' AS tabname,'YD01202506177070' AS rule_no,'患者基本信息表（patient_basic_info）中的医疗机构名称（org_name）不能为空' AS rule_desc ,COUNT(1) AS dty_count FROM patient_basic_info A where 1=1 and trim(A.org_name) is null;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM #temp_result_2;
        
        -- 删除临时表
        DROP TABLE #temp_result_2;
    END TRY
    BEGIN CATCH
        -- 获取错误信息
        SET @v_error_number = ERROR_NUMBER();
        SET @v_error_severity = ERROR_SEVERITY();
        SET @v_error_state = ERROR_STATE();
        SET @v_error_line = ERROR_LINE();
        SET @v_error_proc = ERROR_PROCEDURE();
        SET @v_error_message = N'ERROR ' + CAST(@v_error_number AS NVARCHAR(10)) + 
                              N': ' + ERROR_MESSAGE() + 
                              N' (Procedure: ' + ISNULL(@v_error_proc, N'N/A') + 
                              N', Line: ' + CAST(@v_error_line AS NVARCHAR(10)) + N')';
        
        -- 记录错误信息
        INSERT INTO [dbo].[quality_control_log] ([rule_no], [tabname], [rule_desc], [dty_count], [error_desc])
        VALUES (@v_rule_no, @v_tabname, @v_rule_desc, -1, @v_error_message);
    END CATCH;

END;

GO

-- 执行存储过程
EXEC [dbo].[execute_quality_control_queries];

-- 显示结果
SELECT * FROM [dbo].[quality_control_log] ORDER BY [id];
