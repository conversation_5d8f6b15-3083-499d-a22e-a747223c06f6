-- 质控执行脚本 - 自动生成 (Oracle 11g版)
-- 生成时间: 2025-07-25 20:32:54

-- 删除可能存在的日志表
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE quality_control_log';
EXCEPTION
  WHEN OTHERS THEN
    IF SQLCODE != -942 THEN
      RAISE;
    END IF;
END;
/

-- 创建序列用于自增ID
BEGIN
  EXECUTE IMMEDIATE 'DROP SEQUENCE quality_control_log_seq';
EXCEPTION
  WHEN OTHERS THEN
    IF SQLCODE != -2289 THEN
      RAISE;
    END IF;
END;
/

CREATE SEQUENCE quality_control_log_seq
  START WITH 1
  INCREMENT BY 1
  NOCACHE
  NOCYCLE;
/

-- 创建日志表
CREATE TABLE quality_control_log (
    id NUMBER PRIMARY KEY,
    rule_no VARCHAR2(50),
    tabname VARCHAR2(100),
    rule_desc CLOB,
    dty_count NUMBER,
    error_desc CLOB,
    execution_timestamp TIMESTAMP DEFAULT SYSTIMESTAMP
);
/

-- 创建触发器自动填充ID
CREATE OR REPLACE TRIGGER quality_control_log_bir 
BEFORE INSERT ON quality_control_log 
FOR EACH ROW
BEGIN
  SELECT quality_control_log_seq.NEXTVAL
  INTO :new.id
  FROM dual;
END;
/

-- 创建全局临时表存储查询结果
BEGIN
  EXECUTE IMMEDIATE 'DROP TABLE temp_result';
EXCEPTION
  WHEN OTHERS THEN
    IF SQLCODE != -942 THEN
      RAISE;
    END IF;
END;
/

CREATE GLOBAL TEMPORARY TABLE temp_result (
    tabname VARCHAR2(100),
    rule_no VARCHAR2(50),
    rule_desc CLOB,
    dty_count NUMBER
) ON COMMIT DELETE ROWS;
/

-- 创建存储过程，用于执行质控查询
CREATE OR REPLACE PROCEDURE execute_quality_control AS
    -- 声明变量
    v_tabname VARCHAR2(100);
    v_rule_no VARCHAR2(50);
    v_rule_desc CLOB;
    v_dty_count NUMBER;
    v_error_text CLOB;
    v_error_message VARCHAR2(4000);
    
BEGIN

    -- 执行第 1 条质控规则-oracle11g
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162141';
    v_rule_desc := '查询 #1 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''cis_lh_summary'' AS tabname,''YJ05202507162141'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM cis_lh_summary A WHERE A.dscg_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.dscg_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 2 条质控规则-oracle11g
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162142';
    v_rule_desc := '查询 #2 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_adm_rec'' AS tabname,''YJ05202507162142'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_adm_rec A WHERE A.adm_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.adm_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 3 条质控规则-oracle11g
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162143';
    v_rule_desc := '查询 #3 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_aft_anst'' AS tabname,''YJ05202507162143'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_aft_anst A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 4 条质控规则-oracle11g
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162144';
    v_rule_desc := '查询 #4 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_anst_informed'' AS tabname,''YJ05202507162144'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_anst_informed A WHERE A.plan_oprt_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.plan_oprt_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 5 条质控规则-oracle11g
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162145';
    v_rule_desc := '查询 #5 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_blood_informed'' AS tabname,''YJ05202507162145'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_blood_informed A WHERE A.plan_transfuse_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.plan_transfuse_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 6 条质控规则-oracle11g
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162146';
    v_rule_desc := '查询 #6 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_consult_detail'' AS tabname,''YJ05202507162146'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_consult_detail A WHERE A.consult_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.consult_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 7 条质控规则-oracle11g
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162147';
    v_rule_desc := '查询 #7 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_consult_info'' AS tabname,''YJ05202507162147'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_consult_info A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 8 条质控规则-oracle11g
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162148';
    v_rule_desc := '查询 #8 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_daily_dis_course'' AS tabname,''YJ05202507162148'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_daily_dis_course A WHERE A.sign_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.sign_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 9 条质控规则-oracle11g
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162149';
    v_rule_desc := '查询 #9 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_death_case_discu'' AS tabname,''YJ05202507162149'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_death_case_discu A WHERE A.discu_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.discu_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 10 条质控规则-oracle11g
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162150';
    v_rule_desc := '查询 #10 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_death_record'' AS tabname,''YJ05202507162150'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_death_record A WHERE A.death_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.death_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 11 条质控规则-oracle11g
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162151';
    v_rule_desc := '查询 #11 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_doc_detail'' AS tabname,''YJ05202507162151'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_doc_detail A WHERE A.file_create_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.file_create_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 12 条质控规则-oracle11g
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162152';
    v_rule_desc := '查询 #12 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_doc_rec'' AS tabname,''YJ05202507162152'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_doc_rec A WHERE A.doc_create_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.doc_create_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 13 条质控规则-oracle11g
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162154';
    v_rule_desc := '查询 #13 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_first_postop_course'' AS tabname,''YJ05202507162154'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_first_postop_course A WHERE A.oprn_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.oprn_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 14 条质控规则-oracle11g
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162155';
    v_rule_desc := '查询 #14 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_hard_case_discu'' AS tabname,''YJ05202507162155'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_hard_case_discu A WHERE A.discu_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.discu_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 15 条质控规则-oracle11g
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162156';
    v_rule_desc := '查询 #15 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_informed'' AS tabname,''YJ05202507162156'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_heavy_informed A WHERE A.dying_inform_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.dying_inform_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 16 条质控规则-oracle11g
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162157';
    v_rule_desc := '查询 #16 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_nurse'' AS tabname,''YJ05202507162157'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_heavy_nurse A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 17 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162158';
    v_rule_desc := '查询 #17 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_assess'' AS tabname,''YJ05202507162158'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_inhosp_assess A WHERE A.eval_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.eval_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 18 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162159';
    v_rule_desc := '查询 #18 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_die_in24h'' AS tabname,''YJ05202507162159'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_inhosp_die_in24h A WHERE A.adm_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.adm_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 19 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162160';
    v_rule_desc := '查询 #19 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec'' AS tabname,''YJ05202507162160'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_inout_rec A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 20 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162161';
    v_rule_desc := '查询 #20 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec_in24h'' AS tabname,''YJ05202507162161'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_inout_rec_in24h A WHERE A.adm_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.adm_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 21 条质控规则-oracle11g
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162162';
    v_rule_desc := '查询 #21 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_inout_usedrug'' AS tabname,''YJ05202507162162'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_inout_usedrug A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 22 条质控规则-oracle11g
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162163';
    v_rule_desc := '查询 #22 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_labor'' AS tabname,''YJ05202507162163'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_labor A WHERE A.labor_rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.labor_rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 23 条质控规则-oracle11g
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162164';
    v_rule_desc := '查询 #23 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_nurse'' AS tabname,''YJ05202507162164'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_nurse A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 24 条质控规则-oracle11g
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162165';
    v_rule_desc := '查询 #24 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_nurse_plan'' AS tabname,''YJ05202507162165'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_nurse_plan A WHERE A.sign_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.sign_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 25 条质控规则-oracle11g
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162166';
    v_rule_desc := '查询 #25 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_observmedi'' AS tabname,''YJ05202507162166'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_observmedi A WHERE A.observe_room_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.observe_room_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 26 条质控规则-oracle11g
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162167';
    v_rule_desc := '查询 #26 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_observoprn'' AS tabname,''YJ05202507162167'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_observoprn A WHERE A.oprn_oprt_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.oprn_oprt_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 27 条质控规则-oracle11g
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162168';
    v_rule_desc := '查询 #27 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_opr_informed'' AS tabname,''YJ05202507162168'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_opr_informed A WHERE A.plan_oprt_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.plan_oprt_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 28 条质控规则-oracle11g
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162169';
    v_rule_desc := '查询 #28 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_opr_nurse'' AS tabname,''YJ05202507162169'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_opr_nurse A WHERE A.oprn_begin_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.oprn_begin_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 29 条质控规则-oracle11g
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162170';
    v_rule_desc := '查询 #29 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_opr_rec'' AS tabname,''YJ05202507162170'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_opr_rec A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 30 条质控规则-oracle11g
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162171';
    v_rule_desc := '查询 #30 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_oth_informed'' AS tabname,''YJ05202507162171'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_oth_informed A WHERE A.doc_sign_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.doc_sign_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 31 条质控规则-oracle11g
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162172';
    v_rule_desc := '查询 #31 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_otpmedi'' AS tabname,''YJ05202507162172'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_otpmedi A WHERE A.mdtrt_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.mdtrt_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 32 条质控规则-oracle11g
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162173';
    v_rule_desc := '查询 #32 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp'' AS tabname,''YJ05202507162173'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_outhosp A WHERE A.adm_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.adm_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 33 条质控规则-oracle11g
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162174';
    v_rule_desc := '查询 #33 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp_assess'' AS tabname,''YJ05202507162174'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_outhosp_assess A WHERE A.dscg_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.dscg_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 34 条质控规则-oracle11g
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162175';
    v_rule_desc := '查询 #34 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_periodic_sum'' AS tabname,''YJ05202507162175'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_periodic_sum A WHERE A.sum_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.sum_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 35 条质控规则-oracle11g
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162176';
    v_rule_desc := '查询 #35 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_pre_anst'' AS tabname,''YJ05202507162176'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_pre_anst A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 36 条质控规则-oracle11g
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162177';
    v_rule_desc := '查询 #36 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_predelivery'' AS tabname,''YJ05202507162177'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_predelivery A WHERE A.expectant_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.expectant_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 37 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162178';
    v_rule_desc := '查询 #37 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy'' AS tabname,''YJ05202507162178'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_pregnancy A WHERE A.oprn_end_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.oprn_end_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 38 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162179';
    v_rule_desc := '查询 #38 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_nwb'' AS tabname,''YJ05202507162179'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_pregnancy_nwb A WHERE A.nwb_brdy_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.nwb_brdy_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 39 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162180';
    v_rule_desc := '查询 #39 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_observ'' AS tabname,''YJ05202507162180'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_pregnancy_observ A WHERE A.postpar_observ_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.postpar_observ_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 40 条质控规则-oracle11g
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162181';
    v_rule_desc := '查询 #40 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_preop_discu'' AS tabname,''YJ05202507162181'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_preop_discu A WHERE A.discu_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.discu_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 41 条质控规则-oracle11g
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162182';
    v_rule_desc := '查询 #41 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_preop_sum'' AS tabname,''YJ05202507162182'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_preop_sum A WHERE A.sum_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.sum_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 42 条质控规则-oracle11g
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162183';
    v_rule_desc := '查询 #42 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_resc_rec'' AS tabname,''YJ05202507162183'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_resc_rec A WHERE A.resc_endtime >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.resc_endtime, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 43 条质控规则-oracle11g
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162184';
    v_rule_desc := '查询 #43 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_rescue'' AS tabname,''YJ05202507162184'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_rescue A WHERE A.sign_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.sign_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 44 条质控规则-oracle11g
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162185';
    v_rule_desc := '查询 #44 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_rreat_drug'' AS tabname,''YJ05202507162185'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_rreat_drug A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 45 条质控规则-oracle11g
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162186';
    v_rule_desc := '查询 #45 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_shift_change'' AS tabname,''YJ05202507162186'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_shift_change A WHERE A.shift_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.shift_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 46 条质控规则-oracle11g
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162187';
    v_rule_desc := '查询 #46 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_spe_informed'' AS tabname,''YJ05202507162187'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_spe_informed A WHERE A.doc_sign_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.doc_sign_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 47 条质控规则-oracle11g
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162188';
    v_rule_desc := '查询 #47 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_super_doct_check'' AS tabname,''YJ05202507162188'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_super_doct_check A WHERE A.check_room_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.check_room_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 48 条质控规则-oracle11g
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162189';
    v_rule_desc := '查询 #48 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_sympt_rec'' AS tabname,''YJ05202507162189'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_sympt_rec A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 49 条质控规则-oracle11g
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162191';
    v_rule_desc := '查询 #49 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_treat_rec'' AS tabname,''YJ05202507162191'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_treat_rec A WHERE A.rec_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.rec_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 50 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162192';
    v_rule_desc := '查询 #50 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver'' AS tabname,''YJ05202507162192'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_vagina_deliver A WHERE A.expect_deliver_date >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.expect_deliver_date, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 51 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162193';
    v_rule_desc := '查询 #51 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_nwb'' AS tabname,''YJ05202507162193'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_vagina_deliver_nwb A WHERE A.nwb_brdy_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.nwb_brdy_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 52 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162194';
    v_rule_desc := '查询 #52 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'SELECT max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_observ'' AS tabname,''YJ05202507162194'' AS RULE_NO, ''数据不满足5年和月连续性(缺失月数)'' AS RULE_DSCR, FLOOR(MONTHS_BETWEEN(SYSDATE, TO_DATE(''2020-01-01'', ''YYYY-MM-DD''))) - COUNT(1) AS BIZ_INFO FROM (SELECT max(org_name) as org_name,max(uscid) as uscid FROM emr_vagina_deliver_observ A WHERE A.postpar_observ_time >= TO_DATE(''2020-01-01'',''YYYY-MM-DD'') GROUP BY TO_CHAR(A.postpar_observ_time, ''YYYYMM'')) T;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 53 条质控规则-oracle11g
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162206';
    v_rule_desc := '查询 #53 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''cis_lh_summary'' as tabname ,''YJ05202507162206'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from cis_lh_summary A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 54 条质控规则-oracle11g
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162207';
    v_rule_desc := '查询 #54 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_adm_rec'' as tabname ,''YJ05202507162207'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_adm_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 55 条质控规则-oracle11g
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162208';
    v_rule_desc := '查询 #55 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_aft_anst'' as tabname ,''YJ05202507162208'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_aft_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 56 条质控规则-oracle11g
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162209';
    v_rule_desc := '查询 #56 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_anst_informed'' as tabname ,''YJ05202507162209'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_anst_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 57 条质控规则-oracle11g
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162210';
    v_rule_desc := '查询 #57 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_blood_informed'' as tabname ,''YJ05202507162210'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_blood_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 58 条质控规则-oracle11g
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162211';
    v_rule_desc := '查询 #58 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_detail'' as tabname ,''YJ05202507162211'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_consult_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 59 条质控规则-oracle11g
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162212';
    v_rule_desc := '查询 #59 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_info'' as tabname ,''YJ05202507162212'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_consult_info A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 60 条质控规则-oracle11g
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162213';
    v_rule_desc := '查询 #60 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_daily_dis_course'' as tabname ,''YJ05202507162213'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_daily_dis_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 61 条质控规则-oracle11g
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162214';
    v_rule_desc := '查询 #61 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_case_discu'' as tabname ,''YJ05202507162214'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_death_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 62 条质控规则-oracle11g
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162215';
    v_rule_desc := '查询 #62 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_record'' as tabname ,''YJ05202507162215'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_death_record A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 63 条质控规则-oracle11g
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162216';
    v_rule_desc := '查询 #63 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_detail'' as tabname ,''YJ05202507162216'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_doc_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 64 条质控规则-oracle11g
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162217';
    v_rule_desc := '查询 #64 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_rec'' as tabname ,''YJ05202507162217'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_doc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 65 条质控规则-oracle11g
    v_tabname := 'emr_first_dis_course';
    v_rule_no := 'YJ05202507162218';
    v_rule_desc := '查询 #65 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_first_dis_course'' as tabname ,''YJ05202507162218'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_first_dis_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 66 条质控规则-oracle11g
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162219';
    v_rule_desc := '查询 #66 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_first_postop_course'' as tabname ,''YJ05202507162219'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_first_postop_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 67 条质控规则-oracle11g
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162220';
    v_rule_desc := '查询 #67 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_hard_case_discu'' as tabname ,''YJ05202507162220'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_hard_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 68 条质控规则-oracle11g
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162221';
    v_rule_desc := '查询 #68 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_informed'' as tabname ,''YJ05202507162221'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_heavy_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 69 条质控规则-oracle11g
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162222';
    v_rule_desc := '查询 #69 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_nurse'' as tabname ,''YJ05202507162222'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_heavy_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 70 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162223';
    v_rule_desc := '查询 #70 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_assess'' as tabname ,''YJ05202507162223'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_inhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 71 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162224';
    v_rule_desc := '查询 #71 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_die_in24h'' as tabname ,''YJ05202507162224'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_inhosp_die_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 72 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162225';
    v_rule_desc := '查询 #72 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec'' as tabname ,''YJ05202507162225'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_inout_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 73 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162226';
    v_rule_desc := '查询 #73 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec_in24h'' as tabname ,''YJ05202507162226'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_inout_rec_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 74 条质控规则-oracle11g
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162227';
    v_rule_desc := '查询 #74 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_usedrug'' as tabname ,''YJ05202507162227'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_inout_usedrug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 75 条质控规则-oracle11g
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162228';
    v_rule_desc := '查询 #75 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_labor'' as tabname ,''YJ05202507162228'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_labor A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 76 条质控规则-oracle11g
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162229';
    v_rule_desc := '查询 #76 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse'' as tabname ,''YJ05202507162229'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 77 条质控规则-oracle11g
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162230';
    v_rule_desc := '查询 #77 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse_plan'' as tabname ,''YJ05202507162230'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_nurse_plan A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 78 条质控规则-oracle11g
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162231';
    v_rule_desc := '查询 #78 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observmedi'' as tabname ,''YJ05202507162231'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_observmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 79 条质控规则-oracle11g
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162232';
    v_rule_desc := '查询 #79 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observoprn'' as tabname ,''YJ05202507162232'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_observoprn A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 80 条质控规则-oracle11g
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162233';
    v_rule_desc := '查询 #80 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_informed'' as tabname ,''YJ05202507162233'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_opr_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 81 条质控规则-oracle11g
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162234';
    v_rule_desc := '查询 #81 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_nurse'' as tabname ,''YJ05202507162234'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_opr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 82 条质控规则-oracle11g
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162235';
    v_rule_desc := '查询 #82 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_rec'' as tabname ,''YJ05202507162235'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_opr_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 83 条质控规则-oracle11g
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162236';
    v_rule_desc := '查询 #83 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_oth_informed'' as tabname ,''YJ05202507162236'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_oth_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 84 条质控规则-oracle11g
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162237';
    v_rule_desc := '查询 #84 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_otpmedi'' as tabname ,''YJ05202507162237'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_otpmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 85 条质控规则-oracle11g
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162238';
    v_rule_desc := '查询 #85 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp'' as tabname ,''YJ05202507162238'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_outhosp A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 86 条质控规则-oracle11g
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162239';
    v_rule_desc := '查询 #86 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp_assess'' as tabname ,''YJ05202507162239'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_outhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 87 条质控规则-oracle11g
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162240';
    v_rule_desc := '查询 #87 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_periodic_sum'' as tabname ,''YJ05202507162240'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_periodic_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 88 条质控规则-oracle11g
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162241';
    v_rule_desc := '查询 #88 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pre_anst'' as tabname ,''YJ05202507162241'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_pre_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 89 条质控规则-oracle11g
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162242';
    v_rule_desc := '查询 #89 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_predelivery'' as tabname ,''YJ05202507162242'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_predelivery A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 90 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162243';
    v_rule_desc := '查询 #90 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy'' as tabname ,''YJ05202507162243'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_pregnancy A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 91 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162244';
    v_rule_desc := '查询 #91 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_nwb'' as tabname ,''YJ05202507162244'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_pregnancy_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 92 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162245';
    v_rule_desc := '查询 #92 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_observ'' as tabname ,''YJ05202507162245'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_pregnancy_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 93 条质控规则-oracle11g
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162246';
    v_rule_desc := '查询 #93 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_discu'' as tabname ,''YJ05202507162246'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_preop_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 94 条质控规则-oracle11g
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162247';
    v_rule_desc := '查询 #94 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_sum'' as tabname ,''YJ05202507162247'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_preop_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 95 条质控规则-oracle11g
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162248';
    v_rule_desc := '查询 #95 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_resc_rec'' as tabname ,''YJ05202507162248'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_resc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 96 条质控规则-oracle11g
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162249';
    v_rule_desc := '查询 #96 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rescue'' as tabname ,''YJ05202507162249'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_rescue A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 97 条质控规则-oracle11g
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162250';
    v_rule_desc := '查询 #97 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rreat_drug'' as tabname ,''YJ05202507162250'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_rreat_drug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 98 条质控规则-oracle11g
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162251';
    v_rule_desc := '查询 #98 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_shift_change'' as tabname ,''YJ05202507162251'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_shift_change A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 99 条质控规则-oracle11g
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162252';
    v_rule_desc := '查询 #99 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_spe_informed'' as tabname ,''YJ05202507162252'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_spe_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 100 条质控规则-oracle11g
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162253';
    v_rule_desc := '查询 #100 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_super_doct_check'' as tabname ,''YJ05202507162253'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_super_doct_check A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 101 条质控规则-oracle11g
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162254';
    v_rule_desc := '查询 #101 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_sympt_rec'' as tabname ,''YJ05202507162254'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_sympt_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 102 条质控规则-oracle11g
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162255';
    v_rule_desc := '查询 #102 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_trans_depart'' as tabname ,''YJ05202507162255'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_trans_depart A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 103 条质控规则-oracle11g
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162256';
    v_rule_desc := '查询 #103 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_treat_rec'' as tabname ,''YJ05202507162256'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_treat_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 104 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162257';
    v_rule_desc := '查询 #104 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver'' as tabname ,''YJ05202507162257'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_vagina_deliver A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 105 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162258';
    v_rule_desc := '查询 #105 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_nwb'' as tabname ,''YJ05202507162258'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_vagina_deliver_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 106 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162259';
    v_rule_desc := '查询 #106 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_observ'' as tabname ,''YJ05202507162259'' as rule_no,''表数据量统计'' as rule_dscr, count(1) as biz_info from emr_vagina_deliver_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 107 条质控规则-oracle11g
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162271';
    v_rule_desc := '查询 #107 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''cis_lh_summary'' as tabname ,''YJ05202507162271'' as rule_no,''最小业务日期'' as rule_dscr, min(A.dscg_time) as biz_info from cis_lh_summary A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 108 条质控规则-oracle11g
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162272';
    v_rule_desc := '查询 #108 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_adm_rec'' as tabname ,''YJ05202507162272'' as rule_no,''最小业务日期'' as rule_dscr, min(A.adm_time) as biz_info from emr_adm_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 109 条质控规则-oracle11g
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162273';
    v_rule_desc := '查询 #109 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_aft_anst'' as tabname ,''YJ05202507162273'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_aft_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 110 条质控规则-oracle11g
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162274';
    v_rule_desc := '查询 #110 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_anst_informed'' as tabname ,''YJ05202507162274'' as rule_no,''最小业务日期'' as rule_dscr, min(A.plan_oprt_time) as biz_info from emr_anst_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 111 条质控规则-oracle11g
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162275';
    v_rule_desc := '查询 #111 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_blood_informed'' as tabname ,''YJ05202507162275'' as rule_no,''最小业务日期'' as rule_dscr, min(A.plan_transfuse_time) as biz_info from emr_blood_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 112 条质控规则-oracle11g
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162276';
    v_rule_desc := '查询 #112 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_detail'' as tabname ,''YJ05202507162276'' as rule_no,''最小业务日期'' as rule_dscr, min(A.consult_time) as biz_info from emr_consult_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 113 条质控规则-oracle11g
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162277';
    v_rule_desc := '查询 #113 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_info'' as tabname ,''YJ05202507162277'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_consult_info A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 114 条质控规则-oracle11g
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162278';
    v_rule_desc := '查询 #114 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_daily_dis_course'' as tabname ,''YJ05202507162278'' as rule_no,''最小业务日期'' as rule_dscr, min(A.sign_time) as biz_info from emr_daily_dis_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 115 条质控规则-oracle11g
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162279';
    v_rule_desc := '查询 #115 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_case_discu'' as tabname ,''YJ05202507162279'' as rule_no,''最小业务日期'' as rule_dscr, min(A.discu_time) as biz_info from emr_death_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 116 条质控规则-oracle11g
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162280';
    v_rule_desc := '查询 #116 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_record'' as tabname ,''YJ05202507162280'' as rule_no,''最小业务日期'' as rule_dscr, min(A.death_time) as biz_info from emr_death_record A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 117 条质控规则-oracle11g
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162281';
    v_rule_desc := '查询 #117 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_detail'' as tabname ,''YJ05202507162281'' as rule_no,''最小业务日期'' as rule_dscr, min(A.file_create_time) as biz_info from emr_doc_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 118 条质控规则-oracle11g
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162282';
    v_rule_desc := '查询 #118 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_rec'' as tabname ,''YJ05202507162282'' as rule_no,''最小业务日期'' as rule_dscr, min(A.doc_create_time) as biz_info from emr_doc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 119 条质控规则-oracle11g
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162284';
    v_rule_desc := '查询 #119 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_first_postop_course'' as tabname ,''YJ05202507162284'' as rule_no,''最小业务日期'' as rule_dscr, min(A.oprn_time) as biz_info from emr_first_postop_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 120 条质控规则-oracle11g
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162285';
    v_rule_desc := '查询 #120 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_hard_case_discu'' as tabname ,''YJ05202507162285'' as rule_no,''最小业务日期'' as rule_dscr, min(A.discu_time) as biz_info from emr_hard_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 121 条质控规则-oracle11g
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162286';
    v_rule_desc := '查询 #121 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_informed'' as tabname ,''YJ05202507162286'' as rule_no,''最小业务日期'' as rule_dscr, min(A.dying_inform_time) as biz_info from emr_heavy_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 122 条质控规则-oracle11g
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162287';
    v_rule_desc := '查询 #122 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_nurse'' as tabname ,''YJ05202507162287'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_heavy_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 123 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162288';
    v_rule_desc := '查询 #123 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_assess'' as tabname ,''YJ05202507162288'' as rule_no,''最小业务日期'' as rule_dscr, min(A.eval_time) as biz_info from emr_inhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 124 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162289';
    v_rule_desc := '查询 #124 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_die_in24h'' as tabname ,''YJ05202507162289'' as rule_no,''最小业务日期'' as rule_dscr, min(A.adm_time) as biz_info from emr_inhosp_die_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 125 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162290';
    v_rule_desc := '查询 #125 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec'' as tabname ,''YJ05202507162290'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_inout_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 126 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162291';
    v_rule_desc := '查询 #126 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec_in24h'' as tabname ,''YJ05202507162291'' as rule_no,''最小业务日期'' as rule_dscr, min(A.adm_time) as biz_info from emr_inout_rec_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 127 条质控规则-oracle11g
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162292';
    v_rule_desc := '查询 #127 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_usedrug'' as tabname ,''YJ05202507162292'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_inout_usedrug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 128 条质控规则-oracle11g
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162293';
    v_rule_desc := '查询 #128 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_labor'' as tabname ,''YJ05202507162293'' as rule_no,''最小业务日期'' as rule_dscr, min(A.labor_rec_time) as biz_info from emr_labor A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 129 条质控规则-oracle11g
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162294';
    v_rule_desc := '查询 #129 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse'' as tabname ,''YJ05202507162294'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 130 条质控规则-oracle11g
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162295';
    v_rule_desc := '查询 #130 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse_plan'' as tabname ,''YJ05202507162295'' as rule_no,''最小业务日期'' as rule_dscr, min(A.sign_time) as biz_info from emr_nurse_plan A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 131 条质控规则-oracle11g
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162296';
    v_rule_desc := '查询 #131 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observmedi'' as tabname ,''YJ05202507162296'' as rule_no,''最小业务日期'' as rule_dscr, min(A.observe_room_time) as biz_info from emr_observmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 132 条质控规则-oracle11g
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162297';
    v_rule_desc := '查询 #132 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observoprn'' as tabname ,''YJ05202507162297'' as rule_no,''最小业务日期'' as rule_dscr, min(A.oprn_oprt_time) as biz_info from emr_observoprn A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 133 条质控规则-oracle11g
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162298';
    v_rule_desc := '查询 #133 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_informed'' as tabname ,''YJ05202507162298'' as rule_no,''最小业务日期'' as rule_dscr, min(A.plan_oprt_time) as biz_info from emr_opr_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 134 条质控规则-oracle11g
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162299';
    v_rule_desc := '查询 #134 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_nurse'' as tabname ,''YJ05202507162299'' as rule_no,''最小业务日期'' as rule_dscr, min(A.oprn_begin_time) as biz_info from emr_opr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 135 条质控规则-oracle11g
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162300';
    v_rule_desc := '查询 #135 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_rec'' as tabname ,''YJ05202507162300'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_opr_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 136 条质控规则-oracle11g
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162301';
    v_rule_desc := '查询 #136 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_oth_informed'' as tabname ,''YJ05202507162301'' as rule_no,''最小业务日期'' as rule_dscr, min(A.doc_sign_time) as biz_info from emr_oth_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 137 条质控规则-oracle11g
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162302';
    v_rule_desc := '查询 #137 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_otpmedi'' as tabname ,''YJ05202507162302'' as rule_no,''最小业务日期'' as rule_dscr, min(A.mdtrt_time) as biz_info from emr_otpmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 138 条质控规则-oracle11g
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162303';
    v_rule_desc := '查询 #138 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp'' as tabname ,''YJ05202507162303'' as rule_no,''最小业务日期'' as rule_dscr, min(A.adm_time) as biz_info from emr_outhosp A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 139 条质控规则-oracle11g
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162304';
    v_rule_desc := '查询 #139 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp_assess'' as tabname ,''YJ05202507162304'' as rule_no,''最小业务日期'' as rule_dscr, min(A.dscg_time) as biz_info from emr_outhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 140 条质控规则-oracle11g
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162305';
    v_rule_desc := '查询 #140 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_periodic_sum'' as tabname ,''YJ05202507162305'' as rule_no,''最小业务日期'' as rule_dscr, min(A.sum_time) as biz_info from emr_periodic_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 141 条质控规则-oracle11g
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162306';
    v_rule_desc := '查询 #141 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pre_anst'' as tabname ,''YJ05202507162306'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_pre_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 142 条质控规则-oracle11g
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162307';
    v_rule_desc := '查询 #142 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_predelivery'' as tabname ,''YJ05202507162307'' as rule_no,''最小业务日期'' as rule_dscr, min(A.expectant_time) as biz_info from emr_predelivery A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 143 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162308';
    v_rule_desc := '查询 #143 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy'' as tabname ,''YJ05202507162308'' as rule_no,''最小业务日期'' as rule_dscr, min(A.oprn_end_time) as biz_info from emr_pregnancy A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 144 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162309';
    v_rule_desc := '查询 #144 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_nwb'' as tabname ,''YJ05202507162309'' as rule_no,''最小业务日期'' as rule_dscr, min(A.nwb_brdy_time) as biz_info from emr_pregnancy_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 145 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162310';
    v_rule_desc := '查询 #145 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_observ'' as tabname ,''YJ05202507162310'' as rule_no,''最小业务日期'' as rule_dscr, min(A.postpar_observ_time) as biz_info from emr_pregnancy_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 146 条质控规则-oracle11g
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162311';
    v_rule_desc := '查询 #146 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_discu'' as tabname ,''YJ05202507162311'' as rule_no,''最小业务日期'' as rule_dscr, min(A.discu_time) as biz_info from emr_preop_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 147 条质控规则-oracle11g
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162312';
    v_rule_desc := '查询 #147 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_sum'' as tabname ,''YJ05202507162312'' as rule_no,''最小业务日期'' as rule_dscr, min(A.sum_time) as biz_info from emr_preop_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 148 条质控规则-oracle11g
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162313';
    v_rule_desc := '查询 #148 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_resc_rec'' as tabname ,''YJ05202507162313'' as rule_no,''最小业务日期'' as rule_dscr, min(A.resc_endtime) as biz_info from emr_resc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 149 条质控规则-oracle11g
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162314';
    v_rule_desc := '查询 #149 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rescue'' as tabname ,''YJ05202507162314'' as rule_no,''最小业务日期'' as rule_dscr, min(A.sign_time) as biz_info from emr_rescue A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 150 条质控规则-oracle11g
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162315';
    v_rule_desc := '查询 #150 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rreat_drug'' as tabname ,''YJ05202507162315'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_rreat_drug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 151 条质控规则-oracle11g
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162316';
    v_rule_desc := '查询 #151 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_shift_change'' as tabname ,''YJ05202507162316'' as rule_no,''最小业务日期'' as rule_dscr, min(A.shift_time) as biz_info from emr_shift_change A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 152 条质控规则-oracle11g
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162317';
    v_rule_desc := '查询 #152 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_spe_informed'' as tabname ,''YJ05202507162317'' as rule_no,''最小业务日期'' as rule_dscr, min(A.doc_sign_time) as biz_info from emr_spe_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 153 条质控规则-oracle11g
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162318';
    v_rule_desc := '查询 #153 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_super_doct_check'' as tabname ,''YJ05202507162318'' as rule_no,''最小业务日期'' as rule_dscr, min(A.check_room_time) as biz_info from emr_super_doct_check A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 154 条质控规则-oracle11g
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162319';
    v_rule_desc := '查询 #154 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_sympt_rec'' as tabname ,''YJ05202507162319'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_sympt_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 155 条质控规则-oracle11g
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162320';
    v_rule_desc := '查询 #155 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_trans_depart'' as tabname ,''YJ05202507162320'' as rule_no,''最小业务日期'' as rule_dscr, min(A.adm_time) as biz_info from emr_trans_depart A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 156 条质控规则-oracle11g
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162321';
    v_rule_desc := '查询 #156 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_treat_rec'' as tabname ,''YJ05202507162321'' as rule_no,''最小业务日期'' as rule_dscr, min(A.rec_time) as biz_info from emr_treat_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 157 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162322';
    v_rule_desc := '查询 #157 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver'' as tabname ,''YJ05202507162322'' as rule_no,''最小业务日期'' as rule_dscr, min(A.expect_deliver_date) as biz_info from emr_vagina_deliver A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 158 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162323';
    v_rule_desc := '查询 #158 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_nwb'' as tabname ,''YJ05202507162323'' as rule_no,''最小业务日期'' as rule_dscr, min(A.nwb_brdy_time) as biz_info from emr_vagina_deliver_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 159 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162324';
    v_rule_desc := '查询 #159 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_observ'' as tabname ,''YJ05202507162324'' as rule_no,''最小业务日期'' as rule_dscr, min(A.postpar_observ_time) as biz_info from emr_vagina_deliver_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 160 条质控规则-oracle11g
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162336';
    v_rule_desc := '查询 #160 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''cis_lh_summary'' as tabname ,''YJ05202507162336'' as rule_no,''最大业务日期'' as rule_dscr, max(A.dscg_time) as biz_info from cis_lh_summary A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 161 条质控规则-oracle11g
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162337';
    v_rule_desc := '查询 #161 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_adm_rec'' as tabname ,''YJ05202507162337'' as rule_no,''最大业务日期'' as rule_dscr, max(A.adm_time) as biz_info from emr_adm_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 162 条质控规则-oracle11g
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162338';
    v_rule_desc := '查询 #162 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_aft_anst'' as tabname ,''YJ05202507162338'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_aft_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 163 条质控规则-oracle11g
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162339';
    v_rule_desc := '查询 #163 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_anst_informed'' as tabname ,''YJ05202507162339'' as rule_no,''最大业务日期'' as rule_dscr, max(A.plan_oprt_time) as biz_info from emr_anst_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 164 条质控规则-oracle11g
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162340';
    v_rule_desc := '查询 #164 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_blood_informed'' as tabname ,''YJ05202507162340'' as rule_no,''最大业务日期'' as rule_dscr, max(A.plan_transfuse_time) as biz_info from emr_blood_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 165 条质控规则-oracle11g
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162341';
    v_rule_desc := '查询 #165 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_detail'' as tabname ,''YJ05202507162341'' as rule_no,''最大业务日期'' as rule_dscr, max(A.consult_time) as biz_info from emr_consult_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 166 条质控规则-oracle11g
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162342';
    v_rule_desc := '查询 #166 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_consult_info'' as tabname ,''YJ05202507162342'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_consult_info A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 167 条质控规则-oracle11g
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162343';
    v_rule_desc := '查询 #167 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_daily_dis_course'' as tabname ,''YJ05202507162343'' as rule_no,''最大业务日期'' as rule_dscr, max(A.sign_time) as biz_info from emr_daily_dis_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 168 条质控规则-oracle11g
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162344';
    v_rule_desc := '查询 #168 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_case_discu'' as tabname ,''YJ05202507162344'' as rule_no,''最大业务日期'' as rule_dscr, max(A.discu_time) as biz_info from emr_death_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 169 条质控规则-oracle11g
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162345';
    v_rule_desc := '查询 #169 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_death_record'' as tabname ,''YJ05202507162345'' as rule_no,''最大业务日期'' as rule_dscr, max(A.death_time) as biz_info from emr_death_record A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 170 条质控规则-oracle11g
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162346';
    v_rule_desc := '查询 #170 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_detail'' as tabname ,''YJ05202507162346'' as rule_no,''最大业务日期'' as rule_dscr, max(A.file_create_time) as biz_info from emr_doc_detail A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 171 条质控规则-oracle11g
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162347';
    v_rule_desc := '查询 #171 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_doc_rec'' as tabname ,''YJ05202507162347'' as rule_no,''最大业务日期'' as rule_dscr, max(A.doc_create_time) as biz_info from emr_doc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 172 条质控规则-oracle11g
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162349';
    v_rule_desc := '查询 #172 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_first_postop_course'' as tabname ,''YJ05202507162349'' as rule_no,''最大业务日期'' as rule_dscr, max(A.oprn_time) as biz_info from emr_first_postop_course A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 173 条质控规则-oracle11g
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162350';
    v_rule_desc := '查询 #173 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_hard_case_discu'' as tabname ,''YJ05202507162350'' as rule_no,''最大业务日期'' as rule_dscr, max(A.discu_time) as biz_info from emr_hard_case_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 174 条质控规则-oracle11g
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162351';
    v_rule_desc := '查询 #174 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_informed'' as tabname ,''YJ05202507162351'' as rule_no,''最大业务日期'' as rule_dscr, max(A.dying_inform_time) as biz_info from emr_heavy_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 175 条质控规则-oracle11g
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162352';
    v_rule_desc := '查询 #175 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_heavy_nurse'' as tabname ,''YJ05202507162352'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_heavy_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 176 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162353';
    v_rule_desc := '查询 #176 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_assess'' as tabname ,''YJ05202507162353'' as rule_no,''最大业务日期'' as rule_dscr, max(A.eval_time) as biz_info from emr_inhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 177 条质控规则-oracle11g
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162354';
    v_rule_desc := '查询 #177 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inhosp_die_in24h'' as tabname ,''YJ05202507162354'' as rule_no,''最大业务日期'' as rule_dscr, max(A.adm_time) as biz_info from emr_inhosp_die_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 178 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162355';
    v_rule_desc := '查询 #178 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec'' as tabname ,''YJ05202507162355'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_inout_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 179 条质控规则-oracle11g
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162356';
    v_rule_desc := '查询 #179 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_rec_in24h'' as tabname ,''YJ05202507162356'' as rule_no,''最大业务日期'' as rule_dscr, max(A.adm_time) as biz_info from emr_inout_rec_in24h A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 180 条质控规则-oracle11g
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162357';
    v_rule_desc := '查询 #180 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_inout_usedrug'' as tabname ,''YJ05202507162357'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_inout_usedrug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 181 条质控规则-oracle11g
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162358';
    v_rule_desc := '查询 #181 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_labor'' as tabname ,''YJ05202507162358'' as rule_no,''最大业务日期'' as rule_dscr, max(A.labor_rec_time) as biz_info from emr_labor A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 182 条质控规则-oracle11g
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162359';
    v_rule_desc := '查询 #182 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse'' as tabname ,''YJ05202507162359'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 183 条质控规则-oracle11g
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162360';
    v_rule_desc := '查询 #183 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_nurse_plan'' as tabname ,''YJ05202507162360'' as rule_no,''最大业务日期'' as rule_dscr, max(A.sign_time) as biz_info from emr_nurse_plan A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 184 条质控规则-oracle11g
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162361';
    v_rule_desc := '查询 #184 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observmedi'' as tabname ,''YJ05202507162361'' as rule_no,''最大业务日期'' as rule_dscr, max(A.observe_room_time) as biz_info from emr_observmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 185 条质控规则-oracle11g
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162362';
    v_rule_desc := '查询 #185 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_observoprn'' as tabname ,''YJ05202507162362'' as rule_no,''最大业务日期'' as rule_dscr, max(A.oprn_oprt_time) as biz_info from emr_observoprn A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 186 条质控规则-oracle11g
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162363';
    v_rule_desc := '查询 #186 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_informed'' as tabname ,''YJ05202507162363'' as rule_no,''最大业务日期'' as rule_dscr, max(A.plan_oprt_time) as biz_info from emr_opr_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 187 条质控规则-oracle11g
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162364';
    v_rule_desc := '查询 #187 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_nurse'' as tabname ,''YJ05202507162364'' as rule_no,''最大业务日期'' as rule_dscr, max(A.oprn_begin_time) as biz_info from emr_opr_nurse A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 188 条质控规则-oracle11g
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162365';
    v_rule_desc := '查询 #188 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_opr_rec'' as tabname ,''YJ05202507162365'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_opr_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 189 条质控规则-oracle11g
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162366';
    v_rule_desc := '查询 #189 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_oth_informed'' as tabname ,''YJ05202507162366'' as rule_no,''最大业务日期'' as rule_dscr, max(A.doc_sign_time) as biz_info from emr_oth_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 190 条质控规则-oracle11g
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162367';
    v_rule_desc := '查询 #190 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_otpmedi'' as tabname ,''YJ05202507162367'' as rule_no,''最大业务日期'' as rule_dscr, max(A.mdtrt_time) as biz_info from emr_otpmedi A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 191 条质控规则-oracle11g
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162368';
    v_rule_desc := '查询 #191 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp'' as tabname ,''YJ05202507162368'' as rule_no,''最大业务日期'' as rule_dscr, max(A.adm_time) as biz_info from emr_outhosp A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 192 条质控规则-oracle11g
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162369';
    v_rule_desc := '查询 #192 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_outhosp_assess'' as tabname ,''YJ05202507162369'' as rule_no,''最大业务日期'' as rule_dscr, max(A.dscg_time) as biz_info from emr_outhosp_assess A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 193 条质控规则-oracle11g
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162370';
    v_rule_desc := '查询 #193 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_periodic_sum'' as tabname ,''YJ05202507162370'' as rule_no,''最大业务日期'' as rule_dscr, max(A.sum_time) as biz_info from emr_periodic_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 194 条质控规则-oracle11g
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162371';
    v_rule_desc := '查询 #194 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pre_anst'' as tabname ,''YJ05202507162371'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_pre_anst A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 195 条质控规则-oracle11g
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162372';
    v_rule_desc := '查询 #195 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_predelivery'' as tabname ,''YJ05202507162372'' as rule_no,''最大业务日期'' as rule_dscr, max(A.expectant_time) as biz_info from emr_predelivery A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 196 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162373';
    v_rule_desc := '查询 #196 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy'' as tabname ,''YJ05202507162373'' as rule_no,''最大业务日期'' as rule_dscr, max(A.oprn_end_time) as biz_info from emr_pregnancy A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 197 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162374';
    v_rule_desc := '查询 #197 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_nwb'' as tabname ,''YJ05202507162374'' as rule_no,''最大业务日期'' as rule_dscr, max(A.nwb_brdy_time) as biz_info from emr_pregnancy_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 198 条质控规则-oracle11g
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162375';
    v_rule_desc := '查询 #198 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_pregnancy_observ'' as tabname ,''YJ05202507162375'' as rule_no,''最大业务日期'' as rule_dscr, max(A.postpar_observ_time) as biz_info from emr_pregnancy_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 199 条质控规则-oracle11g
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162376';
    v_rule_desc := '查询 #199 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_discu'' as tabname ,''YJ05202507162376'' as rule_no,''最大业务日期'' as rule_dscr, max(A.discu_time) as biz_info from emr_preop_discu A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 200 条质控规则-oracle11g
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162377';
    v_rule_desc := '查询 #200 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_preop_sum'' as tabname ,''YJ05202507162377'' as rule_no,''最大业务日期'' as rule_dscr, max(A.sum_time) as biz_info from emr_preop_sum A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 201 条质控规则-oracle11g
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162378';
    v_rule_desc := '查询 #201 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_resc_rec'' as tabname ,''YJ05202507162378'' as rule_no,''最大业务日期'' as rule_dscr, max(A.resc_endtime) as biz_info from emr_resc_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 202 条质控规则-oracle11g
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162379';
    v_rule_desc := '查询 #202 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rescue'' as tabname ,''YJ05202507162379'' as rule_no,''最大业务日期'' as rule_dscr, max(A.sign_time) as biz_info from emr_rescue A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 203 条质控规则-oracle11g
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162380';
    v_rule_desc := '查询 #203 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_rreat_drug'' as tabname ,''YJ05202507162380'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_rreat_drug A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 204 条质控规则-oracle11g
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162381';
    v_rule_desc := '查询 #204 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_shift_change'' as tabname ,''YJ05202507162381'' as rule_no,''最大业务日期'' as rule_dscr, max(A.shift_time) as biz_info from emr_shift_change A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 205 条质控规则-oracle11g
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162382';
    v_rule_desc := '查询 #205 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_spe_informed'' as tabname ,''YJ05202507162382'' as rule_no,''最大业务日期'' as rule_dscr, max(A.doc_sign_time) as biz_info from emr_spe_informed A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 206 条质控规则-oracle11g
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162383';
    v_rule_desc := '查询 #206 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_super_doct_check'' as tabname ,''YJ05202507162383'' as rule_no,''最大业务日期'' as rule_dscr, max(A.check_room_time) as biz_info from emr_super_doct_check A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 207 条质控规则-oracle11g
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162384';
    v_rule_desc := '查询 #207 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_sympt_rec'' as tabname ,''YJ05202507162384'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_sympt_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 208 条质控规则-oracle11g
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162385';
    v_rule_desc := '查询 #208 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_trans_depart'' as tabname ,''YJ05202507162385'' as rule_no,''最大业务日期'' as rule_dscr, max(A.adm_time) as biz_info from emr_trans_depart A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 209 条质控规则-oracle11g
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162386';
    v_rule_desc := '查询 #209 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_treat_rec'' as tabname ,''YJ05202507162386'' as rule_no,''最大业务日期'' as rule_dscr, max(A.rec_time) as biz_info from emr_treat_rec A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 210 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162387';
    v_rule_desc := '查询 #210 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver'' as tabname ,''YJ05202507162387'' as rule_no,''最大业务日期'' as rule_dscr, max(A.expect_deliver_date) as biz_info from emr_vagina_deliver A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 211 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162388';
    v_rule_desc := '查询 #211 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_nwb'' as tabname ,''YJ05202507162388'' as rule_no,''最大业务日期'' as rule_dscr, max(A.nwb_brdy_time) as biz_info from emr_vagina_deliver_nwb A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

    -- 执行第 212 条质控规则-oracle11g
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162389';
    v_rule_desc := '查询 #212 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 清空临时表
        DELETE FROM temp_result;
        DECLARE
        v_sql VARCHAR2(4000);
        -- 执行查询并将结果插入临时表
        BEGIN
          v_sql :=   'INSERT INTO temp_result
        'select max(org_name) as org_name,max(uscid) as uscid,''emr_vagina_deliver_observ'' as tabname ,''YJ05202507162389'' as rule_no,''最大业务日期'' as rule_dscr, max(A.postpar_observ_time) as biz_info from emr_vagina_deliver_observ A;'';
          EXECUTE IMMEDIATE v_sql;
        END;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            v_error_message := SUBSTR('ERROR ' || SQLCODE || ': ' || SQLERRM, 1, 4000);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
            
            COMMIT;
    END;

END execute_quality_control;
/

-- 执行存储过程
BEGIN
    execute_quality_control();
END;
/

-- 显示结果
SELECT * FROM quality_control_log ORDER BY id;
