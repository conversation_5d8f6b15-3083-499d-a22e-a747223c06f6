-- 质控执行脚本 - 自动生成 (GaussDB 5.0版)
-- 生成时间: 2025-07-25 20:32:49

-- 删除可能存在的日志表和函数
DROP TABLE IF EXISTS quality_control_log CASCADE;
DROP FUNCTION IF EXISTS execute_quality_control_queries();

-- 创建日志表
CREATE TABLE quality_control_log (
    id SERIAL PRIMARY KEY,
    rule_no VARCHAR(50),
    tabname VARCHAR(100),
    rule_desc TEXT,
    dty_count INTEGER,
    error_desc TEXT,
    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建函数，用于执行质控查询
CREATE OR REPLACE FUNCTION execute_quality_control_queries() RETURNS VOID AS $$
DECLARE
    v_tabname VARCHAR(100);
    v_rule_no VARCHAR(50);
    v_rule_desc TEXT;
    v_dty_count INTEGER;
    v_error_message TEXT;
    v_error RECORD;
BEGIN

    -- 执行第 1 条质控规则-guass5.0
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162141';
    v_rule_desc := '查询 #1 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'cis_lh_summary' AS tabname, 'YJ05202507162141' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM cis_lh_summary A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 2 条质控规则-guass5.0
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162142';
    v_rule_desc := '查询 #2 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_adm_rec' AS tabname, 'YJ05202507162142' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_adm_rec A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 3 条质控规则-guass5.0
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162143';
    v_rule_desc := '查询 #3 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_aft_anst' AS tabname, 'YJ05202507162143' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_aft_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 4 条质控规则-guass5.0
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162144';
    v_rule_desc := '查询 #4 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_anst_informed' AS tabname, 'YJ05202507162144' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_anst_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 5 条质控规则-guass5.0
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162145';
    v_rule_desc := '查询 #5 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_blood_informed' AS tabname, 'YJ05202507162145' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_blood_informed A WHERE A.plan_transfuse_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_transfuse_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 6 条质控规则-guass5.0
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162146';
    v_rule_desc := '查询 #6 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_detail' AS tabname, 'YJ05202507162146' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_detail A WHERE A.consult_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.consult_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 7 条质控规则-guass5.0
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162147';
    v_rule_desc := '查询 #7 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_info' AS tabname, 'YJ05202507162147' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_info A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 8 条质控规则-guass5.0
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162148';
    v_rule_desc := '查询 #8 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_daily_dis_course' AS tabname, 'YJ05202507162148' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_daily_dis_course A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 9 条质控规则-guass5.0
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162149';
    v_rule_desc := '查询 #9 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_case_discu' AS tabname, 'YJ05202507162149' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 10 条质控规则-guass5.0
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162150';
    v_rule_desc := '查询 #10 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_record' AS tabname, 'YJ05202507162150' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_record A WHERE A.death_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.death_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 11 条质控规则-guass5.0
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162151';
    v_rule_desc := '查询 #11 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_detail' AS tabname, 'YJ05202507162151' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_detail A WHERE A.file_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.file_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 12 条质控规则-guass5.0
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162152';
    v_rule_desc := '查询 #12 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_rec' AS tabname, 'YJ05202507162152' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_rec A WHERE A.doc_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 13 条质控规则-guass5.0
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162154';
    v_rule_desc := '查询 #13 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_first_postop_course' AS tabname, 'YJ05202507162154' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_first_postop_course A WHERE A.oprn_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 14 条质控规则-guass5.0
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162155';
    v_rule_desc := '查询 #14 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_hard_case_discu' AS tabname, 'YJ05202507162155' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_hard_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 15 条质控规则-guass5.0
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162156';
    v_rule_desc := '查询 #15 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_informed' AS tabname, 'YJ05202507162156' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_informed A WHERE A.dying_inform_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dying_inform_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 16 条质控规则-guass5.0
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162157';
    v_rule_desc := '查询 #16 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_nurse' AS tabname, 'YJ05202507162157' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 17 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162158';
    v_rule_desc := '查询 #17 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_assess' AS tabname, 'YJ05202507162158' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_assess A WHERE A.eval_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.eval_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 18 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162159';
    v_rule_desc := '查询 #18 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_die_in24h' AS tabname, 'YJ05202507162159' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_die_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 19 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162160';
    v_rule_desc := '查询 #19 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec' AS tabname, 'YJ05202507162160' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 20 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162161';
    v_rule_desc := '查询 #20 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec_in24h' AS tabname, 'YJ05202507162161' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 21 条质控规则-guass5.0
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162162';
    v_rule_desc := '查询 #21 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_usedrug' AS tabname, 'YJ05202507162162' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_usedrug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 22 条质控规则-guass5.0
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162163';
    v_rule_desc := '查询 #22 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_labor' AS tabname, 'YJ05202507162163' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_labor A WHERE A.labor_rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.labor_rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 23 条质控规则-guass5.0
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162164';
    v_rule_desc := '查询 #23 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse' AS tabname, 'YJ05202507162164' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 24 条质控规则-guass5.0
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162165';
    v_rule_desc := '查询 #24 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse_plan' AS tabname, 'YJ05202507162165' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse_plan A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 25 条质控规则-guass5.0
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162166';
    v_rule_desc := '查询 #25 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observmedi' AS tabname, 'YJ05202507162166' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observmedi A WHERE A.observe_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.observe_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 26 条质控规则-guass5.0
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162167';
    v_rule_desc := '查询 #26 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observoprn' AS tabname, 'YJ05202507162167' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observoprn A WHERE A.oprn_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 27 条质控规则-guass5.0
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162168';
    v_rule_desc := '查询 #27 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_informed' AS tabname, 'YJ05202507162168' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 28 条质控规则-guass5.0
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162169';
    v_rule_desc := '查询 #28 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_nurse' AS tabname, 'YJ05202507162169' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_nurse A WHERE A.oprn_begin_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_begin_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 29 条质控规则-guass5.0
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162170';
    v_rule_desc := '查询 #29 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_rec' AS tabname, 'YJ05202507162170' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 30 条质控规则-guass5.0
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162171';
    v_rule_desc := '查询 #30 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_oth_informed' AS tabname, 'YJ05202507162171' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_oth_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 31 条质控规则-guass5.0
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162172';
    v_rule_desc := '查询 #31 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_otpmedi' AS tabname, 'YJ05202507162172' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_otpmedi A WHERE A.mdtrt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.mdtrt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 32 条质控规则-guass5.0
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162173';
    v_rule_desc := '查询 #32 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp' AS tabname, 'YJ05202507162173' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 33 条质控规则-guass5.0
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162174';
    v_rule_desc := '查询 #33 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp_assess' AS tabname, 'YJ05202507162174' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp_assess A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 34 条质控规则-guass5.0
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162175';
    v_rule_desc := '查询 #34 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_periodic_sum' AS tabname, 'YJ05202507162175' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_periodic_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 35 条质控规则-guass5.0
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162176';
    v_rule_desc := '查询 #35 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pre_anst' AS tabname, 'YJ05202507162176' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pre_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 36 条质控规则-guass5.0
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162177';
    v_rule_desc := '查询 #36 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_predelivery' AS tabname, 'YJ05202507162177' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_predelivery A WHERE A.expectant_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expectant_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 37 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162178';
    v_rule_desc := '查询 #37 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy' AS tabname, 'YJ05202507162178' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy A WHERE A.oprn_end_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_end_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 38 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162179';
    v_rule_desc := '查询 #38 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_nwb' AS tabname, 'YJ05202507162179' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 39 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162180';
    v_rule_desc := '查询 #39 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_observ' AS tabname, 'YJ05202507162180' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 40 条质控规则-guass5.0
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162181';
    v_rule_desc := '查询 #40 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_discu' AS tabname, 'YJ05202507162181' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 41 条质控规则-guass5.0
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162182';
    v_rule_desc := '查询 #41 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_sum' AS tabname, 'YJ05202507162182' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 42 条质控规则-guass5.0
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162183';
    v_rule_desc := '查询 #42 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_resc_rec' AS tabname, 'YJ05202507162183' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_resc_rec A WHERE A.resc_endtime >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.resc_endtime, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 43 条质控规则-guass5.0
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162184';
    v_rule_desc := '查询 #43 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rescue' AS tabname, 'YJ05202507162184' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rescue A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 44 条质控规则-guass5.0
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162185';
    v_rule_desc := '查询 #44 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rreat_drug' AS tabname, 'YJ05202507162185' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rreat_drug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 45 条质控规则-guass5.0
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162186';
    v_rule_desc := '查询 #45 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_shift_change' AS tabname, 'YJ05202507162186' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_shift_change A WHERE A.shift_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.shift_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 46 条质控规则-guass5.0
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162187';
    v_rule_desc := '查询 #46 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_spe_informed' AS tabname, 'YJ05202507162187' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_spe_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 47 条质控规则-guass5.0
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162188';
    v_rule_desc := '查询 #47 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_super_doct_check' AS tabname, 'YJ05202507162188' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_super_doct_check A WHERE A.check_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.check_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 48 条质控规则-guass5.0
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162189';
    v_rule_desc := '查询 #48 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_sympt_rec' AS tabname, 'YJ05202507162189' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_sympt_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 49 条质控规则-guass5.0
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162191';
    v_rule_desc := '查询 #49 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_treat_rec' AS tabname, 'YJ05202507162191' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_treat_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 50 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162192';
    v_rule_desc := '查询 #50 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver' AS tabname, 'YJ05202507162192' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver A WHERE A.expect_deliver_date >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expect_deliver_date, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 51 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162193';
    v_rule_desc := '查询 #51 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_nwb' AS tabname, 'YJ05202507162193' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 52 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162194';
    v_rule_desc := '查询 #52 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_observ' AS tabname, 'YJ05202507162194' AS rule_no, '数据不满足5年和月连续性(缺失月数)' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 53 条质控规则-guass5.0
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162206';
    v_rule_desc := '查询 #53 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'cis_lh_summary' AS tabname, 'YJ05202507162206' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM cis_lh_summary A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 54 条质控规则-guass5.0
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162207';
    v_rule_desc := '查询 #54 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_adm_rec' AS tabname, 'YJ05202507162207' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_adm_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 55 条质控规则-guass5.0
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162208';
    v_rule_desc := '查询 #55 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_aft_anst' AS tabname, 'YJ05202507162208' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_aft_anst A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 56 条质控规则-guass5.0
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162209';
    v_rule_desc := '查询 #56 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_anst_informed' AS tabname, 'YJ05202507162209' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_anst_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 57 条质控规则-guass5.0
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162210';
    v_rule_desc := '查询 #57 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_blood_informed' AS tabname, 'YJ05202507162210' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_blood_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 58 条质控规则-guass5.0
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162211';
    v_rule_desc := '查询 #58 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_detail' AS tabname, 'YJ05202507162211' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_detail A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 59 条质控规则-guass5.0
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162212';
    v_rule_desc := '查询 #59 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_info' AS tabname, 'YJ05202507162212' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_info A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 60 条质控规则-guass5.0
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162213';
    v_rule_desc := '查询 #60 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_daily_dis_course' AS tabname, 'YJ05202507162213' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_daily_dis_course A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 61 条质控规则-guass5.0
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162214';
    v_rule_desc := '查询 #61 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_case_discu' AS tabname, 'YJ05202507162214' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_case_discu A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 62 条质控规则-guass5.0
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162215';
    v_rule_desc := '查询 #62 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_record' AS tabname, 'YJ05202507162215' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_record A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 63 条质控规则-guass5.0
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162216';
    v_rule_desc := '查询 #63 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_detail' AS tabname, 'YJ05202507162216' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_detail A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 64 条质控规则-guass5.0
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162217';
    v_rule_desc := '查询 #64 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_rec' AS tabname, 'YJ05202507162217' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 65 条质控规则-guass5.0
    v_tabname := 'emr_first_dis_course';
    v_rule_no := 'YJ05202507162218';
    v_rule_desc := '查询 #65 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_first_dis_course' AS tabname, 'YJ05202507162218' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_first_dis_course A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 66 条质控规则-guass5.0
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162219';
    v_rule_desc := '查询 #66 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_first_postop_course' AS tabname, 'YJ05202507162219' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_first_postop_course A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 67 条质控规则-guass5.0
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162220';
    v_rule_desc := '查询 #67 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_hard_case_discu' AS tabname, 'YJ05202507162220' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_hard_case_discu A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 68 条质控规则-guass5.0
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162221';
    v_rule_desc := '查询 #68 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_informed' AS tabname, 'YJ05202507162221' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 69 条质控规则-guass5.0
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162222';
    v_rule_desc := '查询 #69 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_nurse' AS tabname, 'YJ05202507162222' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_nurse A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 70 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162223';
    v_rule_desc := '查询 #70 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_assess' AS tabname, 'YJ05202507162223' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_assess A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 71 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162224';
    v_rule_desc := '查询 #71 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_die_in24h' AS tabname, 'YJ05202507162224' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_die_in24h A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 72 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162225';
    v_rule_desc := '查询 #72 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec' AS tabname, 'YJ05202507162225' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 73 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162226';
    v_rule_desc := '查询 #73 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec_in24h' AS tabname, 'YJ05202507162226' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec_in24h A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 74 条质控规则-guass5.0
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162227';
    v_rule_desc := '查询 #74 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_usedrug' AS tabname, 'YJ05202507162227' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_usedrug A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 75 条质控规则-guass5.0
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162228';
    v_rule_desc := '查询 #75 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_labor' AS tabname, 'YJ05202507162228' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_labor A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 76 条质控规则-guass5.0
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162229';
    v_rule_desc := '查询 #76 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse' AS tabname, 'YJ05202507162229' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 77 条质控规则-guass5.0
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162230';
    v_rule_desc := '查询 #77 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse_plan' AS tabname, 'YJ05202507162230' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse_plan A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 78 条质控规则-guass5.0
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162231';
    v_rule_desc := '查询 #78 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observmedi' AS tabname, 'YJ05202507162231' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observmedi A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 79 条质控规则-guass5.0
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162232';
    v_rule_desc := '查询 #79 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observoprn' AS tabname, 'YJ05202507162232' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observoprn A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 80 条质控规则-guass5.0
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162233';
    v_rule_desc := '查询 #80 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_informed' AS tabname, 'YJ05202507162233' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 81 条质控规则-guass5.0
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162234';
    v_rule_desc := '查询 #81 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_nurse' AS tabname, 'YJ05202507162234' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_nurse A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 82 条质控规则-guass5.0
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162235';
    v_rule_desc := '查询 #82 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_rec' AS tabname, 'YJ05202507162235' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 83 条质控规则-guass5.0
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162236';
    v_rule_desc := '查询 #83 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_oth_informed' AS tabname, 'YJ05202507162236' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_oth_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 84 条质控规则-guass5.0
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162237';
    v_rule_desc := '查询 #84 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_otpmedi' AS tabname, 'YJ05202507162237' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_otpmedi A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 85 条质控规则-guass5.0
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162238';
    v_rule_desc := '查询 #85 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp' AS tabname, 'YJ05202507162238' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 86 条质控规则-guass5.0
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162239';
    v_rule_desc := '查询 #86 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp_assess' AS tabname, 'YJ05202507162239' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp_assess A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 87 条质控规则-guass5.0
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162240';
    v_rule_desc := '查询 #87 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_periodic_sum' AS tabname, 'YJ05202507162240' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_periodic_sum A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 88 条质控规则-guass5.0
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162241';
    v_rule_desc := '查询 #88 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pre_anst' AS tabname, 'YJ05202507162241' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pre_anst A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 89 条质控规则-guass5.0
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162242';
    v_rule_desc := '查询 #89 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_predelivery' AS tabname, 'YJ05202507162242' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_predelivery A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 90 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162243';
    v_rule_desc := '查询 #90 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy' AS tabname, 'YJ05202507162243' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 91 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162244';
    v_rule_desc := '查询 #91 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_nwb' AS tabname, 'YJ05202507162244' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_nwb A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 92 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162245';
    v_rule_desc := '查询 #92 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_observ' AS tabname, 'YJ05202507162245' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_observ A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 93 条质控规则-guass5.0
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162246';
    v_rule_desc := '查询 #93 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_discu' AS tabname, 'YJ05202507162246' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_discu A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 94 条质控规则-guass5.0
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162247';
    v_rule_desc := '查询 #94 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_sum' AS tabname, 'YJ05202507162247' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_sum A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 95 条质控规则-guass5.0
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162248';
    v_rule_desc := '查询 #95 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_resc_rec' AS tabname, 'YJ05202507162248' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_resc_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 96 条质控规则-guass5.0
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162249';
    v_rule_desc := '查询 #96 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rescue' AS tabname, 'YJ05202507162249' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rescue A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 97 条质控规则-guass5.0
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162250';
    v_rule_desc := '查询 #97 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rreat_drug' AS tabname, 'YJ05202507162250' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rreat_drug A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 98 条质控规则-guass5.0
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162251';
    v_rule_desc := '查询 #98 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_shift_change' AS tabname, 'YJ05202507162251' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_shift_change A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 99 条质控规则-guass5.0
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162252';
    v_rule_desc := '查询 #99 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_spe_informed' AS tabname, 'YJ05202507162252' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_spe_informed A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 100 条质控规则-guass5.0
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162253';
    v_rule_desc := '查询 #100 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_super_doct_check' AS tabname, 'YJ05202507162253' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_super_doct_check A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 101 条质控规则-guass5.0
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162254';
    v_rule_desc := '查询 #101 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_sympt_rec' AS tabname, 'YJ05202507162254' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_sympt_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 102 条质控规则-guass5.0
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162255';
    v_rule_desc := '查询 #102 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_trans_depart' AS tabname, 'YJ05202507162255' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_trans_depart A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 103 条质控规则-guass5.0
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162256';
    v_rule_desc := '查询 #103 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_treat_rec' AS tabname, 'YJ05202507162256' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_treat_rec A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 104 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162257';
    v_rule_desc := '查询 #104 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver' AS tabname, 'YJ05202507162257' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 105 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162258';
    v_rule_desc := '查询 #105 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_nwb' AS tabname, 'YJ05202507162258' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_nwb A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 106 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162259';
    v_rule_desc := '查询 #106 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_observ' AS tabname, 'YJ05202507162259' AS rule_no, '表数据量统计' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_observ A WHERE A. >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A., 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 107 条质控规则-guass5.0
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162271';
    v_rule_desc := '查询 #107 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'cis_lh_summary' AS tabname, 'YJ05202507162271' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM cis_lh_summary A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 108 条质控规则-guass5.0
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162272';
    v_rule_desc := '查询 #108 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_adm_rec' AS tabname, 'YJ05202507162272' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_adm_rec A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 109 条质控规则-guass5.0
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162273';
    v_rule_desc := '查询 #109 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_aft_anst' AS tabname, 'YJ05202507162273' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_aft_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 110 条质控规则-guass5.0
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162274';
    v_rule_desc := '查询 #110 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_anst_informed' AS tabname, 'YJ05202507162274' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_anst_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 111 条质控规则-guass5.0
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162275';
    v_rule_desc := '查询 #111 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_blood_informed' AS tabname, 'YJ05202507162275' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_blood_informed A WHERE A.plan_transfuse_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_transfuse_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 112 条质控规则-guass5.0
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162276';
    v_rule_desc := '查询 #112 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_detail' AS tabname, 'YJ05202507162276' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_detail A WHERE A.consult_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.consult_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 113 条质控规则-guass5.0
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162277';
    v_rule_desc := '查询 #113 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_info' AS tabname, 'YJ05202507162277' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_info A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 114 条质控规则-guass5.0
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162278';
    v_rule_desc := '查询 #114 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_daily_dis_course' AS tabname, 'YJ05202507162278' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_daily_dis_course A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 115 条质控规则-guass5.0
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162279';
    v_rule_desc := '查询 #115 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_case_discu' AS tabname, 'YJ05202507162279' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 116 条质控规则-guass5.0
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162280';
    v_rule_desc := '查询 #116 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_record' AS tabname, 'YJ05202507162280' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_record A WHERE A.death_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.death_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 117 条质控规则-guass5.0
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162281';
    v_rule_desc := '查询 #117 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_detail' AS tabname, 'YJ05202507162281' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_detail A WHERE A.file_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.file_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 118 条质控规则-guass5.0
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162282';
    v_rule_desc := '查询 #118 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_rec' AS tabname, 'YJ05202507162282' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_rec A WHERE A.doc_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 119 条质控规则-guass5.0
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162284';
    v_rule_desc := '查询 #119 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_first_postop_course' AS tabname, 'YJ05202507162284' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_first_postop_course A WHERE A.oprn_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 120 条质控规则-guass5.0
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162285';
    v_rule_desc := '查询 #120 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_hard_case_discu' AS tabname, 'YJ05202507162285' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_hard_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 121 条质控规则-guass5.0
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162286';
    v_rule_desc := '查询 #121 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_informed' AS tabname, 'YJ05202507162286' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_informed A WHERE A.dying_inform_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dying_inform_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 122 条质控规则-guass5.0
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162287';
    v_rule_desc := '查询 #122 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_nurse' AS tabname, 'YJ05202507162287' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 123 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162288';
    v_rule_desc := '查询 #123 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_assess' AS tabname, 'YJ05202507162288' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_assess A WHERE A.eval_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.eval_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 124 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162289';
    v_rule_desc := '查询 #124 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_die_in24h' AS tabname, 'YJ05202507162289' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_die_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 125 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162290';
    v_rule_desc := '查询 #125 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec' AS tabname, 'YJ05202507162290' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 126 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162291';
    v_rule_desc := '查询 #126 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec_in24h' AS tabname, 'YJ05202507162291' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 127 条质控规则-guass5.0
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162292';
    v_rule_desc := '查询 #127 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_usedrug' AS tabname, 'YJ05202507162292' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_usedrug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 128 条质控规则-guass5.0
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162293';
    v_rule_desc := '查询 #128 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_labor' AS tabname, 'YJ05202507162293' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_labor A WHERE A.labor_rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.labor_rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 129 条质控规则-guass5.0
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162294';
    v_rule_desc := '查询 #129 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse' AS tabname, 'YJ05202507162294' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 130 条质控规则-guass5.0
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162295';
    v_rule_desc := '查询 #130 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse_plan' AS tabname, 'YJ05202507162295' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse_plan A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 131 条质控规则-guass5.0
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162296';
    v_rule_desc := '查询 #131 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observmedi' AS tabname, 'YJ05202507162296' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observmedi A WHERE A.observe_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.observe_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 132 条质控规则-guass5.0
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162297';
    v_rule_desc := '查询 #132 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observoprn' AS tabname, 'YJ05202507162297' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observoprn A WHERE A.oprn_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 133 条质控规则-guass5.0
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162298';
    v_rule_desc := '查询 #133 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_informed' AS tabname, 'YJ05202507162298' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 134 条质控规则-guass5.0
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162299';
    v_rule_desc := '查询 #134 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_nurse' AS tabname, 'YJ05202507162299' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_nurse A WHERE A.oprn_begin_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_begin_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 135 条质控规则-guass5.0
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162300';
    v_rule_desc := '查询 #135 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_rec' AS tabname, 'YJ05202507162300' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 136 条质控规则-guass5.0
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162301';
    v_rule_desc := '查询 #136 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_oth_informed' AS tabname, 'YJ05202507162301' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_oth_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 137 条质控规则-guass5.0
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162302';
    v_rule_desc := '查询 #137 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_otpmedi' AS tabname, 'YJ05202507162302' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_otpmedi A WHERE A.mdtrt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.mdtrt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 138 条质控规则-guass5.0
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162303';
    v_rule_desc := '查询 #138 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp' AS tabname, 'YJ05202507162303' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 139 条质控规则-guass5.0
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162304';
    v_rule_desc := '查询 #139 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp_assess' AS tabname, 'YJ05202507162304' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp_assess A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 140 条质控规则-guass5.0
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162305';
    v_rule_desc := '查询 #140 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_periodic_sum' AS tabname, 'YJ05202507162305' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_periodic_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 141 条质控规则-guass5.0
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162306';
    v_rule_desc := '查询 #141 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pre_anst' AS tabname, 'YJ05202507162306' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pre_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 142 条质控规则-guass5.0
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162307';
    v_rule_desc := '查询 #142 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_predelivery' AS tabname, 'YJ05202507162307' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_predelivery A WHERE A.expectant_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expectant_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 143 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162308';
    v_rule_desc := '查询 #143 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy' AS tabname, 'YJ05202507162308' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy A WHERE A.oprn_end_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_end_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 144 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162309';
    v_rule_desc := '查询 #144 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_nwb' AS tabname, 'YJ05202507162309' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 145 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162310';
    v_rule_desc := '查询 #145 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_observ' AS tabname, 'YJ05202507162310' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 146 条质控规则-guass5.0
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162311';
    v_rule_desc := '查询 #146 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_discu' AS tabname, 'YJ05202507162311' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 147 条质控规则-guass5.0
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162312';
    v_rule_desc := '查询 #147 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_sum' AS tabname, 'YJ05202507162312' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 148 条质控规则-guass5.0
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162313';
    v_rule_desc := '查询 #148 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_resc_rec' AS tabname, 'YJ05202507162313' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_resc_rec A WHERE A.resc_endtime >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.resc_endtime, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 149 条质控规则-guass5.0
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162314';
    v_rule_desc := '查询 #149 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rescue' AS tabname, 'YJ05202507162314' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rescue A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 150 条质控规则-guass5.0
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162315';
    v_rule_desc := '查询 #150 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rreat_drug' AS tabname, 'YJ05202507162315' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rreat_drug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 151 条质控规则-guass5.0
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162316';
    v_rule_desc := '查询 #151 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_shift_change' AS tabname, 'YJ05202507162316' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_shift_change A WHERE A.shift_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.shift_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 152 条质控规则-guass5.0
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162317';
    v_rule_desc := '查询 #152 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_spe_informed' AS tabname, 'YJ05202507162317' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_spe_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 153 条质控规则-guass5.0
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162318';
    v_rule_desc := '查询 #153 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_super_doct_check' AS tabname, 'YJ05202507162318' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_super_doct_check A WHERE A.check_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.check_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 154 条质控规则-guass5.0
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162319';
    v_rule_desc := '查询 #154 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_sympt_rec' AS tabname, 'YJ05202507162319' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_sympt_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 155 条质控规则-guass5.0
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162320';
    v_rule_desc := '查询 #155 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_trans_depart' AS tabname, 'YJ05202507162320' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_trans_depart A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 156 条质控规则-guass5.0
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162321';
    v_rule_desc := '查询 #156 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_treat_rec' AS tabname, 'YJ05202507162321' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_treat_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 157 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162322';
    v_rule_desc := '查询 #157 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver' AS tabname, 'YJ05202507162322' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver A WHERE A.expect_deliver_date >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expect_deliver_date, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 158 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162323';
    v_rule_desc := '查询 #158 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_nwb' AS tabname, 'YJ05202507162323' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 159 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162324';
    v_rule_desc := '查询 #159 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_observ' AS tabname, 'YJ05202507162324' AS rule_no, '最小业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 160 条质控规则-guass5.0
    v_tabname := 'cis_lh_summary';
    v_rule_no := 'YJ05202507162336';
    v_rule_desc := '查询 #160 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'cis_lh_summary' AS tabname, 'YJ05202507162336' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM cis_lh_summary A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 161 条质控规则-guass5.0
    v_tabname := 'emr_adm_rec';
    v_rule_no := 'YJ05202507162337';
    v_rule_desc := '查询 #161 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_adm_rec' AS tabname, 'YJ05202507162337' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_adm_rec A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 162 条质控规则-guass5.0
    v_tabname := 'emr_aft_anst';
    v_rule_no := 'YJ05202507162338';
    v_rule_desc := '查询 #162 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_aft_anst' AS tabname, 'YJ05202507162338' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_aft_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 163 条质控规则-guass5.0
    v_tabname := 'emr_anst_informed';
    v_rule_no := 'YJ05202507162339';
    v_rule_desc := '查询 #163 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_anst_informed' AS tabname, 'YJ05202507162339' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_anst_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 164 条质控规则-guass5.0
    v_tabname := 'emr_blood_informed';
    v_rule_no := 'YJ05202507162340';
    v_rule_desc := '查询 #164 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_blood_informed' AS tabname, 'YJ05202507162340' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_blood_informed A WHERE A.plan_transfuse_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_transfuse_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 165 条质控规则-guass5.0
    v_tabname := 'emr_consult_detail';
    v_rule_no := 'YJ05202507162341';
    v_rule_desc := '查询 #165 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_detail' AS tabname, 'YJ05202507162341' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_detail A WHERE A.consult_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.consult_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 166 条质控规则-guass5.0
    v_tabname := 'emr_consult_info';
    v_rule_no := 'YJ05202507162342';
    v_rule_desc := '查询 #166 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_consult_info' AS tabname, 'YJ05202507162342' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_consult_info A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 167 条质控规则-guass5.0
    v_tabname := 'emr_daily_dis_course';
    v_rule_no := 'YJ05202507162343';
    v_rule_desc := '查询 #167 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_daily_dis_course' AS tabname, 'YJ05202507162343' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_daily_dis_course A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 168 条质控规则-guass5.0
    v_tabname := 'emr_death_case_discu';
    v_rule_no := 'YJ05202507162344';
    v_rule_desc := '查询 #168 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_case_discu' AS tabname, 'YJ05202507162344' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 169 条质控规则-guass5.0
    v_tabname := 'emr_death_record';
    v_rule_no := 'YJ05202507162345';
    v_rule_desc := '查询 #169 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_death_record' AS tabname, 'YJ05202507162345' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_death_record A WHERE A.death_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.death_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 170 条质控规则-guass5.0
    v_tabname := 'emr_doc_detail';
    v_rule_no := 'YJ05202507162346';
    v_rule_desc := '查询 #170 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_detail' AS tabname, 'YJ05202507162346' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_detail A WHERE A.file_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.file_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 171 条质控规则-guass5.0
    v_tabname := 'emr_doc_rec';
    v_rule_no := 'YJ05202507162347';
    v_rule_desc := '查询 #171 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_doc_rec' AS tabname, 'YJ05202507162347' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_doc_rec A WHERE A.doc_create_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_create_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 172 条质控规则-guass5.0
    v_tabname := 'emr_first_postop_course';
    v_rule_no := 'YJ05202507162349';
    v_rule_desc := '查询 #172 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_first_postop_course' AS tabname, 'YJ05202507162349' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_first_postop_course A WHERE A.oprn_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 173 条质控规则-guass5.0
    v_tabname := 'emr_hard_case_discu';
    v_rule_no := 'YJ05202507162350';
    v_rule_desc := '查询 #173 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_hard_case_discu' AS tabname, 'YJ05202507162350' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_hard_case_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 174 条质控规则-guass5.0
    v_tabname := 'emr_heavy_informed';
    v_rule_no := 'YJ05202507162351';
    v_rule_desc := '查询 #174 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_informed' AS tabname, 'YJ05202507162351' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_informed A WHERE A.dying_inform_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dying_inform_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 175 条质控规则-guass5.0
    v_tabname := 'emr_heavy_nurse';
    v_rule_no := 'YJ05202507162352';
    v_rule_desc := '查询 #175 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_heavy_nurse' AS tabname, 'YJ05202507162352' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_heavy_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 176 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_assess';
    v_rule_no := 'YJ05202507162353';
    v_rule_desc := '查询 #176 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_assess' AS tabname, 'YJ05202507162353' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_assess A WHERE A.eval_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.eval_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 177 条质控规则-guass5.0
    v_tabname := 'emr_inhosp_die_in24h';
    v_rule_no := 'YJ05202507162354';
    v_rule_desc := '查询 #177 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inhosp_die_in24h' AS tabname, 'YJ05202507162354' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inhosp_die_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 178 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec';
    v_rule_no := 'YJ05202507162355';
    v_rule_desc := '查询 #178 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec' AS tabname, 'YJ05202507162355' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 179 条质控规则-guass5.0
    v_tabname := 'emr_inout_rec_in24h';
    v_rule_no := 'YJ05202507162356';
    v_rule_desc := '查询 #179 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_rec_in24h' AS tabname, 'YJ05202507162356' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_rec_in24h A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 180 条质控规则-guass5.0
    v_tabname := 'emr_inout_usedrug';
    v_rule_no := 'YJ05202507162357';
    v_rule_desc := '查询 #180 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_inout_usedrug' AS tabname, 'YJ05202507162357' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_inout_usedrug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 181 条质控规则-guass5.0
    v_tabname := 'emr_labor';
    v_rule_no := 'YJ05202507162358';
    v_rule_desc := '查询 #181 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_labor' AS tabname, 'YJ05202507162358' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_labor A WHERE A.labor_rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.labor_rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 182 条质控规则-guass5.0
    v_tabname := 'emr_nurse';
    v_rule_no := 'YJ05202507162359';
    v_rule_desc := '查询 #182 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse' AS tabname, 'YJ05202507162359' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 183 条质控规则-guass5.0
    v_tabname := 'emr_nurse_plan';
    v_rule_no := 'YJ05202507162360';
    v_rule_desc := '查询 #183 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_nurse_plan' AS tabname, 'YJ05202507162360' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_nurse_plan A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 184 条质控规则-guass5.0
    v_tabname := 'emr_observmedi';
    v_rule_no := 'YJ05202507162361';
    v_rule_desc := '查询 #184 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observmedi' AS tabname, 'YJ05202507162361' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observmedi A WHERE A.observe_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.observe_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 185 条质控规则-guass5.0
    v_tabname := 'emr_observoprn';
    v_rule_no := 'YJ05202507162362';
    v_rule_desc := '查询 #185 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_observoprn' AS tabname, 'YJ05202507162362' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_observoprn A WHERE A.oprn_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 186 条质控规则-guass5.0
    v_tabname := 'emr_opr_informed';
    v_rule_no := 'YJ05202507162363';
    v_rule_desc := '查询 #186 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_informed' AS tabname, 'YJ05202507162363' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_informed A WHERE A.plan_oprt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.plan_oprt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 187 条质控规则-guass5.0
    v_tabname := 'emr_opr_nurse';
    v_rule_no := 'YJ05202507162364';
    v_rule_desc := '查询 #187 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_nurse' AS tabname, 'YJ05202507162364' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_nurse A WHERE A.oprn_begin_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_begin_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 188 条质控规则-guass5.0
    v_tabname := 'emr_opr_rec';
    v_rule_no := 'YJ05202507162365';
    v_rule_desc := '查询 #188 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_opr_rec' AS tabname, 'YJ05202507162365' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_opr_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 189 条质控规则-guass5.0
    v_tabname := 'emr_oth_informed';
    v_rule_no := 'YJ05202507162366';
    v_rule_desc := '查询 #189 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_oth_informed' AS tabname, 'YJ05202507162366' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_oth_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 190 条质控规则-guass5.0
    v_tabname := 'emr_otpmedi';
    v_rule_no := 'YJ05202507162367';
    v_rule_desc := '查询 #190 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_otpmedi' AS tabname, 'YJ05202507162367' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_otpmedi A WHERE A.mdtrt_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.mdtrt_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 191 条质控规则-guass5.0
    v_tabname := 'emr_outhosp';
    v_rule_no := 'YJ05202507162368';
    v_rule_desc := '查询 #191 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp' AS tabname, 'YJ05202507162368' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 192 条质控规则-guass5.0
    v_tabname := 'emr_outhosp_assess';
    v_rule_no := 'YJ05202507162369';
    v_rule_desc := '查询 #192 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_outhosp_assess' AS tabname, 'YJ05202507162369' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_outhosp_assess A WHERE A.dscg_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.dscg_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 193 条质控规则-guass5.0
    v_tabname := 'emr_periodic_sum';
    v_rule_no := 'YJ05202507162370';
    v_rule_desc := '查询 #193 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_periodic_sum' AS tabname, 'YJ05202507162370' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_periodic_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 194 条质控规则-guass5.0
    v_tabname := 'emr_pre_anst';
    v_rule_no := 'YJ05202507162371';
    v_rule_desc := '查询 #194 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pre_anst' AS tabname, 'YJ05202507162371' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pre_anst A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 195 条质控规则-guass5.0
    v_tabname := 'emr_predelivery';
    v_rule_no := 'YJ05202507162372';
    v_rule_desc := '查询 #195 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_predelivery' AS tabname, 'YJ05202507162372' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_predelivery A WHERE A.expectant_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expectant_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 196 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy';
    v_rule_no := 'YJ05202507162373';
    v_rule_desc := '查询 #196 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy' AS tabname, 'YJ05202507162373' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy A WHERE A.oprn_end_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.oprn_end_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 197 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_nwb';
    v_rule_no := 'YJ05202507162374';
    v_rule_desc := '查询 #197 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_nwb' AS tabname, 'YJ05202507162374' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 198 条质控规则-guass5.0
    v_tabname := 'emr_pregnancy_observ';
    v_rule_no := 'YJ05202507162375';
    v_rule_desc := '查询 #198 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_pregnancy_observ' AS tabname, 'YJ05202507162375' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_pregnancy_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 199 条质控规则-guass5.0
    v_tabname := 'emr_preop_discu';
    v_rule_no := 'YJ05202507162376';
    v_rule_desc := '查询 #199 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_discu' AS tabname, 'YJ05202507162376' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_discu A WHERE A.discu_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.discu_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 200 条质控规则-guass5.0
    v_tabname := 'emr_preop_sum';
    v_rule_no := 'YJ05202507162377';
    v_rule_desc := '查询 #200 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_preop_sum' AS tabname, 'YJ05202507162377' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_preop_sum A WHERE A.sum_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sum_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 201 条质控规则-guass5.0
    v_tabname := 'emr_resc_rec';
    v_rule_no := 'YJ05202507162378';
    v_rule_desc := '查询 #201 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_resc_rec' AS tabname, 'YJ05202507162378' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_resc_rec A WHERE A.resc_endtime >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.resc_endtime, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 202 条质控规则-guass5.0
    v_tabname := 'emr_rescue';
    v_rule_no := 'YJ05202507162379';
    v_rule_desc := '查询 #202 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rescue' AS tabname, 'YJ05202507162379' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rescue A WHERE A.sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 203 条质控规则-guass5.0
    v_tabname := 'emr_rreat_drug';
    v_rule_no := 'YJ05202507162380';
    v_rule_desc := '查询 #203 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_rreat_drug' AS tabname, 'YJ05202507162380' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_rreat_drug A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 204 条质控规则-guass5.0
    v_tabname := 'emr_shift_change';
    v_rule_no := 'YJ05202507162381';
    v_rule_desc := '查询 #204 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_shift_change' AS tabname, 'YJ05202507162381' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_shift_change A WHERE A.shift_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.shift_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 205 条质控规则-guass5.0
    v_tabname := 'emr_spe_informed';
    v_rule_no := 'YJ05202507162382';
    v_rule_desc := '查询 #205 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_spe_informed' AS tabname, 'YJ05202507162382' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_spe_informed A WHERE A.doc_sign_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.doc_sign_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 206 条质控规则-guass5.0
    v_tabname := 'emr_super_doct_check';
    v_rule_no := 'YJ05202507162383';
    v_rule_desc := '查询 #206 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_super_doct_check' AS tabname, 'YJ05202507162383' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_super_doct_check A WHERE A.check_room_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.check_room_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 207 条质控规则-guass5.0
    v_tabname := 'emr_sympt_rec';
    v_rule_no := 'YJ05202507162384';
    v_rule_desc := '查询 #207 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_sympt_rec' AS tabname, 'YJ05202507162384' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_sympt_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 208 条质控规则-guass5.0
    v_tabname := 'emr_trans_depart';
    v_rule_no := 'YJ05202507162385';
    v_rule_desc := '查询 #208 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_trans_depart' AS tabname, 'YJ05202507162385' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_trans_depart A WHERE A.adm_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.adm_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 209 条质控规则-guass5.0
    v_tabname := 'emr_treat_rec';
    v_rule_no := 'YJ05202507162386';
    v_rule_desc := '查询 #209 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_treat_rec' AS tabname, 'YJ05202507162386' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_treat_rec A WHERE A.rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.rec_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 210 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver';
    v_rule_no := 'YJ05202507162387';
    v_rule_desc := '查询 #210 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver' AS tabname, 'YJ05202507162387' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver A WHERE A.expect_deliver_date >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.expect_deliver_date, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 211 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_nwb';
    v_rule_no := 'YJ05202507162388';
    v_rule_desc := '查询 #211 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_nwb' AS tabname, 'YJ05202507162388' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_nwb A WHERE A.nwb_brdy_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.nwb_brdy_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

    -- 执行第 212 条质控规则-guass5.0
    v_tabname := 'emr_vagina_deliver_observ';
    v_rule_no := 'YJ05202507162389';
    v_rule_desc := '查询 #212 - 未找到描述';
    
    -- 执行查询
    BEGIN
        -- 创建临时表来存储查询结果
        DROP TABLE IF EXISTS temp_result;
        CREATE TEMP TABLE temp_result (
            tabname VARCHAR(100),
            rule_no VARCHAR(50),
            rule_desc TEXT,
            dty_count INTEGER
        ) ON COMMIT DELETE ROWS;
        
        -- 执行查询并将结果插入临时表
        INSERT INTO temp_result
        SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid, 'emr_vagina_deliver_observ' AS tabname, 'YJ05202507162389' AS rule_no, '最大业务日期' AS rule_dscr, (EXTRACT(YEAR FROM age(current_date, '2020-01-01'::date)) * 12 + EXTRACT(MONTH FROM age(current_date, '2020-01-01'::date))) -COUNT(1) AS biz_info FROM ( SELECT MAX(org_name) AS org_name, MAX(uscid) AS uscid FROM emr_vagina_deliver_observ A WHERE A.postpar_observ_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') GROUP BY TO_CHAR(A.postpar_observ_time, 'YYYYMM')) T;
        
        -- 从临时表获取数据并插入日志表
        INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
        SELECT rule_no, tabname, rule_desc, dty_count, NULL FROM temp_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 获取错误信息
            GET STACKED DIAGNOSTICS v_error_message = PG_EXCEPTION_CONTEXT;
            v_error_message := CONCAT('ERROR: ', SQLERRM, ' - ', v_error_message);
            
            -- 记录错误信息
            INSERT INTO quality_control_log (rule_no, tabname, rule_desc, dty_count, error_desc)
            VALUES (v_rule_no, v_tabname, v_rule_desc, -1, v_error_message);
    END;

END;
$$ LANGUAGE plpgsql;

-- 执行函数
SELECT execute_quality_control_queries();

-- 显示结果
SELECT * FROM quality_control_log ORDER BY id;
