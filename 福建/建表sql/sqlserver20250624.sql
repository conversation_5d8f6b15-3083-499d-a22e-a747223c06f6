-- 生成时间：2025-06-24 14:14:01
-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建


-- 创建日志表
IF OBJECT_ID('dbo.table_creation_log', 'U') IS NOT NULL
BEGIN
    DROP TABLE [dbo].[table_creation_log]
END
GO

CREATE TABLE [dbo].[table_creation_log] (
    [id] INT IDENTITY(1,1) PRIMARY KEY,
    [table_name] NVARCHAR(255) NOT NULL,
    [status] NVARCHAR(10) NOT NULL,
    [message] NVARCHAR(MAX),
    [error_code] NVARCHAR(10),
    [create_time] DATETIME DEFAULT GETDATE()
)
GO

-- 创建存储过程
IF OBJECT_ID('dbo.log_table_result', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[log_table_result]
END
GO

CREATE PROCEDURE [dbo].[log_table_result]
    @table_name NVARCHAR(255),
    @status NVARCHAR(10),
    @message NVARCHAR(MAX),
    @error_code NVARCHAR(10)
AS
BEGIN
    INSERT INTO [dbo].[table_creation_log] ([table_name], [status], [message], [error_code])
    VALUES (@table_name, @status, @message, @error_code)
END
GO


-- 创建表 tj_charge
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[tj_charge] (
    [rid] NVARCHAR(255),
    [org_name] NVARCHAR(100),
    [uscid] NVARCHAR(100),
    [upload_time] DATETIME,
    [sys_prdr_code] NVARCHAR(50),
    [sys_prdr_name] NVARCHAR(255),
    [fee_date] DATETIME,
    [fee_no] NVARCHAR(50),
    [refd_mark] NVARCHAR(1),
    [comb_type] NVARCHAR(1),
    [tj_comb_sno] NVARCHAR(50),
    [emp_code] NVARCHAR(50),
    [mp_name] NVARCHAR(100),
    [medfee_sumamt] NVARCHAR(255),
    [payment_amt] NVARCHAR(255),
    [offer_amt] NVARCHAR(255),
    [invo_no] NVARCHAR(64),
    [invono_print_time] DATETIME,
    [state] NVARCHAR(1),
    [reserve1] NVARCHAR(255),
    [reserve2] NVARCHAR(255),
    [data_clct_prdr_name] NVARCHAR(255),
    [crte_time] DATETIME,
    [updt_time] DATETIME,
    [deleted] NVARCHAR(1),
    [deleted_time] DATETIME,
    CONSTRAINT [PK_1_tj_] PRIMARY KEY ([uscid], [upload_time], [sys_prdr_code], [fee_date], [fee_no], [refd_mark])
)
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') AND minor_id = 0
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'体检收费表',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge'
END

IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'rid'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码uscid+数据上传时间upload_time+系统建设厂商代码sys_prdr_code+收/退费日期fee_date+收/退费编号fee_no+退费标志refd_mark',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'rid'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'org_name'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'医疗机构名称',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'org_name'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'uscid'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'uscid'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'upload_time'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'upload_time'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'sys_prdr_code'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'系统建设厂商代码 复合主键，系统建设厂商名称首字母大写',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'sys_prdr_code'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'sys_prdr_name'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'系统建设厂商名称 见公共字段【系统建设厂商名称】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'sys_prdr_name'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'fee_date'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'收/退费日期 复合主键；格式为YYYY-MM-DD hh:mm:ss',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'fee_date'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'fee_no'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'收/退费编号 复合主键；见门诊收费表说明（1）',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'fee_no'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'refd_mark'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'退费标志 复合主键；1：收费；2：退费',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'refd_mark'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'comb_type'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'体检类别代码 1个人体检2单位体检',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'comb_type'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'tj_comb_sno'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'体检流水号 体检类别为个人体检时必填',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'tj_comb_sno'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'emp_code'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'体检单位编码 院内表示一个体检单位的唯一编码，体检类别为单位体检时必填',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'emp_code'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'mp_name'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'体检单位名称 体检类别为单位体检时必填',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'mp_name'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'medfee_sumamt'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'收/退费总额 收退费均以正数表达。',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'medfee_sumamt'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'payment_amt'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'实收金额',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'payment_amt'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'offer_amt'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'优惠金额',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'offer_amt'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'invo_no'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'发票号 超过一张发票时，以“；”间隔',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'invo_no'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'invono_print_time'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'发票打印日期时间',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'invono_print_time'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'state'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'修改标志 1 是 0 否',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'state'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'reserve1'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'预留一 为系统处理该数据而预留',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'reserve1'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'reserve2'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'预留二 为系统处理该数据而预留',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'reserve2'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'data_clct_prdr_name'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据改造厂商名称 见公共字段【数据改造厂商名称】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'data_clct_prdr_name'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'crte_time'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据创建时间 见公共字段【数据创建时间】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'crte_time'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'updt_time'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据更新时间 见公共字段【数据更新时间】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'updt_time'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'deleted'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据删除状态 见公共字段【数据删除状态】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'deleted'
END
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID(N'[dbo].[tj_charge]') 
    AND minor_id = (
        SELECT column_id 
        FROM sys.columns 
        WHERE object_id = OBJECT_ID(N'[dbo].[tj_charge]') 
        AND name = N'deleted_time'
    )
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'数据删除时间 见公共字段【数据删除时间】说明',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tj_charge',
        @level2type = N'COLUMN',
        @level2name = N'deleted_time'
END
IF NOT EXISTS (
    SELECT * FROM sys.indexes 
    WHERE name = 'IDX_U_1_tj_' 
    AND object_id = OBJECT_ID(N'[dbo].[tj_charge]')
)
BEGIN
    CREATE UNIQUE INDEX [IDX_U_1_tj_] ON [dbo].[tj_charge] ([rid], [upload_time])
END
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE()
    DECLARE @ErrorNumber NVARCHAR(10) = CAST(ERROR_NUMBER() AS NVARCHAR(10))
    
    -- 记录失败
    EXEC [dbo].[log_table_result]
        @table_name = N'tj_charge',
        @status = N'失败',
        @message = @ErrorMessage,
        @error_code = @ErrorNumber
END CATCH
GO

-- 记录成功（如果表创建成功）
IF NOT EXISTS (
    SELECT 1 FROM [dbo].[table_creation_log] 
    WHERE [table_name] = N'tj_charge'
)
BEGIN
    EXEC [dbo].[log_table_result] 
        @table_name = N'tj_charge',
        @status = N'成功',
        @message = N'表创建成功',
        @error_code = NULL
END
GO

-- 如果发生错误，继续执行下一个表
IF @@ERROR <> 0
BEGIN
    PRINT '创建表 tj_charge 时发生错误，继续执行下一个表'
END
GO


-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = N'成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = N'失败' THEN 1 ELSE 0 END) as failed_count
FROM [dbo].[table_creation_log]
GO

-- 显示失败的表详情
SELECT 
    [table_name], 
    [message], 
    [error_code],
    [create_time] 
FROM [dbo].[table_creation_log] 
WHERE [status] = N'失败' 
ORDER BY [create_time]
GO