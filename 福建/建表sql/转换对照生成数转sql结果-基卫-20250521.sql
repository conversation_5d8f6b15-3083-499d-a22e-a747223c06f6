insert overwrite table mid_hos_phd_health_basic partition(dt)
select 
    concat(files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t_dwellerfile.df_id as patientid,
    t_dwellerfile.sscardno as card_no,
    '0' as card_type_code,
    '社保卡' as card_type_name,
    t_dwellerfile.adress_pro as addr_city,
    t_dwellerfile.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t_dwellerfile.sex as gender_code,
    d_gender_name.字典值名称 as gender_name,
    t_dwellerfile.name as full_name,
    '待定' as patient_souc_code,
    '待定' as patient_souc_name,
    t_dwellerfile.mstatus as mrg_stas_code,
    d_mrg_stas_name.字典值名称 as mrg_stas_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    null as birth_addr,
    t_dwellerfile.folk as nation_code,
    d_nation_name.字典值名称 as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as mobile,
    t_dwellerfile.telphone as tel,
    null as empr_poscode,
    t_dwellerfile.workplace as empr_name,
    null as empr_addr,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    t_dwellerfile.adress_pro as curr_addr_prov_name,
    null as curr_addr_city_code,
    t_dwellerfile.adress_city as curr_addr_city_name,
    null as curr_addr_coty_code,
    t_dwellerfile.adress_county as curr_addr_coty_name,
    null as curr_addr_town_code,
    t_dwellerfile.adress_rural as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    t_dwellerfile.adress_village as curr_addr_cotry_name,
    t_dwellerfile.adrss_hnumber as curr_addr_housnum,
    null as residential_addr,
    null as resd_addr_code,
    null as resd_addr_name,
    null as resd_addr_prov_code,
    t_dwellerfile.hkdshe as resd_addr_prov_name,
    null as resd_addr_coty_code,
    t_dwellerfile.hkdxia as resd_addr_coty_name,
    null as resd_addr_subd_code,
    t_dwellerfile.hkdzhe as resd_addr_subd_name,
    null as resd_addr_comm_code,
    t_dwellerfile.hkdcun as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    t_dwellerfile.hkdmph as resd_addr_housnum,
    null as resd_addr,
    null as resd_addr_poscode,
    null as coner_name,
    null as relation_code,
    null as relation_name,
    null as coner_addr,
    null as coner_org_name,
    null as coner_poscode,
    null as coner_tel,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from src_data.t_dwellerfile as t
left join 字典表 as d_gender_name on d_gender_name.字典类型='GENDER_CODE' and d_gender_name.字典值=t.sex
left join 字典表 as d_mrg_stas_name on d_mrg_stas_name.字典类型='MRG_STAS_NAME' and d_mrg_stas_name.字典值=t.mstatus
left join 字典表 as d_nation_name on d_nation_name.字典类型='NATION_NAME' and d_nation_name.字典值=t.folk
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_home partition(dt)
select 
    concat(files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t_dwellerfile.df_id as patientid,
    t_dwellerfile.name as full_name,
    t_dwellerfile.sex as gender_code,
    d_gender_name.字典值名称 as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    t_dwellerfile.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t_dwellerfile.cdegree as edu_background_code,
    d_edu_background_name.字典值名称 as edu_background_name,
    null as occup_code,
    null as occup_name,
    null as emp_status_code,
    null as emp_status_name,
    t_dwellerfile.rprtype as per_addr_code,
    d_per_addr_name.字典值名称 as per_addr_name,
    t_dwellerfile.folk as nation_code,
    d_nation_name.字典值名称 as nation_name,
    t_dwellerfile.bloodtype as blotype_abo_code,
    d_blotype_abo_name.字典值名称 as blotype_abo_name,
    t_dwellerfile.rhxx as blotype_rh_code,
    d_blotype_rh_name.字典值名称 as blotype_rh_name,
    t_dwellerfile.mstatus as mrg_stas_code,
    d_mrg_stas_name.字典值名称 as mrg_stas_name,
    null as medfee_paymtd_code,
    null as medfee_paymtd_name,
    null as expose_his_code,
    t_dwellerfile.expose as expose_his_name,
    case when ycbs00 is null then '0' else '1' end as hereditary_mark,
    null as hereditary_code,
    t_dwellerfile.ycbs00 as hereditary_name,
    null as chronic_code,
    null as chronic_name,
    d_disa_info_code.字典值 as disa_info_code,
    t_elderlyhealth.cjqk as disa_info_name,
    t_dwellerfile.cjqk0 as disable_certificate_no,
    null as brf_code,
    null as brf_name,
    case when cfpfss is null then '0' else '1' end as kitchen_exhaust_mark,
    null as kitchen_exhaust_code,
    t_dwellerfile.cfpfss as kitchen_exhaust_name,
    null as fuel_type_code,
    t_dwellerfile.rllx as fuel_type_name,
    null as drink_water_type_code,
    t_dwellerfile.ys as drink_water_type_name,
    null as wc_type_code,
    t_dwellerfile.cs as wc_type_name,
    null as avian_corral_type_code,
    t_dwellerfile.qcl as avian_corral_type_name,
    to_date(t.jdrq00,'%Y%m%d') as build_date,
    null as build_org_code,
    null as build_org_name,
    null as build_org_tel,
    null as mang_org_code,
    t_dwellerfile.doctor as duty_dor_no,
    sysuserinfo.zwxm00 as duty_dor_name,
    t_dwellerfile.bz0000 as dscr,
    sysuserinfo.lxdh00 as duty_dor_tel,
    t_dwellerfile.lrybh0 as registerhiscode,
    t_dwellerfile.lryxm0 as registerhisname,
    null as reg_dor_no,
    null as enter_dor_name,
    t_dwellerfile.cjsj as reg_date,
    null as inquirer_name,
    t_dwellerfile.investigators as inquirer_no,
    to_date(t.idate,'%Y%m%d') as inquirer_date,
    case t.sfyxda when '1' then '1' when '0' then '2' else t.sfyxda end as health_rec_status_code,
    '待定' as health_rec_status_name,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    t_dwellerfile.workplace as workplace,
    t_dwellerfile.feepaytype as feepay_type_code,
    t_dwellerfile.feepaytype as feepay_type_name,
    t_dwellerfile.pobservationtypecode as pobservation_type_code,
    t_dwellerfile.pobservationtypecode as pobservation_type_name,
    t_dwellerfile.pobservationcode as pobservation_code,
    t_dwellerfile.pobservationname as pobservation_name,
    t_dwellerfile.pobservationmethods as pobservationmethods_code,
    t_dwellerfile.pobservationmethods as pobservationmethods_name,
    t_dwellerfile.pobservationresultcode as pobservationresult_code,
    t_dwellerfile.pobservationresult as pobservationresult_name,
    t_dwellerfile.observationsdate as observationsdate,
    t_dwellerfile.observationedate as observationedate,
    t_dwellerfile.czzgbx as medpay_uebmi,
    t_dwellerfile.czjmbx as medpay_trpbmi,
    t_dwellerfile.xrhzyl as medpay_nrcmc,
    t_dwellerfile.pkjz as med_fee_pay_way_poor_assi,
    t_dwellerfile.syylbx as med_fee_pay_way_busi_hi,
    t_dwellerfile.qgf as medpay_fape,
    t_dwellerfile.qzf as med_fee_pay_way_full_ownpay,
    t_dwellerfile.qtfs00 as med_fee_pay_way_oth_way,
    t_dwellerfile.jzsfq as fmhis_fthr,
    t_dwellerfile.jzsmq as fmhis_mthr,
    t_dwellerfile.jzsxm as fmhis_brot_sist,
    t_dwellerfile.jzszn as fmhis_child,
    jktj_shfs.shfs_tydl_jcdlsj as phys_exer_adhe_excs_time,
    jktj_shfs.shsf_ysxg as habits_diet_code,
    jktj_shfs.shsf_ysxg as habits_diet_name,
    jktj_shfs.shsf_xyqk_xynl as start_smoke_way_age,
    jktj_shfs.shsf_xyqk_jynl as stop_smoke_way_age,
    jktj_shfs.shfs_yjqk_jjnl as stop_drink_way_age,
    jktj_shfs.shfs_yjqk_ksyjnl as start_drink_way_age,
    jktj_shfs.shfs_yjqk_sfcjj as last_year_drunkenness_mark,
    jktj_shfs.shfs_yjzl_ as drink_type_code,
    jktj_shfs.shfs_yjzl_ as drink_type_name,
    jktj_shfs.shfs_yjzl_qt0000 as drink_type_others,
    jktj_shfs.shfs_zybl_qk as prfs_expo_info,
    jktj_shfs.yyid00 as ins_id,
    jktj_shfs.jktjcs as hl_phys_exam_cnt,
    jktj_shfs.sfhs_zybl_hxpcsnr as prfs_expo_chem_prot_mes_cont,
    jktj_shfs.sfhs_zybl_dwcsnr as prfs_expo_toxi_prot_mes_cont,
    jktj_shfs.sfhs_zybl_sxcsnr as prfs_expo_rdat_prot_mes_cont,
    jktj_shfs.shfs_tydl_dlpl as excs_frqu_code,
    jktj_shfs.shfs_tydl_dlpl as excs_frqu_name,
    jktj_shfs.shfs_tydl_mcdlsj as each_excs_time,
    jktj_shfs.shfs_tydl_dlfs as excs_way,
    jktj_shfs.shsf_xyqk_xyzk as smoke_mark_code,
    jktj_shfs.shsf_xyqk_xyzk as smoke_mark_name,
    jktj_shfs.shsf_xyqk_rxyl as smok_day,
    jktj_shfs.shfs_yjqk_yjpl as drnk_frqu_code,
    jktj_shfs.shfs_yjqk_yjpl as drnk_frqu_name,
    jktj_shfs.shfs_yjqk_ryjl as drnk_day,
    jktj_shfs.shfs_yjqk_sfjj as stop_drink_code,
    jktj_shfs.shfs_yjqk_sfjj as stop_drink_name,
    jktj_shfs.sfhs_zybl_fc as prfs_expo_dust,
    jktj_shfs.sfhs_zybl_fccs as prfs_expo_dust_prot_mes,
    jktj_shfs.sfhs_zybl_qt as prfs_expo_oth,
    jktj_shfs.sfhs_zybl_qtcs as prfs_expo_oth_prot_mes,
    jktj_shfs.sfhs_zybl_fccsnr as prfs_expo_dust_prot_mes_cont,
    jktj_shfs.sfhs_zybl_qtcsnr as prfs_expo_oth_prot_mes_cont,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from src_data.t_dwellerfile as t
left join 字典表 as d_gender_name on d_gender_name.字典类型='GENDER_CODE' and d_gender_name.字典值=t.sex
left join sysuserinfo on sysuserinfo.ygbh00=t.doctor
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_illness partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    ot_jbgw_person_pdh.disease_history_id as ref_no,
    '待定' as files_uscid,
    ot_jbgw_person_pdh.health_record_no as patientid,
    ot_jbgw_person_pdh.ot_jbgw_person_pdh_001 as disease_code,
    '待定' as disease_name,
    ot_jbgw_person_pdh.ot_jbgw_person_pdh_002 as cnfm_date,
    null as remark,
    '0' as state,
    ot_jbgw_person_pdh.update_date as business_time,
    null as reserve1,
    null as reserve2,
    ot_jbgw_person_pdh.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    ot_jbgw_person_pdh.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    ot_jbgw_person_pdh.provice_district_code as admdvs,
    ot_jbgw_person_pdh.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.ot_jbgw_person_pdh as t
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_surgery partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    ot_jbgw_person_poh.surgery_history_id as ref_no,
    '待定' as files_uscid,
    ot_jbgw_person_poh.health_record_no as patientid,
    ot_jbgw_person_poh.ot_jbgw_person_poh_001 as proc_name,
    ot_jbgw_person_poh.ot_jbgw_person_poh_002 as proc_date,
    null as remark,
    '0' as state,
    ot_jbgw_person_poh.update_date as business_time,
    null as reserve1,
    null as reserve2,
    ot_jbgw_person_poh.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    ot_jbgw_person_poh.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    ot_jbgw_person_poh.provice_district_code as admdvs,
    ot_jbgw_person_poh.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.ot_jbgw_person_poh as t
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_injury partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    ot_jbgw_person_trauma.trauma_history_id as ref_no,
    '待定' as files_uscid,
    ot_jbgw_person_trauma.health_record_no as patientid,
    ot_jbgw_person_trauma.ot_jbgw_person_trauma001 as trauma_name,
    ot_jbgw_person_trauma.ot_jbgw_person_trauma02 as trauma_date,
    null as remark,
    '0' as state,
    ot_jbgw_person_trauma.update_date as business_time,
    null as reserve1,
    null as reserve2,
    ot_jbgw_person_trauma.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    ot_jbgw_person_trauma.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    ot_jbgw_person_trauma.provice_district_code as admdvs,
    ot_jbgw_person_trauma.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.ot_jbgw_person_trauma as t
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_transfusion partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    ot_jbgw_person_blood.blood_transfusion_history_id as ref_no,
    '待定' as files_uscid,
    ot_jbgw_person_blood.health_record_no as patientid,
    ot_jbgw_person_blood.ot_jbgw_person_blood_002 as transfuse_reason,
    ot_jbgw_person_blood.ot_jbgw_person_blood_001 as transfuse_datetime,
    null as remark,
    '0' as state,
    ot_jbgw_person_blood.update_date as business_time,
    null as reserve1,
    null as reserve2,
    ot_jbgw_person_blood.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    ot_jbgw_person_blood.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    ot_jbgw_person_blood.provice_district_code as admdvs,
    ot_jbgw_person_blood.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.ot_jbgw_person_blood as t
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phd_health_family partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as ref_no,
    '待定' as files_uscid,
    t_dwellerfile.df_id as patientid,
    '待定' as relation_patient_code,
    '待定' as relation_patient_name,
    '待定' as disease_code,
    t_dwellerfile.jzsfq as disease_name,
    null as remark,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from src_data. as t
where jzsfq is not null
union all
where jzsmq is not null
union all
where jzsxm is not null
union all
where jzszn is not null
) as tab

-- ================================================
insert overwrite table mid_hos_phf_health_sign partition(dt)
select 
    concat(sign_up_uscid,signid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    ot_jtys_sign.ot_jtys_sign_id as signid,
    ot_jtys_sign.family_doctor_id as sign_dor_no,
    '待定' as sign_dor_name,
    ot_jtys_sign.jtys_team_id as sign_team_code,
    oe_jtys_team.team_name as sign_team_name,
    ot_jtys_sign. as psncert_type_code,
    d_psncert_type_name.字典值名称 as psncert_type_name,
    ot_jtys_sign.id_card_no as cert_no,
    ot_jtys_sign.name as resident_name,
    ot_jtys_sign.health_record_id as health_rec_id,
    ot_jtys_sign.service_date as sign_datetime,
    ot_jtys_sign.ot_jtys_sign_004 as unsign_date,
    null as unsign_reason,
    '待定' as sign_status_code,
    '待定' as sign_status_name,
    null as reg_dor_code,
    null as reg_dor_name,
    null as reg_time,
    null as remark,
    '0' as state,
    ot_jtys_sign.create_time as create_time,
    null as reserve1,
    null as reserve2,
    oe_jtys_team.presideid_card_type as presideid_card_type_code,
    oe_jtys_team.presideid_card_type as presideid_card_type_name,
    oe_jtys_team.presideid_card_no as presideid_card_no,
    default_update_time as upload_time,
    ot_jtys_sign.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    ot_jtys_sign.provice_district_code as admdvs,
    ot_jtys_sign.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.ot_jtys_sign as t
left join oe_jtys_team on t.FAMILY_DOCTOR_ ID = oe_jtys_team.FAMILY_DOCTOR_ ID
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phf_health_record partition(dt)
select 
    concat(sign_up_uscid,health_rec_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t_familyfile.zzdah0 as health_rec_id,
    '待定' as householder_psncert_type_code,
    '待定' as householder_psncert_type_name,
    '待定' as householder_cert_no,
    t_familyfile.hmname as resident_name,
    '待定' as patientid,
    t_familyfile.tel as family_tel,
    null as family_addr_code,
    null as family_addr_name,
    null as family_addr_prov_code,
    t_familyfile.adress_pro as family_addr_prov_name,
    null as family_addr_city_code,
    t_familyfile.adress_city as family_addr_city_name,
    null as family_addr_coty_code,
    t_familyfile.adress_county as family_addr_coty_name,
    null as family_addr_town_code,
    t_familyfile.adress_rural as family_addr_town_name,
    null as family_addr_comm_code,
    null as family_addr_comm_name,
    t_familyfile.adress_village as family_addr_cotry_name,
    t_familyfile.adrss_hnumber as family_addr_housnum,
    concat(adress_pro,adress_city,adress_county,adress_rural,adress_village,adrss_hnumber) as family_addr,
    null as poscode,
    null as family_rec_status_code,
    null as family_rec_status_name,
    null as inquirer_staff_no,
    null as inquirer_staff_name,
    null as inquirer_date,
    t_familyfile.creator as registerhiscode,
    '待定' as registerhisname,
    to_date(t.cdate,'%Y%m%d) as reg_date,
    null as data_rank,
    default_create_time as create_time,
    t_familyfile.isdel as state,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    default_update_time as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    to_date(t.cdate,'%Y%m%d) as crte_time,
    t_familyfile.isdel as deleted,
    null as deleted_time
from src_data.t_familyfile as t
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phf_health_member partition(dt)
select 
    concat(sign_up_uscid,health_rec_id,psncert_type_code,psncert_type_name,cert_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t_familyfile.f_id as health_rec_id,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t_dwellerfile.idcardno as cert_no,
    t_familyfile.hmname as resident_name,
    case t.r_id ='0' then '1' else '0' end as householder_mark,
    t_dwellerfile.r_id as householder_relation_code,
    d_householder_relation_name.字典值名称 as householder_relation_name,
    null as data_rank,
    t_dwellerfile.cjsj as create_time,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(zhgxrq,zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from src_data.t_dwellerfile as t
left join t_familyfile on t.f_id = t_familyfile.f_id
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phf_health_staff partition(dt)
select 
    concat(job_no,uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    sysuserinfo.userid as job_no,
    '待定' as uscid,
    '待定' as rgst_name,
    sysuserinfo.zwxm00 as full_name,
    sysuserinfo.sfzh00 as id_card,
    sysuserinfo.groupid as dept_code,
    null as team,
    null as proftechttl_code,
    null as proftechttl_name,
    null as job_title_code,
    null as job_title_name,
    sysuserinfo.csrq00 as brdy,
    sysuserinfo.ygxz00 as psn_type_code,
    '待定' as psn_type_name,
    null as practice_type_code,
    null as practice_type_name,
    null as gp_flag,
    null as edu_background_code,
    null as edu_background_name,
    null as professional_code,
    null as professional_name,
    '0' as state,
    sysuserinfo.xgrqsj as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    sysuserinfo.xgrqsj as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    sysuserinfo.cjrqsj as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data. as t
where t.ygxz00 in ('0','1','2') -- 0实习医生,1处方医生,2护士
) as tab

-- ================================================
insert overwrite table mid_hos_phf_health_team partition(dt)
select 
    concat(unified_uscid,team_code,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as unified_uscid,
    oe_jtys_team.jtys_team_id as team_code,
    oe_jtys_team.team_name as team_name,
    '1' as state,
    oe_jtys_team.upload_date as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    oe_jtys_team.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    oe_jtys_team.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_phe_record_info partition(dt)
select 
    concat(test_uscid,examination_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    jktj_ybzk.df_id as patientid,
    t_dwellerfile.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    null as appoint_id,
    null as plan_code,
    t_dwellerfile.name as full_name,
    to_date(t.edate,'%Y%m%d') as examination_date,
    null as main_dor_no,
    jktj_ybzk.doctor as main_dor_name,
    d_sympt_code.字典值 as sympt_code,
    jktj_ybzk.tjzzqk as sympt_name,
    jktj_ybzk.ybzk_tiwen as tprt,
    jktj_ybzk.ybzk_ml as pule,
    jktj_ybzk.ybzk_hxpl as vent_frqu,
    jktj_ybzk.ybzk_zszy as left_dbp,
    jktj_ybzk.ybzk_zssy as left_sbp,
    jktj_ybzk.ybzk_yszy as right_dbp,
    jktj_ybzk.ybzk_yssy as right_sbp,
    jktj_ybzk.ybzk_sg as height,
    jktj_ybzk.ybzk_tz as wt,
    jktj_ybzk.ybzk_tzzs as bmi,
    jktj_ybzk.ybzk_yw as waist,
    jktj_ybzk.ybzk_tunwei as hipline,
    jktj_ybzk.ybzk_ytwbz as whr,
    jktj_ybzk.ybzk_lnrzgn as elder_cognition_res_code,
    d_elder_cognition_res_name.字典值名称 as elder_cognition_res_name,
    jktj_ybzk.ybzk_lnzljc as elder_wit_score,
    jktj_ybzk.lnrjkpj as elder_health_status_code,
    d_elder_health_status_name.字典值名称 as elder_health_status_name,
    jktj_ybzk.lnrshpj as elder_self_eval_code,
    d_elder_self_eval_name.字典值名称 as elder_self_eval_name,
    jktj_ybzk.ybzk_lnqgzt as elder_emotional_status_code,
    d_elder_emotional_status_name.字典值名称 as elder_emotional_status_name,
    jktj_ybzk.ybzk_lnyypf as elder_depression_score,
    jktj_shfs.shfs_tydl_dlpl as excs_frqu_code,
    d_excs_frqu_name.字典值名称 as excs_frqu_name,
    jktj_shfs.shfs_tydl_mcdlsj as exercise_each,
    jktj_shfs.shfs_tydl_jcdlsj as insist_exercise_month,
    jktj_shfs.shfs_tydl_dlfs as exercise_code,
    jktj_shfs.shsf_ysxg as habits_diet_code,
    d_habits_diet_name.字典值名称 as habits_diet_name,
    jktj_shfs.shsf_xyqk_xyzk as smok_info_code,
    d_smok_info_name.字典值名称 as smok_info_name,
    jktj_shfs.shsf_xyqk_rxyl as smok_day,
    jktj_shfs.shsf_xyqk_xynl as start_smoke_age,
    jktj_shfs.shsf_xyqk_jynl as stop_smoke_age,
    jktj_shfs.shfs_yjqk_yjpl as drnk_frqu_code,
    d_drnk_frqu_name.字典值名称 as drnk_frqu_name,
    jktj_shfs.shfs_yjqk_ryjl as drnk_day,
    jktj_shfs.shfs_yjqk_sfjj as stop_drink_code,
    d_stop_drink_name.字典值名称 as stop_drink_name,
    jktj_shfs.shfs_yjqk_jjnl as stop_drink_age,
    jktj_shfs.shfs_yjqk_ksyjnl as start_drink_age,
    jktj_shfs.shfs_yjqk_sfcjj as last_year_drunkenness_mark,
    jktj_shfs.shfs_yjzl_ as drink_type_code,
    d_drink_type_name.字典值名称 as drink_type_name,
    jktj_zqgn.jktj_zqgn_kqkc as oral_exterior_code,
    d_oral_exterior_name.字典值名称 as oral_exterior_name,
    jktj_zqgn.jktj_zqgn_kqcl as dentition_type_code,
    d_dentition_type_name.字典值名称 as dentition_type_name,
    concat(jktj_zqgn.jktj_zqgn_quechi,';',jktj_zqgn.jktj_zqgn_quchi0,';',jktj_zqgn.jktj_zqgn_yichi0) as dentition_explain,
    jktj_zqgn.jktj_zqgn_kqyb as phary_exam_res_code,
    d_phary_exam_res_name.字典值名称 as phary_exam_res_name,
    jktj_zqgn.jktj_zqgn_slzy as left_original_value,
    jktj_zqgn.jktj_zqgn_slyy as right_original_hyperopia_value,
    jktj_zqgn.jktj_zqgn_jzslzy as left_redress_value,
    jktj_zqgn.jktj_zqgn_jzslyy as right_redress_hyperopia_value,
    null as left_vision_code,
    null as left_vision_name,
    null as right_vision_code,
    null as right_vision_name,
    jktj_ct.ct_yd as eyeground_abnorm_mark,
    jktj_ct.ct_ydqt as fundoscopy_abnorm_descr,
    jktj_zqgn.jktj_zqgn_tl as hear_check_res_code,
    d_hear_check_res_name.字典值名称 as hear_check_res_name,
    jktj_zqgn.jktj_zqgn_ydgn as sport_function_status_code,
    d_sport_function_status_name.字典值名称 as sport_function_status_name,
    jktj_ct.ct_pf as skin_check_abnorm_code,
    d_skin_check_abnorm_name.字典值名称 as skin_check_abnorm_name,
    jktj_ct.ct_gm as scleral_check_res_code,
    d_scleral_check_res_name.字典值名称 as scleral_check_res_name,
    jktj_ct.ct_lbj as lymph_check_res_code,
    d_lymph_check_res_name.字典值名称 as lymph_check_res_name,
    jktj_ct.ct_ftzx as barrel_chest_mark,
    jktj_ct.ct_fhxy as lung_abnorm_breath_mark,
    jktj_ct.ct_fhxyyc as lung_abnorm_breath_descr,
    jktj_ct.ct_fly as lung_rale_code,
    d_lung_rale_name.字典值名称 as lung_rale_name,
    jktj_ct.ct_flyqt as lung_rale_describe,
    jktj_ct.ct_xzxl as heart_rate,
    jktj_ct.ct_xzxinl as heart_rate_type_code,
    d_heart_rate_type_name.字典值名称 as heart_rate_type_name,
    jktj_ct.ct_xzzy as heart_murmur_mark,
    jktj_ct.ct_xzzyqt as heart_murmur_describe,
    jktj_ct.ct_fbyt as abdominal_tend_mark,
    jktj_ct.ct_fbytqt as abdominal_tend_descr,
    jktj_ct.ct_fbbk as abdominal_mass_mark,
    jktj_ct.ct_fbbkqt as abdominal_mass_descr,
    jktj_ct.ct_fbgd as abdominal_hepatauxe_mark,
    jktj_ct.ct_fbgdqt as abdominal_hepatauxe_descr,
    jktj_ct.ct_fbpd as splenauxe_mark,
    jktj_ct.ct_fbpdqt as splenauxe_descr,
    jktj_ct.ct_fbydzy as abdominal_dullness_mark,
    jktj_ct.ct_fbydzyqt as abdominal_dullness_descr,
    jktj_ct.ct_xzsz as legs_edema_check_res_code,
    d_legs_edema_check_res_name.字典值名称 as legs_edema_check_res_name,
    jktj_ct.ct_zbdmbd as foot_dorsal_artery_code,
    d_foot_dorsal_artery_name.字典值名称 as foot_dorsal_artery_name,
    jktj_ct.ct_gmzz as anus_check_res_type_code,
    d_anus_check_res_type_name.字典值名称 as anus_check_res_type_name,
    jktj_ct.ct_rxqk as breast_check_res_code,
    d_breast_check_res_name.字典值名称 as breast_check_res_name,
    jktj_ct.ct_fkwy as vulva_abnorm_mark,
    jktj_ct.ct_fkwyqt as vulva_abnorm_descr,
    jktj_ct.ct_fkyd as vagina_abnorm_mark,
    jktj_ct.ct_fkydqt as vagina_abnorm_descr,
    jktj_ct.ct_fkgj as cervix_abnorm_mark,
    jktj_ct.ct_fkgjqt as cervix_abnorm_descr,
    jktj_ct.ct_fkgt as corpusuteri_abnorm_mark,
    jktj_ct.ct_fkgtqt as corpusuteri_abnorm_descr,
    jktj_ct.ct_fkfj as adnex_abnorm_mark,
    jktj_ct.ct_fkfjqt as gyn_adnex_abnorm_descr,
    jktj_ct.ct_qt as other_health_check_res,
    jktj_fzjc.fzjc_xdt as ecg_abnorm_mark,
    jktj_fzjc.fzjc_xdtqt as ecg_abnorm_descr,
    jktj_fzjc.fzjc_xbxxp as xray_abnorm_mark,
    jktj_fzjc.fzjc_xbxxqt as xray_abnorm_descr,
    jktj_fzjc.fzjc_bc as bscan_abnorm_mark,
    jktj_fzjc.fzjc_bcqt as bscan_abnorm_descr,
    jktj_fzjc.fzjc_gjtp as cps_abnorm_mark,
    jktj_fzjc.fzjc_gjtpqt as cps_abnorm_descr,
    jktj_fzjc.fzjc_qt0000 as other_assist_check,
    jktj_zyjkwt.zyjkwt_nxg as cardiovascular_code,
    jktj_zyjkwt.zyjkwt_nxgqt as cardiovascular_name,
    jktj_zyjkwt.zyjkwt_sz as chronic_kidney_code,
    jktj_zyjkwt.zyjkwt_szqt as chronic_kidney_name,
    jktj_zyjkwt.zyjkwt_xzwfx as cardiopathy_code,
    jktj_zyjkwt.zyjkwt_xzqt as cardiopathy_name,
    jktj_zyjkwt.zyjkwt_xgwfx as vas_code,
    jktj_zyjkwt.zyjkwt_xgqt as vas_name,
    jktj_zyjkwt.zyjkwt_ybwfx as oculopathy_code,
    jktj_zyjkwt.zyjkwt_ybqt as oculopathy_name,
    jktj_zyjkwt.zyjkwt_sjxtjb as neuro_exam_abnormal_mark,
    jktj_zyjkwt.zyjkwt_sjxtqt as neuro_exam_abnormal_descr,
    jktj_zyjkwt.zyjkwt_qtxtjb as systemic_disease,
    jktj_zyjkwt.zyjkwt_qtxtqt as systemic_disease_descr,
    jktj_jkpj.jkpj_tjsfyc as hl_eval_abnorm_flag,
    concat(jktj_jkpj.jkpj_tjyc1,';',jktj_jkpj.jkpj_tjyc2,';',jktj_jkpj.jkpj_tjyc3,';',jktj_jkpj.jkpj_tjyc4) as abnormal_descr,
    d_health_guide_code.字典值 as health_guide_code,
    jktj_jkpj.jkzd as health_guide_name,
    d_risk_control_ad_code.字典值 as risk_control_ad_code,
    jktj_jkpj.wxyskz as risk_control_ad_name,
    jktj_jkpj.wxyskz_jtzmb as aim_weight,
    d_offer_vacc_code.字典值 as offer_vacc_code,
    jktj_jkpj.wxyskz_ymjz as offer_vacc_name,
    null as data_rank,
    '0' as state,
    jktj_ybzk.ctime as business_time,
    null as reserve1,
    null as reserve2,
    jktj_ybzk.gxygbh as updt_emplo_no,
    jktj_ybzk.gxygxm as updt_emplo_name,
    default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_ybzk.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.jktj_ybzk as t
and t.df_id= t_dwellerfile.df_id
and t.df_id= jktj_shfs.df_id
and t.df_id= jktj_zqgn.df_id
and t.df_id= jktj_ct.df_id
and t.df_id= jktj_fzjc.df_id
and t.df_id= jktj_zyjkwt.df_id
and t.df_id= jktj_jkpj.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phe_med_info partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t_mxjb_sf_yyqk.ywbh_id as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    t_mxjb_sf_yyqk.ywmc as drug_name,
    null as drugusage,
    null as drug_used_dosunt,
    null as drug_used_sdose,
    null as drug_used_idose,
    d_drug_used_way_code.字典值 as drug_used_way_code,
    t_mxjb_sf_yyqk.ywyf as drug_used_way_name,
    t_mxjb_sf_yyqk.yysj as medication_time,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    t_mxjb_sf_yyqk.fyycx as medication_compliance_code,
    d_medication_compliance_name.字典值名称 as medication_compliance_name,
    null as data_rank,
    '0' as state,
    default_update_time as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    default_update_time as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    default_update_time as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.t_mxjb_sf_yyqk as t
and t.df_id= jktj_ybzk.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phe_home_bed_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_jtbcs.jtbcsid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    to_date(t.jcrq,'%Y%m%d') as build_bed_date,
    to_date(t.ccrq,'%Y%m%d') as remove_bed_date,
    jktj_jtbcs.bjyy as bed_reason,
    jktj_jtbcs.yljgmc as bed_org_name,
    jktj_jtbcs.bcbah as medcasno,
    null as data_rank,
    '0' as state,
    jktj_jtbcs.cdate as business_time,
    null as reserve1,
    null as reserve2,
    jktj_jtbcs.cdate as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_jtbcs.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.jktj_jtbcs as t
and t.df_id= jktj_ybzk.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phe_nonimm_vacc_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_fmygh.fmyghid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    d_vacc_code.字典值 as vacc_code,
    jktj_fmygh.ymmc as vacc_name,
    to_date(t.jzrq,'%Y%m%d') as vacc_time,
    '待定' as vaccinate_uscid,
    jktj_fmygh.jzyy as vaccinate_org_name,
    null as data_rank,
    '0' as state,
    jktj_fmygh.cdate as business_time,
    null as reserve1,
    null as reserve2,
    jktj_fmygh.cdate as upload_time,
    to_timestamp(t.edate) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_fmygh.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data.jktj_fmygh as t
and t.df_id= jktj_ybzk.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_phe_elderly_care_assess partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_ybzk.jktj_ybzkid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    null as dinner_score,
    null as freshen_score,
    null as dress_score,
    null as toilet_score,
    null as acty_score,
    jktj_ybzk.lnrshpj as total_score,
    null as data_rank,
    '0' as state,
    jktj_ybzk.ctime as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_ybzk.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data. as t
where t.lnrshpj is not null
) as tab

-- ================================================
insert overwrite table mid_hos_phe_lab_record partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_fzjc.jktj_fzjcid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    null as check_proj_type_code,
    null as check_proj_type_name,
    null as check_proj_code,
    null as check_proj_name,
    '301' as check_code,
    '空腹血糖' as check_name,
    null as check_type,
    jktj_ybzk.edate as rpt_date,
    jktj_fzjc.fzjc_kfxt as check_result,
    'mmol/L' as unit,
    '3.9~6.1' as ref_val,
    '待定' as exam_rslt_abn_code,
    '待定' as exam_rslt_abn_name,
    jktj_ybzk.gxygbh as checker_no,
    jktj_ybzk.gxygxm as checker_name,
    jktj_ybzk.gxygbh as chker_no,
    jktj_ybzk.gxygxm as audit_operator_name,
    jktj_fzjc.jktj_fzjcid as sort,
    null as data_rank,
    '0' as state,
    jktj_fzjc.cdate as business_time,
    null as reserve1,
    null as reserve2,
    jktj_fzjc.cdate as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_fzjc.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from src_data. as t
where 1=1 and fzjc_kfxt is not null
and t.df_id= jktj_ybzk.df_id
) as tab

-- ================================================
insert overwrite table mid_hos_phe_physical_exam partition(dt)
select 
    concat(ref_no,test_uscid) as rid
    ,tab.*
from 
(
select  
    jktj_ct.jktj_ctid as ref_no,
    null as exam_datetime,
    '待定' as test_uscid,
    jktj_ct.df_id as patientid,
    null as examination_no,
    null as check_id,
    null as skin_codition_code,
    null as skin_codition_name,
    null as skin_condition_remark,
    null as sclera_code,
    null as sclera_name,
    null as sclera_remark,
    null as lymph_node_code,
    null as lymph_node_name,
    null as lymph_node_remark,
    null as barrel_chest_flag,
    null as breath_sound_code,
    null as breath_sound_name,
    null as breath_sound_abnormal_remark,
    null as rale_code,
    null as rale_name,
    null as rale_remark,
    null as heart_rate,
    null as heart_rhythm_code,
    null as heart_rhythm_name,
    null as heart_noise_flag,
    null as heart_noise_remark,
    null as tenderness_flag,
    null as tenderness_remark,
    null as bag_piece_flag,
    null as bag_piece_remark,
    null as hepatomegaly_flag,
    null as hepatomegaly_remark,
    null as splenomegaly_flag,
    null as splenomegaly_remark,
    null as move_dullness_flag,
    null as move_dullness_remark,
    null as legs_edema_check_res_code,
    null as legs_edema_check_res_name,
    null as foot_dorsal_artery_code,
    null as foot_dorsal_artery_name,
    null as anus_check_res_type_code,
    null as anus_check_res_type_name,
    null as anus_dre_remark,
    null as mammary_gland_code,
    null as mammary_gland_name,
    null as mammary_gland_remark,
    null as vulva_code,
    null as vulva_name,
    null as vulva_remark,
    null as vagina_code,
    null as vagina_name,
    null as vagina_remark,
    null as cervical_code,
    null as cervical_name,
    null as cervical_remark,
    null as uterine_body_code,
    null as uterine_body_name,
    null as uterine_body_remark,
    null as attachment_code,
    null as attachment_name,
    null as attachment_remark,
    null as fundus_code,
    null as fundus_name,
    null as fundus_remark
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_phe_physical_exam_extend partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as exam_datetime,
    '待定' as test_uscid,
    null as patientid,
    null as examination_no,
    null as check_id,
    jktj_zqgn.jktj_zqgn_kqkc as oral_lips_code,
    '待定' as oral_lips_name,
    jktj_zqgn.jktj_zqgn_kqcl as oral_dentition_code,
    '待定' as oral_dentition_name,
    jktj_zqgn.jktj_zqgn_kqyb as oral_pharyngeal_code,
    '待定' as oral_pharyngeal_name,
    jktj_zqgn.jktj_zqgn_quechi as oral_dentition_missing_tooth,
    jktj_zqgn.jktj_zqgn_quchi0 as oral_dentition_decayed_tooth,
    jktj_zqgn.jktj_zqgn_yichi0 as oral_dentition_false_tooth,
    null as eyesight_left,
    null as eyesight_right,
    null as left_redress_value,
    null as right_redress_hyperopia_value,
    null as hearing_code,
    null as hearing_name,
    null as motor_function_code,
    null as motor_function_name,
    null as other_remark,
    null as modify_datetime,
    null as upload_time,
    null as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_phe_main_issues partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as exam_datetime,
    '待定' as test_uscid,
    jktj_zyjkwt.df_id as patientid,
    null as examination_no,
    null as hl_phys_exam_cnt,
    null as cardiovascular_code,
    null as cardiovascular_name,
    null as cerebrovascular_disease_remark,
    null as chronic_kidney_code,
    null as chronic_kidney_name,
    null as kidney_disease_remark,
    null as cardiopathy_code,
    null as cardiopathy_name,
    null as heart_disease_remark,
    null as vas_code,
    null as vas_name,
    null as vascular_disease_remark,
    null as oculopathy_code,
    null as oculopathy_name,
    null as eyes_disease_remark,
    null as nervous_disease_code,
    null as nervous_disease_name,
    null as nervous_disease_remark,
    null as other_disease_code,
    null as other_disease_name,
    null as other_disease_remark,
    null as gentle_constitution,
    null as tcm_vdc,
    null as yang_deficiency_constitution,
    null as dryness_constitution,
    null as phlegm_damp_constitution,
    null as damp_heat_constitution,
    null as blood_stasis_constitution,
    null as look_depressed_constitution,
    null as sensitive_constitution,
    null as medical_abnormal_flag,
    null as medical_abnormal_remark1,
    null as medical_abnormal_remark2,
    null as medical_abnormal_remark3,
    null as medical_abnormal_remark4,
    null as health_guide_code,
    null as health_guide_name,
    null as risk_factor_control_code,
    null as risk_factor_control_name,
    null as weight_target,
    null as vaccination,
    null as risk_factor_control_remark,
    null as health_guide_content,
    null as healthy_teaching,
    null as register_datetime,
    null as upload_time,
    null as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_phe_organ_function partition(dt)
select 
    concat(hl_phys_exam_orga_efcc_id,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_zqgn.jktj_zqgnid as hl_phys_exam_orga_efcc_id,
    '待定' as test_uscid,
    jktj_zqgn.df_id as patientid,
    jktj_zqgn.jktjcs as hl_phys_exam_cnt,
    jktj_zqgn.jktj_zqgn_kqkc as oral_lips_code,
    '待定' as oral_lips_name,
    jktj_zqgn.jktj_zqgn_kqcl as oral_dentition_code,
    '待定' as oral_dentition_name,
    jktj_zqgn.jktj_zqgn_kqyb as oral_pharyngeal_code,
    '待定' as oral_pharyngeal_name,
    jktj_zqgn.jktj_zqgn_slzy as eyesight_left,
    jktj_zqgn.jktj_zqgn_slyy as eyesight_right,
    jktj_zqgn.jktj_zqgn_jzslzy as left_redress_value,
    jktj_zqgn.jktj_zqgn_jzslyy as right_redress_hyperopia_value,
    jktj_zqgn.jktj_zqgn_tl as hearing_code,
    jktj_zqgn.jktj_zqgn_tl as hearing_name,
    jktj_zqgn.jktj_zqgn_ydgn as motor_function_code,
    jktj_zqgn.jktj_zqgn_ydgn as motor_function_name,
    jktj_zqgn.jktj_zqgn_quechi as ora_tooth_row_mis,
    jktj_zqgn.jktj_zqgn_quchi0 as ora_tooth_row_cari,
    jktj_zqgn.jktj_zqgn_yichi0 as ora_tooth_row_denti,
    null as upload_time,
    null as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dmh_report_card partition(dt)
select 
    concat(rpt_org_uscid,rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as rpt_org_uscid,
    '待定' as rpot_card_id,
    t_dwellerfile.name as full_name,
    t_dwellerfile.sex as gender_code,
    字典. as gender_name,
    t_dwellerfile.birthday as brdy,
    t_dwellerfile.idcardno as certno,
    case when IDCARDNO is not null then '01' else null as psncert_type_code,
    case when IDCARDNO is not null then '01' else null as psncert_type_name,
    t_dwellerfile.workstatus as occup_code,
    字典. as occup_name,
    '待定' as rpt_date,
    '待定' as report_sbp,
    '待定' as rpot_dbp,
    '待定' as reporter_id,
    '待定' as report_name,
    '待定' as rpt_dept_code,
    '待定' as rpt_dept_name,
    t_cm_dinfo.cdate as business_time,
    case when EDATE is not null then '1' else '0' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t
T_CM_DINFO.DF_ID = T_DWELLERFILE.DF_ID
gender_code = SEX
occup_type_code = WORKSTATUS
WHERE 1=1
AND T_CM_DINFO.CCL_ID = '1'
) as tab

-- ================================================
insert overwrite table mid_hos_dmh_manage_card partition(dt)
select 
    concat(control_uscid,hpid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as hpid,
    '待定' as medcasno,
    '待定' as card_no,
    '待定' as card_type_code,
    '待定' as card_type_name,
    t_dwellerfile.name as full_name,
    t_dwellerfile.sex as gender_code,
    字典. as gender_name,
    t_dwellerfile.birthday as brdy,
    t_dwellerfile.telphone as tel,
    t_dwellerfile.idcardno as certno,
    case when IDCARDNO is not null then '01' else null as psncert_type_code,
    case when IDCARDNO is not null then '01' else null as psncert_type_name,
    t_dwellerfile.workstatus as occup_code,
    字典. as occup_name,
    t_dwellerfile.weight as wt,
    t_dwellerfile.height as height,
    t_dwellerfile.ycbs00 as family_his_code,
    这个码表在哪？用说明里的内容？. as family_his_name,
    t_cm_dinfo.smokestatus as exist_smoke_code,
    字典. as exist_smoke_name,
    t_cm_dinfo.nosmokeage as stop_smoke_time,
    t_cm_dinfo.bsmokeage as start_smoke_age,
    t_cm_dinfo.drinkfrequency as drnk_frqu_code,
    字典. as drnk_frqu_name,
    t_cm_dinfo.bdrinkage as start_drink_year,
    '待定' as overdrink_mark,
    '待定' as habits_exercise_code,
    '待定' as habits_exercise_name,
    '待定' as not_medication_sbp,
    '待定' as un_dose_dbp,
    '待定' as self_help_ability_code,
    '待定' as self_help_ability_name,
    t_cm_dinfo.hazardworkmark as occup_mark,
    t_cm_dinfo.hazardworkname as occup_risk_name,
    t_cm_dinfo.hazardworktype as occup_risk_type_code,
    这个码表在哪？用说明里的内容？. as occup_risk_type_name,
    t_cm_dinfo.hazardwork as danger_occup,
    t_cm_dinfo.hazardworktime as danger_occup_year,
    t_cm_dinfo.protectmark as protectivemeasures_mark,
    '待定' as stop_mang_date,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as info_source_code,
    '待定' as info_source_name,
    '待定' as rpot_card_id,
    '待定' as comm_resp_dor_no,
    '待定' as comm_resp_dor_name,
    '待定' as now_mang_dor_no,
    '待定' as now_mang_dor_name,
    '待定' as now_mang_team_code,
    '待定' as now_mang_team_name,
    '待定' as dft_fu_dor_no,
    '待定' as dft_fu_dor_name,
    '待定' as build_cards_dor_no,
    '待定' as build_cards_dor_name,
    '待定' as build_cards_dept_code,
    '待定' as build_cards_dept_name,
    '待定' as build_cards_team_code,
    '待定' as build_cards_team_name,
    '待定' as build_cards_org_code,
    '待定' as build_cards_org_name,
    '待定' as build_cards_time,
    '待定' as obj_state_code,
    '待定' as obj_state_name,
    '待定' as addr_type_code,
    '待定' as addr_type_name,
    '待定' as addr_name,
    '待定' as residential_code,
    '待定' as residential_name,
    t_dwellerfile.adress_pro as curr_addr_prov_code,
    字典. as curr_addr_prov_name,
    t_dwellerfile. as curr_addr_city_code,
    字典. as curr_addr_city_name,
    t_dwellerfile.adress_county as curr_addr_coty_code,
    字典. as curr_addr_coty_name,
    t_dwellerfile.adress_rural as curr_addr_town_code,
    字典. as curr_addr_town_name,
    '待定' as curr_addr_comm_code,
    '待定' as curr_addr_comm_name,
    t_dwellerfile.adress_village as curr_addr_cotry_name,
    t_dwellerfile.adrss_hnumber as residential_housnum,
    '待定' as residential_addr,
    '待定' as poscode,
    t_cm_dinfo.hsick as complication_code,
    字典. as complication_name,
    t_cm_dinfo.ci_type as hp_type_code,
    字典. as hp_type_name,
    '待定' as mang_level_code,
    '待定' as mang_level_name,
    '待定' as case_discussion,
    '待定' as discuss_reason,
    '待定' as discuss_rslt,
    '待定' as detail_mang,
    '待定' as detail_mang_no,
    '待定' as business_time,
    '待定' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    这个码表是啥 as asick_code,
    t_cm_dinfo. as asick_name,
    t_cm_dinfo.udrug as udrug,
    这个码表是啥 as cstatus_code,
    t_cm_dinfo. as cstatus_name,
    t_cm_dinfo.oxygentime as oxygentime,
    t_cm_dinfo.fbedreason as fbedreason,
    t_cm_dinfo.fbedcreatedata as fbedcreatedata,
    t_cm_dinfo.fbedcanceldata as fbedcanceldata,
    t_cm_dinfo.coalslong as coalslong,
    t_cm_dinfo.coalsmark as coalsmark,
    t_cm_dinfo.fsmoke as family_smoke_mark,
    这个码表是啥 as cbehavior_code,
    t_cm_dinfo.cbehavior as cbehavior_name,
    t_cm_dinfo.psychological as mind_info_code,
    t_cm_dinfo. as mind_info_name,
    t_cm_dinfo.nodrinkage as stop_drink_age,
    t_cm_dinfo.nodrinkmark as stop_drink_mark,
    t_cm_dinfo.drunkmark as drunkenness_mark,
    这个码表是啥 as drink_type_code,
    t_cm_dinfo. as drink_type_name,
    t_cm_dinfo.smokestatus as smok_info_code,
    t_cm_dinfo. as smok_info_name,
    这个码表是啥 as habits_diet_code,
    t_cm_dinfo. as habits_diet_name,
    t_cm_dinfo.weeksport as weeksport,
    t_cm_dinfo.holdonsport as holdonsport,
    t_cm_dinfo.sporttime as sporttime,
    这个码表是啥 as sportfrequency_code,
    t_cm_dinfo. as sportfrequency_name,
    t_cm_dinfo.sportdesc as sportdesc,
    t_cm_dinfo.daysmoke as smok_day,
    t_cm_dinfo.daydrink as drnk_day,
    t_cm_dinfo.zzdah0 as paper_file_no,
    t_cm_dinfo.ssid00 as soci_secu_cardno,
    t_cm_dinfo.isdel0 as whtr_del,
    t_cm_dinfo.jdrbh0 as filed_huma_no,
    t_cm_dinfo.jdrxm0 as filed_huma_name,
    t_cm_dinfo.sfbyqz as whtr_our_hosp_cnfm,
    t_cm_dinfo.qzjgid as cnfm_ins,
    t_cm_dinfo.qzysbh as cnfm_dr_no,
    t_cm_dinfo.qzysxm as cnfm_dr_name,
    t_cm_dinfo.gxygbh as updt_emplo_no,
    t_cm_dinfo.gxygxm as updt_emplo_name,
    t_cm_dinfo.sfgg00 as whtr_spec_mgt,
    t_cm_dinfo.kzmy as ctrl_whtr_satis,
    t_cm_dinfo.tsrq00 as info_psh_date,
    t_cm_dinfo.sfts00 as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t
T_CM_DINFO.DF_ID = T_DWELLERFILE.DF_ID
gender_code = SEX
occup_type_code = WORKSTATUS
T_CM_DINFO.CCL_ID = '1'
WHERE 1=1
) as tab

-- ================================================
insert overwrite table mid_hos_dmh_assess_card partition(dt)
select 
    concat(control_uscid,evaid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as evaid,
    '待定' as hpid,
    t_cm_dinfo.ci_type as diag_class_code,
    字典映射？. as diag_class_name,
    '待定' as highest_diag_class_code,
    '待定' as highest_diag_class_name,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as highest_risk_factor_layer_code,
    '待定' as highest_risk_factor_layer_name,
    '待定' as risk_change_code,
    '待定' as risk_change_name,
    '待定' as bp_control_code,
    '待定' as bp_control_name,
    '待定' as aim_fu,
    '待定' as aim_fu_norm,
    '待定' as fu_total_year,
    t_mxjb_sf. as bldpresshigh,
    t_mxjb_sf. as bldpresslow,
    '待定' as risk_factor_code,
    '待定' as danger_name,
    '待定' as target_organ_damage_code,
    '待定' as target_organ_damage_name,
    t_cm_dinfo.hsick as complication_code,
    字典是啥？. as complication_name,
    t_dwellerfile.height as height,
    t_dwellerfile.weight as wt,
    '待定' as serum_uric_acid,
    '待定' as check_time1,
    '待定' as check_org1,
    '待定' as bun2,
    '待定' as check_time2,
    '待定' as check_org2,
    '待定' as cr3,
    '待定' as check_time3,
    '待定' as check_org3,
    '待定' as pro4_code,
    '待定' as pro4_name,
    '待定' as check_date4,
    '待定' as check_org4,
    '待定' as albuminuria5,
    '待定' as check_date5,
    '待定' as check_org5,
    '待定' as c_reactive_protein6,
    '待定' as check_date6,
    '待定' as check_org6,
    '待定' as hs_crp7,
    '待定' as check_date7,
    '待定' as check_org7,
    '待定' as tg8,
    '待定' as check_date8,
    '待定' as check_org8,
    '待定' as ldl_c9,
    '待定' as check_date9,
    '待定' as check_org9,
    '待定' as hdl_c10,
    '待定' as check_date10,
    '待定' as check_org10,
    '待定' as tc11,
    '待定' as check_date11,
    '待定' as check_org11,
    '待定' as glu12,
    '待定' as check_date12,
    '待定' as check_org12,
    '待定' as check_fundus13,
    '待定' as check_date13,
    '待定' as check_org13,
    '待定' as carotid_artery_ultrasound14,
    '待定' as check_date14,
    '待定' as check_org14,
    '待定' as electrocardiography15,
    '待定' as check_date15,
    '待定' as check_org15,
    '待定' as chest_x_ray16,
    '待定' as check_date16,
    '待定' as check_org16,
    '待定' as echocardiogram17,
    '待定' as check_date17,
    '待定' as check_org17,
    '待定' as eval_year,
    '待定' as eval_code,
    '待定' as eval_name,
    '待定' as eval_org_code,
    '待定' as assessment_date,
    '待定' as eval_dept_code,
    '待定' as eval_dept_name,
    '待定' as eval_team_code,
    '待定' as eval_team_name,
    '待定' as business_time,
    '待定' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t
评分卡用哪个表当主表？？？？
) as tab

-- ================================================
insert overwrite table mid_hos_dmh_followup_card partition(dt)
select 
    concat(control_uscid,fu_hpid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as fu_hpid,
    '待定' as hpid,
    t_mxjb_sf.sf_id0000 as fu_hp_code,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as hp_mang_group_code,
    '待定' as hp_mang_group_name,
    '待定' as group_dscr_code,
    '待定' as group_dscr_name,
    t_mxjb_sf.sffs00 as visit_way_code,
    字典. as visit_way_name,
    '待定' as fu_mang_code,
    '待定' as fu_mang_name,
    '待定' as loss_fu_code,
    '待定' as loss_fu_name,
    '待定' as now_symptom_code,
    '待定' as now_symptom_name,
    '待定' as reger_no,
    '待定' as reger_name,
    '待定' as non_druggery_treatment_code,
    '待定' as non_druggery_treatment_name,
    '待定' as medication_compliance_code,
    '待定' as medication_compliance_name,
    '待定' as medicine_irreg_code,
    '待定' as medicine_irreg_name,
    '待定' as non_medicine_code,
    '待定' as non_medicine_name,
    t_mxjb_sf. as bldpresslow,
    t_mxjb_sf. as bldpresshigh,
    t_dwellerfile.height as height,
    t_dwellerfile.weight as wt_kg,
    t_mxjb_sf.tzone as aim_wt,
    t_mxjb_sf.tzzs00 as bmi,
    t_mxjb_sf.tzzstwo as aim_bmi,
    t_cm_dinfo.sportdesc as sportdesc,
    t_cm_dinfo.sportfrequency as sport_freq_code,
    字典在哪里找. as sport_freq_name,
    '待定' as aim_sport_freq_code,
    '待定' as aim_sport_freq_name,
    '待定' as sport_min_length,
    '待定' as aim_sport_duration,
    t_mxjb_sf.xlone as heart_rate,
    '待定' as positive_names,
    t_mxjb_sf.rxylone as smok_day,
    t_mxjb_sf.rxyltwo as tagt_day_smoke_value,
    t_mxjb_sf.ryjlone as drnk_day,
    '待定' as salt_level_code,
    '待定' as salt_level_name,
    '待定' as aim_salt_level_code,
    '待定' as aim_salt_level_name,
    '待定' as fu_diet_type_code,
    '待定' as fu_diet_type_name,
    '待定' as psycho_adjust_res_code,
    '待定' as psycho_adjust_res_name,
    t_mxjb_sf.zyxwqk as fu_compliance_res_code,
    字典在哪里找. as fu_compliance_res_name,
    t_mxjb_sf.ywblfy as drug_dys_mark,
    t_mxjb_sf.blfyms as drug_dys_dscr,
    '待定' as complication_code,
    '待定' as complication_name,
    '待定' as health_edu_mark,
    '待定' as advices_code,
    '待定' as advices_name,
    '待定' as accept_code,
    '待定' as accept_name,
    '待定' as fu_eval_res_code,
    '待定' as fu_eval_res_name,
    '待定' as fu_doc_no,
    '待定' as fu_doc_name,
    t_mxjb_sf.sfrq00 as visit_date,
    t_mxjb_sf.xcsfrq as next_follow_date,
    '待定' as fu_org_code,
    '待定' as fu_dept_code,
    '待定' as fu_dept_name,
    '待定' as fu_team_code,
    '待定' as fu_team_name,
    t_dwellerfile.doctor as duty_dor_no,
    '待定' as duty_dor_name,
    t_mxjb_sf.zzyy00 as referral_reason,
    '待定' as accept_org_name,
    '待定' as accept_dept_code,
    '待定' as referral_dept_name,
    t_mxjb_sf.cdate as business_time,
    case when ZHXGSJ is null then '0' else '1' end as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    t_mxjb_sf.xltwo as hetat_plan,
    t_mxjb_sf.zsqkone as stap_food_info,
    t_mxjb_sf.zsqktwo as stap_food_plan,
    t_mxjb_sf.kfxtz0 as empt_stom_blgval,
    t_mxjb_sf.thxhdb as glyc_hemog,
    t_mxjb_sf.jcrq00 as exam_date,
    t_mxjb_sf.qtfzjc as oth_asst_exam,
    字典在哪里找 as hypo_react_code,
    t_mxjb_sf.dxtfy as hypo_react_name,
    t_mxjb_sf.ywysxm as yard_exte_dr_name,
    t_mxjb_sf.yds000 as insn,
    t_mxjb_sf.ydsyf0 as insn_used_and_dos,
    t_mxjb_sf.zzbz00 as refl_memo,
    t_mxjb_sf.chxtz0 as after_meal_bloo_gluc,
    t_mxjb_sf.hzlx as patient_type_code,
    t_mxjb_sf.hzlx as patient_type_name,
    字典在哪里找 as sput_bacteria_code,
    t_mxjb_sf.tjqk as sput_bacteria_name,
    字典在哪里找 as drug_resi_code,
    t_mxjb_sf.nyqk as drug_resi_name,
    字典在哪里找 as eval_type_code,
    t_mxjb_sf.ddryxz as eval_type_name,
    t_mxjb_sf.ddjz as alon_of_live_room,
    字典在哪里找 as vent_situ_code,
    t_mxjb_sf.tfqk as vent_situ_name,
    t_mxjb_sf.qydd as take_medi_loc,
    t_mxjb_sf.yqsj as take_medi_time,
    t_mxjb_sf.fyjlkdtx as dose_reco_card_of_fillin,
    t_mxjb_sf.fyffjypcf as dose_mtd_wth_drug_stor,
    t_mxjb_sf.fjhzllc as tuber_trt_cour_trea,
    t_mxjb_sf.bjlfywh as no_regu_dose_hazr,
    t_mxjb_sf.fyhblfyjcl as dose_new_defs_wth_dspo,
    t_mxjb_sf.zlqjfzct as trt_cose_flup_sput,
    t_mxjb_sf.wcqjrhjcfy as out_cose_how_adhe_dose,
    t_mxjb_sf.shxgjzysx as habi_wth_mnan,
    t_mxjb_sf.mqjczjc as clos_cont_the_exam,
    t_mxjb_sf.kzmy as ctrl_whtr_satis,
    字典在哪里找 as interface_mark_code,
    t_mxjb_sf.jkbj as interface_mark_name,
    字典在哪里找 as data_source_code,
    t_mxjb_sf.datasourcesf as data_source_name,
    t_mxjb_sf.equipcode as equipcode,
    t_mxjb_sf.ncg000 as urin_rout,
    t_mxjb_sf.yd0000 as fund,
    t_mxjb_sf.sjbb00 as neuro_chng,
    t_mxjb_sf.zbdm00 as adequ_dors_arter,
    t_mxjb_sf.bfzbs0 as cop_exam,
    t_mxjb_sf.jkzdnr as hl_guid,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t
where MXJBBZ = '1'
t_mxjb_sf.REF_ID = t_dwellerfile.DF_ID
t_mxjb_sf.REF_ID = t_cm_dinfo.DF_ID
) as tab

-- ================================================
insert overwrite table mid_hos_dmd_manage_card partition(dt)
select 
    concat(control_uscid,dmid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as control_uscid,
    null as dmid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as tel,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as brdy,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as height,
    null as wt,
    null as edu_background_code,
    null as edu_background_name,
    null as occup_code,
    null as occup_name,
    null as payment_term_code,
    null as payment_term_name,
    null as dm_family_his,
    null as dm_type_code,
    null as dm_type_name,
    null as cnfm_date,
    null as case_source_code,
    null as case_source_name,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as rpot_card_id,
    null as obj_state_code,
    null as obj_state_name,
    null as curr_duty_dor_no,
    null as curr_duty_dor_name,
    null as now_mang_team_code,
    null as now_mang_team_name,
    null as dft_fu_dor_no,
    null as dft_fu_dor_name,
    null as build_cards_org_code,
    null as build_org_name,
    null as build_cards_dor_no,
    null as build_cards_dor_name,
    null as build_cards_time,
    null as build_cards_dept_code,
    null as build_cards_dept_name,
    null as build_cards_team_name,
    null as build_cards_team_id,
    null as addr_type_code,
    null as addr_type_name,
    null as addr_name,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as residential_addr,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_cm_dinfo.asick as asick_code,
    t_cm_dinfo.asick as asick_name,
    t_cm_dinfo.udrug as udrug,
    t_cm_dinfo.cstatus as cstatus_code,
    t_cm_dinfo.cstatus as cstatus_name,
    t_cm_dinfo.oxygentime as oxygentime,
    t_cm_dinfo.fbedreason as fbedreason,
    t_cm_dinfo.fbedcreatedata as fbedcreatedata,
    t_cm_dinfo.fbedcanceldata as fbedcanceldata,
    t_cm_dinfo.coalslong as coalslong,
    t_cm_dinfo.coalsmark as coalsmark,
    t_cm_dinfo.fsmoke as family_smoke_mark,
    t_cm_dinfo.cbehavior as cbehavior_code,
    t_cm_dinfo.cbehavior as cbehavior_name,
    t_cm_dinfo.psychological as mind_info_code,
    t_cm_dinfo.psychological as mind_info_name,
    t_cm_dinfo.nodrinkage as stop_drink_age,
    t_cm_dinfo.nodrinkmark as stop_drink_mark,
    t_cm_dinfo.drunkmark as drunkenness_mark,
    t_cm_dinfo.drinktype as drink_type_code,
    t_cm_dinfo.drinktype as drink_type_name,
    t_cm_dinfo.smokestatus as smok_info_code,
    t_cm_dinfo.smokestatus as smok_info_name,
    t_cm_dinfo.dietcode as habits_diet_code,
    t_cm_dinfo.dietcode as habits_diet_name,
    t_cm_dinfo.weeksport as weeksport,
    t_cm_dinfo.holdonsport as holdonsport,
    t_cm_dinfo.sporttime as sporttime,
    t_cm_dinfo.sportfrequency as sportfrequency_code,
    t_cm_dinfo.sportfrequency as sportfrequency_name,
    t_cm_dinfo.sportdesc as sportdesc,
    t_cm_dinfo.daysmoke as smok_day,
    t_cm_dinfo.daydrink as drnk_day,
    t_cm_dinfo.zzdah0 as paper_file_no,
    t_cm_dinfo.ssid00 as soci_secu_cardno,
    t_cm_dinfo.isdel0 as whtr_del,
    t_cm_dinfo.jdrbh0 as filed_huma_no,
    t_cm_dinfo.jdrxm0 as filed_huma_name,
    t_cm_dinfo.sfbyqz as whtr_our_hosp_cnfm,
    t_cm_dinfo.qzjgid as cnfm_ins,
    t_cm_dinfo.qzysbh as cnfm_dr_no,
    t_cm_dinfo.qzysxm as cnfm_dr_name,
    t_cm_dinfo.gxygbh as updt_emplo_no,
    t_cm_dinfo.gxygxm as updt_emplo_name,
    t_cm_dinfo.sfgg00 as whtr_spec_mgt,
    t_cm_dinfo.kzmy as ctrl_whtr_satis,
    t_cm_dinfo.tsrq00 as info_psh_date,
    t_cm_dinfo.sfts00 as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dmd_followup_card partition(dt)
select 
    concat(control_uscid,fu_dmid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as control_uscid,
    null as fu_dmid,
    null as dmid,
    null as fu_mang_code,
    null as fu_mang_name,
    null as loss_fu_code,
    null as loss_fu_name,
    null as next_follow_date,
    null as visit_way_code,
    null as visit_way_name,
    null as fu_eval_res_code,
    null as fu_eval_res_name,
    null as dm_clinical_symptoms_code,
    null as clinical_symptoms_name,
    null as foot_dorsal_artery_code,
    null as foot_dorsal_artery_name,
    null as dm_family_his,
    null as regular_activities,
    null as regular_activities_type_code,
    null as regular_activities_type_name,
    null as sport_freq_code,
    null as sport_freq_name,
    null as aim_sport_freq_code,
    null as aim_sport_freq_name,
    null as sport_duration,
    null as aim_sport_duration,
    null as height,
    null as wt_kg,
    null as aim_wt,
    null as bmi,
    null as aim_bmi,
    null as daily_staple_num,
    null as aim_daily_staple_num,
    null as diet_info_code,
    null as diet_info_name,
    null as bldpresshigh,
    null as bldpresslow,
    null as waist_cm,
    null as hip,
    null as waist_hip_whr,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as hba1c,
    null as tc,
    null as hdl_c,
    null as ldl_c,
    null as tg,
    null as acr,
    null as ndbdl,
    null as take_medicine_code,
    null as take_medicine_name,
    null as drug_dys_mark,
    null as drug_dys_dscr,
    null as smok_day,
    null as aim_day_smoke_value,
    null as drnk_day,
    null as other_sign_dscr,
    null as psycho_adjust_res_code,
    null as psycho_adjust_res_name,
    null as fu_compliance_res_code,
    null as fu_compliance_res_name,
    null as referral_reason,
    null as accept_org_name,
    null as accept_dept_name,
    null as fu_eval_prop_code,
    null as fu_eval_prop_name,
    null as fu_doc_no,
    null as fu_doc_name,
    null as duty_dor_no,
    null as duty_dor_name,
    null as visit_date,
    null as fu_dept_code,
    null as fu_dept_name,
    null as fu_team_id,
    null as fu_team_name,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_mxjb_sf.xltwo as hetat_plan,
    t_mxjb_sf.zsqkone as stap_food_info,
    t_mxjb_sf.zsqktwo as stap_food_plan,
    t_mxjb_sf.kfxtz0 as empt_stom_blgval,
    t_mxjb_sf.thxhdb as glyc_hemog,
    t_mxjb_sf.jcrq00 as exam_date,
    t_mxjb_sf.qtfzjc as oth_asst_exam,
    t_mxjb_sf.dxtfy as hypo_react_code,
    t_mxjb_sf.dxtfy as hypo_react_name,
    t_mxjb_sf.ywysxm as yard_exte_dr_name,
    t_mxjb_sf.yds000 as insn,
    t_mxjb_sf.ydsyf0 as insn_used_and_dos,
    t_mxjb_sf.zzbz00 as refl_memo,
    t_mxjb_sf.chxtz0 as after_meal_bloo_gluc,
    t_mxjb_sf.hzlx as patient_type_code,
    t_mxjb_sf.hzlx as patient_type_name,
    t_mxjb_sf.tjqk as sput_bacteria_code,
    t_mxjb_sf.tjqk as sput_bacteria_name,
    t_mxjb_sf.nyqk as drug_resi_code,
    t_mxjb_sf.nyqk as drug_resi_name,
    t_mxjb_sf.ddryxz as eval_type_code,
    t_mxjb_sf.ddryxz as eval_type_name,
    t_mxjb_sf.ddjz as alon_of_live_room,
    t_mxjb_sf.tfqk as vent_situ_code,
    t_mxjb_sf.tfqk as vent_situ_name,
    t_mxjb_sf.qydd as take_medi_loc,
    t_mxjb_sf.yqsj as take_medi_time,
    t_mxjb_sf.fyjlkdtx as dose_reco_card_of_fillin,
    t_mxjb_sf.fyffjypcf as dose_mtd_wth_drug_stor,
    t_mxjb_sf.fjhzllc as tuber_trt_cour_trea,
    t_mxjb_sf.bjlfywh as no_regu_dose_hazr,
    t_mxjb_sf.fyhblfyjcl as dose_new_defs_wth_dspo,
    t_mxjb_sf.zlqjfzct as trt_cose_flup_sput,
    t_mxjb_sf.wcqjrhjcfy as out_cose_how_adhe_dose,
    t_mxjb_sf.shxgjzysx as habi_wth_mnan,
    t_mxjb_sf.mqjczjc as clos_cont_the_exam,
    t_mxjb_sf.kzmy as ctrl_whtr_satis,
    t_mxjb_sf.jkbj as interface_mark_code,
    t_mxjb_sf.jkbj as interface_mark_name,
    t_mxjb_sf.datasourcesf as data_source_code,
    t_mxjb_sf.datasourcesf as data_source_name,
    t_mxjb_sf.equipcode as equipcode,
    t_mxjb_sf.ncg000 as urin_rout,
    t_mxjb_sf.yd0000 as fund,
    t_mxjb_sf.sjbb00 as neuro_chng,
    t_mxjb_sf.zbdm00 as adequ_dors_arter,
    t_mxjb_sf.bfzbs0 as cop_exam,
    t_mxjb_sf.jkzdnr as hl_guid,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dmp_manage_card partition(dt)
select 
    concat(psycho_card,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t_zxjsjb_info.id0000 as psycho_card,
    null as control_uscid,
    t_dwellerfile.sscardno as card_no,
    '0' as card_type_code,
    '社保卡' as card_type_name,
    t_zxjsjb_info.id0000 as medcasno,
    t_zxjsjb_info. as build_card_date,
    t_dwellerfile.name as full_name,
    t_dwellerfile.sex as gender_code,
    d_gender_name.字典值名称 as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    null as edu_background_code,
    t_dwellerfile.cdegree as edu_background_name,
    null as resd_natu_code,
    t_dwellerfile.rprtype as resd_natu_name,
    null as employmentstatus_code,
    t_zxjsjb_info.jyqk00 as employmentstatus_name,
    null as mrg_stas_code,
    t_dwellerfile.mstatus as mrg_stas_name,
    null as nation_code,
    t_dwellerfile.folk as nation_name,
    null as psncert_type_code,
    null as psncert_type_name,
    t_dwellerfile.idcardno as certno,
    null as rescue_source_code,
    null as rescue_source_name,
    null as medfee_paymtd_code,
    t_dwellerfile. as medfee_paymtd_name,
    null as mang_type_code,
    null as mang_type_name,
    t_dwellerfile.doctor as duty_doc_no,
    null as duty_doc_name,
    null as resp_dor_tel,
    t_zxjsjb_info.jhrname as guardian_name,
    null as guardian_relation_code,
    t_zxjsjb_info.rel as guardian_relation_name,
    t_zxjsjb_info.jhradd as guardian_addr,
    t_zxjsjb_info.jhrtel as guardian_telephone,
    null as committee_name,
    t_zxjsjb_info.jwhname as committee_coner_name,
    t_zxjsjb_info.jwhtel as committee_telephone,
    null as seve_ment_diso_sort_code,
    null as seve_ment_diso_sort_name,
    null as seve_ment_diso_fami_hist,
    t_zxjsjb_info.ccfbtime as first_disease_time,
    null as major_psyc_symp_code,
    t_zxjsjb_info.jwszz0 as major_psyc_symp_name,
    null as major_psyc_symp_dscr,
    null as outp_trea_code,
    t_zxjsjb_info.jwmzzlqk as outp_trea_name,
    null as psychiatric_hos_num,
    null as recently_ment_illn_diag_code,
    t_zxjsjb_info.zjzd as recently_ment_illn_diag_name,
    t_zxjsjb_info.qztime as cnfm_date,
    t_zxjsjb_info.qzyy as conf_diag_org_name,
    null as conf_diag_org_code,
    null as trea_effe_cate_code,
    t_zxjsjb_info.zjzlxg as trea_effe_cate_name,
    null as risk_level_code,
    null as risk_level_name,
    null as anti_medi_sign,
    t_zxjsjb_info.sczlsj as first_anti_trea_time,
    null as latest_treat_effect_code,
    null as latest_treat_effect_name,
    t_zxjsjb_info.qdzscs as mild_trouble_num,
    t_zxjsjb_info.zhscs0 as cause_trouble_num,
    t_zxjsjb_info.zhhcs0 as trouble_num,
    t_zxjsjb_info.zswscs as self_injury_num,
    t_zxjsjb_info. as atte_suic_num,
    t_zxjsjb_info.qtwhcs as other_harm_beha_num,
    t_zxjsjb_info.zqty00 as inform_agree_manage_mark,
    null as inform_agree_nameer_name,
    t_zxjsjb_info.zqtysj as inform_agree_name_date,
    null as economy_status_code,
    t_zxjsjb_info.jjzk00 as economy_status_name,
    t_zxjsjb_info.zkysyj as speist_dor_ad,
    null as locked_case_code,
    t_zxjsjb_info.gsqk as locked_case_name,
    null as lock_start_date,
    null as unlock_start_date,
    null as reger_no,
    null as reger_name,
    null as build_cards_org_code,
    null as build_org_name,
    null as addr_districts_code,
    null as addr_districts_name,
    null as curr_addr_prov_code,
    t_dwellerfile.adress_pro as curr_addr_prov_name,
    null as curr_addr_city_code,
    t_dwellerfile.adress_city as curr_addr_city_name,
    null as curr_addr_coty_code,
    t_dwellerfile. as curr_addr_coty_name,
    null as curr_addr_town_code,
    t_dwellerfile.adress_rural as curr_addr_town_name,
    null as curr_addr_comm_code,
    t_dwellerfile.adress_village as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    t_dwellerfile.adrss_hnumber as residential_housnum,
    null as poscode,
    t_dwellerfile.weight as weight,
    null as code_686,
    null as name_686,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_zxjsjb_info.jwzyzlqk as past_hospi_info,
    t_zxjsjb_info.wyysxm as otp_dr_name,
    t_zxjsjb_info.ssid00 as soci_secu_cardno_mark,
    t_zxjsjb_info.isdel0 as whtr_del,
    t_zxjsjb_info.icd100 as diag_icd10_code,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t
left join 字典表 as d_gender_name on d_gender_name.字典类型='GENDER_CODE' and d_gender_name.字典值=t.sex
) as tab

-- ================================================
insert overwrite table mid_hos_dmp_followup_form partition(dt)
select 
    concat(fu_psycho_code,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_psycho_code,
    null as control_uscid,
    null as psycho_card,
    null as case_manage_logo,
    null as the_patient_cond_code,
    null as the_patient_cond_name,
    null as pati_soci_func_revi_code,
    null as pati_soci_func_revi_name,
    null as visit_sign,
    null as visit_way_code,
    null as visit_way_name,
    null as loss_fu_code,
    null as loss_fu_name,
    null as death_date,
    null as die_reason_code,
    null as die_reason_name,
    null as full_name,
    null as fu_doctor_name,
    null as fu_doctor_no,
    null as now_symptom_code,
    null as now_symptom_name,
    null as treatment_form_code,
    null as treatment_form_name,
    null as recovery_code,
    null as recovery_name,
    null as risk_level_code,
    null as risk_level_name,
    null as risk_type_code,
    null as risk_type_name,
    null as personal_life_code,
    null as personal_life_name,
    null as housework_code,
    null as housework_name,
    null as prod_labor_and_work_code,
    null as prod_labor_and_work_name,
    null as learning_ability_code,
    null as learning_ability_name,
    null as soci_inte_comm_code,
    null as soci_inte_comm_name,
    null as mild_trouble_num,
    null as cause_trouble_num,
    null as trouble_num,
    null as self_injury_num,
    null as atte_suic_num,
    null as other_harm_beha_num,
    null as hosp_betwe_two_fu_code,
    null as hosp_betwe_two_fu_name,
    null as patn_ipt_cnt,
    null as ment_illn_diag_code,
    null as ment_illn_diag_name,
    null as treatment_code,
    null as treatment_name,
    null as timeliness_code,
    null as timeliness_name,
    null as way_of_treatment_code,
    null as way_of_treatment_name,
    null as outp_spec_hosp,
    null as lock_up_betw_two_fu_code,
    null as lock_up_betw_two_fu_name,
    null as now_status_code,
    null as now_status_name,
    null as marriage_function_code,
    null as marriage_function_name,
    null as parental_functions_code,
    null as parental_functions_name,
    null as social_withdrawal_code,
    null as social_withdrawal_name,
    null as soci_acti_outs_the_home_code,
    null as soci_acti_outs_the_home_name,
    null as family_activ_code,
    null as family_activ_name,
    null as family_functions_code,
    null as family_functions_name,
    null as outs_inte_and_conc_code,
    null as outs_inte_and_conc_name,
    null as resp_and_plan_code,
    null as resp_and_plan_name,
    null as sdss_total,
    null as insi_eval_result_code,
    null as insi_eval_result_name,
    null as physical_disease,
    null as auxi_insp_sign,
    null as asst_exam_rslt,
    null as drug_dys_mark,
    null as trea_effe_cate_code,
    null as trea_effe_cate_name,
    null as ment_diso_fu_eval_code,
    null as ment_diso_fu_eval_name,
    null as take_drug_compliance_code,
    null as take_drug_compliance_name,
    null as speist_dor_ad_code,
    null as speist_dor_ad_name,
    null as emer_medi_trea_sign,
    null as referral_flag,
    null as referral_reason,
    null as referral_org_code,
    null as referral_org_name,
    null as treatment_opinion,
    null as fu_date,
    null as ment_illn_fami_hist_sign,
    null as fami_ment_illn_name,
    null as major_psyc_symp_code,
    null as major_psyc_symp_name,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_zxjsjb_sf.sf_bhid as heav_ment_illn_flup_no,
    t_zxjsjb_sf.zzlqk0 as self_know_forc_code,
    t_zxjsjb_sf.zzlqk0 as self_know_forc_name,
    t_zxjsjb_sf.smqk00 as slep_info_code,
    t_zxjsjb_sf.smqk00 as slep_info_name,
    t_zxjsjb_sf.ysqk00 as diet_info_code,
    t_zxjsjb_sf.ysqk00 as diet_info_name,
    t_zxjsjb_sf.grshll as psn_life_cate_code,
    t_zxjsjb_sf.grshll as psn_life_cate_name,
    t_zxjsjb_sf.jwld00 as housw_code,
    t_zxjsjb_sf.jwld00 as housw_name,
    t_zxjsjb_sf.scldgz as gena_lbr_wth_job_code,
    t_zxjsjb_sf.scldgz as gena_lbr_wth_job_name,
    t_zxjsjb_sf.xxnl00 as stdy_ablt_code,
    t_zxjsjb_sf.xxnl00 as stdy_ablt_name,
    t_zxjsjb_sf.shrjjw as soca_inter_pers_code,
    t_zxjsjb_sf.shrjjw as soca_inter_pers_name,
    t_zxjsjb_sf.sysjc0 as lab_exam,
    t_zxjsjb_sf.fyycx0 as medication_compliance_code,
    t_zxjsjb_sf.fyycx0 as medication_compliance_name,
    t_zxjsjb_sf.ywblfy as drug_dys_case,
    t_zxjsjb_sf.blfyms as drug_dys_dscr,
    t_zxjsjb_sf.shldnl as life_lbr_ablt,
    t_zxjsjb_sf.zyxlqk as prfs_trai,
    t_zxjsjb_sf.xxnlqk as stdy_ablt,
    t_zxjsjb_sf.shjwqk as soci_inter,
    t_zxjsjb_sf.qtqk00 as oth,
    t_zxjsjb_sf.xcsfrq as next_follow_date,
    t_zxjsjb_sf.sfys00 as flup_dr,
    t_zxjsjb_sf.wyysxm as otp_dr_name,
    t_zxjsjb_sf.qkbz00 as info_memo,
    t_zxjsjb_sf.wxxing as dange,
    t_zxjsjb_sf.zyqk00 as ipt_info,
    t_zxjsjb_sf.mccysj as last_dscg_time,
    t_zxjsjb_sf.sfyy00 as lstvs_rea,
    t_zxjsjb_sf.swyy01 as die_rea_body_dise,
    t_zxjsjb_sf.swyy00 as die_reason,
    t_zxjsjb_sf.swsj00 as death_time,
    t_zxjsjb_sf.bcsfdx as crt_flup_obj_code,
    t_zxjsjb_sf.bcsfdx as crt_flup_obj_name,
    t_zxjsjb_sf.sysjc2 as lab_exam_opt,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dme_special_form partition(dt)
select 
    concat(elderly_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as elderly_id,
    null as control_uscid,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as sign_date,
    null as smok_day,
    null as drnk_day,
    null as living_capacity_code,
    null as living_capacity_name,
    null as lifestyle_support_code,
    null as lifestyle_support_name,
    null as eyesight_index_code,
    null as eyesight_index_name,
    null as health_problem_code,
    null as health_problem_name,
    null as incomplete_teeth_code,
    null as incomplete_teeth_name,
    null as addr_type_code,
    null as addr_type_name,
    null as per_addr_prov_code,
    null as per_addr_prov_name,
    null as per_addr_city_code,
    null as per_addr_city_name,
    null as per_addr_coty_code,
    null as per_addr_coty_name,
    null as per_addr_town_code,
    null as per_addr_town_name,
    null as per_addr_comm_code,
    null as per_addr_comm_name,
    null as per_addr_cotry_name,
    null as per_addr_housnum,
    null as inquirer_date,
    null as inquirer_no,
    null as inquirer_name,
    null as care_code,
    null as care_name,
    null as inspect_find_gyne_type,
    null as inspect_gyne_code,
    null as inspect_gyne_name,
    null as almost_two_years_ge_type,
    null as gynecological_symptoms_code,
    null as gynecological_symptoms_name,
    null as medcasno,
    null as addr_districts_code,
    null as addr_districts_name,
    null as poscode,
    null as duty_doc_name,
    null as duty_doc_no,
    null as symp_code,
    null as health_symp_name,
    null as check_dor_no,
    null as check_dor_name,
    null as check_date,
    null as have_fraction_code,
    null as have_fraction_name,
    null as wash_fraction_code,
    null as wash_fraction_name,
    null as dress_fraction_code,
    null as dress_fraction_name,
    null as toilet_fraction_code,
    null as toilet_fraction_name,
    null as motion_fraction_code,
    null as motion_fraction_name,
    null as total_score,
    null as oxygen_uptake,
    null as builder_dor_no,
    null as builder_dor_name,
    null as builder_dept_code,
    null as builder_dept_name,
    null as builder_org_name,
    null as builder_org_code,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_elderlyhealth.jjsrqk as jjsrqk,
    t_elderlyhealth.bxqk as bxqk,
    t_elderlyhealth.yjs as mena_his,
    t_elderlyhealth.sys as sys,
    t_elderlyhealth.wcqk as wcqk,
    t_elderlyhealth.cjqk as cjqk,
    t_elderlyhealth.mxbqk as mxbqk,
    t_elderlyhealth.fbedreason as fbedreason,
    t_elderlyhealth.fbedcreatedata as fbedcreatedata,
    t_elderlyhealth.fbedcanceldata as fbedcanceldata,
    t_elderlyhealth.coalslong as coalslong,
    t_elderlyhealth.coalsmark as coalsmark,
    t_elderlyhealth.fsmoke as family_smoke_mark,
    t_elderlyhealth.protectmark as protectivemeasures_mark,
    t_elderlyhealth.hazardworktime as hazardworktime,
    t_elderlyhealth.hazardwork as danger_occup,
    t_elderlyhealth.hazardworktype as hazardworktype,
    t_elderlyhealth.hazardworkname as occup_risk_name,
    t_elderlyhealth.hazardworkmark as occup_mark,
    t_elderlyhealth.cbehavior as cbehavior_code,
    t_elderlyhealth.cbehavior as cbehavior_name,
    t_elderlyhealth.psychological as mind_info_code,
    t_elderlyhealth.psychological as mind_info_name,
    t_elderlyhealth.nodrinkage as stop_drink_age,
    t_elderlyhealth.nodrinkmark as stop_drink_mark,
    t_elderlyhealth.drunkmark as drunkenness_mark,
    t_elderlyhealth.bdrinkage as start_drink_age,
    t_elderlyhealth.drinktype as drink_type_code,
    t_elderlyhealth.drinkfrequency as drink_type_name,
    t_elderlyhealth.nosmokeage as stop_smoke_year,
    t_elderlyhealth.bsmokeage as start_smoke_age,
    t_elderlyhealth.smokestatus as smok_info_code,
    t_elderlyhealth.smokestatus as smok_info_name,
    t_elderlyhealth.dietcode as habits_diet_code,
    t_elderlyhealth.dietcode as habits_diet_name,
    t_elderlyhealth.weeksport as weeksport,
    t_elderlyhealth.holdonsport as holdonsport,
    t_elderlyhealth.sporttime as sporttime,
    t_elderlyhealth.sportfrequency as sportfrequency_code,
    t_elderlyhealth. as sportfrequency_name,
    t_elderlyhealth.sportdesc as sportdesc,
    t_elderlyhealth.yyno00 as hosp_intl_no,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dme_hospital_history partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as elderly_id,
    null as admission_date,
    null as leave_date,
    null as reason,
    null as uscn,
    null as inhosp_org_code,
    null as medcasno,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_elderlyinhospital.departments as departments,
    t_elderlyinhospital.diagnosis as diagnosis,
    t_elderlyinhospital.surgery as surgery,
    t_elderlyinhospital.result as result,
    t_elderlyinhospital.jktjcs as hl_phys_exam_cnt,
    t_elderlyinhospital.df_id as df_id,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_dme_followup_form partition(dt)
select 
    concat(fu_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_id,
    null as control_uscid,
    null as elderly_id,
    null as fu_no,
    null as visit_date,
    null as visit_way_code,
    null as visit_way_name,
    null as outcome_state_code,
    null as outcome_state_name,
    null as lstvs_rea,
    null as sympt_code,
    null as sympt_name,
    null as psychological_guidance_code,
    null as psychological_guidance_name,
    null as wt,
    null as smok_day,
    null as stop_smoke_year,
    null as smoke_time,
    null as drnk_day,
    null as stop_drink_time,
    null as stop_drink_year,
    null as sportdesc,
    null as sportfrequency_code,
    null as sportfrequency_name,
    null as sport_time,
    null as insist_sport_year,
    null as weeksport,
    null as psycho_adjust_res_code,
    null as psycho_adjust_res_name,
    null as chd_prevention,
    null as osteoporosis_prevention,
    null as fu_advice,
    null as next_visit_goal,
    null as next_follow_date,
    null as fu_doc_name,
    null as fu_doc_no,
    null as fu_diet_type_code,
    null as fu_diet_type_name,
    null as fu_compliance_res_code,
    null as fu_compliance_res_name,
    null as fu_psyc_guidance_dscr,
    null as medication_compliance_code,
    null as medication_compliance_name,
    null as dbp,
    null as systpre,
    null as bmi,
    null as heart_rate,
    null as other1,
    null as vaccination_status,
    null as other_sympt_dscrr,
    null as salt_intake_profile_code,
    null as salt_intake_profile_name,
    null as asst_exam,
    null as drug_dys_dscr,
    null as fu_up_type_code,
    null as fu_up_type_name,
    null as referral_reason,
    null as accept_org_name,
    null as accept_dept_name,
    null as habits_diet_code,
    null as habits_diet_name,
    null as smok_info_code,
    null as smok_info_name,
    null as drnk_frqu_code,
    null as drnk_frqu_name,
    null as drink_type_code,
    null as drink_type_name,
    null as drunkenness_mark,
    null as stop_drink_mark,
    null as occup_mark,
    null as occup_risk_name,
    null as occup_risk_type_code,
    null as occup_risk_type_name,
    null as danger_occup,
    null as harm_occup_duration_year,
    null as family_smoke_mark,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as business_time,
    null as data_rank,
    null as state,
    null as reserve1,
    null as reserve2,
    t_elderly_sfxx.tz0000 as wt_emp,
    t_elderly_sfxx.xy0000 as smok_emp,
    t_elderly_sfxx.ysqk00 as diet_info_code,
    t_elderly_sfxx.ysqk00 as diet_info_name,
    t_elderly_sfxx.bz0000 as remark,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_cdb_report_card partition(dt)
select 
    concat(rpot_card_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpot_card_id,
    null as unified_uscid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as adequ_age,
    null as age_unit_code,
    null as age_unit_name,
    null as brdy,
    null as nation_code,
    null as nation_name,
    null as occup_code,
    null as occup_name,
    null as resd_local_type_code,
    null as resd_local_type_name,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    null as resd_addr_housnum,
    null as resd_addr,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as cur_addr,
    null as org_addr_prov_code,
    null as org_addr_prov_name,
    null as org_addr_city_code,
    null as org_addr_city_name,
    null as org_addr_coty_code,
    null as org_addr_coty_name,
    null as org_addr_town_code,
    null as org_addr_town_name,
    null as org_addr_steet,
    null as org_addr_housnum,
    null as empr_tel,
    null as empr_name,
    null as psn_tel,
    null as tel_home,
    null as tel_contact,
    null as coner_name,
    null as addr_districts_code,
    null as addr_districts_name,
    null as otp_no,
    null as ipt_no,
    null as reg_no,
    null as detainee_mark,
    null as tb_hole_mark,
    null as like_tb_code,
    null as like_tb_name,
    null as first_symptom_date,
    null as mdtrt_date,
    null as cnfm_date,
    null as start_date,
    null as crt_check_date,
    null as check_rpt_date,
    null as discovery_mode_code,
    null as discovery_mode_name,
    null as sputum_exam_rslt_code,
    null as sputum_exam_rslt_name,
    null as sputum_not_exam_reason,
    null as culture_rslt_code,
    null as culture_rslt_name,
    null as culture_uncheck_rslt,
    null as dr_rslt_code,
    null as dr_rslt_name,
    null as dr_rslt_des,
    null as ct_exam_rslt,
    null as liver_check_rslt_code,
    null as liver_check_rslt_name,
    null as fecal_routine_exam_rslt_code,
    null as fecal_routine_exam_rslt_name,
    null as urinalysis_exam_rslt_code,
    null as urinalysis_exam_rslt_name,
    null as blood_routine_check_rslt_code,
    null as blood_routine_check_rslt_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as accept_anti_trt_mark,
    null as anti_trt_start_date,
    null as accept_smz_tmp_trt_mark,
    null as sms_tmp_trt_start_date,
    null as accept_anti_tb_mark_code,
    null as accept_anti_tb_mark_name,
    null as lab_drug_alle_code,
    null as lab_drug_alle_name,
    null as lab_drug_alle_rslt_code,
    null as lab_drug_alle_rslt_name,
    null as detection_tb_flora_rslt_code,
    null as detection_tb_flora_rslt_name,
    null as tb_out_part_code,
    null as tb_out_part__name,
    null as diag_category_code,
    null as diag_category_name,
    null as diag_category_rslt_code,
    null as diag_category_rslt_name,
    null as phthisical_mark,
    null as hepatitis_mark,
    null as tb_touch_his,
    null as complicat_code,
    null as complicat_name,
    null as trt_category_code,
    null as trt_category_name,
    null as reg_category_code,
    null as reg_category_name,
    null as drug_resi_code,
    null as drug_resi_name,
    null as trt_plan_code,
    null as trt_plan_name,
    null as tb_chemotherapy_plan_code,
    null as tb_chemotherapy_plan_name,
    null as drug_dys_mark,
    null as trt_stop_date,
    null as trt_stop_reason_code,
    null as trt_stop_reason_name,
    null as mang_way_code,
    null as mang_way_name,
    null as supervise_way_code,
    null as supervise_way_name,
    null as normal_use_drug_mark,
    null as first_mang_org_name,
    null as cur_mang_org_name,
    null as first_mang_org_code,
    null as cur_org_code,
    null as diag_org_name,
    null as diage_dor_code,
    null as diage_dor_name,
    null as first_trt_org_name,
    null as cur_trt_org_name,
    null as duty_dor_name,
    null as patient_ascr_code,
    null as patient_ascr_name,
    null as rpt_dor_name,
    null as report_doctor_no,
    null as remark,
    null as input_orgname,
    null as input_date,
    null as enter_dor_name,
    null as reg_dor_no,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_cm_dinfo.oxygentime as oxygentime,
    t_cm_dinfo.fbedreason as fbedreason,
    t_cm_dinfo.fbedcreatedata as fbedcreatedata,
    t_cm_dinfo.fbedcanceldata as fbedcanceldata,
    t_cm_dinfo.coalslong as coalslong,
    t_cm_dinfo.coalsmark as coalsmark,
    t_cm_dinfo.fsmoke as family_smoke_mark,
    t_cm_dinfo.protectmark as protectivemeasures_mark,
    t_cm_dinfo.hazardworktime as harm_occup_duration_year,
    t_cm_dinfo.hazardwork as danger_occup,
    t_cm_dinfo.hazardworktype as occup_risk_type_code,
    t_cm_dinfo.hazardworkname as occup_risk_name,
    t_cm_dinfo.hazardworkmark as occup_mark,
    t_cm_dinfo.cbehavior as cbehavior_code,
    t_cm_dinfo.cbehavior as cbehavior_name,
    t_cm_dinfo.psychological as mind_info_code,
    t_cm_dinfo.psychological as mind_info_name,
    t_cm_dinfo.sfbyqz as whtr_our_hosp_cnfm,
    t_cm_dinfo.sfgg00 as whtr_spec_mgt,
    t_cm_dinfo.kzmy as ctrl_whtr_satis,
    t_cm_dinfo.tsrq00 as info_psh_date,
    t_cm_dinfo.sfts00 as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_cdb_first_home_visit partition(dt)
select 
    concat(tuberculosis_card_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as tuberculosis_card_id,
    null as control_uscid,
    null as mana_card_id,
    null as visit_date,
    null as fu_org_code,
    null as visit_way_code,
    null as visit_way_name,
    null as patient_type_code,
    null as patient_type_name,
    null as sputum_exam_rslt_code,
    null as sputum_exam_rslt_name,
    null as drug_resi_code,
    null as drug_resi_name,
    null as symptom_signs_code,
    null as symptom_signs_name,
    null as other_symptom,
    null as trt_plan_code,
    null as trt_plan_name,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_form_code,
    null as drug_form_name,
    null as supervisorcategorycode,
    null as supervisorcategoryname,
    null as eval_other_type,
    null as alon_of_live_room,
    null as vent_situ_code,
    null as vent_situ_name,
    null as cur_smoke_value,
    null as aim_smoke_value,
    null as cur_drink_value,
    null as aim_drink_value,
    null as take_medi_loc,
    null as take_medi_time,
    null as dose_reco_card_of_fillin,
    null as dose_mtd_wth_drug_stor,
    null as tuber_trt_cour_trea,
    null as no_regu_dose_hazr,
    null as dose_new_defs_wth_dspo,
    null as trt_cose_flup_sput,
    null as out_cose_how_adhe_dose,
    null as habi_wth_mnan,
    null as clos_cont_the_exam,
    null as next_follow_date,
    null as fill_date,
    null as eval_dor_code,
    null as eval_dor_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_mxjb_fjhsf.sfzz as whtr_refl,
    t_mxjb_fjhsf.tzzlsj as stop_trt_time,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_code,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_name,
    t_mxjb_fjhsf.pgysqm as eval_dr_sign,
    t_mxjb_fjhsf.wyysqm as otp_dr_sign,
    t_mxjb_fjhsf.sjxzrq as on_ins_sub_turn_reg_time,
    t_mxjb_sf_yyqk.ywbh_id as drug_name_code,
    t_mxjb_sf_yyqk.ywmc as drug_name,
    t_mxjb_sf_yyqk.ywyf as used,
    t_mxjb_sf_yyqk.ywyl as dos,
    t_mxjb_sf_yyqk.yysj as medication_time,
    t_mxjb_sf_yyqk.fyycx as dose_adhe,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_cdb_followup_service partition(dt)
select 
    concat(tuberculosis_card_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as tuberculosis_card_id,
    null as control_uscid,
    null as mana_card_id,
    null as visit_date,
    null as treatment_seq_month,
    null as supervisorcategorycode,
    null as supervisorcategoryname,
    null as eval_other_type,
    null as supervisoname,
    null as mang_way_code,
    null as mang_way_name,
    null as mang_way_change_mark,
    null as change_date,
    null as urine_spot_check_code,
    null as urine_spot_check_name,
    null as check_drug_mark_code,
    null as check_drug_mark_name,
    null as fill_card_mark_code,
    null as fill_card_mark_name,
    null as deal_opinion,
    null as psn_name,
    null as psn_comm_code,
    null as psn_comm_name,
    null as create_time,
    null as tel,
    null as fu_org_code,
    null as visit_way_code,
    null as visit_way_name,
    null as trt_plan_code,
    null as trt_plan_name,
    null as trt_stage_code,
    null as trt_stage_name,
    null as follow_dor_advice_mark,
    null as recheck_mark,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_form_code,
    null as drug_form_name,
    null as omit_times,
    null as supervise_way_code,
    null as supervise_way_name,
    null as drug_dys_mark,
    null as defs_code,
    null as medn_defs_name,
    null as trt_change_mark,
    null as trt_change_reason_code,
    null as trt_change_reason_name,
    null as trt_change_date,
    null as new_trt_code,
    null as new_trt_name,
    null as new_use_dscr,
    null as omit_times_per_month,
    null as omit_reason,
    null as symptom_signs_code,
    null as symptom_signs_name,
    null as other_symptom,
    null as cur_smoke_value,
    null as aim_smoke_value,
    null as cur_drink_value,
    null as aim_drink_value,
    null as complicat_flag,
    null as complicat_dscr,
    null as weight,
    null as tprt,
    null as breathing,
    null as lung_respiratory_sound_code,
    null as lung_respiratory_sound_name,
    null as abn_sound_position_code,
    null as abn_sound_position_name,
    null as other_positive_names,
    null as smear_afs_code,
    null as smear_afs_name,
    null as sputum_not_exam_reason,
    null as sample_medium_rstl_code,
    null as sample_medium_rstl_name,
    null as drug_sensitivity_rstl_code,
    null as drug_sensitivity_rstl_name,
    null as culture_rslt_code,
    null as culture_rslt_name,
    null as culture_uncheck_rslt,
    null as drug_fast_dscr,
    null as dr_abn_code,
    null as dr_abn_name,
    null as galt,
    null as gast,
    null as gtbil,
    null as sscr,
    null as sbun,
    null as sua,
    null as xhb,
    null as xrbc,
    null as xwbc,
    null as xplt,
    null as xxc,
    null as other_abn_record,
    null as conclude_code,
    null as conclude_name,
    null as referral_flag,
    null as accept_org_name,
    null as accept_depart_name,
    null as referral_reason,
    null as in_date,
    null as out_agency_name,
    null as transferoutdate,
    null as fu_rslt_2week,
    null as next_follow_date,
    null as fill_date,
    null as filler_no,
    null as fill_name,
    null as flu_times,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_mxjb_fjhsf.sfzz as whtr_refl,
    t_mxjb_fjhsf.tzzlsj as stop_trt_time,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_code,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_name,
    t_mxjb_fjhsf.pgysqm as eval_dr_sign,
    t_mxjb_fjhsf.wyysqm as otp_dr_sign,
    t_mxjb_fjhsf.sjxzrq as on_ins_sub_turn_reg_time,
    t_mxjb_sf_yyqk.ywbh_id as drug_name_code,
    t_mxjb_sf_yyqk.ywmc as drug_name,
    t_mxjb_sf_yyqk.ywyf as used,
    t_mxjb_sf_yyqk.ywyl as dos,
    t_mxjb_sf_yyqk.yysj as medication_time,
    t_mxjb_sf_yyqk.fyycx as dose_adhe,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_basic_info partition(dt)
select 
    concat(woman_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as woman_id,
    null as unified_uscid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as nation_code,
    null as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as occup_code,
    null as occup_name,
    null as edu_background_code,
    null as edu_background_name,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as resd_prov_code,
    null as resd_prov_name,
    null as resd_city_code,
    null as resd_city_name,
    null as resd_coty_code,
    null as resd_coty_name,
    null as resd_town_code,
    null as resd_town_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_cotry_name,
    null as resd_addr_housnum,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as cur_addr_poscode,
    null as addr_districts_code,
    null as addr_districts_name,
    null as empr_name,
    null as tel,
    null as mobile,
    null as past_dis_his,
    null as his_breast_cancer_diag_date,
    null as his_breast_cancer_treatment,
    null as his_contact_bleed,
    null as his_cervical_cancer_diag_date,
    null as his_cervical_cancer_treatment,
    null as vulva_disease_his,
    null as sex_bleeding_his,
    null as oprn_his,
    null as gynaecology_proc_his,
    null as dise_now,
    null as family_hereditary_his,
    null as pat_relate_code,
    null as pat_relate_name,
    null as breast_cancer_family_his,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as consanguine_relate_name,
    null as birth_control,
    null as algs_his,
    null as menarche_age,
    null as term_birth_times,
    null as pre_birth_times,
    null as abortion_sum_cnt,
    null as prg_cnt,
    null as matn_cnt,
    null as spontaneous_abortion_times,
    null as induced_abortion_times,
    null as vaginal_midwifery_times,
    null as caesar_times,
    null as dead_fetus_no,
    null as stillbirth_no,
    null as puerperium_infected,
    null as birth_defect_no,
    null as pregnancy_hp,
    null as pregnancy_dm,
    null as pregnancy_dm_other_com,
    null as history_of_macrosomia,
    null as last_gest_end_date,
    null as last_gest_end_code,
    null as last_gest_end_name,
    null as last_deliver_date,
    null as last_deliver_way_code,
    null as last_deliver_way_name,
    null as birth_girl_no,
    null as birth_boy_no,
    null as children_genetic_diseases,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_fnbjsc.scbh00 as manl_no,
    fy_fnbjsc.sbkh00 as ssc_no,
    fy_fnbjsc.jddw00 as filed_emp,
    fy_fnbjsc.sczt00 as manl_stas_code,
    fy_fnbjsc.sczt00 as manl_stas_name,
    fy_fnbjsc.yfzt00 as matn_stas_code,
    fy_fnbjsc.yfzt00 as matn_stas_name,
    fy_fnbjsc.sfgw00 as whtr_hrisk,
    fy_fnbjsc.ynbh00 as inhosp_no,
    fy_fnbjsc.wi_id as imp_data_of_old_sys_no,
    fy_fnbjsc.sffm00 as whtr_brth,
    fy_fnbjsc.sflc00 as whtr_misc,
    fy_fnbjsc.appfs0 as appf_sco_10_m,
    fy_fnbjsc.xgchd0 as postp_recu_plc_modi_flag,
    fy_fnbjsc.mqnl00 as mthr_age,
    fy_fnbjsc.fqnl00 as fthr_age,
    fy_fnbjsc.sfdq00 as whtr_sing_paren,
    fy_fnbjsc.fmxzflag as brth_rcd_dld_mark,
    fy_fnbjsc.scsfxzflag as fst_flup_dld_mark,
    fy_fnbjsc.edwsfxzflag as two_to_fifth_flup_dld_mark,
    fy_fnbjsc.ch42xzflag as postp_42_days_visu_dld_mark,
    fy_fnbjsc.dhzt00 as tel_stas,
    fy_fnbjsc.xczfrq as next_follo_up_date,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_prenatal_extend_info partition(dt)
select 
    concat(antenatal_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as antenatal_id,
    null as unified_uscid,
    null as pregnant_card_id,
    null as exam_date,
    null as otp_no,
    null as ipt_no,
    fy_fncqjc.scxtbh as manl_sys_no,
    fy_fncqjc.sznl00 as adequ_age,
    fy_fncqjc.zfxm00 as husb_name,
    fy_fncqjc.zfnl00 as husb_age,
    fy_fncqjc.zfgzdw as husb_job_emp,
    fy_fncqjc.zfjkqk as hus_health_code,
    fy_fncqjc.zfjkqk as hus_health_name,
    fy_fncqjc.mcyj00 as mrg_his_last_mena,
    fy_fncqjc.jhnl00 as mrg_his_age_marr,
    fy_fncqjc.qyjh00 as mrg_his_affi_mrg,
    fy_fncqjc.yjs000 as mrg_his_mena_his,
    fy_fncqjc.ycq000 as mrg_his_edc,
    fy_fncqjc.yunci0 as mrg_his_preg_time,
    fy_fncqjc.ccydfm as mrg_his_fetl_birt_vagi_brth,
    fy_fncqjc.ccpgc0 as mrg_his_fetl_birt_cesr_sect,
    fy_fncqjc.rsfy00 as hist_cur_preg_prey_react,
    fy_fncqjc.cgtd00 as curpreg_fsm_code,
    fy_fncqjc.cgtd00 as curpreg_fsm_name,
    fy_fncqjc.jt0000 as hist_cur_preg_seve_vomi,
    fy_fncqjc.ydcx00 as hist_cur_preg_vagi_blee,
    fy_fncqjc.fr0000 as hist_cur_preg_feve,
    fy_fncqjc.gm0000 as hist_cur_preg_alg,
    fy_fncqjc.fy0000 as hist_cur_preg_dose,
    fy_fncqjc.bfgr00 as hist_cur_preg_viri_infe,
    fy_fncqjc.jcyhwz as hist_cur_preg_cont_harm_subs,
    fy_fncqjc.fbyy0 as curpreg_bcp,
    fy_fncqjc.xysqt0 as hist_cur_preg_oth,
    fy_fncqjc.rsdyrq as prey_his_first_chid_date,
    fy_fncqjc.rsdyqk as preyhis_fcnm_code,
    fy_fncqjc.rsdyqk as preyhis_fcnm_name,
    fy_fncqjc.rsderq as prey_his_seco_birt_date,
    fy_fncqjc.rsdeqk as prey_his_seco_birt_info,
    fy_fncqjc.rsdsrq as prey_his_thrd_chid_date,
    fy_fncqjc.rsdsqk as prey_his_thrd_chid_info,
    fy_fncqjc.rsstrq as prey_his_four_trim_date,
    fy_fncqjc.rsstqk as prey_his_four_trim_info,
    fy_fncqjc.jwsx00 as past_hear,
    fy_fncqjc.jwsf00 as past_lung,
    fy_fncqjc.jwsg00 as past_live,
    fy_fncqjc.jwss00 as past_his_kid,
    fy_fncqjc.jwsgxy as past_his_high_bloo_pres,
    fy_fncqjc.jwstnb as past_his_diab,
    fy_fncqjc.jwsjk0 as past_his_hyper,
    fy_fncqjc.jwsgms as past_his_algs,
    fy_fncqjc.jwsjsb as past_his_psych,
    fy_fncqjc.jwsxyb as past_his_hema,
    fy_fncqjc.jwsdx0 as past_his_epile,
    fy_fncqjc.jwssss as past_his_oprn_his,
    fy_fncqjc.jwsqt0 as past_his_oth,
    fy_fncqjc.jzsbr0 as fmhis_in_pers,
    fy_fncqjc.jzsar0 as fmhis_love_ones,
    fy_fncqjc.tjjcxy as phys_exam_bas_bloo_pres,
    fy_fncqjc.tjxy00 as phys_exam_bloo_pres,
    fy_fncqjc.tjtzzs as phys_exam_wt_ind,
    fy_fncqjc.tjs000 as phys_exam_kid,
    fy_fncqjc.tjjzsz as phys_exam_spin_fors,
    fy_fncqjc.tjfz00 as phys_exam_swel,
    fy_fncqjc.tjjfs0 as phys_exam_tend_refl,
    fy_fncqjc.tjjmqz as phys_exam_vari_vein,
    fy_fncqjc.tjqt00 as phys_exam_oth,
    fy_fncqjc.fjwy00 as gyn_exam_vulv_code,
    fy_fncqjc.fjwy00 as gyn_exam_vulv_name,
    fy_fncqjc.fjyd00 as gyn_exam_vagi_code,
    fy_fncqjc.fjyd00 as gyn_exam_vagi_name,
    fy_fncqjc.fjgj00 as gyn_exam_cerv_code,
    fy_fncqjc.fjgj00 as gyn_exam_cerv_name,
    fy_fncqjc.fjgt00 as gyn_exam_uter_code,
    fy_fncqjc.fjgt00 as gyn_exam_uter_name,
    fy_fncqjc.fjfj00 as gyn_exam_att,
    fy_fncqjc.cjgjjj as pelvic_iip,
    fy_fncqjc.cjgj00 as pelvic_icp,
    fy_fncqjc.cjdcwj as pelvic_sfp,
    fy_fncqjc.cjzgjj as pelvic_sip,
    fy_fncqjc.fjxdb0 as asst_exam_hemog,
    fy_fncqjc.fjncg0 as asst_exam_urin_rout_urin_prot,
    fy_fncqjc.fjbcg0 as asst_exam_leuk_cnvl_vagi_secre,
    fy_fncqjc.fjxx00 as asst_exam_blotype_abo,
    fy_fncqjc.fjhiv0 as asst_exam_hiv,
    fy_fncqjc.fjhbsa as asst_exam_hbsag,
    fy_fncqjc.fjcqsc as asst_exam_labr_old_scre,
    fy_fncqjc.fjsg00 as asst_exam_kidn_fun,
    fy_fncqjc.fjgg00 as asstexam_lft_gpt,
    fy_fncqjc.fjbc00 as asst_exam_bmus,
    fy_fncqjc.fjxdt0 as asst_exam_electro,
    fy_fncqjc.bjzd00 as hcare_guid,
    fy_fncqjc.czzdg0 as fstdiag_diag_g,
    fy_fncqjc.czzdp0 as fstdiag_diag_p,
    fy_fncqjc.czzdrs as fstdiag_diag_week_womb_prey,
    fy_fncqjc.czzdnr as fstdiag_diag_diag_cont,
    fy_fncqjc.ryrq00 as admission_date,
    fy_fncqjc.zs0000 as chfcomp,
    fy_fncqjc.ryzd00 as adm_diag,
    fy_fncqjc.cl0000 as dspo,
    fy_fncqjc.zz0000 as refl,
    fy_fncqjc.jcdw00 as exam_emp,
    fy_fncqjc.ckmzbh as obst_otp_no,
    fy_fncqjc.rsdytc as prey_his_fetts_one,
    fy_fncqjc.rsdetc as prey_his_fetts_2,
    fy_fncqjc.rsdstc as prey_his_fetts_thr,
    fy_fncqjc.rssttc as prey_his_fetts_4,
    fy_fncqjc.bsxwz0 as illhis_inqer,
    fy_fncqjc.jcz000 as exam,
    fy_fncqjc.hkzt00 as resd_status_code,
    fy_fncqjc.hkzt00 as resd_status_name,
    fy_fncqjc.qm0000 as sign,
    fy_fncqjc.jcdwna as exam_unit_name,
    fy_fncqjc.jcys00 as exam_dr,
    fy_fncqjc.jcysna as exam_dr_name,
    fy_fncqjc.fjxxrh as asst_exam_blotype_rh,
    fy_fncqjc.ggngc0 as sero_grain_grass_transa,
    fy_fncqjc.ggnbdb as blo_prot,
    fy_fncqjc.ggnzdh as totl_bili,
    fy_fncqjc.ggnjhd as comb_bili,
    fy_fncqjc.sgnxns as blo_urea_nitr,
    fy_fncqjc.tbys00 as fill_form_dr,
    fy_fncqjc.zhbjz0 as last_edit,
    fy_fncqjc.zhbjsj as last_edit_time,
    fy_fncqjc.tjg000 as phys_exam_live,
    fy_fncqjc.tbyz00 as fill_form_geso,
    fy_fncqjc.tbyzts as fill_form_geso_days,
    fy_fncqjc.gwys00 as hrisk_fac,
    fy_fncqjc.gwysbh as hrisk_fac_no,
    fy_fncqjc.gwpf00 as hrisk_sco,
    fy_fncqjc.ncgnt0 as asst_exam_urin_rout_urin_gluc,
    fy_fncqjc.ncgntt as asst_exam_urin_rout_urin_keto,
    fy_fncqjc.ncgnqx as asstexam_urrout_occultblood,
    fy_fncqjc.ncgqt0 as asst_exam_urin_rout_oth,
    fy_fncqjc.ydqjd0 as asst_exam_leuk_cnvl_vagi_clea,
    fy_fncqjc.jzsgxy as fmhis_high_bloo_pres,
    fy_fncqjc.jzstnb as fmhis_diab,
    fy_fncqjc.jzsycb as fmhis_hered_dise,
    fy_fncqjc.jzsjsb as fmhis_psych,
    fy_fncqjc.jzscd0 as fmhis_demen,
    fy_fncqjc.jzsjx0 as fmhis_defo,
    fy_fncqjc.jzsqt0 as fmhis_oth,
    fy_fncqjc.jzsgxya as fmhis_high_bloo_pres_love,
    fy_fncqjc.jzstnba as fmhis_diab_love,
    fy_fncqjc.jzsycba as fmhis_hered_dise_love,
    fy_fncqjc.jzsjsba as fmhis_psych_love,
    fy_fncqjc.jzscd0a as fmhis_demen_love,
    fy_fncqjc.jzsjx0a as fmhis_defo_love,
    fy_fncqjc.jzsqt0a as fmhis_oth_love,
    fy_fncqjc.fjjcgg as gyn_exam_uter_heig,
    fy_fncqjc.fjbg00 as asst_exam_hepa_c,
    fy_fncqjc.scybst as labr_old_scre_18_tris,
    fy_fncqjc.sceyst as labr_old_scre_21_tris,
    fy_fncqjc.sjgqx0 as labr_old_scre_neur_tube_defe,
    fy_fncqjc.rsdwtc as prey_his_fetts_five,
    fy_fncqjc.rsdwrq as prey_his_fifth_fets_date,
    fy_fncqjc.rsdwqk as prey_his_fifth_fets_info,
    fy_fncqjc.gwyyrq as lynn_cocc,
    fy_fncqjc.cjz000 as crter,
    fy_fncqjc.cjrq00 as crte_date,
    fy_fncqjc.zfsfzh as husb_iden_card,
    fy_fncqjc.td0000 as fetal_move,
    fy_fncqjc.ckjcgg as obst_exam_bott_uter_hgt,
    fy_fncqjc.ckjcfw as obst_exam_abde,
    fy_fncqjc.xl0000 as first_exp_hwb,
    fy_fncqjc.ckjctw as obst_exam_fetl_posi,
    fy_fncqjc.cktxl0 as dropsy,
    fy_fncqjc.fz0000 as err_addr_modi_hosp,
    fy_fncqjc.dzxgyy as err_addr_modi_dr_id,
    fy_fncqjc.dzxgid as err_addr_modi_name,
    fy_fncqjc.dzxgxm as err_addr_modi_time,
    fy_fncqjc.dzxgsj as tel,
    fy_fncqjc.dhhm00 as whtr_notc,
    fy_fncqjc.fjtsc0 as asst_exam_sug_scre,
    fy_fncqjc.fjtsc2 as asst_exam_empt_stom_bloo_gluc,
    fy_fncqjc.bcts00 as last_mena_whtr_bmus_proj,
    fy_fncqjc.sfwz00 as whtr_intact_mark,
    fy_fncqjc.gwyse0 as hrisk_fac_2,
    fy_fncqjc.gwyss0 as hrisk_fac_3,
    fy_fncqjc.gwysbhe as hrisk_fac_no_2,
    fy_fncqjc.gwysbhs as hrisk_fac_no_3,
    fy_fncqjc.gwpfe0 as hrisk_sco_2,
    fy_fncqjc.gwyze0 as high_crit_preg_week_2,
    fy_fncqjc.gwpfs0 as hrisk_sco_3,
    fy_fncqjc.gwyzs0 as high_crit_preg_week_3,
    fy_fncqjc.xcgbxb as asstexam_br_wbc_count,
    fy_fncqjc.xcgxxb as asstexam_br_plateletcount,
    fy_fncqjc.xcgqt0 as asst_exam_bloo_rout_oth,
    fy_fncqjc.ygbmky as asstexam_hbsag,
    fy_fncqjc.ygbmkt as asstexam_hbsab,
    fy_fncqjc.ygeky0 as asstexam_hbeag,
    fy_fncqjc.ygekt0 as asstexam_hbeab,
    fy_fncqjc.yghxkt as asstexam_hbcab,
    fy_fncqjc.fqcsrq as husb_brdy,
    fy_fncqjc.resbz0 as prey_his_memo,
    fy_fncqjc.jwspx0 as past_his_anem,
    fy_fncqjc.fkssbz as gyn_oprn_his_flag,
    fy_fncqjc.fksss0 as gynaecology_proc_his,
    fy_fncqjc.grsxy0 as psn_his_smok,
    fy_fncqjc.grsyj0 as whtr_drnk,
    fy_fncqjc.grsyw0 as psn_his_taki_medn,
    fy_fncqjc.grsyhw as psn_his_cont_toxi_harm_subs,
    fy_fncqjc.grsfsx as psn_his_cont_radi,
    fy_fncqjc.grsqt0 as psn_his_oth,
    fy_fncqjc.rshbzs as prey_copn_his,
    fy_fncqjc.rsbfzs as prey_cop_his,
    fy_fncqjc.yqtz00 as pre_preg_wt,
    fy_fncqjc.fjjjbz as gyn_exam_refu_exam_flag,
    fy_fncqjc.wyjjbz as vulv_refu_exam_flag,
    fy_fncqjc.ydjjbz as vagi_refu_exam_flag,
    fy_fncqjc.gjjjbz as cerv_refu_exam_flag,
    fy_fncqjc.gtjjbz as uter_refu_exam_flag,
    fy_fncqjc.fjflag as att_refu_exam_flag,
    fy_fncqjc.mdxqlb as syph_sero_test_type_code,
    fy_fncqjc.mdxqlb as syph_sero_test_type_name,
    fy_fncqjc.mqgj00 as mthr_ntly_code,
    fy_fncqjc.mqgj00 as mthr_ntly_name,
    fy_fncqjc.fqgj00 as fthr_ntly_code,
    fy_fncqjc.fqgj00 as fthr_ntly_name,
    fy_fncqjc.mqsfzl as mthr_iden_card_type_code,
    fy_fncqjc.mqsfzl as mthr_iden_card_type_name,
    fy_fncqjc.fqsfzl as fthr_iden_card_type_code,
    fy_fncqjc.fqsfzl as fthr_iden_card_type_name,
    fy_fncqjc.blflag as add_flag,
    fy_fncqjc.tjrq00 as examination_date,
    fy_fncqjc.sbkh00 as ssc_no,
    fy_fncqjc.ncgbxb as asstexam_urrout_wbc,
    fy_fncqjc.ncgwyc as asstexam_urrout_noabn,
    fy_fncqjc.zstj00 as chfcomp_menop_week,
    fy_fncqjc.zszz00 as chfcomp_symp,
    fy_fncqjc.fkjcqt as gyn_exam_oth,
    fy_fncqjc.zszzqt as chfcomp_symp_oth,
    fy_fncqjc.bcsdj0 as asstexam_bus_bpd,
    fy_fncqjc.bctw00 as asstexam_bus_headcirc,
    fy_fncqjc.bcfw00 as asst_exam_b_colo_ultr_abde,
    fy_fncqjc.bcys00 as asstexam_bus_amnioticfluid,
    fy_fncqjc.bctx00 as asstexam_bus_fetalheart,
    fy_fncqjc.bctpcs as asstexam_bus_placentamaturity,
    fy_fncqjc.bctpfy as asstexam_bus_placentaattach,
    fy_fncqjc.bctpxy as asstexam_bus_placcervdist,
    fy_fncqjc.bcjxl0 as asstexam_bus_umbilicalflow,
    fy_fncqjc.bczgxd as asstexam_bus_uterusthick,
    fy_fncqjc.bcqt00 as asst_exam_b_colo_ultr_oth,
    fy_fncqjc.bcjl00 as asst_exam_b_colo_ultr_ccls,
    fy_fncqjc.ft4000 as asst_exam_thyr_dise_scre_ft4,
    fy_fncqjc.tsh000 as asst_exam_thyr_dise_scre_tsh,
    fy_fncqjc.tpoab0 as asst_exam_thyr_dise_scre_tpoab,
    fy_fncqjc.fjshqt as asstexam_biofull_noabn,
    fy_fncqjc.shkfxt as asstexam_biofull_fbg,
    fy_fncqjc.shgbza as asstexam_biofull_gpt,
    fy_fncqjc.shgcza as asstexam_biofull_got,
    fy_fncqjc.shzdb0 as asstexam_biofull_tp,
    fy_fncqjc.shbdb0 as asst_exam_bioch_full_set_albu,
    fy_fncqjc.shzdzs as asstexam_biofull_tba,
    fy_fncqjc.shzdhs as asstexam_biofull_tbil,
    fy_fncqjc.shjg00 as asst_exam_bioch_full_set_myon,
    fy_fncqjc.shnsd0 as asstexam_biofull_bun,
    fy_fncqjc.shns00 as asstexam_biofull_ua,
    fy_fncqjc.shldh0 as asst_exam_bioch_full_set_ldh,
    fy_fncqjc.shqt00 as asst_exam_bioch_full_set_oth,
    fy_fncqjc.rprdd0 as asst_exam_rpr,
    fy_fncqjc.ggkfxt as asstexam_lf_fbg,
    fy_fncqjc.ggqt00 as asst_exam_live_fun_oth,
    fy_fncqjc.sgqt00 as asst_exam_kidn_fun_oth,
    fy_fncqjc.jzsh00 as asst_exam_er_bioch,
    fy_fncqjc.xdtwyc as asst_exam_electro_un_abn,
    fy_fncqjc.fjnxgn as asst_exam_coag_efcc,
    fy_fncqjc.bcggj0 as asstexam_bus_femurneck,
    fy_fncqjc.jcxmjc as asst_exam_refu_check,
    fy_fncqjc.mz0000 as mthr_naty_code,
    fy_fncqjc.mz0000 as mthr_naty_name,
    fy_fncqjc.fqmz00 as fthr_naty_code,
    fy_fncqjc.fqmz00 as fthr_naty_name,
    fy_fncqjc.fjtppa as asst_exam_tppa,
    fy_fncqjc.sfjh00 as whtr_mrg,
    fy_fncqjc.bjzdqt as hcare_guid_oth,
    fy_fncqjc.fjqt00 as asst_exam_oth,
    fy_fncqjc.wcjysb as asstexam_nipt_tris13,
    fy_fncqjc.wcjyey as asstexam_nipt_tris21,
    fy_fncqjc.jcqt00 as asst_exam_refu_check_oth,
    fy_fncqjc.xcgzxl as asstexam_coag_neutpct,
    fy_fncqjc.nxpt00 as asst_exam_coag_efcc_pt,
    fy_fncqjc.nxinr0 as asst_exam_coag_efcc_inr,
    fy_fncqjc.nxfg00 as asst_exam_coag_efcc_fg,
    fy_fncqjc.nxappt as asst_exam_coag_efcc_aptt,
    fy_fncqjc.nxdejt as asst_exam_coag_efcc_d_dime,
    fy_fncqjc.gwyzy0 as high_crit_preg_week_1,
    fy_fncqjc.yyrqe0 as hrisk_ordr_date_2,
    fy_fncqjc.yyrqs0 as hrisk_ordr_date_3,
    fy_fncqjc.xcgkdrq as bloo_rout_bilg_date,
    fy_fncqjc.xxkdrq as blotype_bilg_date,
    fy_fncqjc.ncgkdrq as urin_rout_bilg_date,
    fy_fncqjc.bdcgkdrq as leuk_cnvl_bilg_date,
    fy_fncqjc.yxgykdrq as hepa_b_five_bilg_date,
    fy_fncqjc.rprkdrq as rpr_bilg_date,
    fy_fncqjc.tppakdrq as tppa_bilg_date,
    fy_fncqjc.hivkdrq as hiv_bilg_date,
    fy_fncqjc.bgkdrq as hepa_c_bilg_date,
    fy_fncqjc.hbsagkdrq as hbsag_bilg_date,
    fy_fncqjc.cqsckdrq as labr_old_scre_bilg_date,
    fy_fncqjc.wcjykdrq as ninve_gene_dect_bilg_date,
    fy_fncqjc.ogttkdrq as oggt_bilg_date,
    fy_fncqjc.sgkdrq as kidn_fun_bilg_date,
    fy_fncqjc.ggkdrq as live_fun_bilg_date,
    fy_fncqjc.shqtkdrq as bioch_full_set_bilg_date,
    fy_fncqjc.bckdrq as b_colo_ultr_bilg_date,
    fy_fncqjc.jzshkdrq as er_bioch_bilg_date,
    fy_fncqjc.jzxkdrq as thyr_dise_scre_bilg_date,
    fy_fncqjc.xdtkdrq as electro_bilg_date,
    fy_fncqjc.nxgnkdrq as coag_efcc_bilg_date,
    fy_fncqjc.shk000 as asst_exam_bioch_full_set_pot,
    fy_fncqjc.shna00 as asst_exam_bioch_full_set_sodi,
    fy_fncqjc.shcl00 as asst_exam_bioch_full_set_chlo,
    fy_fncqjc.shca00 as asst_exam_bioch_full_set_calc,
    fy_fncqjc.shmg00 as asst_exam_bioch_full_set_magn,
    fy_fncqjc.jzk000 as asst_exam_er_bioch_pot,
    fy_fncqjc.jzna00 as asst_exam_er_bioch_sodi,
    fy_fncqjc.jzcl00 as asst_exam_er_bioch_chlo,
    fy_fncqjc.jzca00 as asst_exam_er_bioch_calc,
    fy_fncqjc.jzmg00 as asst_exam_er_bioch_magn,
    fy_fncqjc.jzzdb0 as asst_exam_er_bioch_totl_prot,
    fy_fncqjc.jzbdb0 as asst_exam_er_bioch_albu,
    fy_fncqjc.jzzdhs as asst_exam_er_bioch_drt_bili,
    fy_fncqjc.jzjdhs as asst_exam_er_bioch_indi_bili,
    fy_fncqjc.jzgbzam as asstexam_er_gpt,
    fy_fncqjc.jzgczam as asstexam_er_got,
    fy_fncqjc.jzxt00 as asst_exam_er_bioch_bloo_gluc,
    fy_fncqjc.jzrstam as asst_exam_er_bioch_lacta_deam,
    fy_fncqjc.jzns00 as asst_exam_er_bioch_urea,
    fy_fncqjc.jzjg00 as asst_exam_er_bioch_creat,
    fy_fncqjc.ft3000 as asst_exam_thyr_ft3,
    fy_fncqjc.nxmsj0 as asst_exam_coag_efcc_proth_time,
    fy_fncqjc.ggzdzs as asstexam_lf_tba,
    fy_fncqjc.ggnzdb as asst_exam_live_fun_totl_prot,
    fy_fncqjc.jznsh0 as asst_exam_er_bioch_uric_acid,
    fy_fncqjc.xcglbx as asst_exam_bloo_rout_lymph_prct,
    fy_fncqjc.xcghxb as asstexam_br_mcv,
    fy_fncqjc.xcgxhd as asstexam_br_mch,
    fy_fncqjc.gtqt00 as uter_oth,
    fy_fncqjc.ncgndy as asst_exam_urin_rout_urobi,
    fy_fncqjc.ncgdhs as asst_exam_urin_rout_bili,
    fy_fncqjc.jzzdgc as asst_exam_er_bioch_totl_chol,
    fy_fncqjc.bdcgmj as leuk_cnvl_mold,
    fy_fncqjc.bdcgdc as leuk_cnvl_tric,
    fy_fncqjc.bdcgbv as leuk_cnvl_bacte_vagi_dise_bv,
    fy_fncqjc.bdcgqt as leuk_cnvl_oth,
    fy_fncqjc.fjbdcg as leuk_cnvl_whtr_abn,
    fy_fncqjc.cxgzxl as asstexam_br_neutpct,
    fy_fncqjc.fqhkd0 as husb_domi,
    fy_fncqjc.nz0000 as to_be_diag,
    fy_fncqjc.fjyyrq as retes_ordr_date,
    fy_fncqjc.fjmr00 as retes_def_btn,
    fy_fncqjc.tjmr00 as phys_exam_def_btn,
    fy_fncqjc.ogttqt as ogtt_oth,
    fy_fncqjc.cqscqt as labr_old_scre_oth,
    fy_fncqjc.amyzx0 as aids_syph_b_cslt,
    fy_fncqjc.thxhkdrq as glyc_hemog_bilg_date,
    fy_fncqjc.thxhdb as asst_exam_glyc_hemog,
    fy_fncqjc.lrzxm0 as inpt_the_name,
    fy_fncqjc.jczxm0 as exam_operator_name,
    fy_fncqjc.qxlpi0 as fetus_umbil_bloo_flow_mnit_pi,
    fy_fncqjc.qxlri0 as fetus_umbil_bloo_flow_mnit_ri,
    fy_fncqjc.qxljckdrq as fetus_umbflow_orderdate,
    fy_fncqjc.scbz00 as upld_flag,
    fy_fncqjc.rsstc6 as prey_his_fetts_six,
    fy_fncqjc.rsstc7 as prey_his_fetts_seve,
    fy_fncqjc.xcgjcdw as bloo_rout_exam_emp,
    fy_fncqjc.pttcd0 as bloo_gluc_mes,
    fy_fncqjc.pttkdrq as bloo_gluc_mes_bilg_date,
    fy_fncqjc.kaxjkdrq as anti_a_pote_bilg_date,
    fy_fncqjc.kbxjkdrq as anti_b_pote_bilg_date,
    fy_fncqjc.kaxj00 as anti_a_pote,
    fy_fncqjc.kbxj00 as anti_b_pote,
    fy_fncqjc.yszd00 as amnio_flui_diag,
    fy_fncqjc.gjxbjc as cerv_cyt_exam,
    fy_fncqjc.cszghd as ultra_uterusthick,
    fy_fncqjc.csgjcd as ultra_measu_cerv_leng,
    fy_fncqjc.cscrp0 as rapi_crp,
    fy_fncqjc.pxjy01 as medt_anem_gene_dect,
    fy_fncqjc.coombsiggc as drt_reta_glon_test,
    fy_fncqjc.tdhcgz as fetus_mri,
    fy_fncqjc.rsfyy0 as prey_react_mon,
    fy_fncqjc.cgtdy0 as first_sens_fetl_mov_mon,
    fy_fncqjc.fjtrus as trust,
    fy_fncqjc.trusjcdw as trust_exam_emp,
    fy_fncqjc.truskdrq as trust_bilg_date,
    fy_fncqjc.fjelis as elisa,
    fy_fncqjc.elisjcdw as elisa_exam_emp,
    fy_fncqjc.eliskdrq as elisa_bilg_date,
    fy_fncqjc.fjrt00 as rt,
    fy_fncqjc.rtjcdw as rt_exam_emp,
    fy_fncqjc.rtkdrq as rt_bilg_date,
    fy_fncqjc.ft30dw as asst_exam_thyr_ft3_emp,
    fy_fncqjc.ft40dw as asst_exam_thyr_ft4_emp,
    fy_fncqjc.tsh0dw as asst_exam_thyr_tsh_emp,
    fy_fncqjc.bzyszs as asstexam_bus_afi,
    fy_fncqjc.fjjcbz as asst_exam,
    fy_fncqjc.rprjc0 as syph_whtr_dect,
    fy_fncqjc.rprzdgr as syph_whtr_ifet,
    fy_fncqjc.rprzl as syph_whtr_trt,
    fy_fncqjc.ygjc0 as hepa_b_whtr_dect,
    fy_fncqjc.ygjcjg as hepa_b_dect_rslt,
    fy_fncqjc.hivjcqzrq as hiv_cnfm_date,
    fy_fncqjc.hivjcjg as hiv_dect_rslt,
    fy_fncqjc.hivjc0 as hiv_whtr_dect,
    fy_fncqjc.scqks0 as scre_rslt_info_no_3,
    fy_fncqjc.pgnr00 as eval_cont,
    fy_fncqjc.rspgbh as eval_cont_no,
    fy_fncqjc.pgqk00 as eval_rslt_info_no,
    fy_fncqjc.pgnre0 as eval_cont_2,
    fy_fncqjc.pgnrs0 as eval_cont_3,
    fy_fncqjc.rspgbhe as eval_cont_no_2,
    fy_fncqjc.rspgbhs as eval_cont_no_3,
    fy_fncqjc.pgqke0 as eval_rslt_info_no_2,
    fy_fncqjc.pgqks0 as eval_rslt_info_no_3,
    fy_fncqjc.scnr00 as scre_cont,
    fy_fncqjc.rsscbh as scre_cont_no,
    fy_fncqjc.scqk00 as scre_rslt_info_no,
    fy_fncqjc.scnre0 as scre_cont_2,
    fy_fncqjc.scnrs0 as scre_cont_3,
    fy_fncqjc.rsscbhe as scre_cont_no_2,
    fy_fncqjc.rsscbhs as scre_cont_no_3,
    fy_fncqjc.scqke0 as scre_rslt_info_no_2,
    null as rchk_flag,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_first_prenatal_visit partition(dt)
select 
    concat(unified_uscid,fu_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as fu_no,
    null as pregnant_card_id,
    null as fu_org_code,
    null as full_name,
    null as register_datetime,
    null as input_gest_weeks,
    null as pregnancy_age,
    null as husb_name,
    null as husb_age,
    null as husband_tel,
    null as prg_cnt,
    null as deliver_matn_cnt,
    null as pregnancy_matn_cnt,
    null as last_menses_date_mark,
    null as last_mena_date,
    null as expect_deliver_date,
    null as past_history_code,
    null as past_history_name,
    null as family_his_code,
    null as family_his_name,
    null as per_his_code,
    null as per_his_name,
    null as gyn_oprn_his_flag,
    null as deliver_his_code,
    null as deliver_his_name,
    null as height,
    null as weight,
    null as bmi,
    null as sbp,
    null as dbp,
    null as heart_check_rslt_code,
    null as heart_check_rslt_name,
    null as heart_check_rslt_dscr,
    null as lungs_check_rslt_code,
    null as lungs_check_rslt_name,
    null as lungs_check_rslt_dscr,
    null as vulva_rslt_code,
    null as vulva_rslt_name,
    null as vulva_rslt_dscr,
    null as deliver_rslt_code,
    null as deliver_rslt_name,
    null as deliver_rslt_dscr,
    null as cervical_rslt_code,
    null as cervical_rslt_name,
    null as cervical_rslt_dscr,
    null as uterine_rslt_code,
    null as uterine_rslt_name,
    null as uterine_rslt_dscr,
    null as adnexa_uteri_rslt_code,
    null as adnexa_uteri_rslt_name,
    null as adnexa_uteri_rslt_dscr,
    null as hgb_value,
    null as wbc_value,
    null as platelet_value,
    null as other_exam_res,
    null as urinary_protein,
    null as uglu_quan_check_value,
    null as urine_ket_code,
    null as urine_ket_name,
    null as urine_occult_blood_code,
    null as urine_occult_blood_name,
    null as urine_other_rslt,
    null as blotype_abo_code,
    null as blotype_abo_name,
    null as blotype_rh_code,
    null as blotype_rh_name,
    null as serum_transa_value,
    null as ast_value,
    null as sgot_value,
    null as tbi_value,
    null as dbil_value,
    null as scr_bilirubin_value,
    null as scr_value,
    null as blood_urea_nitrogen_value,
    null as vagina_secret_code,
    null as vagina_secret_name,
    null as vagina_clean_code,
    null as vagina_clean_name,
    null as hbsag_b_code,
    null as hbsag_b_name,
    null as hbsab_code,
    null as hbsab_name,
    null as hbeag_code,
    null as hbeag_name,
    null as hbeab_code,
    null as hbeab_name,
    null as hbab_code,
    null as hbab_name,
    null as vdrl_code,
    null as vdrl_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as bscan_exam_rslt,
    null as check_other_rslt,
    null as overall_ability_code,
    null as overall_ability_name,
    null as overall_ability_dscr,
    null as guidelines_code,
    null as guidelines_name,
    null as transfer_treatment,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as next_follow_date,
    null as fu_doc_no,
    null as fu_doc_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    填表孕周天数. as fill_form_geso_days,
    woman_first_prenatal_followup. as b_us_bpd,
    woman_first_prenatal_followup. as b_ultrasonic_conclusion,
    woman_first_prenatal_followup. as b_ultrasonic_other,
    头围. as head_circumference,
    股骨颈. as femur,
    腹围. as abdominal_circumference,
    羊水. as amniotic_fluid,
    胎心. as cardiac,
    胎盘成熟度. as placenta_maturity,
    胎盘附于. as placenta_attached,
    胎盘下缘距子宫颈内口. as placenta_uterus_spacing,
    脐血流. as umbilical_blood_flow,
    子宫下段厚度. as lower_uterus_thickness,
    羊水指数. as amniotic_fluid_index,
    人工流产. as induced_abortion_num,
    总体评估为其他时描述. as overall_evaluation_remark,
    保健指导代码为其他时描述. as health_guide_remark,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_prenatal_visits_2_5 partition(dt)
select 
    concat(fu_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_no,
    null as pregnant_card_id,
    null as fu_org_code,
    null as fu_times,
    null as full_name,
    null as urge_date,
    null as geso_val,
    null as chfcomp,
    null as weight,
    null as cervix_height,
    null as sbp,
    null as dbp,
    null as abdominal_circumference,
    null as fetalpositionstatus,
    null as fhr,
    null as hgb_value,
    null as urinary_protein,
    null as oth_asst_exam,
    null as fu_rslt_code,
    null as fu_rslt_name,
    null as fu_rslt_dscr,
    null as guide_code,
    null as guide_name,
    null as transfer_treatment,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as next_follow_date,
    null as fu_doc_no,
    null as fu_doc_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_edwcsf.cbbj00 as subdi_mark_code,
    fy_edwcsf.cbbj00 as subdi_mark_name,
    fy_edwcsf.yzts00 as geso,
    fy_edwcsf.wi_id as imp_data_of_old_sys_no,
    fy_edwcsf.drwdw0 as whtr_imp_exte_emp,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_delivery_record partition(dt)
select 
    concat(deliverid,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as deliverid,
    null as unified_uscid,
    null as pregnant_card_id,
    null as deliver_date,
    null as total_reproduct_min,
    null as first_labor_min,
    null as second_labor_min,
    null as third_labor_min,
    null as contractions_start_time,
    null as induce_way_code,
    null as induce_way_name,
    null as membranes_rupture_way_code,
    null as membranes_rupture_way_name,
    null as membranes_rupture_time,
    null as fetus_delivery_time,
    null as placenta_delivery_time,
    null as uterus_full_open_time,
    null as placenta_delivery_way_code,
    null as placenta_delivery_way_name,
    null as membranes_complete_case_flag,
    null as deliver_preg_week,
    null as deliver_way_othdcr,
    null as deliver_way_code,
    null as deliver_way_name,
    null as total_bleeding_no,
    null as birthing_bleeding_no,
    null as blood_reason,
    null as after_birth_2h_bleeding_nu,
    null as use_drug_dscr,
    null as after_birth_30m_dscr,
    null as after_birth_60m_dscr,
    null as after_birth_90m_dscr,
    null as after_birth_120m_dscr,
    null as urine_flag,
    null as amn_fluid_case,
    null as umb_dscr,
    null as placenta_dscr,
    null as birth_canal_injury_dscr,
    null as skin_touch_time,
    null as operation_testify,
    null as oprn_oprt_name,
    null as proc_special_dscr,
    null as is_accompany_childbirth,
    null as is_early_contact_sucking,
    null as nwb_name,
    null as nwb_gend_code,
    null as nwb_gend_name,
    null as birth_weight,
    null as birth_length,
    null as head_circum,
    null as chest_circum,
    null as apgar1_score,
    null as apgar5_score,
    null as apgar10_score,
    null as birth_situat_code,
    null as birth_situat_name,
    null as pregnancy_result_code,
    null as pregnancy_result_name,
    null as birth_defect_code,
    null as birth_defect_name,
    null as birth_defect_dscr,
    null as nwb_hear_screen_case_code,
    null as nwb_hear_screen_case_name,
    null as leave_date,
    null as diag_res,
    null as deliver_org_code,
    null as deliver_org_name,
    null as reg_org_name,
    null as case_mark,
    null as end_case_date,
    null as close_case_org_code,
    null as close_case_org_name,
    null as deliver_emp_code,
    null as deliver_emp_name,
    null as record_emp_name,
    null as record_emp_code,
    null as nurse_emp_name,
    null as fill_date,
    null as amn_fluid_amount,
    null as hcg,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as pulse,
    null as nwb_rescue_code,
    null as nwb_rescue_name,
    null as nwb_rescue_flag,
    null as nwb_intensive_dscr_code,
    null as nwb_intensive_dscr_name,
    null as nwb_intensive_mark,
    null as diag_way_code,
    null as diag_way_name,
    null as nwb_urine_dscr,
    null as red_hip_flag,
    null as jaundice_level_code,
    null as jaundice_level_name,
    null as head_circ,
    null as bas_wt,
    null as ht,
    null as nwb_heart_rate,
    null as last_gestate_stop_date,
    null as deliver_time,
    null as previous_gest_end_code,
    null as previous_gest_end_name,
    null as previous_deliver_way_code,
    null as previous_deliver_way_name,
    null as preg_name,
    null as brdy,
    null as ipt_no,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as perineum_cut_mark,
    null as perine_suture_no,
    null as perinaeum_case_code,
    null as perinaeum_case_name,
    null as is_critical,
    null as intensive_code,
    null as intensive_name,
    null as blood_pressure,
    null as breast_milk_duration,
    null as mother_res,
    null as fetals,
    null as nwb_body_temperat,
    null as birth_defect_mark,
    null as birth_defect_type_code,
    null as birth_defect_type_name,
    fy_fmqkjl.csqxls as birth_defect_no,
    null as deliver_res,
    null as nwb_death_flag,
    null as nwb_death_reason,
    null as nwb_death_time,
    null as midwifery_psn_name,
    null as midwifery_org_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_fmqkjl.fmdd00 as brth_loc,
    fy_fmqkjl.tfw000 as fets_part,
    fy_fmqkjl.zyrq00 as ipt_date,
    fy_fmqkjl.zhbjz0 as last_edit,
    fy_fmqkjl.zhbjrq as last_edit_date,
    fy_fmqkjl.sftz00 as whtr_notc_code,
    fy_fmqkjl.sftz00 as whtr_notc_name,
    fy_fmqkjl.wi_id as imp_data_of_old_sys_no,
    fy_fmqkjl.gwys00 as hrisk_fac,
    fy_fmqkjl.gwysbh as hrisk_fac_id,
    fy_fmqkjl.gwyyrq as hrisk_ordr_date,
    fy_fmqkjl.gwpf00 as hrisk_sco,
    fy_fmqkjl.jszid1 as midy_the_id2,
    fy_fmqkjl.jsznam2 as midy_the_name_2,
    fy_fmqkjl.cszbh0 as bir_cert_num,
    fy_fmqkjl.sfgw00 as whtr_hrisk,
    fy_fmqkjl.chsscx as postp_thirty_m_blee,
    fy_fmqkjl.chlscx as postp_60_m_blee,
    fy_fmqkjl.chjscx as postp_90_m_blee,
    fy_fmqkjl.chlxscx as postp_120_m_blee,
    fy_fmqkjl.chxyss1 as postpartum_30m_sbp,
    fy_fmqkjl.chxyss2 as postpartum_30m_dbp,
    fy_fmqkjl.chxyls1 as postp_60_m_bloo_pres_systpre,
    fy_fmqkjl.chxyls2 as postpartum_60m_dbp,
    fy_fmqkjl.chxyjs1 as postp_90_m_bloo_pres_systpre,
    fy_fmqkjl.chxyjs2 as postpartum_90m_dbp,
    fy_fmqkjl.ccszsl as oxyto_inj_amt,
    fy_fmqkjl.ccsbw0 as oxyto_inj_part,
    fy_fmqkjl.mjzsl0 as ergot_inj_amt,
    fy_fmqkjl.mjzsbw as ergot_inj_part,
    fy_fmqkjl.mjzsqt as ergot_inj_oth,
    fy_fmqkjl.nfzs00 as in_stit_val,
    fy_fmqkjl.nfxx00 as in_stit_type,
    fy_fmqkjl.wfzs00 as exte_stit_val,
    fy_fmqkjl.wfxx00 as exte_stit_type,
    fy_fmqkjl.hyfhgj as perin_sutur_new_anal_exam,
    fy_fmqkjl.yxkssj as earl_expo_earl_aspi_begn_time,
    fy_fmqkjl.yxcxsj as earl_expo_earl_aspi_dura,
    fy_fmqkjl.yxqk00 as earl_expo_earl_aspi_info_code,
    fy_fmqkjl.yxqk00 as earl_expo_earl_aspi_info_name,
    fy_fmqkjl.wyxyy0 as noskincontact_reason,
    fy_fmqkjl.lkcfsj as matn_leave_labo_deli_time,
    fy_fmqkjl.chxbsj as labr_real_esta_new_uri_time,
    fy_fmqkjl.hyqkzz as perin_incs_indi,
    fy_fmqkjl.pgczj0 as cesr_sect_indi,
    fy_fmqkjl.pgcfzz as cesr_sect_gyn_ind,
    fy_fmqkjl.yzhbz0 as sev_copn,
    fy_fmqkjl.newbz0 as memo,
    fy_fmqkjl.ch0000 as bedno,
    fy_fmqkjl.sfxtgl as matr_syst_admi,
    fy_fmqkjl.ynxc00 as inhosp_prete_labo,
    fy_fmqkjl.sfwzyf as criti_matr_flag,
    fy_fmqkjl.ncys00 as diffic_fac,
    fy_fmqkjl.qdyc00 as umbil_cord_abn,
    fy_fmqkjl.crqbfz as puer_cop,
    fy_fmqkjl.lcsj00 as parturient_time,
    fy_fmqkjl.hyfhzs as perin_sutur_num_ned,
    fy_fmqkjl.hylsqk as perin_fiss_info,
    fy_fmqkjl.gjqk00 as cervical_case,
    fy_fmqkjl.gg0000 as uter_heig,
    fy_fmqkjl.twyc00 as fetl_posi_abn,
    fy_fmqkjl.csycqk as time_deli_abnor,
    fy_fmqkjl.zx0000 as eclam,
    fy_fmqkjl.zxyl00 as inhosp_eclam,
    fy_fmqkjl.jsqk00 as midy_info_ipt_brth_code,
    fy_fmqkjl.jsqk00 as midy_info_ipt_brth_name,
    fy_fmqkjl.blflag as add_flag,
    fy_fmqkjl.sfswfm as whtr_city_exte_brth,
    fy_fmqkjl.chmb01 as postpartum_30m_pulse,
    fy_fmqkjl.chmb02 as postp_pul_60_sco_emp_per_min,
    fy_fmqkjl.chmb03 as postp_pul_90_sco_emp_per_min,
    fy_fmqkjl.chmb04 as postp_pul_120_sco_emp_per_min,
    fy_fmqkjl.lsqk00 as fulfi_info,
    fy_fmqkjl.sfzcjg as whtr_assi_labo_ins,
    fy_fmqkjl.zybah0 as matn_ipt_medcas_no,
    fy_fmqkjl.dwfzr0 as emp_resper,
    fy_fmqkjl.jglxdh as tel,
    fy_fmqkjl.fmjgnw as brth_loc_code,
    fy_fmqkjl.fmjgnw as brth_loc_name,
    fy_fmqkjl.fyscbz as prov_matr_child_upld_flag,
    fy_fmqkjl.cxrq00 as bloo_collec_date,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_folic_acid_register partition(dt)
select 
    concat(unified_uscid,sys_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    fy_yslqdj.xtbh00 as sys_no,
    fy_yslqdj.iccard as card_no,
    fy_yslqdj.scxtbh as manl_sys_no,
    null as woman_id,
    fy_yslqdj.yzz000 as preg_week_w,
    fy_yslqdj.yzt000 as preg_week_d,
    fy_yslqdj.lqrq00 as rece_date,
    fy_yslqdj.lqs000 as rece_val,
    fy_yslqdj.lqr000 as rece_huma,
    fy_yslqdj.sfzbhy as whtr_prpa_prg,
    fy_yslqdj.sfgwdy as whtr_high_crit_preg,
    fy_yslqdj.ffr000 as issu_huma,
    fy_yslqdj.ffrxm0 as issu_huma_name,
    fy_yslqdj.ffdw00 as issu_emp,
    fy_yslqdj.remark as remark,
    fy_yslqdj.cjz000 as add_the,
    fy_yslqdj.cjrq00 as add_date,
    fy_yslqdj.zhbjz0 as last_edit,
    fy_yslqdj.zhbjrq as last_edit_date,
    fy_yslqdj.yyid00 as hosp_no,
    fy_yslqdj.name as full_name,
    fy_yslqdj.birthday as brdy,
    fy_yslqdj.age as age,
    fy_yslqdj.idcardno as id_card,
    fy_yslqdj.lxdh00 as tel,
    fy_yslqdj.adress_pro as adress_pro,
    fy_yslqdj.adress_city as adress_city,
    fy_yslqdj.adress_county as adress_county,
    fy_yslqdj.adress_rural as adress_rural,
    fy_yslqdj.adress_village as adress_village,
    fy_yslqdj.adrss_hnumber as location_detl_addr,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_marital_exam_record partition(dt)
select 
    concat(unified_uscid,sort,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    fy_hqyxjc. as woman_id,
    fy_hqyxjc. as unified_uscid,
    fy_hqyxjc.xh0000 as sort,
    fy_hqyxjc.txrq00 as fillin_date,
    fy_hqyxjc.xm0000 as full_name,
    fy_hqyxjc.xb0000 as gender_code,
    fy_hqyxjc.xb0000 as gender_name,
    fy_hqyxjc.csrq00 as brdy,
    fy_hqyxjc.sfzh00 as id_card,
    fy_hqyxjc.zy0000 as occup_code,
    fy_hqyxjc.zy0000 as occup_name,
    fy_hqyxjc.whcd00 as edu_background_code,
    fy_hqyxjc.whcd00 as edu_background_name,
    fy_hqyxjc.mz0000 as nation_code,
    fy_hqyxjc.mz0000 as nation_name,
    fy_hqyxjc.tbshe0 as prenup_prov,
    fy_hqyxjc.tbshi0 as prenup_city,
    fy_hqyxjc.tbxian as prenup_county,
    fy_hqyxjc.tbjd00 as prenup_subdistrict,
    fy_hqyxjc.tbcun0 as filli_prenup_check_time_of_vil,
    fy_hqyxjc.tbmph0 as prenup_housenum,
    fy_hqyxjc.hkshe0 as resd_addr_prov_code,
    fy_hqyxjc.hkshe0 as resd_addr_prov_name,
    fy_hqyxjc.hkshi0 as resd_addr_city_code,
    fy_hqyxjc.hkshi0 as resd_addr_city_name,
    fy_hqyxjc.hkxian as resd_coty_code,
    fy_hqyxjc.hkxian as resd_coty_name,
    fy_hqyxjc.hkjd00 as resd_town_code,
    fy_hqyxjc.hkjd00 as resd_town_name,
    fy_hqyxjc.hkcun0 as resd_addr_comm_code,
    fy_hqyxjc.hkmph0 as resd_addr_comm_name,
    fy_hqyxjc.xzzshe as curr_addr_prov,
    fy_hqyxjc.xzzshi as curr_addr_city,
    fy_hqyxjc.xzzxia as cur_addr_coty_code,
    fy_hqyxjc.xzzxia as cur_addr_coty_name,
    fy_hqyxjc.xzzjd0 as curr_addr_subd,
    fy_hqyxjc.xzzcun as curr_addr_vil,
    fy_hqyxjc.xzzmph as curr_addr_hsnum,
    fy_hqyxjc.yb0000 as poscode,
    fy_hqyxjc.gzdw00 as workplace,
    fy_hqyxjc.lxdh00 as tel,
    fy_hqyxjc.dfxm00 as oth_name,
    fy_hqyxjc.bh0000 as ref_no,
    fy_hqyxjc.dfbh00 as oppo_no,
    fy_hqyxjc.jcrq00 as exam_date,
    fy_hqyxjc.xygx00 as bloo_rela,
    fy_hqyxjc.jwbs00 as past_illhis,
    fy_hqyxjc.sss000 as oprn_his,
    fy_hqyxjc.jwbsqt as past_illhis_oth,
    fy_hqyxjc.xbs000 as dise_now,
    fy_hqyxjc.yjccnl as mena_menar_age,
    fy_hqyxjc.jq1000 as menst_prd_of_divi,
    fy_hqyxjc.jq2000 as menst_prd_divi,
    fy_hqyxjc.jl0000 as mens_flow,
    fy_hqyxjc.tj0000 as dysme,
    fy_hqyxjc.mcyj00 as last_mena_date,
    fy_hqyxjc.jwhys0 as past_mrg_his,
    fy_hqyxjc.znrs00 as child_psncnt,
    fy_hqyxjc.zycs00 as adequ_mon_cnt,
    fy_hqyxjc.zccs00 as pret_cnt,
    fy_hqyxjc.lccs00 as misc_cnt,
    fy_hqyxjc.ycjzs0 as with_hered_rel_of_fmhis,
    fy_hqyxjc.hzbrgx as relation_patient_code,
    fy_hqyxjc.hzbrgx as relation_patient_name,
    fy_hqyxjc.jzjqhp as fm_clos_rela_marr,
    fy_hqyxjc.bssjqm as prenup_illhis_exam_sign,
    fy_hqyxjc.bsysid as prenup_illhis_exam_dr_id,
    fy_hqyxjc.bsysqm as prenup_illhis_exam_dr_sign,
    fy_hqyxjc.xy1000 as bloo_pres_of_divid,
    fy_hqyxjc.xy2000 as bloo_pres_of_divis,
    fy_hqyxjc.tstt00 as sp_body_cond,
    fy_hqyxjc.jszt00 as ment_sta,
    fy_hqyxjc.tsmr00 as sp_face,
    fy_hqyxjc.zl0000 as inte,
    fy_hqyxjc.pfmf00 as skin_hai,
    fy_hqyxjc.wg0000 as five_sens,
    fy_hqyxjc.jzx000 as thyr,
    fy_hqyxjc.xl1000 as hetat,
    fy_hqyxjc.xl2000 as rhythm_heart_dscr,
    fy_hqyxjc.zayin0 as murm,
    fy_hqyxjc.fei000 as lung,
    fy_hqyxjc.gan000 as live,
    fy_hqyxjc.szjz00 as fors_spin,
    fy_hqyxjc.tgjcqt as physexm_oth,
    fy_hqyxjc.tgysid as prenup_physexm_dr_id,
    fy_hqyxjc.tgysqm as prenup_physexm_dr_sign,
    fy_hqyxjc.hj0000 as laryn,
    fy_hqyxjc.mym000 as male_pubi_hair,
    fy_hqyxjc.yj0000 as peni,
    fy_hqyxjc.bp0000 as foresk,
    fy_hqyxjc.gwscmj as testi_bilat_ask_wth,
    fy_hqyxjc.gwtjz0 as testi_volu_left,
    fy_hqyxjc.gwtjy0 as testi_volu_rght,
    fy_hqyxjc.gww000 as testi_un_ask_wth,
    fy_hqyxjc.fg0000 as epidi,
    fy_hqyxjc.jjz000 as nodu_left,
    fy_hqyxjc.jjy000 as nodu_rght,
    fy_hqyxjc.jsjmqz as sperm_cord_vari_vein,
    fy_hqyxjc.jscd00 as sperm_cord_vari_vein_deg,
    fy_hqyxjc.mxzqt0 as male_second_sex_char_oth,
    fy_hqyxjc.mysid0 as malessc_examdrid,
    fy_hqyxjc.mysqm0 as malessc_examdrsign,
    fy_hqyxjc.fym000 as fml_pubi_hair,
    fy_hqyxjc.rf0000 as breast_exam_rslt,
    fy_hqyxjc.gcwy00 as anal_chec_vulv,
    fy_hqyxjc.gcfmw0 as anal_chec_secre,
    fy_hqyxjc.gczg00 as anal_chec_uter,
    fy_hqyxjc.gcfj00 as anal_chec_att,
    fy_hqyxjc.ydwy00 as vagi_exam_vulv,
    fy_hqyxjc.ydyd00 as vagi_exam_vagi,
    fy_hqyxjc.ydgj00 as vagi_exam_cerv,
    fy_hqyxjc.ydzg00 as vagi_exam_uter,
    fy_hqyxjc.ydfj00 as vagi_exam_att,
    fy_hqyxjc.fxzqt0 as fml_second_sex_char_oth,
    fy_hqyxjc.fjcqm0 as fema_vagi_exam_sign,
    fy_hqyxjc.fysid0 as fml_second_sex_char_exam_dr_id,
    fy_hqyxjc.fysqm0 as femalessc_examdrsign,
    fy_hqyxjc.xt0000 as chest_xray,
    fy_hqyxjc.zam000 as transa,
    fy_hqyxjc.xcg000 as bloo_rout,
    fy_hqyxjc.ygbmky as hbsag_check_res_name,
    fy_hqyxjc.ncg000 as urin_rout,
    fy_hqyxjc.mdsx00 as syph_fitr,
    fy_hqyxjc.ydfmw0 as vagi_secre,
    fy_hqyxjc.lqj000 as gonoc,
    fy_hqyxjc.qttsjc as oth_sp_exam,
    fy_hqyxjc.jcjg00 as exam_rslt,
    fy_hqyxjc.jbzd00 as disediag,
    fy_hqyxjc.jbzdbm as disediag_codg,
    fy_hqyxjc.yxyj00 as medi_opnn,
    fy_hqyxjc.myjid0 as medi_opnn_exam_mal_id,
    fy_hqyxjc.myjqm0 as medi_opnn_exam_mal_sign,
    fy_hqyxjc.fyjid0 as medi_opnn_exam_fml_id,
    fy_hqyxjc.fyjqm0 as medi_opnn_exam_fml_sign,
    fy_hqyxjc.hqwszx as prenup_hc_cslt,
    fy_hqyxjc.zxzdjg as cslt_guid_rslt,
    fy_hqyxjc.mzdid0 as cslt_guid_rslt_exam_mal_id,
    fy_hqyxjc.mzdqm0 as cslt_guid_rslt_exam_mal_sign,
    fy_hqyxjc.fzdid0 as cslt_guid_rslt_exam_fml_id,
    fy_hqyxjc.fzdqm0 as cslt_guid_rslt_exam_fml_sign,
    fy_hqyxjc.zzbz00 as referral_flag,
    fy_hqyxjc.zzyyid as refl_hosp_id,
    fy_hqyxjc.zzyy00 as refl_hosp,
    fy_hqyxjc.zzrq00 as transfer_treatment_date,
    fy_hqyxjc.yyfzrq as appoint_flup_date,
    fy_hqyxjc.cjzmrq as issuance_date,
    fy_hqyxjc.zjysid as cnvl_main_test_dr_id,
    fy_hqyxjc.zjysqm as cnvl_main_test_dr_sign,
    fy_hqyxjc.yyid00 as hosp_id,
    fy_hqyxjc.bz0000 as remark,
    fy_hqyxjc.jlysid as prenupexam_drid,
    fy_hqyxjc.jlysqm as prenupexam_drsign,
    fy_hqyxjc.zmffrq as prenupexam_certissudate,
    fy_hqyxjc.status as rcd_of_stas,
    fy_hqyxjc.hivsc0 as hiv_anti_code,
    fy_hqyxjc.hivsc0 as hiv_anti_name,
    fy_hqyxjc.sw0000 as whtr_fore_rela,
    fy_hqyxjc.zjlx00 as cert_code,
    fy_hqyxjc.zjlx00 as cert_name,
    fy_hqyxjc.nl0000 as age,
    fy_hqyxjc.dyzt00 as prt_stas,
    fy_hqyxjc.zrs000 as son_psncnt,
    fy_hqyxjc.sznl00 as adequ_age,
    fy_hqyxjc.szyl00 as adequ_agem,
    fy_hqyxjc.xcgz00 as bloo_rout_val,
    fy_hqyxjc.zamz00 as transa_val,
    fy_hqyxjc.ncgndb as urinary_protein,
    fy_hqyxjc.ncgnt0 as urin_gluc,
    fy_hqyxjc.ncgyx0 as occu_bloo,
    fy_hqyxjc.ycqk00 as abnormal_rslt,
    fy_hqyxjc.ysff00 as folat_issu,
    fy_hqyxjc.ysjc00 as euge_dect,
    fy_hqyxjc.ncgbxq as whit_bloo_cells,
    fy_hqyxjc.zhxgrq as last_modi_date,
    fy_hqyxjc.xxlrry as bas_info_inpt_psn,
    fy_hqyxjc.bslrry as illhis_inpt_psn,
    fy_hqyxjc.tglrry as physexm_inpt_psn,
    fy_hqyxjc.cglrry as cnvl_exam_inpt_psn,
    fy_hqyxjc.sfsf00 as whtr_flup,
    fy_hqyxjc.sfzy00 as whtr_earl_preg,
    fy_hqyxjc.scbz00 as upld_flag,
    fy_hqyxjc.xhdba2 as hemog_a2,
    fy_hqyxjc.xhdbaf as hemog_a_+_f,
    fy_hqyxjc.xhdbba as hemog_bart_'_s,
    fy_hqyxjc.xhdbc0 as hemog_c,
    fy_hqyxjc.xhdbf0 as hemog_f,
    fy_hqyxjc.xhdbh0 as hemog_h,
    fy_hqyxjc.xhdbs0 as hemog_s,
    fy_hqyxjc.cjzcd0 as prom_thyr_horm_mes,
    fy_hqyxjc.sfysjc as pre_preg_euge_exam_untes,
    fy_hqyxjc.jxbigg as gian_cell_viru_igg_anti_mes,
    fy_hqyxjc.fzigg0 as rube_viru_igg_anti_mes,
    fy_hqyxjc.gxcigm as toxo_igm_anti_mes,
    fy_hqyxjc.dpesys as cnfm_diab_child_matn_his,
    fy_hqyxjc.dpjzs0 as diab_fmhis,
    fy_hqyxjc.dpmr00 as diab_face,
    fy_hqyxjc.dpsc00 as diab_scre,
    fy_hqyxjc.dpscs0 as confdmchild_stillbirthcnt,
    fy_hqyxjc.dpzrlc as confdmchild_natmisccnt,
    fy_hqyxjc.exigg0 as hsv-ii_igg_test,
    fy_hqyxjc.exigm0 as hsv-ii_igm_test,
    fy_hqyxjc.fybl00 as dyspla,
    fy_hqyxjc.hd0000 as jaun,
    fy_hqyxjc.hkxz00 as resd_natu_code,
    fy_hqyxjc.hkxz00 as resd_natu_name,
    fy_hqyxjc.jxbigm as gian_cell_viru_igm_anti_mes,
    fy_hqyxjc.qssz00 as whol_bod_edem,
    fy_hqyxjc.sxs000 as bld_his,
    fy_hqyxjc.syzh00 as matn_certno,
    fy_hqyxjc.whcb00 as face_atro_pall,
    fy_hqyxjc.ygbmkt as hepa_b_surf_anti,
    fy_hqyxjc.ygekt0 as hepa_b_e_hbeab,
    fy_hqyxjc.ygeky0 as hepa_b_e_hbeag,
    fy_hqyxjc.yghxkt as hepa_b_cor_anti,
    fy_hqyxjc.gxcigg as toxo_igg_anti_mes,
    fy_hqyxjc.fzigm0 as rube_viru_igm_anti_mes,
    fy_hqyxjc.mqsffy as are_you_curr_taki_medi,
    fy_hqyxjc.zsgym0 as whtr_inj_excs_vac,
    fy_hqyxjc.xybycs as currcontraceptive,
    fy_hqyxjc.bycxy0 as contr_cont_used_time,
    fy_hqyxjc.bytyn0 as years_discontinue,
    fy_hqyxjc.bytyy0 as months_discontinue,
    fy_hqyxjc.sfjsrl as whtr_into_meat_eat_eggs,
    fy_hqyxjc.sfyssc as whtr_anor_vege,
    fy_hqyxjc.sfsrsh as whtr_yes_consu_fles_hob,
    fy_hqyxjc.sfxy00 as whtr_smok,
    fy_hqyxjc.sfbdxy as whtr_exis_passi_smok,
    fy_hqyxjc.sfyj00 as whtr_drnk,
    fy_hqyxjc.sfzsdp as whtr_used_coca_etc._toxo_drug,
    fy_hqyxjc.sfkc00 as whtr_bad_brea,
    fy_hqyxjc.sfyxcx as whtr_gums_blee,
    fy_hqyxjc.sfjcys as envexposure,
    fy_hqyxjc.sfgdyl as whtr_feel_life_job_pre,
    fy_hqyxjc.sfgxjz as relationstension,
    fy_hqyxjc.sfjjyl as whtr_feel_econ_pre,
    fy_hqyxjc.sfhyzb as whtr_do_well_prg_prpa,
    fy_hqyxjc.xlysqt as other1,
    fy_hqyxjc.sg0000 as height,
    fy_hqyxjc.tz0000 as wt,
    fy_hqyxjc.tzzs00 as wt_ind,
    fy_hqyxjc.cggbza as liverkidney_gpt,
    fy_hqyxjc.cgjg00 as creat_umoll,
    fy_hqyxjc.cgxx00 as blotype_abo_code,
    fy_hqyxjc.cgxx00 as blotype_abo_name,
    fy_hqyxjc.cgxt00 as bloo_gluc,
    fy_hqyxjc.cgfkbc as gynecological_check,
    fy_hqyxjc.cgqt00 as cnvl_exam_oth,
    fy_hqyxjc.jclx00 as exam_type_code,
    fy_hqyxjc.jclx00 as exam_type_name,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_newborn_basic_info partition(dt)
select 
    concat(unified_uscid,nebo_guid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    newborninfo. as unified_uscid,
    newborninfo.nebo_guid as nebo_guid,
    newborninfo.nebo_birthno as nebo_birthno,
    newborninfo.nebo_childno as nebo_childno,
    newborninfo.nebo_mother as chil_mother,
    newborninfo.nebo_motherno as nebo_motherno,
    newborninfo.nebo_father as chil_father,
    newborninfo.nebo_fatherno as nebo_fatherno,
    newborninfo.nebo_tel as tel,
    newborninfo.nebo_mobile as mobile,
    newborninfo.nebo_mhbsag as nebo_mhbsag_code,
    newborninfo.nebo_mhbsag as nebo_mhbsag_name,
    newborninfo.nebo_fhbsag as nebo_fhbsag_code,
    newborninfo.nebo_fhbsag as nebo_fhbsag_name,
    newborninfo.nebo_address_habi_id as cur_addr_coty_code,
    newborninfo.nebo_address_habi_id as cur_addr_coty_name,
    newborninfo.nebo_address_depa_id as nebo_address_depa_id,
    newborninfo.nebo_depa_id as nebo_depa_id,
    newborninfo.nebo_address as nebo_address,
    newborninfo.nebo_name as nwb_name,
    newborninfo.nebo_sex as gender_code,
    newborninfo.nebo_sex as gender_name,
    newborninfo.nebo_birthtime as nebo_birthtime,
    newborninfo.nebo_weight as nebo_weight,
    newborninfo.nebo_nati_id as nebo_nati_code,
    newborninfo.nebo_nati_id as nebo_nati_name,
    newborninfo.nebo_habi_id as resd_coty_code,
    newborninfo.nebo_habi_id as resd_coty_name,
    newborninfo.nebo_habi_depa_id as resd_town_code,
    newborninfo.nebo_habi_depa_id as resd_town_name,
    newborninfo.nebo_habiaddress as resd_addr,
    newborninfo.nebo_mother_nati_id as mthr_naty_code,
    newborninfo.nebo_mother_nati_id as mthr_naty_name,
    newborninfo.nebo_childcode as nebo_childcode,
    newborninfo.nebo_mothercode as nebo_mothercode,
    newborninfo.nebo_fathercode as nebo_fathercode,
    newborninfo.nebo_parity as chil_fetus,
    newborninfo.nebo_weeks_delivery as nebo_weeks_delivery,
    newborninfo.nebo_birthstate as nebo_birthstate,
    newborninfo.nebo_defects as nebo_defects,
    newborninfo.nebo_defects_desc as nebo_defects_desc,
    newborninfo.nebo_screen as nebo_screen,
    newborninfo.nebo_apgar1 as nebo_apgar1,
    newborninfo.nebo_apgar2 as nebo_apgar2,
    newborninfo.nebo_apgar3 as nebo_apgar3,
    newborninfo.nebo_doctor as nebo_doctor,
    newborninfo.nebo_writedate as fillin_date,
    newborninfo.xtbh00 as sys_no,
    newborninfo.nebo_bcg as nebo_bcg,
    newborninfo.nebi_bcg_reason as nebi_bcg_reason,
    newborninfo.nebo_hepb as nebo_hepb,
    newborninfo.nebi_hepb_reason as nebi_hepb_reason,
    newborninfo.nebi_bcg_reasonother as nebi_hepb_reasonother,
    newborninfo.nebo_hepbig as nebo_hepbig,
    newborninfo.uploadtime as uploadtime,
    newborninfo.scxtbh as wom_hcare_manl_sys_no,
    newborninfo.cjrq00 as crte_date,
    newborninfo.cjz000 as crter,
    newborninfo.zhbjrq as last_edit_date,
    newborninfo.zhbjz0 as last_edit,
    newborninfo.hr99_99_113 as modify_datetime,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hwm_newborn_vaccin_record partition(dt)
select 
    concat(upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    newborninoculate.nebi_nebo_id as nebi_nebo_id,
    newborninoculate.nebi_bact_code as vacc_code,
    newborninoculate.nebi_bact_code as vacc_name,
    newborninoculate.nebi_time as vaccinate_inoc_time,
    newborninoculate.nebi_free as nebi_free,
    newborninoculate.nebi_date as nebi_date,
    newborninoculate.nebi_dose as nebi_dose,
    newborninoculate.nebi_batchno as vacc_batch,
    newborninoculate.nebi_corp_code as nebi_corp_code,
    newborninoculate.nebi_nurse as nebi_nurse,
    newborninoculate.nebi_inpl_id as nebi_inpl_id,
    newborninoculate.nebi_inoculateway as nebi_inoculateway,
    newborninoculate.nebi_editdate as nebi_editdate,
    newborninoculate.xtbhjz as vcnt_info_id,
    newborninoculate.yyid00 as hosp_id,
    newborninoculate.brid00 as patn_id,
    newborninoculate.uploadtime as uploadtime,
    newborninoculate.cjrq00 as crte_date,
    newborninoculate.cjz000 as crter,
    newborninoculate.zhbjrq as last_edit_date,
    newborninoculate.zhbjz0 as last_edit,
    newborninoculate.hr99_99_113 as modify_datetime,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hch_basic_info partition(dt)
select 
    concat(chilid,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as unified_uscid,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as birth_certificate_no,
    null as chil_mother,
    null as nwb_name,
    null as gender_code,
    null as gender_name,
    null as preg_matn_cnt,
    null as geso_val,
    null as chil_wt,
    null as brdy,
    null as birth_prov_code,
    null as birth_prov_name,
    null as birth_city_code,
    null as birth_city_name,
    null as birth_coty_code,
    null as birth_coty_name,
    null as birth_town_code,
    null as birth_town_name,
    null as birth_comm_code,
    null as birth_comm_name,
    null as birth_cotry_name,
    null as birth_addr_housnum,
    null as birth_addr,
    null as first_time,
    null as otp_no,
    null as no_consult,
    null as remark,
    null as family_hereditary_his,
    null as relation_patient_baby_code,
    null as relation_patient_baby_name,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as relation_inbreed_name,
    null as pregnancy_card_id,
    null as gravidity,
    null as matn_cnt,
    null as abn_fert_his,
    null as matn_edu_background_code,
    null as matn_edu_background_name,
    null as per_family_income_code,
    null as per_family_income_name,
    null as family_addr,
    null as family_cur_prov_code,
    null as family_cur_prov_name,
    null as family_cur_city_code,
    null as family_cur_city_name,
    null as family_cur_coty_code,
    null as family_cur_coty_name,
    null as family_cur_town_code,
    null as family_cur_town_name,
    null as family_cur_comm_code,
    null as family_cur_comm_name,
    null as family_cur_cotry_name,
    null as family_cur_addr_housnum,
    null as family_cur_postalcode,
    null as hometelephonenumber,
    null as resd_flag_code,
    null as resd_flag_name,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_etbjsc.klx00 as card_type_code,
    fy_etbjsc.klx00 as card_type_name,
    fy_etbjsc.kh0000 as card_no,
    fy_etbjsc.fqbh00 as fthr_no,
    fy_etbjsc.xsezz0 as nwb_asph,
    fy_etbjsc.tltemp as hear_scre_asoc_chara_sec,
    fy_etbjsc.jbtemp as dise_scre_asoc_chara_sec,
    fy_etbjsc.hkzt00 as resd_status_code,
    fy_etbjsc.hkzt00 as resd_status_name,
    fy_etbjsc.hksx00 as resd_natu_code,
    fy_etbjsc.hksx00 as resd_natu_name,
    fy_etbjsc.sjhm00 as mob,
    fy_etbjsc.sfgw00 as whtr_hrisk,
    fy_etbjsc.sfry00 as whtr_adm,
    fy_etbjsc.sfygym as whtr_hepa_b_vac_vcnt,
    fy_etbjsc.sfkjm0 as whtr_bcg_vac_vcnt,
    fy_etbjsc.sfzt00 as manl_stas_code,
    fy_etbjsc.sfzt00 as manl_stas_name,
    fy_etbjsc.jzzt00 as live_stas_code,
    fy_etbjsc.jzzt00 as live_stas_name,
    fy_etbjsc.zxdh00 as coun,
    fy_etbjsc.sfsw00 as whtr_die,
    fy_etbjsc.dabh00 as vill_for_emp_set_file_no,
    fy_etbjsc.wi_id as imp_data_of_old_sys_no,
    fy_etbjsc.appfy0 as appf_sco_1_m,
    fy_etbjsc.appfw0 as appf_sco_5_m,
    fy_etbjsc.sfqc00 as whtr_emig,
    fy_etbjsc.sftj00 as whtr_stt,
    fy_etbjsc.sfdq00 as whtr_sing_paren,
    fy_etbjsc.appfs0 as appf_sco_10_m,
    fy_etbjsc.sfzc00 as whtr_pret,
    fy_etbjsc.sfst00 as whtr_twin_preg,
    fy_etbjsc.jbscjyx as newbornscreen_negcode,
    fy_etbjsc.jbscjyx as newbornscreen_negname,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hch_newborn_visit_record partition(dt)
select 
    concat(nb_visit_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as nb_visit_id,
    null as unified_uscid,
    null as visit_no,
    null as children_visit_id,
    null as interview_org_code,
    null as together_mark,
    null as nb_hear_screen_case_code,
    null as nb_hear_screen_case_name,
    null as nb_dis_screen_proj_code,
    null as nb_dis_screen_proj_name,
    null as nebo_screen,
    null as feedway_type_code,
    null as feedway_type_name,
    null as daily_suck_no,
    null as daily_suck_times,
    null as diet_case_code,
    null as diet_case_name,
    null as sleep_case_code,
    null as sleep_case_name,
    null as nb_shit_code,
    null as nb_shit_name,
    null as nb_shit_dscr,
    null as baby_shit_times,
    null as vomit_mark,
    null as not_breastfeed_dscr,
    null as baby_tprt,
    null as baby_wt,
    null as baby_pulse,
    null as heart_rate,
    null as br,
    null as jaundice_part_mark,
    null as jaundice_part_code,
    null as jaundice_part_name,
    null as other_jaundice_part,
    null as bregma_horiz_diam,
    null as bregma_vert_diam,
    null as bregma_tension_code,
    null as bregma_tension_name,
    null as infant_spirit_type,
    null as child_complexion_code,
    null as child_complexion_name,
    null as child_head_hematoma_size,
    null as skin_check_res_code,
    null as skin_check_res_name,
    null as eye_appear_check_abnorm_mark,
    null as eye_appear_check_abnorm_dscr,
    null as ear_appear_check_abnorm_mark,
    null as ear_appear_check_abnorm_dscr,
    null as nose_abnorm_check_mark,
    null as nose_abnorm_check_res_dscr,
    null as oral_check_abnorm_mark,
    null as oral_check_abnorm_res_dscr,
    null as head_check_abnorm_mark,
    null as head_check_abnorm_res_dscr,
    null as heart_auscultate_abnorm_mark,
    null as heart_auscultate_abnorm_dscr,
    null as lung_auscultate_abnorm_mark,
    null as lung_auscultate_abnorm_dscr,
    null as chest_check_abnorm_mark,
    null as chest_check_abnorm_res_dscr,
    null as abdominal_check_abnorm_mark,
    null as abdominal_check_abnorm_dscr,
    null as hips_check_abnorm_mark,
    null as hips_check_abnorm_res_dscr,
    null as aedea_check_abnorm_mark,
    null as aedea_check_abnorm_dscr,
    null as cm_check_abnorm_mark,
    null as umbilical_check_res_code,
    null as umbilical_check_res_name,
    null as red_hip_flag,
    null as limbs_act_abnorm_mark,
    null as limbs_act_check_abnorm_rscr,
    null as baby_neck_mass_mark,
    null as neck_mass_check_res_dscr,
    null as anus_check_abnorm_mark,
    null as anus_check_abnorm_res_dscr,
    null as spine_check_abnorm_mark,
    null as spine_check_abnorm_res_dscr,
    null as referral_flag,
    null as accept_org_name,
    null as accept_depart_name,
    null as referral_reason,
    null as transfer_treatment_mark,
    null as referral_rec_dscr,
    null as nb_health_guide_type_code,
    null as nb_health_guide_type_name,
    null as check_sum_dscr,
    null as nb_dscr,
    null as chkpsn_name,
    null as exam_operator_code,
    null as reg_org_code,
    null as visits_org_name,
    null as appoint_nwb_sys_date,
    null as exam_emp,
    null as interview_date,
    null as next_visit_date,
    null as next_visit_place,
    null as special_situat_record,
    null as process_guide_ad,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_etfsjl.ssnnz0 as belo_to_age_gro_code,
    fy_etfsjl.ssnnz0 as belo_to_age_gro_name,
    fy_etfsjl.xmxl00 as exam_item_time_peri_code,
    fy_etfsjl.xmxl00 as exam_item_time_peri_name,
    fy_etfsjl.sgqk00 as hgt_info_code,
    fy_etfsjl.sgqk00 as hgt_info_name,
    fy_etfsjl.tzqk00 as wt_code,
    fy_etfsjl.tzqk00 as wt_name,
    fy_etfsjl.wi_id as imp_data_of_old_sys_no,
    fy_etfsjl.yscys as one_yea_old_teethi,
    fy_etfsjl.tzpjxx as wt_eval_detl_code,
    fy_etfsjl.tzpjxx as wt_eval_detl_name,
    fy_etfsjl.sgpjxx as hgt_eval_detl_code,
    fy_etfsjl.sgpjxx as hgt_eval_detl_name,
    fy_etfsjl.twpj00 as head_circ_eval,
    fy_etfsjl.zdjgyy as diag_rslt_nurt_code,
    fy_etfsjl.zdjgyy as diag_rslt_nurt_name,
    fy_etfsjl.zdjgbt as diag_rslt_tonsi_code,
    fy_etfsjl.zdjgbt as diag_rslt_tonsi_name,
    fy_etfsjl.zdjgqt as diag_rslt_oth_code,
    fy_etfsjl.zdjgqt as diag_rslt_oth_name,
    fy_etfsjl.zdjggw as diag_rslt_hrisk_code,
    fy_etfsjl.zdjggw as diag_rslt_hrisk_name,
    fy_etfsjl.zhpj00 as com_eval_code,
    fy_etfsjl.zhpj00 as com_eval_name,
    fy_etfsjl.yypj00 as nurt_eval_code,
    fy_etfsjl.yypj00 as nurt_eval_name,
    fy_etfsjl.yypjna as nurt_eval_na,
    fy_etfsjl.issc00 as measu_way,
    fy_etfsjl.sctzpj as wt_hgt_stdv_code,
    fy_etfsjl.sctzpj as wt_hgt_stdv_name,
    fy_etfsjl.yygs00 as over_nutr_eval_code,
    fy_etfsjl.yygs00 as over_nutr_eval_name,
    fy_etfsjl.gwidyy as malnu_hrisk_file_id,
    fy_etfsjl.gwidpx as anem_hrisk_file_id,
    fy_etfsjl.gwidgl as ricke_hrisk_file_id,
    fy_etfsjl.gwiddj as reg_hrisk_file_id,
    fy_etfsjl.gwpg00 as hrisk_eval,
    fy_etfsjl.czwt00 as hrisk_exis_prb,
    fy_etfsjl.gwzd00 as hrisk_guid,
    fy_etfsjl.gwzl00 as trt,
    fy_etfsjl.gwvdzl as vitd_trt,
    fy_etfsjl.czwtpx as exis_prb_anem,
    fy_etfsjl.gwzdpx as hrisk_guid_anem,
    fy_etfsjl.czwtgl as exis_prb_ricke,
    fy_etfsjl.gwzdgl as hrisk_guid_ricke,
    fy_etfsjl.yeyid0 as kinde_id,
    fy_etfsjl.bjid00 as clss_id,
    fy_etfsjl.ysty00 as tcm_diet_condi,
    fy_etfsjl.qjts00 as tcm_rise_fall_regu,
    fy_etfsjl.xwarff as pass_paren_pt_by_rubb_mtd,
    fy_etfsjl.zwqk00 as parenttcm_knowledge,
    fy_etfsjl.myd000 as tcmadvice_satisfaction,
    fy_etfsjl.gwidza as hrisk_chld_spec_cas_mgt_rcd_id,
    fy_etfsjl.pgjg00 as eval_rslt,
    fy_etfsjl.pgjgcl as dspo,
    fy_etfsjl.pgffxl as highriskchild_evalmethod,
    fy_etfsjl.pgjgxl as highriskchild_evalresult,
    fy_etfsjl.gwzdxl as highriskchild_guidance,
    fy_etfsjl.gwzdcl as highriskchild_handling,
    fy_etfsjl.gwidxl as mind_devt_abn_hrisk_file_id,
    fy_etfsjl.sfwc00 as whtr_out,
    fy_etfsjl.zd00za as highriskcase_guidance,
    fy_etfsjl.cl00za as highriskcase_handling,
    fy_etfsjl.scxtbhtemp as guid_data_used,
    fy_etfsjl.qdpx00 as mil_anem,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
insert overwrite table mid_hos_hch_high_risk_register partition(dt)
select 
    concat(unified_uscid,manl_sys_inte_code,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    fy_gweda. as chilid,
    fy_gweda. as unified_uscid,
    fy_gweda.scxtnm as manl_sys_inte_code,
    fy_gweda.rdate as casefil_time,
    fy_gweda.laszz as caseopen_childsymptoms,
    fy_gweda.bcategory as norm_labo_diffic,
    fy_gweda.feed as feed_his,
    fy_gweda.rage as adequ_age,
    fy_gweda.pdweeks as pret_week,
    fy_gweda.diagnoses as infi_child_diag,
    fy_gweda.lastrecord as infi_child_past_his,
    fy_gweda.height as bir_hgt,
    fy_gweda.weight as nebo_weight,
    fy_gweda.times as chil_fetus,
    fy_gweda.onetwo as sin_twin_preg_code,
    fy_gweda.onetwo as sin_twin_preg_name,
    fy_gweda.closingdate as end_case_date,
    fy_gweda.closingzz as caseclose_childsymptoms,
    fy_gweda.closinger as case_close_dor_name,
    fy_gweda.closingorg as case_close_org_name,
    fy_gweda.creatdate as build_date,
    fy_gweda.jddw00 as filed_emp,
    fy_gweda.zdjgbh as diag_rslt_no,
    fy_gweda.sfja00 as whtr_clscase,
    fy_gweda.wi_id as imp_data_of_old_sys_no,
    fy_gweda.cjrq00 as crte_date,
    fy_gweda.cjz000 as crter,
    fy_gweda.zhxgrq as last_modi_date,
    fy_gweda.zhxgz0 as last_modi_the,
    fy_gweda.dalx00 as file_type_code,
    fy_gweda.dalx00 as file_type_name,
    fy_gweda.zglx00 as trans_type_code,
    fy_gweda.zglx00 as trans_type_name,
    fy_gweda.css000 as bir_his_code,
    fy_gweda.css000 as bir_his_name,
    fy_gweda.lgywys as 6_indi_mon_in_feed_his_code,
    fy_gweda.lgywys as 6_indi_mon_in_feed_his_name,
    fy_gweda.mpxyz0 as moth_prg_anem_weeks,
    fy_gweda.mpxxdb as moth_prg_anem_hemoglobin,
    fy_gweda.mpxtj0 as moth_prg_anem_info,
    fy_gweda.mpxyw0 as moth_prg_anem_medication,
    fy_gweda.mpxjl0 as moth_prg_anem_dosage,
    fy_gweda.mpxlc0 as moth_prg_anem_cure,
    fy_gweda.swzhnl as child_ironfoodstartage,
    fy_gweda.yqbrvd as moth_prg_and_lacta_code,
    fy_gweda.yqbrvd as moth_prg_and_lacta_name,
    fy_gweda.fyvd00 as chld_taki_vitd,
    fy_gweda.fyvdyl as begn_taki_vitd_age_mon,
    fy_gweda.fyvdtl as begn_taki_vitd_age_day,
    fy_gweda.fyvdjl as begn_taki_vitd,
    fy_gweda.glbtz0 as rickets_sign_code,
    fy_gweda.glbtz0 as rickets_sign_name,
    fy_gweda.xg0000 as calc,
    fy_gweda.xl0000 as blo_phos,
    fy_gweda.xakp00 as blo_akp,
    fy_gweda.xsh000 as blo_25d,
    fy_gweda.xxjc00 as x_line_exam,
    fy_gweda.gwpg00 as hrisk_eval_code,
    fy_gweda.gwpg00 as hrisk_eval_name,
    fy_gweda.gwfd00 as hrisk_sec,
    fy_gweda.djrq00 as reg_date,
    fy_gweda.gwys00 as hrisk_fac_or_abnor,
    fy_gweda.zfjg00 as follo_up_rslt,
    fy_gweda.gwczwt as hrisk_spec_cas_mgt_exis_prb,
    fy_gweda.gwzdyj as hrisk_spec_cas_mgt_guid,
    fy_gweda.zzdwid as refl_emp_id,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from src_data. as t

) as tab

-- ================================================
