ALTER TABLE patient_basic_info ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE patient_clinic_activity ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE outp_register_record ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE outp_visit_record ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE inp_admission_record ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE inp_discharge_record ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE lis_request_form ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE lis_report_master ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE exam_request_form ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE exam_report_master ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_otpmedi ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_observmedi ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE cis_lh_summary ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_adm_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_inhosp_assess ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_first_dis_course ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_daily_dis_course ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_outhosp ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_death_record ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_consult_info ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_sympt_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_opr_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_opr_informed ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_anst_informed ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_oth_informed ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_doc_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE pacu_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE case_base_info ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE emr_referral ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE infect_rpt ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE infec_rec ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE medmanage_ae_saes ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE medmanage_ae_maes ALTER COLUMN patient_name VARCHAR(255);
ALTER TABLE medmanage_ae_daes ALTER COLUMN patient_name VARCHAR(255);