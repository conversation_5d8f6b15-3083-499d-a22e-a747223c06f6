-- ORACLE 数据库字段长度变更脚本
-- 生成日期: 2025-04-01 09:23:04


-- 2、福建省三医一张网_数据采集标准规范 门急诊业务分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE outp_charge_detail MODIFY quantity NUMBER(16);
ALTER TABLE outp_order_detail MODIFY medc_days NUMBER(16);
ALTER TABLE outp_order_detail MODIFY quantity NUMBER(16);
ALTER TABLE outp_visit_record MODIFY age_year NUMBER(16);
ALTER TABLE outp_visit_record MODIFY age_month NUMBER(16);
ALTER TABLE outp_visit_record MODIFY age_day NUMBER(16);
ALTER TABLE outp_visit_record MODIFY geso_val NUMBER(16);


-- 3、福建省三医一张网_数据采集标准规范 住院业务分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE inp_admission_record MODIFY age_year NUMBER(16);
ALTER TABLE inp_admission_record MODIFY age_month NUMBER(16);
ALTER TABLE inp_admission_record MODIFY age_day NUMBER(16);
ALTER TABLE inp_charge_detail MODIFY quantity NUMBER(16);
ALTER TABLE inp_discharge_record MODIFY age_year NUMBER(16);
ALTER TABLE inp_discharge_record MODIFY age_month NUMBER(16);
ALTER TABLE inp_discharge_record MODIFY age_day NUMBER(16);
ALTER TABLE inp_order_detail MODIFY medc_days NUMBER(16);
ALTER TABLE inp_settle_charge_detail MODIFY quantity NUMBER(16);


-- 4、福建省三医一张网_数据采集标准规范 医技业务分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE exam_report_master MODIFY age_year NUMBER(16);
ALTER TABLE exam_report_master MODIFY age_month NUMBER(16);
ALTER TABLE exam_report_master MODIFY age_day NUMBER(16);
ALTER TABLE exam_report_master MODIFY quantity NUMBER(16);
ALTER TABLE exam_report_master MODIFY rpt_print_times NUMBER(16);
ALTER TABLE exam_request_form MODIFY age_year NUMBER(16);
ALTER TABLE exam_request_form MODIFY age_month NUMBER(16);
ALTER TABLE exam_request_form MODIFY age_day NUMBER(16);
ALTER TABLE exam_request_form MODIFY quantity NUMBER(16);
ALTER TABLE lis_report_master MODIFY age_year NUMBER(16);
ALTER TABLE lis_report_master MODIFY age_month NUMBER(16);
ALTER TABLE lis_report_master MODIFY age_day NUMBER(16);
ALTER TABLE lis_report_master MODIFY print_times NUMBER(16);
ALTER TABLE lis_request_form MODIFY age_year NUMBER(16);
ALTER TABLE lis_request_form MODIFY age_month NUMBER(16);
ALTER TABLE lis_request_form MODIFY age_day NUMBER(16);


-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE cis_lh_summary MODIFY age_year NUMBER(16);
ALTER TABLE cis_lh_summary MODIFY age_month NUMBER(16);
ALTER TABLE cis_lh_summary MODIFY age_day NUMBER(16);
ALTER TABLE cis_lh_summary MODIFY patn_ipt_cnt NUMBER(16);
ALTER TABLE cis_lh_summary MODIFY ipt_days NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY age_year NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY age_month NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY age_day NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY phys_exam_pule NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY phys_exam_vent_frqu NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY phys_exam_sbp NUMBER(16);
ALTER TABLE emr_adm_rec MODIFY phys_exam_dbp NUMBER(16);
ALTER TABLE emr_aft_anst MODIFY age_year NUMBER(16);
ALTER TABLE emr_aft_anst MODIFY age_month NUMBER(16);
ALTER TABLE emr_aft_anst MODIFY age_day NUMBER(16);
ALTER TABLE emr_anst_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_anst_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_anst_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_blood_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_blood_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_blood_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_consult_info MODIFY age_year NUMBER(16);
ALTER TABLE emr_consult_info MODIFY age_month NUMBER(16);
ALTER TABLE emr_consult_info MODIFY age_day NUMBER(16);
ALTER TABLE emr_daily_dis_course MODIFY age_year NUMBER(16);
ALTER TABLE emr_daily_dis_course MODIFY age_month NUMBER(16);
ALTER TABLE emr_daily_dis_course MODIFY age_day NUMBER(16);
ALTER TABLE emr_death_case_discu MODIFY age_year NUMBER(16);
ALTER TABLE emr_death_case_discu MODIFY age_month NUMBER(16);
ALTER TABLE emr_death_case_discu MODIFY age_day NUMBER(16);
ALTER TABLE emr_death_record MODIFY age_year NUMBER(16);
ALTER TABLE emr_death_record MODIFY age_month NUMBER(16);
ALTER TABLE emr_death_record MODIFY age_day NUMBER(16);
ALTER TABLE emr_death_record MODIFY act_ipt_days NUMBER(16);
ALTER TABLE emr_doc_detail MODIFY file_display_sort NUMBER(16);
ALTER TABLE emr_first_dis_course MODIFY age_year NUMBER(16);
ALTER TABLE emr_first_dis_course MODIFY age_month NUMBER(16);
ALTER TABLE emr_first_dis_course MODIFY age_day NUMBER(16);
ALTER TABLE emr_first_postop_course MODIFY age_year NUMBER(16);
ALTER TABLE emr_first_postop_course MODIFY age_month NUMBER(16);
ALTER TABLE emr_first_postop_course MODIFY age_day NUMBER(16);
ALTER TABLE emr_hard_case_discu MODIFY age_year NUMBER(16);
ALTER TABLE emr_hard_case_discu MODIFY age_month NUMBER(16);
ALTER TABLE emr_hard_case_discu MODIFY age_day NUMBER(16);
ALTER TABLE emr_heavy_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_heavy_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_heavy_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY age_year NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY age_month NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY age_day NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY sbp NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY dbp NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY vent_frqu NUMBER(16);
ALTER TABLE emr_heavy_nurse MODIFY heart_rate NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY age_year NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY age_month NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY age_day NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY sbp NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY dbp NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY pule NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY vent_frqu NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY apgr NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY smok_day NUMBER(16);
ALTER TABLE emr_inhosp_assess MODIFY drnk_day NUMBER(16);
ALTER TABLE emr_inhosp_die_in24h MODIFY age_year NUMBER(16);
ALTER TABLE emr_inhosp_die_in24h MODIFY age_month NUMBER(16);
ALTER TABLE emr_inhosp_die_in24h MODIFY age_day NUMBER(16);
ALTER TABLE emr_inout_rec MODIFY age_year NUMBER(16);
ALTER TABLE emr_inout_rec MODIFY age_month NUMBER(16);
ALTER TABLE emr_inout_rec MODIFY age_day NUMBER(16);
ALTER TABLE emr_inout_rec_in24h MODIFY age_year NUMBER(16);
ALTER TABLE emr_inout_rec_in24h MODIFY age_month NUMBER(16);
ALTER TABLE emr_inout_rec_in24h MODIFY age_day NUMBER(16);
ALTER TABLE emr_labor MODIFY sbp NUMBER(16);
ALTER TABLE emr_labor MODIFY dbp NUMBER(16);
ALTER TABLE emr_labor MODIFY pule NUMBER(16);
ALTER TABLE emr_labor MODIFY vent_frqu NUMBER(16);
ALTER TABLE emr_labor MODIFY fhr NUMBER(16);
ALTER TABLE emr_nurse MODIFY age_year NUMBER(16);
ALTER TABLE emr_nurse MODIFY age_month NUMBER(16);
ALTER TABLE emr_nurse MODIFY age_day NUMBER(16);
ALTER TABLE emr_nurse MODIFY sbp NUMBER(16);
ALTER TABLE emr_nurse MODIFY dbp NUMBER(16);
ALTER TABLE emr_nurse MODIFY pule NUMBER(16);
ALTER TABLE emr_nurse MODIFY vent_frqu NUMBER(16);
ALTER TABLE emr_nurse_plan MODIFY age_year NUMBER(16);
ALTER TABLE emr_nurse_plan MODIFY age_month NUMBER(16);
ALTER TABLE emr_nurse_plan MODIFY age_day NUMBER(16);
ALTER TABLE emr_observmedi MODIFY age_year NUMBER(16);
ALTER TABLE emr_observmedi MODIFY age_month NUMBER(16);
ALTER TABLE emr_observmedi MODIFY age_day NUMBER(16);
ALTER TABLE emr_observoprn MODIFY oprn_oprt_times NUMBER(16);
ALTER TABLE emr_opr_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_opr_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_opr_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_opr_nurse MODIFY age_year NUMBER(16);
ALTER TABLE emr_opr_nurse MODIFY age_month NUMBER(16);
ALTER TABLE emr_opr_nurse MODIFY age_day NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY age_year NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY age_month NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY age_day NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY bleeding_num NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY bld_amt NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY transfuse_num NUMBER(16);
ALTER TABLE emr_opr_rec MODIFY urine_amt NUMBER(16);
ALTER TABLE emr_oth_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_oth_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_oth_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_otpmedi MODIFY age_year NUMBER(16);
ALTER TABLE emr_otpmedi MODIFY age_month NUMBER(16);
ALTER TABLE emr_otpmedi MODIFY age_day NUMBER(16);
ALTER TABLE emr_outhosp MODIFY age_year NUMBER(16);
ALTER TABLE emr_outhosp MODIFY age_month NUMBER(16);
ALTER TABLE emr_outhosp MODIFY age_day NUMBER(16);
ALTER TABLE emr_outhosp_assess MODIFY age_year NUMBER(16);
ALTER TABLE emr_outhosp_assess MODIFY age_month NUMBER(16);
ALTER TABLE emr_outhosp_assess MODIFY age_day NUMBER(16);
ALTER TABLE emr_periodic_sum MODIFY age_year NUMBER(16);
ALTER TABLE emr_periodic_sum MODIFY age_month NUMBER(16);
ALTER TABLE emr_periodic_sum MODIFY age_day NUMBER(16);
ALTER TABLE emr_pre_anst MODIFY age_year NUMBER(16);
ALTER TABLE emr_pre_anst MODIFY age_month NUMBER(16);
ALTER TABLE emr_pre_anst MODIFY age_day NUMBER(16);
ALTER TABLE emr_predelivery MODIFY age_year NUMBER(16);
ALTER TABLE emr_predelivery MODIFY prg_cnt NUMBER(16);
ALTER TABLE emr_predelivery MODIFY matn_cnt NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY age_year NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY amn_fluid_amount NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY umb_around_circle NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY umb_lenghth NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY umb_turn_circle NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY bleeding_num NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY bld_amt NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY transfuse_num NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY oxygen_supply_time NUMBER(16);
ALTER TABLE emr_pregnancy MODIFY proc_whole_min NUMBER(16);
ALTER TABLE emr_pregnancy_observ MODIFY postpar_exam_time NUMBER(16);
ALTER TABLE emr_pregnancy_observ MODIFY postpar_pulse_rate NUMBER(16);
ALTER TABLE emr_pregnancy_observ MODIFY postpar_heart_rate NUMBER(16);
ALTER TABLE emr_pregnancy_observ MODIFY postoprn_sbp NUMBER(16);
ALTER TABLE emr_pregnancy_observ MODIFY postoprn_dbp NUMBER(16);
ALTER TABLE emr_preop_discu MODIFY age_year NUMBER(16);
ALTER TABLE emr_preop_discu MODIFY age_month NUMBER(16);
ALTER TABLE emr_preop_discu MODIFY age_day NUMBER(16);
ALTER TABLE emr_preop_sum MODIFY age_year NUMBER(16);
ALTER TABLE emr_preop_sum MODIFY age_month NUMBER(16);
ALTER TABLE emr_preop_sum MODIFY age_day NUMBER(16);
ALTER TABLE emr_rescue MODIFY age_year NUMBER(16);
ALTER TABLE emr_rescue MODIFY age_month NUMBER(16);
ALTER TABLE emr_rescue MODIFY age_day NUMBER(16);
ALTER TABLE emr_rescue MODIFY oprn_oprt_times NUMBER(16);
ALTER TABLE emr_shift_change MODIFY age_year NUMBER(16);
ALTER TABLE emr_shift_change MODIFY age_month NUMBER(16);
ALTER TABLE emr_shift_change MODIFY age_day NUMBER(16);
ALTER TABLE emr_spe_informed MODIFY age_year NUMBER(16);
ALTER TABLE emr_spe_informed MODIFY age_month NUMBER(16);
ALTER TABLE emr_spe_informed MODIFY age_day NUMBER(16);
ALTER TABLE emr_super_doct_check MODIFY age_year NUMBER(16);
ALTER TABLE emr_super_doct_check MODIFY age_month NUMBER(16);
ALTER TABLE emr_super_doct_check MODIFY age_day NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY age_year NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY age_month NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY age_day NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY sbp NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY dbp NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY pule NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY vent_frqu NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY act_ipt_days NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY heart_rate NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY afpn_days NUMBER(16);
ALTER TABLE emr_sympt_rec MODIFY pat_heart_rate NUMBER(16);
ALTER TABLE emr_trans_depart MODIFY age_year NUMBER(16);
ALTER TABLE emr_trans_depart MODIFY age_month NUMBER(16);
ALTER TABLE emr_trans_depart MODIFY age_day NUMBER(16);
ALTER TABLE emr_treat_rec MODIFY age_year NUMBER(16);
ALTER TABLE emr_treat_rec MODIFY age_month NUMBER(16);
ALTER TABLE emr_treat_rec MODIFY age_day NUMBER(16);
ALTER TABLE emr_treat_rec MODIFY oprn_oprt_times NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY age_year NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY prg_cnt NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY matn_cnt NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY pre_amn_fluid_num NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY first_labor_min NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY second_labor_min NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY third_labor_min NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY total_reproduct_min NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY amn_fluid_amount NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY umb_around_circle NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY umb_lenghth NUMBER(16);
ALTER TABLE emr_vagina_deliver MODIFY perine_suture_num NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_exam_time NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_sbp NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_dbp NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_pulse_rate NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_heart_rate NUMBER(16);
ALTER TABLE emr_vagina_deliver_observ MODIFY postpar_blood_num NUMBER(16);


-- 6、福建省三医一张网_数据采集标准规范 手术麻醉分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE anst_drug MODIFY speed NUMBER(16);
ALTER TABLE anst_rec MODIFY age_year NUMBER(16);
ALTER TABLE anst_rec MODIFY age_month NUMBER(16);
ALTER TABLE anst_rec MODIFY age_day NUMBER(16);
ALTER TABLE anst_rec MODIFY bleeding_num NUMBER(16);
ALTER TABLE anst_rec MODIFY bld_amt NUMBER(16);
ALTER TABLE operation_detail MODIFY age_year NUMBER(16);
ALTER TABLE operation_detail MODIFY age_month NUMBER(16);
ALTER TABLE operation_detail MODIFY age_day NUMBER(16);
ALTER TABLE operation_detail MODIFY bleeding_num NUMBER(16);
ALTER TABLE operation_detail MODIFY bld_amt NUMBER(16);
ALTER TABLE operation_detail MODIFY transfuse_num NUMBER(16);
ALTER TABLE operation_detail MODIFY urine_amt NUMBER(16);
ALTER TABLE operation_detail MODIFY tumor_part_cnt NUMBER(16);
ALTER TABLE operation_detail MODIFY anst_con_time NUMBER(16);
ALTER TABLE operation_detail MODIFY abtl_medn_days NUMBER(16);
ALTER TABLE sign_monitor MODIFY sbp NUMBER(16);
ALTER TABLE sign_monitor MODIFY dbp NUMBER(16);
ALTER TABLE sign_monitor MODIFY pule NUMBER(16);
ALTER TABLE sign_monitor MODIFY vent_frqu NUMBER(16);
ALTER TABLE sign_monitor MODIFY heart_rate NUMBER(16);


-- 7、福建省三医一张网_数据采集标准规范 输血管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE blood_apply MODIFY age_year NUMBER(16);
ALTER TABLE blood_apply MODIFY age_month NUMBER(16);
ALTER TABLE blood_apply MODIFY age_day NUMBER(16);
ALTER TABLE blood_infusion MODIFY age_year NUMBER(16);
ALTER TABLE blood_infusion MODIFY age_month NUMBER(16);
ALTER TABLE blood_infusion MODIFY age_day NUMBER(16);
ALTER TABLE blood_infusion MODIFY bld_amt NUMBER(16);
ALTER TABLE blood_infusion MODIFY transfuse_times NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY age_year NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY age_month NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY age_day NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY beginspeed NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY endspeed NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY quarterbldpresslow NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY quarterbldpresshigh NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY quarterpulse NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY quarterbreathing NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY beginbldpresslow NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY beginbldpresshigh NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY beginpulse NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY beginbreathing NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY endbldpresslow NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY endbldpresshigh NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY endpulse NUMBER(16);
ALTER TABLE blood_infusion_del MODIFY endbreathing NUMBER(16);
ALTER TABLE blood_inurse MODIFY age_year NUMBER(16);
ALTER TABLE blood_inurse MODIFY age_month NUMBER(16);
ALTER TABLE blood_inurse MODIFY age_day NUMBER(16);
ALTER TABLE blood_inurse MODIFY transvolume NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY speed NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY bldpresslow NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY bldpresshigh NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY pulse NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY breathing NUMBER(16);
ALTER TABLE blood_inurse_inspection MODIFY oflow NUMBER(16);
ALTER TABLE blood_reaction MODIFY age_year NUMBER(16);
ALTER TABLE blood_reaction MODIFY age_month NUMBER(16);
ALTER TABLE blood_reaction MODIFY age_day NUMBER(16);


-- 8、福建省三医一张网_数据采集标准规范 体检管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE tj_charge MODIFY age_year NUMBER(16);
ALTER TABLE tj_charge MODIFY age_month NUMBER(16);
ALTER TABLE tj_charge MODIFY age_day NUMBER(16);
ALTER TABLE tj_charge MODIFY comb_times NUMBER(16);
ALTER TABLE tj_comb_reg MODIFY age_year NUMBER(16);
ALTER TABLE tj_comb_reg MODIFY age_month NUMBER(16);
ALTER TABLE tj_comb_reg MODIFY age_day NUMBER(16);
ALTER TABLE tj_comb_reg MODIFY comb_times NUMBER(16);
ALTER TABLE tj_dept_rpt MODIFY dtl_index_cnt NUMBER(16);
ALTER TABLE tj_ovall_eva MODIFY age NUMBER(16);
ALTER TABLE tj_ovall_eva MODIFY rec_sum NUMBER(16);


-- 9、福建省三医一张网_数据采集标准规范 病案管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE case_base_info MODIFY age_year NUMBER(16);
ALTER TABLE case_base_info MODIFY age_month NUMBER(16);
ALTER TABLE case_base_info MODIFY age_day NUMBER(16);
ALTER TABLE case_base_info MODIFY patn_ipt_cnt NUMBER(16);
ALTER TABLE case_base_info MODIFY postpar_blood_num NUMBER(16);
ALTER TABLE case_base_info MODIFY act_ipt_days NUMBER(16);
ALTER TABLE case_base_info MODIFY resc_cnt NUMBER(16);
ALTER TABLE case_base_info MODIFY resc_succ_cnt NUMBER(16);
ALTER TABLE case_base_info MODIFY red_cell_bld_amt NUMBER(16);
ALTER TABLE case_base_info MODIFY platelet_bld_amt NUMBER(16);
ALTER TABLE case_base_info MODIFY plasma_bld_amt NUMBER(16);
ALTER TABLE case_base_info MODIFY whole_bld_amt NUMBER(16);
ALTER TABLE case_base_info MODIFY oth_bld_amt NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_bfadm_coma_d NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_bfadm_coma_h NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_bfadm_coma_m NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_afadm_coma_d NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_afadm_coma_h NUMBER(16);
ALTER TABLE case_base_info MODIFY brn_damg_afadm_coma_m NUMBER(16);
ALTER TABLE case_base_info MODIFY vent_used_dura NUMBER(16);


-- 10、福建省三医一张网_数据采集标准规范 转诊（院）服务分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE emr_referral MODIFY age_year NUMBER(16);
ALTER TABLE emr_referral MODIFY age_month NUMBER(16);
ALTER TABLE emr_referral MODIFY age_hour NUMBER(16);


-- 11、福建省三医一张网_数据采集标准规范 医疗管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE infect_rpt MODIFY age NUMBER(16);
ALTER TABLE infect_rpt MODIFY inject_count NUMBER(16);
ALTER TABLE infect_rpt MODIFY nonweb_count NUMBER(16);
ALTER TABLE infect_rpt MODIFY sm_count NUMBER(16);
ALTER TABLE medmanage_ae_daes MODIFY patient_age_year NUMBER(16);
ALTER TABLE medmanage_ae_daes MODIFY patient_age_month NUMBER(16);
ALTER TABLE medmanage_ae_maes MODIFY patient_age_year NUMBER(16);
ALTER TABLE medmanage_ae_maes MODIFY patient_age_month NUMBER(16);


-- 12、福建省三医一张网_数据采集标准规范 药品管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE drug_account_record MODIFY quantity NUMBER(16);
ALTER TABLE drug_buy_import MODIFY quantity NUMBER(16);
ALTER TABLE drug_export_detail MODIFY quantity NUMBER(16);
ALTER TABLE drug_import_detail MODIFY quantity NUMBER(16);


-- 13、福建省三医一张网_数据采集标准规范 医用耗材管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE matl_consume_dtl MODIFY sell_sale_back_cnt NUMBER(16);
ALTER TABLE matl_consume_dtl MODIFY cell_sale_retn_cnt NUMBER(16);
ALTER TABLE matl_in_pack_dtl MODIFY sell_pack_cnt NUMBER(16);
ALTER TABLE matl_in_pack_dtl MODIFY cell_pack_cnt NUMBER(16);
ALTER TABLE matl_use_record MODIFY age_year NUMBER(16);
ALTER TABLE matl_use_record MODIFY age_month NUMBER(16);
ALTER TABLE matl_use_record MODIFY age_day NUMBER(16);
ALTER TABLE matl_use_record MODIFY quantity NUMBER(16);


-- 14、福建省三医一张网_数据采集标准规范 物资管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE material_export_detail MODIFY quantity NUMBER(16);
ALTER TABLE material_import_detail MODIFY quantity NUMBER(16);


-- 15、福建省三医一张网_数据采集标准规范 医院数据统计分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE stat_hosp_report_d MODIFY total_visit_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY fd_numb NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY ed_numb NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY abs_pat_numbo NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY hot_opt_numb NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY opt_drug_pre NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY opt_pre_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY exam_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY lis_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY exam_rpt_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY Lis_rpt_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY intel_visit_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY intel_consul_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY intel_access_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY intel_pres_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY ope_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY level_ope_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY in_pat_numb NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY out_pat_numb NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY prepare_bed_num NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY l_h_br NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY fact_open_bed NUMBER(16);
ALTER TABLE stat_hosp_report_d MODIFY outp_stay_day NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY ope_num NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY level_ope_num NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY in_pat_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY out_pat_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY outp_stay_day NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY avai_bed NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY use_bed NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY add_bed NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY empty_bed NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY int_care_bed NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY pat_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY int_care_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY abs_pat_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY death_numb NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY employee_doctors NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY doctor_director NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY doctor_vice_director NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY doctor_in_charge NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY doctor_common NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY employee_nurses NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY nurse_director NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY nurse_vice_director NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY nurse_in_charge NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY nurse_advanced NUMBER(16);
ALTER TABLE stat_inp_report_d MODIFY nurse_common NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY prepare_bed_num NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY l_h_br NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY outp_stay_day NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY a_h_pn NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY o_d_pn NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY t_d_pn NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY l_h_tp NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY now_pn NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY actual_bed_num NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY p_o_bed NUMBER(16);
ALTER TABLE stat_inp_workload_report_m MODIFY p_oc_bed NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY lis_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY otp_lis_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY ipt_lis_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY oth_lis_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY rpt_total_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY otp_rpt_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY ipt_rpt_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY oth_rpt_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY chemistry_rpt_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY bacteria_rpt_num NUMBER(16);
ALTER TABLE stat_lis_report_d MODIFY drug_allergy_rpt_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY fd_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY ed_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY abs_pat_numbo NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY hot_opt_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY opt_drug_pre NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY opt_pre_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY intel_visit_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY intel_consul_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY intel_access_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY intel_pres_num NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY expd_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY ott_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY gcd_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY opt_oprn_numb NUMBER(16);
ALTER TABLE stat_outp_workload_report_d MODIFY ghe_numb NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY exam_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY rpt_total_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY otp_rpt_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY ipt_rpt_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY oth_rpt_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY otp_exam_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY ipt_exam_num NUMBER(16);
ALTER TABLE stat_ris_report_d MODIFY oth_exam_num NUMBER(16);


-- 16、福建省三医一张网_数据采集标准规范 设备管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE equip_benefit_record MODIFY exam_num NUMBER(16);
ALTER TABLE equip_card_info MODIFY useful_life NUMBER(16);
ALTER TABLE equip_depr_record MODIFY depr_period NUMBER(16);
ALTER TABLE equip_discarded_record MODIFY used_life NUMBER(16);
ALTER TABLE equip_repair_record MODIFY repair_staff_time NUMBER(16);


-- 17、福建省三医一张网_数据采集标准规范 机构基础字典分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE dic_dept MODIFY prepare_bed_num NUMBER(16);
ALTER TABLE dic_hospital MODIFY infocenter_staff_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY rated_bed_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY staff_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY regular_payroll_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY doctor_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY nurse_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY cure_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY drug_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY valuable_equip_quantity NUMBER(16);
ALTER TABLE dic_hospital MODIFY business_area NUMBER(16);
ALTER TABLE dic_hospital MODIFY building_area NUMBER(16);
ALTER TABLE dic_staff MODIFY top_edu_variant NUMBER(16);


-- 18、福建省三医一张网_数据采集标准规范 人力资源管理分册(征求意见稿).docx 相关表字段长度变更
ALTER TABLE hum_psn_edu_info MODIFY variant NUMBER(16);
ALTER TABLE hum_psn_heigth_award MODIFY member_sort NUMBER(16);
ALTER TABLE hum_psn_info MODIFY work_country_timer NUMBER(16);


-- 19、福建省三医一张网_数据采集标准规范 专病库阿尔默兹(征求意见稿).docx 相关表字段长度变更
ALTER TABLE sdd_ad_ad8 MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_adl MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_cdr MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_dprd MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_faq MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_ffq MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_mmse MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_moca MODIFY age NUMBER(16);
ALTER TABLE sdd_ad_scale MODIFY age NUMBER(16);

