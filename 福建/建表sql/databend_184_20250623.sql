-- 生成时间：2025-06-23 15:22:20
DROP TABLE IF EXISTS `tj_charge`;

CREATE TABLE IF NOT EXISTS `tj_charge` (
    `rid` STRING COMMENT '数据唯一记录号',
    `org_name` STRING COMMENT '医疗机构名称',
    `uscid` STRING COMMENT '医疗机构统一社会信用代码',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `sys_prdr_code` STRING COMMENT '系统建设厂商代码',
    `sys_prdr_name` STRING COMMENT '系统建设厂商名称',
    `fee_date` DATE COMMENT '收退费日期',
    `fee_no` STRING COMMENT '收退费编号',
    `refd_mark` STRING COMMENT '退费标志',
    `comb_type` STRING COMMENT '体检类别代码',
    `tj_comb_sno` STRING COMMENT '体检流水号',
    `emp_code` STRING COMMENT '体检单位编码',
    `mp_name` STRING COMMENT '体检单位名称',
    `fee_time` TIMESTAMP COMMENT '收退费日期时间',
    `medfee_sumamt` STRING COMMENT '收退费总额',
    `payment_amt` STRING COMMENT '实收金额',
    `offer_amt` STRING COMMENT '优惠金额',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `invo_no` STRING COMMENT '发票号',
    `invono_print_time` TIMESTAMP COMMENT '发票打印日期时间',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `data_clct_prdr_name` STRING COMMENT '数据改造厂商名称',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间',
    `dty_flag` STRING COMMENT '脏数据标志',
    `dty_descr` STRING COMMENT '脏数据描述'
) COMMENT='体检收费表';

