insert overwrite table ods_hcs_phd_health_basic partition(dt)
select 
    concat(files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t.df_id as patientid,
    t.sscardno as card_no,
    '0' as card_type_code,
    '社保卡' as card_type_name,
    t.adress_pro as addr_city,
    t.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t.sex as gender_code,
    upper(name) as gender_name,
    t.name as full_name,
    '待定' as patient_souc_code,
    '待定' as patient_souc_name,
    t.mstatus as mrg_stas_code,
    d_mrg_stas_name.dic_val_name as mrg_stas_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    null as birth_addr,
    t.folk as nation_code,
    d_nation_name.dic_val_name as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as mobile,
    t.telphone as tel,
    null as empr_poscode,
    t.workplace as empr_name,
    null as empr_addr,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    t.adress_pro as curr_addr_prov_name,
    null as curr_addr_city_code,
    t.adress_city as curr_addr_city_name,
    null as curr_addr_coty_code,
    t.adress_county as curr_addr_coty_name,
    null as curr_addr_town_code,
    t.adress_rural as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    t.adress_village as curr_addr_cotry_name,
    t.adrss_hnumber as curr_addr_housnum,
    null as residential_addr,
    null as resd_addr_code,
    null as resd_addr_name,
    null as resd_addr_prov_code,
    t.hkdshe as resd_addr_prov_name,
    null as resd_addr_coty_code,
    t.hkdxia as resd_addr_coty_name,
    null as resd_addr_subd_code,
    t.hkdzhe as resd_addr_subd_name,
    null as resd_addr_comm_code,
    t. as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    t. as resd_addr_housnum,
    null as resd_addr,
    null as resd_addr_poscode,
    null as coner_name,
    null as relation_code,
    null as relation_name,
    null as coner_addr,
    null as coner_org_name,
    null as coner_poscode,
    null as coner_tel,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from zl3500000000000004_syyzw.t_dwellerfile as t
left join data_dic_a as d_gender_name on d_gender_name.dic_type_code='GENDER_CODE' and d_gender_name.dic_val_code=t.sex
left join data_dic_a as d_mrg_stas_name on d_mrg_stas_name.dic_type_code='MRG_STAS_CODE' and d_mrg_stas_name.dic_val_code=t.mstatus
left join data_dic_a as d_nation_name on d_nation_name.dic_type_code='NATION_CODE' and d_nation_name.dic_val_code=t.folk
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_home partition(dt)
select 
    concat(files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t. as patientid,
    t. as full_name,
    t. as gender_code,
    d_gender_name.dic_val_name as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    t. as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t. as edu_background_code,
    d_edu_background_name.dic_val_name as edu_background_name,
    null as occup_code,
    null as occup_name,
    null as emp_status_code,
    null as emp_status_name,
    t. as per_addr_code,
    d_per_addr_name.dic_val_name as per_addr_name,
    t. as nation_code,
    d_nation_name.dic_val_name as nation_name,
    t. as blotype_abo_code,
    d_blotype_abo_name.dic_val_name as blotype_abo_name,
    t. as blotype_rh_code,
    d_blotype_rh_name.dic_val_name as blotype_rh_name,
    t. as mrg_stas_code,
    d_mrg_stas_name.dic_val_name as mrg_stas_name,
    null as medfee_paymtd_code,
    null as medfee_paymtd_name,
    null as expose_his_code,
    t. as expose_his_name,
    case when ycbs00 is null then '0' else '1' end as hereditary_mark,
    null as hereditary_code,
    t. as hereditary_name,
    null as chronic_code,
    null as chronic_name,
    null as disa_info_code,
    null as disa_info_name,
    t. as disable_certificate_no,
    null as brf_code,
    null as brf_name,
    case when cfpfss is null then '0' else '1' end as kitchen_exhaust_mark,
    null as kitchen_exhaust_code,
    t. as kitchen_exhaust_name,
    null as fuel_type_code,
    t. as fuel_type_name,
    null as drink_water_type_code,
    t. as drink_water_type_name,
    null as wc_type_code,
    t. as wc_type_name,
    null as avian_corral_type_code,
    t. as avian_corral_type_name,
    to_date(t.jdrq00,'%Y%m%d') as build_date,
    null as build_org_code,
    null as build_org_name,
    null as build_org_tel,
    null as mang_org_code,
    t. as duty_dor_no,
    sysuserinfo. as duty_dor_name,
    t. as dscr,
    sysuserinfo. as duty_dor_tel,
    t. as registerhiscode,
    t. as registerhisname,
    null as reg_dor_no,
    null as enter_dor_name,
    t. as reg_date,
    null as inquirer_name,
    t. as inquirer_no,
    to_date(t.idate,'%Y%m%d') as inquirer_date,
    case t.sfyxda when '1' then '1' when '0' then '2' else t.sfyxda end as health_rec_status_code,
    '待定' as health_rec_status_name,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    t. as workplace,
    t. as feepay_type_code,
    t. as feepay_type_name,
    t. as pobservation_type_code,
    t. as pobservation_type_name,
    t. as pobservation_code,
    t. as pobservation_name,
    t. as pobservationmethods_code,
    t. as pobservationmethods_name,
    t. as pobservationresult_code,
    t. as pobservationresult_name,
    t. as observations_date,
    t. as observatione_date,
    t. as medpay_uebmi,
    t. as medpay_trpbmi,
    t. as medpay_nrcmc,
    t. as med_fee_pay_way_poor_assi,
    t. as med_fee_pay_way_busi_hi,
    t. as medpay_fape,
    t. as med_fee_pay_way_full_ownpay,
    t. as med_fee_pay_way_oth_way,
    t. as fmhis_fthr,
    t. as fmhis_mthr,
    t. as fmhis_brot_sist,
    t. as fmhis_child,
    jktj_shfs. as phys_exer_adhe_excs_time,
    jktj_shfs. as habits_diet_code,
    jktj_shfs. as habits_diet_name,
    jktj_shfs. as start_smoke_way_age,
    jktj_shfs. as stop_smoke_way_age,
    jktj_shfs. as stop_drink_way_age,
    jktj_shfs. as start_drink_way_age,
    jktj_shfs. as last_year_drunkenness_mark,
    jktj_shfs. as drink_type_code,
    jktj_shfs. as drink_type_name,
    jktj_shfs. as drink_type_others,
    jktj_shfs. as prfs_expo_info,
    jktj_shfs. as ins_id,
    jktj_shfs. as hl_phys_exam_cnt,
    jktj_shfs. as prfs_expo_chem_prot_mes_cont,
    jktj_shfs. as prfs_expo_toxi_prot_mes_cont,
    jktj_shfs. as prfs_expo_rdat_prot_mes_cont,
    jktj_shfs. as excs_frqu_code,
    jktj_shfs. as excs_frqu_name,
    jktj_shfs. as each_excs_time,
    jktj_shfs. as excs_way,
    jktj_shfs. as smoke_mark_code,
    jktj_shfs. as smoke_mark_name,
    jktj_shfs. as smok_day,
    jktj_shfs. as drnk_frqu_code,
    jktj_shfs. as drnk_frqu_name,
    jktj_shfs. as drnk_day,
    jktj_shfs. as stop_drink_code,
    jktj_shfs. as stop_drink_name,
    jktj_shfs. as prfs_expo_dust,
    jktj_shfs. as prfs_expo_dust_prot_mes,
    jktj_shfs. as prfs_expo_oth,
    jktj_shfs. as prfs_expo_oth_prot_mes,
    jktj_shfs. as prfs_expo_dust_prot_mes_cont,
    jktj_shfs. as prfs_expo_oth_prot_mes_cont,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from zl3500000000000004_syyzw.t_dwellerfile as t
left join data_dic_a as d_gender_name on d_gender_name.dic_type_code='GENDER_CODE' and d_gender_name.dic_val_code=t.sex
left join data_dic_a as d_edu_background_name on d_edu_background_name.dic_type_code='EDU_BACKGROUND_CODE' and d_edu_background_name.dic_val_code=t.cdegree
left join data_dic_a as d_per_addr_name on d_per_addr_name.dic_type_code='PER_ADDR_CODE' and d_per_addr_name.dic_val_code=t.rprtype
left join data_dic_a as d_nation_name on d_nation_name.dic_type_code='NATION_CODE' and d_nation_name.dic_val_code=t.folk
left join data_dic_a as d_blotype_abo_name on d_blotype_abo_name.dic_type_code='BLOTYPE_ABO_CODE' and d_blotype_abo_name.dic_val_code=t.bloodtype
left join data_dic_a as d_blotype_rh_name on d_blotype_rh_name.dic_type_code='BLOTYPE_RH_CODE' and d_blotype_rh_name.dic_val_code=t.rhxx
left join data_dic_a as d_mrg_stas_name on d_mrg_stas_name.dic_type_code='MRG_STAS_CODE' and d_mrg_stas_name.dic_val_code=t.mstatus
left join sysuserinfo on sysuserinfo.ygbh00=t.doctor
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_allergy partition(dt)
select 
    concat(allergy_id,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as allergy_id,
    '待定' as files_uscid,
    '待定' as patientid,
    '待定' as aise_code,
    '待定' as aise_name,
    null as remark,
    '待定' as state,
    '待定' as business_time,
    null as reserve1,
    null as reserve2,
    '待定' as upload_time,
    '待定' as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    '待定' as crte_time,
    '待定' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_illness partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as files_uscid,
    t. as patientid,
    t. as disease_code,
    '待定' as disease_name,
    t. as cnfm_date,
    null as remark,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    t. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t. as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.ot_jbgw_person_pdh as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_surgery partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as files_uscid,
    t. as patientid,
    t. as proc_name,
    t. as proc_date,
    null as remark,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    t. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t. as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.ot_jbgw_person_poh as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_injury partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as files_uscid,
    t. as patientid,
    t. as trauma_name,
    t. as trauma_date,
    null as remark,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    t. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t. as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.ot_jbgw_person_trauma as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_transfusion partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as files_uscid,
    t. as patientid,
    t. as transfuse_reason,
    t. as transfuse_datetime,
    null as remark,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as provice_district_code,
    '待定' as provice_district_name,
    default_update_time as upload_time,
    t. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t. as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.ot_jbgw_person_blood as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_family partition(dt)
select 
    concat(ref_no,files_uscid,patientid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as ref_no,
    '待定' as files_uscid,
    t_dwellerfile. as patientid,
    '待定' as relation_patient_code,
    '待定' as relation_patient_name,
    '待定' as disease_code,
    t_dwellerfile. as disease_name,
    null as remark,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile. as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from zl3500000000000004_syyzw. as t
where jzsfq is not null
union all
where jzsmq is not null
union all
where jzsxm is not null
union all
where jzszn is not null
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_sign partition(dt)
select 
    concat(sign_up_uscid,signid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t. as signid,
    t. as sign_dor_no,
    '待定' as sign_dor_name,
    t. as sign_team_code,
    oe_jtys_team. as sign_team_name,
    t. as psncert_type_code,
    d_psncert_type_name.dic_val_name as psncert_type_name,
    t. as certno,
    t. as resident_name,
    t. as health_rec_id,
    t. as sign_datetime,
    t. as unsign_date,
    null as unsign_reason,
    '待定' as sign_status_code,
    '待定' as sign_status_name,
    null as reg_dor_code,
    null as reg_dor_name,
    null as reg_time,
    null as remark,
    '0' as state,
    t. as create_time,
    null as reserve1,
    null as reserve2,
    oe_jtys_team. as presideid_card_type_code,
    oe_jtys_team. as presideid_card_type_name,
    oe_jtys_team. as presideid_card_no,
    default_update_time as upload_time,
    t. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t. as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.ot_jtys_sign as t
left join oe_jtys_team on t.FAMILY_DOCTOR_ ID = oe_jtys_team.FAMILY_DOCTOR_ ID
left join data_dic_a as d_psncert_type_name on d_psncert_type_name.dic_type_code='PSNCERT_TYPE_CODE' and d_psncert_type_name.dic_val_code=t.id_card_type
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_record partition(dt)
select 
    concat(sign_up_uscid,health_rec_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t. as health_rec_id,
    '待定' as householder_psncert_type_code,
    '待定' as householder_psncert_type_name,
    '待定' as householder_cert_no,
    t. as resident_name,
    '待定' as patientid,
    t. as family_tel,
    null as family_addr_code,
    null as family_addr_name,
    null as family_addr_prov_code,
    t. as family_addr_prov_name,
    null as family_addr_city_code,
    t. as family_addr_city_name,
    null as family_addr_coty_code,
    t. as family_addr_coty_name,
    null as family_addr_town_code,
    t. as family_addr_town_name,
    null as family_addr_comm_code,
    null as family_addr_comm_name,
    t. as family_addr_cotry_name,
    t. as family_addr_housnum,
    concat(adress_pro,adress_city,adress_county,adress_rural,adress_village,adrss_hnumber) as family_addr,
    null as poscode,
    null as family_rec_status_code,
    null as family_rec_status_name,
    null as inquirer_staff_no,
    null as inquirer_staff_name,
    null as inquirer_date,
    t. as registerhiscode,
    '待定' as registerhisname,
    to_date(t.cdate,'%Y%m%d) as reg_date,
    null as data_rank,
    default_create_time as create_time,
    t. as state,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    default_update_time as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    to_date(t.cdate,'%Y%m%d) as crte_time,
    t. as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.t_familyfile as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_member partition(dt)
select 
    concat(sign_up_uscid,health_rec_id,psncert_type_code,psncert_type_name,certno,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t_familyfile. as health_rec_id,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t. as certno,
    t_familyfile. as resident_name,
    case t.r_id ='0' then '1' else '0' end as householder_mark,
    t. as householder_relation_code,
    d_householder_relation_name.dic_val_name as householder_relation_name,
    null as data_rank,
    t. as create_time,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(concat(zhgxrq,zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,t.zhgxsj)) end as deleted_time
from zl3500000000000004_syyzw.t_dwellerfile as t
left join t_familyfile on t.f_id = t_familyfile.f_id
left join data_dic_a as d_householder_relation_name on d_householder_relation_name.dic_type_code='HOUSEHOLDER_RELATION_CODE' and d_householder_relation_name.dic_val_code=t.r_id
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_staff partition(dt)
select 
    concat(job_no,uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    sysuserinfo. as job_no,
    '待定' as uscid,
    '待定' as rgst_name,
    sysuserinfo. as full_name,
    sysuserinfo. as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    sysuserinfo. as dept_code,
    null as team,
    null as proftechttl_code,
    null as proftechttl_name,
    null as job_title_code,
    null as job_title_name,
    to_date(t.csrq00,'%Y%m%d') as brdy,
    sysuserinfo. as psn_type_code,
    '待定' as psn_type_name,
    null as practice_type_code,
    null as practice_type_name,
    null as gp_flag,
    null as edu_background_code,
    null as edu_background_name,
    null as professional_code,
    null as professional_name,
    '0' as state,
    to_timestamp(t.xgrqsj) as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(t.xgrqsj) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    to_timestamp(t.cjrqsj) as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
where t.ygxz00 in ('0','1','2') -- 0实习医生,1处方医生,2护士
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_team partition(dt)
select 
    concat(unified_uscid,team_code,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as unified_uscid,
    oe_jtys_team. as team_code,
    oe_jtys_team. as team_name,
    '1' as state,
    oe_jtys_team. as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    oe_jtys_team. as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    oe_jtys_team. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_phe_record_info partition(dt)
select 
    concat(test_uscid,examination_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as test_uscid,
    t. as examination_no,
    t. as patientid,
    t_dwellerfile. as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    null as appoint_id,
    null as plan_code,
    t_dwellerfile. as full_name,
    to_date(t.edate,'%Y%m%d') as examination_date,
    null as main_dor_no,
    t. as main_dor_name,
    d_sympt_code.dic_val_code as sympt_code,
    t. as sympt_name,
    t. as tprt,
    t. as pule,
    t. as vent_frqu,
    t. as left_dbp,
    t. as left_sbp,
    t. as right_dbp,
    t. as right_sbp,
    t. as height,
    t. as weight,
    t. as bmi,
    t. as waist_cm,
    t. as hipline,
    t. as whr,
    t. as elder_cognition_res_code,
    d_elder_cognition_res_name.dic_val_name as elder_cognition_res_name,
    t. as elder_wit_score,
    t. as elder_health_status_code,
    d_elder_health_status_name.dic_val_name as elder_health_status_name,
    t. as elder_self_eval_code,
    d_elder_self_eval_name.dic_val_name as elder_self_eval_name,
    t. as elder_emotional_status_code,
    d_elder_emotional_status_name.dic_val_name as elder_emotional_status_name,
    t. as elder_depression_score,
    jktj_shfs. as excs_frqu_code,
    d_excs_frqu_name.dic_val_name as excs_frqu_name,
    jktj_shfs. as exercise_each,
    jktj_shfs. as insist_exercise_month,
    jktj_shfs. as exercise_code,
    jktj_shfs. as habits_diet_code,
    d_habits_diet_name.dic_val_name as habits_diet_name,
    jktj_shfs. as smok_info_code,
    d_smok_info_name.dic_val_name as smok_info_name,
    jktj_shfs. as smok_day,
    jktj_shfs. as start_smoke_age,
    jktj_shfs. as stop_smoke_age,
    jktj_shfs. as drnk_frqu_code,
    d_drnk_frqu_name.dic_val_name as drnk_frqu_name,
    jktj_shfs. as drnk_day,
    jktj_shfs. as stop_drink_code,
    d_stop_drink_name.dic_val_name as stop_drink_name,
    jktj_shfs. as stop_drink_age,
    jktj_shfs. as start_drink_age,
    jktj_shfs. as last_year_drunkenness_mark,
    jktj_shfs. as drink_type_code,
    d_drink_type_name.dic_val_name as drink_type_name,
    jktj_zqgn. as oral_exterior_code,
    d_oral_exterior_name.dic_val_name as oral_exterior_name,
    jktj_zqgn. as dentition_type_code,
    d_dentition_type_name.dic_val_name as dentition_type_name,
    concat(jktj_zqgn.jktj_zqgn_quechi,';',jktj_zqgn.jktj_zqgn_quchi0,';',jktj_zqgn.jktj_zqgn_yichi0) as dentition_explain,
    jktj_zqgn. as phary_exam_res_code,
    d_phary_exam_res_name.dic_val_name as phary_exam_res_name,
    jktj_zqgn. as left_original_value,
    jktj_zqgn. as right_original_hyperopia_value,
    jktj_zqgn. as left_redress_value,
    jktj_zqgn. as right_redress_hyperopia_value,
    null as left_vision_code,
    null as left_vision_name,
    null as right_vision_code,
    null as right_vision_name,
    jktj_ct. as eyeground_abnorm_mark,
    jktj_ct. as fundoscopy_abnorm_descr,
    jktj_zqgn. as hear_check_res_code,
    d_hear_check_res_name.dic_val_name as hear_check_res_name,
    jktj_zqgn. as sport_function_status_code,
    d_sport_function_status_name.dic_val_name as sport_function_status_name,
    jktj_ct. as skin_check_abnorm_code,
    d_skin_check_abnorm_name.dic_val_name as skin_check_abnorm_name,
    jktj_ct. as scleral_check_res_code,
    d_scleral_check_res_name.dic_val_name as scleral_check_res_name,
    jktj_ct. as lymph_check_res_code,
    d_lymph_check_res_name.dic_val_name as lymph_check_res_name,
    jktj_ct. as barrel_chest_mark,
    jktj_ct. as lung_abnorm_breath_mark,
    jktj_ct. as lung_abnorm_breath_descr,
    jktj_ct. as lung_rale_code,
    d_lung_rale_name.dic_val_name as lung_rale_name,
    jktj_ct. as lung_rale_describe,
    jktj_ct. as heart_rate,
    jktj_ct. as heart_rate_type_code,
    d_heart_rate_type_name.dic_val_name as heart_rate_type_name,
    jktj_ct. as heart_murmur_mark,
    jktj_ct. as heart_murmur_describe,
    jktj_ct. as abdominal_tend_mark,
    jktj_ct. as abdominal_tend_descr,
    jktj_ct. as abdominal_mass_mark,
    jktj_ct. as abdominal_mass_descr,
    jktj_ct. as abdominal_hepatauxe_mark,
    jktj_ct. as abdominal_hepatauxe_descr,
    jktj_ct. as splenauxe_mark,
    jktj_ct. as splenauxe_descr,
    jktj_ct. as abdominal_dullness_mark,
    jktj_ct. as abdominal_dullness_descr,
    jktj_ct. as legs_edema_check_res_code,
    d_legs_edema_check_res_name.dic_val_name as legs_edema_check_res_name,
    jktj_ct. as foot_dorsal_artery_code,
    d_foot_dorsal_artery_name.dic_val_name as foot_dorsal_artery_name,
    jktj_ct. as anus_check_res_type_code,
    d_anus_check_res_type_name.dic_val_name as anus_check_res_type_name,
    jktj_ct. as breast_check_res_code,
    d_breast_check_res_name.dic_val_name as breast_check_res_name,
    jktj_ct. as vulva_abnorm_mark,
    jktj_ct. as vulva_abnorm_descr,
    jktj_ct. as vagina_abnorm_mark,
    jktj_ct. as vagina_abnorm_descr,
    jktj_ct. as cervix_abnorm_mark,
    jktj_ct. as cervix_abnorm_descr,
    jktj_ct. as corpusuteri_abnorm_mark,
    jktj_ct. as corpusuteri_abnorm_descr,
    jktj_ct. as adnex_abnorm_mark,
    jktj_ct. as gyn_adnex_abnorm_descr,
    jktj_ct. as other_health_check_res,
    jktj_fzjc. as ecg_abnorm_mark,
    jktj_fzjc. as ecg_abnorm_descr,
    jktj_fzjc. as xray_abnorm_mark,
    jktj_fzjc. as xray_abnorm_descr,
    jktj_fzjc. as bscan_abnorm_mark,
    jktj_fzjc. as bscan_abnorm_descr,
    jktj_fzjc. as cps_abnorm_mark,
    jktj_fzjc. as cps_abnorm_descr,
    jktj_fzjc. as other_assist_check,
    jktj_zyjkwt. as cardiovascular_code,
    jktj_zyjkwt. as cardiovascular_name,
    jktj_zyjkwt. as chronic_kidney_code,
    jktj_zyjkwt. as chronic_kidney_name,
    jktj_zyjkwt. as cardiopathy_code,
    jktj_zyjkwt. as cardiopathy_name,
    jktj_zyjkwt. as vas_code,
    jktj_zyjkwt. as vas_name,
    jktj_zyjkwt. as oculopathy_code,
    jktj_zyjkwt. as oculopathy_name,
    jktj_zyjkwt. as neuro_exam_abnormal_mark,
    jktj_zyjkwt. as neuro_exam_abnormal_descr,
    jktj_zyjkwt. as systemic_disease,
    jktj_zyjkwt. as systemic_disease_descr,
    jktj_jkpj. as hl_eval_abnorm_flag,
    concat(jktj_jkpj.jkpj_tjyc1,';',jktj_jkpj.jkpj_tjyc2,';',jktj_jkpj.jkpj_tjyc3,';',jktj_jkpj.jkpj_tjyc4) as abnormal_descr,
    d_health_guide_code.dic_val_code as health_guide_code,
    jktj_jkpj. as health_guide_name,
    d_risk_control_ad_code.dic_val_code as risk_control_ad_code,
    jktj_jkpj. as risk_control_ad_name,
    jktj_jkpj. as aim_weight,
    d_offer_vacc_code.dic_val_code as offer_vacc_code,
    jktj_jkpj. as offer_vacc_name,
    null as data_rank,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as updt_emplo_no,
    t. as updt_emplo_name,
    default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_ybzk as t
and t.df_id= t_dwellerfile.df_id
left join data_dic_a as d_sympt_code on d_sympt_code.dic_type_code='SYMPT_CODE' and d_sympt_code.dic_val_name=t.tjzzqk
left join data_dic_a as d_elder_cognition_res_name on d_elder_cognition_res_name.dic_type_code='ELDER_COGNITION_RES_CODE' and d_elder_cognition_res_name.dic_val_code=t.ybzk_lnrzgn
left join data_dic_a as d_elder_health_status_name on d_elder_health_status_name.dic_type_code='ELDER_HEALTH_STATUS_CODE' and d_elder_health_status_name.dic_val_code=t.lnrjkpj
left join data_dic_a as d_elder_self_eval_name on d_elder_self_eval_name.dic_type_code='ELDER_SELF_EVAL_CODE' and d_elder_self_eval_name.dic_val_code=t.lnrshpj
left join data_dic_a as d_elder_emotional_status_name on d_elder_emotional_status_name.dic_type_code='ELDER_EMOTIONAL_STATUS_CODE' and d_elder_emotional_status_name.dic_val_code=t.ybzk_lnqgzt
and t.df_id= jktj_shfs.df_id
left join data_dic_a as d_excs_frqu_name on d_excs_frqu_name.dic_type_code='EXCS_FRQU_CODE' and d_excs_frqu_name.dic_val_code=t.shfs_tydl_dlpl
left join data_dic_a as d_habits_diet_name on d_habits_diet_name.dic_type_code='HABITS_DIET_CODE' and d_habits_diet_name.dic_val_code=t.shsf_ysxg
left join data_dic_a as d_smok_info_name on d_smok_info_name.dic_type_code='SMOK_INFO_CODE' and d_smok_info_name.dic_val_code=t.shsf_xyqk_xyzk
left join data_dic_a as d_drnk_frqu_name on d_drnk_frqu_name.dic_type_code='DRNK_FRQU_CODE' and d_drnk_frqu_name.dic_val_code=t.shfs_yjqk_yjpl
left join data_dic_a as d_stop_drink_name on d_stop_drink_name.dic_type_code='STOP_DRINK_CODE' and d_stop_drink_name.dic_val_code=t.shfs_yjqk_sfjj
left join data_dic_a as d_drink_type_name on d_drink_type_name.dic_type_code='DRINK_TYPE_CODE' and d_drink_type_name.dic_val_code=t.shfs_yjzl_
and t.df_id= jktj_zqgn.df_id
left join data_dic_a as d_oral_exterior_name on d_oral_exterior_name.dic_type_code='ORAL_EXTERIOR_CODE' and d_oral_exterior_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_dentition_type_name on d_dentition_type_name.dic_type_code='DENTITION_TYPE_CODE' and d_dentition_type_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_phary_exam_res_name on d_phary_exam_res_name.dic_type_code='PHARY_EXAM_RES_CODE' and d_phary_exam_res_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hear_check_res_name on d_hear_check_res_name.dic_type_code='HEAR_CHECK_RES_CODE' and d_hear_check_res_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_sport_function_status_name on d_sport_function_status_name.dic_type_code='SPORT_FUNCTION_STATUS_CODE' and d_sport_function_status_name.dic_val_code=t.jktj_zqgn_ydgn
and t.df_id= jktj_ct.df_id
left join data_dic_a as d_skin_check_abnorm_name on d_skin_check_abnorm_name.dic_type_code='SKIN_CHECK_ABNORM_CODE' and d_skin_check_abnorm_name.dic_val_code=t.ct_pf
left join data_dic_a as d_scleral_check_res_name on d_scleral_check_res_name.dic_type_code='SCLERAL_CHECK_RES_CODE' and d_scleral_check_res_name.dic_val_code=t.ct_gm
left join data_dic_a as d_lymph_check_res_name on d_lymph_check_res_name.dic_type_code='LYMPH_CHECK_RES_CODE' and d_lymph_check_res_name.dic_val_code=t.ct_lbj
left join data_dic_a as d_lung_rale_name on d_lung_rale_name.dic_type_code='LUNG_RALE_CODE' and d_lung_rale_name.dic_val_code=t.ct_fly
left join data_dic_a as d_heart_rate_type_name on d_heart_rate_type_name.dic_type_code='HEART_RATE_TYPE_CODE' and d_heart_rate_type_name.dic_val_code=t.ct_xzxinl
left join data_dic_a as d_legs_edema_check_res_name on d_legs_edema_check_res_name.dic_type_code='LEGS_EDEMA_CHECK_RES_CODE' and d_legs_edema_check_res_name.dic_val_code=t.ct_xzsz
left join data_dic_a as d_foot_dorsal_artery_name on d_foot_dorsal_artery_name.dic_type_code='FOOT_DORSAL_ARTERY_CODE' and d_foot_dorsal_artery_name.dic_val_code=t.ct_zbdmbd
left join data_dic_a as d_anus_check_res_type_name on d_anus_check_res_type_name.dic_type_code='ANUS_CHECK_RES_TYPE_CODE' and d_anus_check_res_type_name.dic_val_code=t.ct_gmzz
left join data_dic_a as d_breast_check_res_name on d_breast_check_res_name.dic_type_code='BREAST_CHECK_RES_CODE' and d_breast_check_res_name.dic_val_code=t.ct_rxqk
and t.df_id= jktj_fzjc.df_id
and t.df_id= jktj_zyjkwt.df_id
and t.df_id= jktj_jkpj.df_id
left join data_dic_a as d_health_guide_code on d_health_guide_code.dic_type_code='HEALTH_GUIDE_CODE' and d_health_guide_code.dic_val_name=t.jkzd
left join data_dic_a as d_risk_control_ad_code on d_risk_control_ad_code.dic_type_code='RISK_CONTROL_AD_CODE' and d_risk_control_ad_code.dic_val_name=t.wxyskz
left join data_dic_a as d_offer_vacc_code on d_offer_vacc_code.dic_type_code='OFFER_VACC_CODE' and d_offer_vacc_code.dic_val_name=t.wxyskz_ymjz
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_hosp_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as test_uscid,
    null as examination_no,
    null as admission_date,
    null as leave_date,
    null as reason,
    null as uscn,
    null as medcasno,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_phe_med_info partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as test_uscid,
    jktj_ybzk. as examination_no,
    t. as drug_name,
    null as drugusage,
    null as drug_used_dosunt,
    null as drug_used_sdose,
    null as drug_used_idose,
    d_drug_used_way_code.dic_val_code as drug_used_way_code,
    t. as drug_used_way_name,
    t. as medication_time,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    t. as medication_compliance_code,
    d_medication_compliance_name.dic_val_name as medication_compliance_name,
    null as data_rank,
    '0' as state,
    default_update_time as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    default_update_time as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    default_update_time as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.t_mxjb_sf_yyqk as t
and t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_drug_used_way_code on d_drug_used_way_code.dic_type_code='DRUG_USED_WAY_CODE' and d_drug_used_way_code.dic_val_name=t.ywyf
left join data_dic_a as d_medication_compliance_name on d_medication_compliance_name.dic_type_code='MEDICATION_COMPLIANCE_CODE' and d_medication_compliance_name.dic_val_code=t.fyycx
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_home_bed_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as test_uscid,
    jktj_ybzk. as examination_no,
    to_date(t.jcrq,'%Y%m%d') as build_bed_date,
    to_date(t.ccrq,'%Y%m%d') as remove_bed_date,
    t. as bed_reason,
    t. as bed_org_name,
    t. as medcasno,
    null as data_rank,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_jtbcs as t
and t.df_id= jktj_ybzk.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_occ_exp_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as test_uscid,
    null as examination_no,
    null as occup_risk_type_code,
    null as occup_risk_type_name,
    null as occup_risk_name,
    null as occup_protect_mark,
    null as occup_protect_descr,
    null as occup,
    null as occup_time,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_phe_nonimm_vacc_history partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    '待定' as test_uscid,
    jktj_ybzk. as examination_no,
    d_vacc_code.dic_val_code as vacc_code,
    t. as vacc_name,
    to_date(t.jzrq,'%Y%m%d') as vacc_time,
    '待定' as vaccinate_uscid,
    t. as vaccinate_org_name,
    null as data_rank,
    '0' as state,
    t. as business_time,
    null as reserve1,
    null as reserve2,
    t. as upload_time,
    to_timestamp(t.edate) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_fmygh as t
and t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_vacc_code on d_vacc_code.dic_type_code='VACC_CODE' and d_vacc_code.dic_val_name=t.ymmc
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_elderly_care_assess partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_ybzk. as ref_no,
    '待定' as test_uscid,
    jktj_ybzk. as examination_no,
    null as dinner_score,
    null as freshen_score,
    null as dress_score,
    null as toilet_score,
    null as acty_score,
    jktj_ybzk. as total_score,
    null as data_rank,
    '0' as state,
    jktj_ybzk. as business_time,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_ybzk. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
where t.lnrshpj is not null
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_lab_record partition(dt)
select 
    concat(test_uscid,hl_phys_exam_asst_exam_id,asst_exam_kdn_fun_血钠_conc,asst_exam_pl_trigl,asst_exam_pl_sero_high_den_lipopr_chol) as rid
    ,tab.*
from 
(
select  
    '待定' as test_uscid,
    t. as hl_phys_exam_asst_exam_id,
    t. as ins_id,
    t. as hl_file_id,
    t. as hl_phys_exam_cnt,
    t. as asst_exam_empt_stom_bloo_gluc,
    t. as asst_exam_empt_stom_bloo_gluc_mg,
    t. as asst_exam_bloo_rout_hemog,
    t. as asst_exam_bloo_rout_whit_bloo_cells,
    t. as asst_exam_bloo_rout_plate,
    t. as asst_exam_bloo_rout_oth,
    t. as asst_exam_urin_rout_urin_prot,
    t. as asst_exam_urin_rout_urin_gluc,
    t. as asst_exam_urin_rout_urin_keto,
    t. as asst_exam_urin_rout_urin_occu_bloo,
    t. as asst_exam_urin_rout_oth,
    t. as asst_exam_urin_micro_albu,
    t. as asst_exam_sto_occu_bloo,
    t. as asst_exam_live_fun_sero_gluta_transa,
    t. as asst_exam_live_fun_sero_grain_grass_transa,
    t. as asst_exam_live_fun_albu,
    t. as asst_exam_live_fun_totl_bili,
    t. as asst_exam_live_fun_comb_bili,
    t. as asst_exam_kdn_fun_sero_creat,
    t. as asst_exam_kdn_fun_blo_urea_nitr,
    t. as asst_exam_kdn_fun_k_conc,
    t. as asst_exam_kdn_fun_血钠_conc,
    t. as asst_exam_pl_totl_chol,
    t. as asst_exam_pl_trigl,
    t. as asst_exam_pl_sero_low_den_lipopr_chol,
    t. as asst_exam_pl_sero_high_den_lipopr_chol,
    t. as asst_exam_glyc_hemog,
    t. as asst_exam_hepa_b_surf_anti,
    t. as asst_exam_fund,
    t. as asst_exam_fund_abn,
    t. as asst_exam_electro,
    t. as asst_exam_electro_abn,
    t. as asst_exam_chst_x_line_piec,
    t. as asst_exam_chst_x_line_piec_abn,
    t. as asst_exam_b_over,
    t. as asst_exam_b_over_oth,
    t. as asst_exam_cerv_smea,
    t. as asst_exam_cerv_smea_abn,
    t. as asst_exam_oth,
    t. as asst_exam_urin_rout_urin_prot,
    t. as asst_exam_urin_rout_urin_gluc,
    t. as asst_exam_urin_rout_urin_keto,
    t. as asst_exam_urin_rout_urin_occu_bloo,
    t. as urin_rout_urin_whit_bloo_cells,
    t. as urin_rout_urin_bili,
    t. as urin_rout_urin_nit,
    t. as live_fun_albu_glon_rat,
    t. as live_fun_indi_bili,
    t. as live_fun_alp,
    t. as live_fun_r_ggt,
    t. as kdn_fun_uric_acid,
    t. as asst_exam_electro1,
    t. as asst_exam_electro2,
    t. as asst_exam_electro3,
    t. as abd_b_over_lgp_sple,
    t. as abd_b_over_lgp_sple_oth,
    t. as asst_exam_rcd_sav_time,
    t. as modify_datetime,
    t. as crte_time,
    t. as asst_exam_abd_b_over1,
    t. as asst_exam_abd_b_over2,
    t. as asst_exam_abd_b_over3,
    t. as asst_exam_abd_b_over4,
    null as reserve1,
    null as reserve2,
    default_update_time as upload_time,
    t. as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_fzjc as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_physical_exam partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t. as patientid,
    jktj_ybzk. as examination_no,
    t. as check_id,
    t. as skin_codition_code,
    d_skin_codition_name.dic_val_name as skin_codition_name,
    t. as skin_condition_remark,
    t. as sclera_code,
    d_sclera_name.dic_val_name as sclera_name,
    t. as sclera_remark,
    t. as lymph_node_code,
    d_lymph_node_name.dic_val_name as lymph_node_name,
    t. as lymph_node_remark,
    t. as barrel_chest_flag,
    t. as breath_sound_code,
    d_breath_sound_name.dic_val_name as breath_sound_name,
    t. as breath_sound_abnormal_remark,
    t. as rale_code,
    d_rale_name.dic_val_name as rale_name,
    t. as rale_remark,
    t. as heart_rate,
    t. as heart_rhythm_code,
    d_heart_rhythm_name.dic_val_name as heart_rhythm_name,
    t. as heart_noise_flag,
    t. as heart_noise_remark,
    t. as tenderness_flag,
    t. as tenderness_remark,
    t. as bag_piece_flag,
    t. as bag_piece_remark,
    t. as hepatomegaly_flag,
    t. as hepatomegaly_remark,
    t. as splenomegaly_flag,
    t. as splenomegaly_remark,
    t. as move_dullness_flag,
    t. as move_dullness_remark,
    t. as legs_edema_check_res_code,
    d_legs_edema_check_res_name.dic_val_name as legs_edema_check_res_name,
    t. as foot_dorsal_artery_code,
    d_foot_dorsal_artery_name.dic_val_name as foot_dorsal_artery_name,
    t. as anus_check_res_type_code,
    d_anus_check_res_type_name.dic_val_name as anus_check_res_type_name,
    t. as anus_dre_remark,
    t. as mammary_gland_code,
    d_mammary_gland_name.dic_val_name as mammary_gland_name,
    t. as mammary_gland_remark,
    t. as vulva_code,
    d_vulva_name.dic_val_name as vulva_name,
    t. as vulva_remark,
    t. as vagina_code,
    d_vagina_name.dic_val_name as vagina_name,
    t. as vagina_remark,
    t. as cervical_code,
    d_cervical_name.dic_val_name as cervical_name,
    t. as cervical_remark,
    t. as uterine_body_code,
    d_uterine_body_name.dic_val_name as uterine_body_name,
    t. as uterine_body_remark,
    t. as attachment_code,
    d_attachment_name.dic_val_name as attachment_name,
    t. as attachment_remark,
    t. as fundus_code,
    d_fundus_name.dic_val_name as fundus_name,
    t. as fundus_remark,
    default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_ct as t
and t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_skin_codition_name on d_skin_codition_name.dic_type_code='SKIN_CODITION_CODE' and d_skin_codition_name.dic_val_code=t.ct_pf
left join data_dic_a as d_sclera_name on d_sclera_name.dic_type_code='SCLERA_CODE' and d_sclera_name.dic_val_code=t.ct_gm
left join data_dic_a as d_lymph_node_name on d_lymph_node_name.dic_type_code='LYMPH_NODE_CODE' and d_lymph_node_name.dic_val_code=t.ct_lbj
left join data_dic_a as d_breath_sound_name on d_breath_sound_name.dic_type_code='BREATH_SOUND_CODE' and d_breath_sound_name.dic_val_code=t.ct_fhxy
left join data_dic_a as d_rale_name on d_rale_name.dic_type_code='RALE_CODE' and d_rale_name.dic_val_code=t.ct_fly
left join data_dic_a as d_heart_rhythm_name on d_heart_rhythm_name.dic_type_code='HEART_RHYTHM_CODE' and d_heart_rhythm_name.dic_val_code=t.ct_xzxinl
left join data_dic_a as d_legs_edema_check_res_name on d_legs_edema_check_res_name.dic_type_code='LEGS_EDEMA_CHECK_RES_CODE' and d_legs_edema_check_res_name.dic_val_code=t.ct_xzsz
left join data_dic_a as d_foot_dorsal_artery_name on d_foot_dorsal_artery_name.dic_type_code='FOOT_DORSAL_ARTERY_CODE' and d_foot_dorsal_artery_name.dic_val_code=t.ct_zbdmbd
left join data_dic_a as d_anus_check_res_type_name on d_anus_check_res_type_name.dic_type_code='ANUS_CHECK_RES_TYPE_CODE' and d_anus_check_res_type_name.dic_val_code=t.ct_gmzz
left join data_dic_a as d_mammary_gland_name on d_mammary_gland_name.dic_type_code='MAMMARY_GLAND_CODE' and d_mammary_gland_name.dic_val_code=t.ct_rxqk
left join data_dic_a as d_vulva_name on d_vulva_name.dic_type_code='VULVA_CODE' and d_vulva_name.dic_val_code=t.ct_fkwy
left join data_dic_a as d_vagina_name on d_vagina_name.dic_type_code='VAGINA_CODE' and d_vagina_name.dic_val_code=t.ct_fkyd
left join data_dic_a as d_cervical_name on d_cervical_name.dic_type_code='CERVICAL_CODE' and d_cervical_name.dic_val_code=t.ct_fkgj
left join data_dic_a as d_uterine_body_name on d_uterine_body_name.dic_type_code='UTERINE_BODY_CODE' and d_uterine_body_name.dic_val_code=t.ct_fkgt
left join data_dic_a as d_attachment_name on d_attachment_name.dic_type_code='ATTACHMENT_CODE' and d_attachment_name.dic_val_code=t.ct_fkfj
left join data_dic_a as d_fundus_name on d_fundus_name.dic_type_code='FUNDUS_CODE' and d_fundus_name.dic_val_code=t.ct_yd
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_physical_exam_extend partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t. as patientid,
    jktj_ybzk. as examination_no,
    t. as check_id,
    jktj_zqgn. as oral_lips_code,
    d_oral_lips_name.dic_val_name as oral_lips_name,
    jktj_zqgn. as oral_dentition_code,
    d_oral_dentition_name.dic_val_name as oral_dentition_name,
    jktj_zqgn. as oral_pharyngeal_code,
    d_oral_pharyngeal_name.dic_val_name as oral_pharyngeal_name,
    jktj_zqgn. as oral_dentition_missing_tooth,
    jktj_zqgn. as oral_dentition_decayed_tooth,
    jktj_zqgn. as oral_dentition_false_tooth,
    jktj_zqgn. as eyesight_left,
    jktj_zqgn. as eyesight_right,
    jktj_zqgn. as left_redress_value,
    jktj_zqgn. as right_redress_hyperopia_value,
    jktj_zqgn. as hearing_code,
    d_hearing_name.dic_val_name as hearing_name,
    jktj_zqgn. as motor_function_code,
    d_motor_function_name.dic_val_name as motor_function_name,
    t. as other_remark,
    to_timestamp(t.zhxgsj) as modify_datetime,
    default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_ct as t
and t.df_id= jktj_ybzk.df_id
and t.df_id= jktj_zqgn.df_id
left join data_dic_a as d_oral_lips_name on d_oral_lips_name.dic_type_code='ORAL_LIPS_CODE' and d_oral_lips_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_oral_dentition_name on d_oral_dentition_name.dic_type_code='ORAL_DENTITION_CODE' and d_oral_dentition_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_oral_pharyngeal_name on d_oral_pharyngeal_name.dic_type_code='ORAL_PHARYNGEAL_CODE' and d_oral_pharyngeal_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hearing_name on d_hearing_name.dic_type_code='HEARING_CODE' and d_hearing_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_motor_function_name on d_motor_function_name.dic_type_code='MOTOR_FUNCTION_CODE' and d_motor_function_name.dic_val_code=t.jktj_zqgn_ydgn
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_main_issues partition(dt)
select 
    concat(ref_no,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t. as patientid,
    jktj_ybzk. as examination_no,
    t. as hl_phys_exam_cnt,
    t. as cardiovascular_code,
    d_cardiovascular_name.dic_val_name as cardiovascular_name,
    t. as cerebrovascular_disease_remark,
    t. as chronic_kidney_code,
    d_chronic_kidney_name.dic_val_name as chronic_kidney_name,
    t. as kidney_disease_remark,
    t. as cardiopathy_code,
    d_cardiopathy_name.dic_val_name as cardiopathy_name,
    t. as heart_disease_remark,
    t. as vas_code,
    d_vas_name.dic_val_name as vas_name,
    t. as vascular_disease_remark,
    t. as oculopathy_code,
    d_oculopathy_name.dic_val_name as oculopathy_name,
    t. as eyes_disease_remark,
    t. as nervous_disease_code,
    d_nervous_disease_name.dic_val_name as nervous_disease_name,
    t. as nervous_disease_remark,
    t. as other_disease_code,
    d_other_disease_name.dic_val_name as other_disease_name,
    t. as other_disease_remark,
    t. as gentle_constitution,
    t. as tcm_vdc,
    t. as yang_deficiency_constitution,
    t. as dryness_constitution,
    t. as phlegm_damp_constitution,
    t. as damp_heat_constitution,
    t. as blood_stasis_constitution,
    t. as look_depressed_constitution,
    t. as sensitive_constitution,
    jktj_jkpj. as medical_abnormal_flag,
    jktj_jkpj. as medical_abnormal_remark1,
    jktj_jkpj. as medical_abnormal_remark2,
    jktj_jkpj. as medical_abnormal_remark3,
    jktj_jkpj. as medical_abnormal_remark4,
    jktj_jkpj. as health_guide_code,
    d_health_guide_name.dic_val_name as health_guide_name,
    jktj_jkpj. as risk_factor_control_code,
    d_risk_factor_control_name.dic_val_name as risk_factor_control_name,
    jktj_jkpj. as weight_target,
    jktj_jkpj. as vaccination,
    jktj_jkpj. as risk_factor_control_remark,
    jktj_jkpj. as health_guide_content,
    jktj_jkpj. as healthy_teaching,
    t. as register_datetime,
    default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_zyjkwt as t
and t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_cardiovascular_name on d_cardiovascular_name.dic_type_code='CARDIOVASCULAR_CODE' and d_cardiovascular_name.dic_val_code=t.zyjkwt_nxg
left join data_dic_a as d_chronic_kidney_name on d_chronic_kidney_name.dic_type_code='CHRONIC_KIDNEY_CODE' and d_chronic_kidney_name.dic_val_code=t.zyjkwt_sz
left join data_dic_a as d_cardiopathy_name on d_cardiopathy_name.dic_type_code='CARDIOPATHY_CODE' and d_cardiopathy_name.dic_val_code=t.zyjkwt_xzwfx
left join data_dic_a as d_vas_name on d_vas_name.dic_type_code='VAS_CODE' and d_vas_name.dic_val_code=t.zyjkwt_xgwfx
left join data_dic_a as d_oculopathy_name on d_oculopathy_name.dic_type_code='OCULOPATHY_CODE' and d_oculopathy_name.dic_val_code=t.zyjkwt_ybwfx
left join data_dic_a as d_nervous_disease_name on d_nervous_disease_name.dic_type_code='NERVOUS_DISEASE_CODE' and d_nervous_disease_name.dic_val_code=t.zyjkwt_sjxtjb
left join data_dic_a as d_other_disease_name on d_other_disease_name.dic_type_code='OTHER_DISEASE_CODE' and d_other_disease_name.dic_val_code=t.zyjkwt_qtxtjb
and t.df_id= jktj_jkpj.df_id
left join data_dic_a as d_health_guide_name on d_health_guide_name.dic_type_code='HEALTH_GUIDE_CODE' and d_health_guide_name.dic_val_code=t.jkzd
left join data_dic_a as d_risk_factor_control_name on d_risk_factor_control_name.dic_type_code='RISK_FACTOR_CONTROL_CODE' and d_risk_factor_control_name.dic_val_code=t.wxyskz
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_organ_function partition(dt)
select 
    concat(hl_phys_exam_orga_efcc_id,test_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as hl_phys_exam_orga_efcc_id,
    '待定' as test_uscid,
    t. as patientid,
    t. as hl_phys_exam_cnt,
    t. as oral_lips_code,
    d_oral_lips_name.dic_val_name as oral_lips_name,
    t. as oral_dentition_code,
    d_oral_dentition_name.dic_val_name as oral_dentition_name,
    t. as oral_pharyngeal_code,
    d_oral_pharyngeal_name.dic_val_name as oral_pharyngeal_name,
    t. as eyesight_left,
    t. as eyesight_right,
    t. as left_redress_value,
    t. as right_redress_hyperopia_value,
    t. as hearing_code,
    d_hearing_name.dic_val_name as hearing_name,
    t. as motor_function_code,
    d_motor_function_name.dic_val_name as motor_function_name,
    t. as ora_tooth_row_mis,
    t. as ora_tooth_row_cari,
    t. as ora_tooth_row_denti,
    default_update_time as upload_time,
    to_timestamp(t.edate) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.jktj_zqgn as t
left join data_dic_a as d_oral_lips_name on d_oral_lips_name.dic_type_code='ORAL_LIPS_CODE' and d_oral_lips_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_oral_dentition_name on d_oral_dentition_name.dic_type_code='ORAL_DENTITION_CODE' and d_oral_dentition_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_oral_pharyngeal_name on d_oral_pharyngeal_name.dic_type_code='ORAL_PHARYNGEAL_CODE' and d_oral_pharyngeal_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hearing_name on d_hearing_name.dic_type_code='HEARING_CODE' and d_hearing_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_motor_function_name on d_motor_function_name.dic_type_code='MOTOR_FUNCTION_CODE' and d_motor_function_name.dic_val_code=t.jktj_zqgn_ydgn
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_report_card partition(dt)
select 
    concat(rpt_org_uscid,rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as rpt_org_uscid,
    '待定' as rpot_card_id,
    t_dwellerfile. as full_name,
    t_dwellerfile. as gender_code,
    字典. as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    t_dwellerfile. as certno,
    case when IDCARDNO is not null then '01' else null as psncert_type_code,
    case when IDCARDNO is not null then '01' else null as psncert_type_name,
    t_dwellerfile. as occup_code,
    字典. as occup_name,
    '待定' as rpt_date,
    '待定' as report_sbp,
    '待定' as rpot_dbp,
    '待定' as reporter_id,
    '待定' as report_name,
    '待定' as rpt_dept_code,
    '待定' as rpt_dept_name,
    t_cm_dinfo. as business_time,
    case when EDATE is not null then '1' else '0' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
T_CM_DINFO.DF_ID = T_DWELLERFILE.DF_ID
gender_code = SEX
occup_type_code = WORKSTATUS
WHERE 1=1
AND T_CM_DINFO.CCL_ID = '1'
) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_manage_card partition(dt)
select 
    concat(control_uscid,hpid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as hpid,
    '待定' as medcasno,
    '待定' as card_no,
    '待定' as card_type_code,
    '待定' as card_type_name,
    t_dwellerfile. as full_name,
    t_dwellerfile. as gender_code,
    字典. as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    t_dwellerfile. as tel,
    t_dwellerfile. as certno,
    case when IDCARDNO is not null then '01' else null as psncert_type_code,
    case when IDCARDNO is not null then '01' else null as psncert_type_name,
    t_dwellerfile. as occup_code,
    字典. as occup_name,
    t_dwellerfile. as weight,
    t_dwellerfile. as height,
    t_dwellerfile. as family_his_code,
    这个码表在哪？用说明里的内容？. as family_his_name,
    t_cm_dinfo. as exist_smoke_code,
    字典. as exist_smoke_name,
    null as stop_smoke_time,
    t_cm_dinfo. as start_smoke_age,
    t_cm_dinfo. as drnk_frqu_code,
    字典. as drnk_frqu_name,
    t_cm_dinfo. as start_drink_year,
    '待定' as overdrink_mark,
    '待定' as habits_exercise_code,
    '待定' as habits_exercise_name,
    '待定' as not_medication_sbp,
    '待定' as un_dose_dbp,
    '待定' as self_help_ability_code,
    '待定' as self_help_ability_name,
    t_cm_dinfo. as occup_mark,
    t_cm_dinfo. as occup_risk_name,
    t_cm_dinfo. as occup_risk_type_code,
    这个码表在哪？用说明里的内容？. as occup_risk_type_name,
    t_cm_dinfo. as danger_occup,
    t_cm_dinfo. as danger_occup_year,
    t_cm_dinfo. as protectivemeasures_mark,
    '待定' as stop_mang_date,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as info_source_code,
    '待定' as info_source_name,
    '待定' as rpot_card_id,
    '待定' as comm_resp_dor_no,
    '待定' as comm_resp_dor_name,
    '待定' as now_mang_dor_no,
    '待定' as now_mang_dor_name,
    '待定' as now_mang_team_code,
    '待定' as now_mang_team_name,
    '待定' as dft_fu_dor_no,
    '待定' as dft_fu_dor_name,
    '待定' as build_cards_dor_no,
    '待定' as build_cards_dor_name,
    '待定' as build_cards_dept_code,
    '待定' as build_cards_dept_name,
    '待定' as build_cards_team_code,
    '待定' as build_cards_team_name,
    '待定' as build_cards_org_code,
    '待定' as build_cards_org_name,
    '待定' as build_cards_time,
    '待定' as obj_state_code,
    '待定' as obj_state_name,
    '待定' as addr_type_code,
    '待定' as addr_type_name,
    '待定' as addr_name,
    '待定' as residential_code,
    '待定' as residential_name,
    t_dwellerfile. as curr_addr_prov_code,
    字典. as curr_addr_prov_name,
    t_dwellerfile. as curr_addr_city_code,
    字典. as curr_addr_city_name,
    t_dwellerfile. as curr_addr_coty_code,
    字典. as curr_addr_coty_name,
    t_dwellerfile. as curr_addr_town_code,
    字典. as curr_addr_town_name,
    '待定' as curr_addr_comm_code,
    '待定' as curr_addr_comm_name,
    t_dwellerfile. as curr_addr_cotry_name,
    t_dwellerfile. as residential_housnum,
    '待定' as residential_addr,
    '待定' as poscode,
    t_cm_dinfo. as complication_code,
    字典. as complication_name,
    t_cm_dinfo. as hp_type_code,
    字典. as hp_type_name,
    '待定' as mang_level_code,
    '待定' as mang_level_name,
    '待定' as case_discussion,
    '待定' as discuss_reason,
    '待定' as discuss_rslt,
    '待定' as detail_mang,
    '待定' as detail_mang_no,
    '待定' as business_time,
    '待定' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    这个码表是啥 as asick_code,
    t_cm_dinfo. as asick_name,
    t_cm_dinfo. as udrug,
    这个码表是啥 as cstatus_code,
    t_cm_dinfo. as cstatus_name,
    t_cm_dinfo. as oxygentime,
    t_cm_dinfo. as fbedreason,
    t_cm_dinfo. as fbedcreatedata,
    t_cm_dinfo. as fbedcanceldata,
    t_cm_dinfo. as coalslong,
    t_cm_dinfo. as coalsmark,
    t_cm_dinfo. as family_smoke_mark,
    这个码表是啥 as cbehavior_code,
    t_cm_dinfo. as cbehavior_name,
    t_cm_dinfo. as mind_info_code,
    t_cm_dinfo. as mind_info_name,
    t_cm_dinfo. as stop_drink_age,
    t_cm_dinfo. as stop_drink_mark,
    t_cm_dinfo. as drunkenness_mark,
    这个码表是啥 as drink_type_code,
    t_cm_dinfo. as drink_type_name,
    t_cm_dinfo. as smok_info_code,
    t_cm_dinfo. as smok_info_name,
    这个码表是啥 as habits_diet_code,
    t_cm_dinfo. as habits_diet_name,
    t_cm_dinfo. as weeksport,
    t_cm_dinfo. as holdonsport,
    t_cm_dinfo. as sporttime,
    这个码表是啥 as sportfrequency_code,
    t_cm_dinfo. as sportfrequency_name,
    t_cm_dinfo. as sportdesc,
    t_cm_dinfo. as smok_day,
    t_cm_dinfo. as drnk_day,
    t_cm_dinfo. as paper_file_no,
    t_cm_dinfo. as soci_secu_cardno,
    t_cm_dinfo. as whtr_del,
    t_cm_dinfo. as filed_huma_no,
    t_cm_dinfo. as filed_huma_name,
    t_cm_dinfo. as whtr_our_hosp_cnfm,
    t_cm_dinfo. as cnfm_ins,
    t_cm_dinfo. as cnfm_dr_no,
    t_cm_dinfo. as cnfm_dr_name,
    t_cm_dinfo. as updt_emplo_no,
    t_cm_dinfo. as updt_emplo_name,
    t_cm_dinfo. as whtr_spec_mgt,
    t_cm_dinfo. as ctrl_whtr_satis,
    t_cm_dinfo. as info_psh_date,
    t_cm_dinfo. as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
T_CM_DINFO.DF_ID = T_DWELLERFILE.DF_ID
gender_code = SEX
occup_type_code = WORKSTATUS
T_CM_DINFO.CCL_ID = '1'
WHERE 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_assess_card partition(dt)
select 
    concat(control_uscid,evaid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as evaid,
    '待定' as hpid,
    t_cm_dinfo. as diag_class_code,
    字典映射？. as diag_class_name,
    '待定' as highest_diag_class_code,
    '待定' as highest_diag_class_name,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as highest_risk_factor_layer_code,
    '待定' as highest_risk_factor_layer_name,
    '待定' as risk_change_code,
    '待定' as risk_change_name,
    '待定' as bp_control_code,
    '待定' as bp_control_name,
    '待定' as aim_fu,
    '待定' as aim_fu_norm,
    '待定' as fu_total_year,
    t_mxjb_sf. as bldpresshigh,
    t_mxjb_sf. as dbp,
    '待定' as risk_factor_code,
    '待定' as danger_name,
    '待定' as target_organ_damage_code,
    '待定' as target_organ_damage_name,
    t_cm_dinfo. as complication_code,
    字典是啥？. as complication_name,
    t_dwellerfile. as height,
    t_dwellerfile. as weight,
    '待定' as serum_uric_acid,
    '待定' as check_time1,
    '待定' as check_org1,
    '待定' as bun2,
    '待定' as check_time2,
    '待定' as check_org2,
    '待定' as cr3,
    '待定' as check_time3,
    '待定' as check_org3,
    '待定' as pro4_code,
    '待定' as pro4_name,
    '待定' as check_date4,
    '待定' as check_org4,
    '待定' as albuminuria5,
    '待定' as check_date5,
    '待定' as check_org5,
    '待定' as c_reactive_protein6,
    '待定' as check_date6,
    '待定' as check_org6,
    '待定' as hs_crp7,
    '待定' as check_date7,
    '待定' as check_org7,
    '待定' as tg8,
    '待定' as check_date8,
    '待定' as check_org8,
    '待定' as ldl_c9,
    '待定' as check_date9,
    '待定' as check_org9,
    '待定' as hdl_c10,
    '待定' as check_date10,
    '待定' as check_org10,
    '待定' as tc11,
    '待定' as check_date11,
    '待定' as check_org11,
    '待定' as glu12,
    '待定' as check_date12,
    '待定' as check_org12,
    '待定' as check_fundus13,
    '待定' as check_date13,
    '待定' as check_org13,
    '待定' as carotid_artery_ultrasound14,
    '待定' as check_date14,
    '待定' as check_org14,
    '待定' as electrocardiography15,
    '待定' as check_date15,
    '待定' as check_org15,
    '待定' as chest_x_ray16,
    '待定' as check_date16,
    '待定' as check_org16,
    '待定' as echocardiogram17,
    '待定' as check_date17,
    '待定' as check_org17,
    '待定' as eval_year,
    '待定' as eval_code,
    '待定' as eval_name,
    '待定' as eval_org_code,
    '待定' as assessment_date,
    '待定' as eval_dept_code,
    '待定' as eval_dept_name,
    '待定' as eval_team_code,
    '待定' as eval_team_name,
    '待定' as business_time,
    '待定' as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
评分卡用哪个表当主表？？？？
) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_followup_card partition(dt)
select 
    concat(control_uscid,fu_hpid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as control_uscid,
    '待定' as fu_hpid,
    '待定' as hpid,
    t_mxjb_sf. as fu_hp_code,
    '待定' as risk_factor_layer_code,
    '待定' as risk_factor_layer_name,
    '待定' as hp_mang_group_code,
    '待定' as hp_mang_group_name,
    '待定' as group_dscr_code,
    '待定' as group_dscr_name,
    t_mxjb_sf. as visit_way_code,
    字典. as visit_way_name,
    '待定' as fu_mang_code,
    '待定' as fu_mang_name,
    '待定' as loss_fu_code,
    '待定' as loss_fu_name,
    '待定' as now_symptom_code,
    '待定' as now_symptom_name,
    '待定' as reger_no,
    '待定' as reger_name,
    '待定' as non_druggery_treatment_code,
    '待定' as non_druggery_treatment_name,
    '待定' as medication_compliance_code,
    '待定' as medication_compliance_name,
    '待定' as medicine_irreg_code,
    '待定' as medicine_irreg_name,
    '待定' as non_medicine_code,
    '待定' as non_medicine_name,
    t_mxjb_sf. as dbp,
    t_mxjb_sf. as bldpresshigh,
    t_dwellerfile. as height,
    t_dwellerfile. as weight,
    t_mxjb_sf. as aim_wt,
    t_mxjb_sf. as bmi,
    t_mxjb_sf. as aim_bmi,
    t_cm_dinfo. as sportdesc,
    t_cm_dinfo. as sport_freq_code,
    字典在哪里找. as sport_freq_name,
    '待定' as aim_sport_freq_code,
    '待定' as aim_sport_freq_name,
    '待定' as sport_min_length,
    '待定' as aim_sport_duration,
    t_mxjb_sf. as heart_rate,
    '待定' as positive_names,
    t_mxjb_sf. as smok_day,
    t_mxjb_sf. as tagt_day_smoke_value,
    t_mxjb_sf. as drnk_day,
    '待定' as salt_level_code,
    '待定' as salt_level_name,
    '待定' as aim_salt_level_code,
    '待定' as aim_salt_level_name,
    '待定' as fu_diet_type_code,
    '待定' as fu_diet_type_name,
    '待定' as psycho_adjust_res_code,
    '待定' as psycho_adjust_res_name,
    t_mxjb_sf. as fu_compliance_res_code,
    字典在哪里找. as fu_compliance_res_name,
    t_mxjb_sf. as drug_dys_mark,
    t_mxjb_sf. as drug_dys_dscr,
    '待定' as complication_code,
    '待定' as complication_name,
    '待定' as health_edu_mark,
    '待定' as advices_code,
    '待定' as advices_name,
    '待定' as accept_code,
    '待定' as accept_name,
    '待定' as fu_eval_res_code,
    '待定' as fu_eval_res_name,
    '待定' as fu_doc_no,
    '待定' as fu_doc_name,
    t_mxjb_sf. as visit_date,
    t_mxjb_sf. as next_follow_date,
    '待定' as fu_org_code,
    '待定' as fu_dept_code,
    '待定' as fu_dept_name,
    '待定' as fu_team_code,
    '待定' as fu_team_name,
    t_dwellerfile. as duty_dor_no,
    '待定' as duty_dor_name,
    t_mxjb_sf. as referral_reason,
    '待定' as accept_org_name,
    '待定' as accept_dept_code,
    '待定' as referral_dept_name,
    t_mxjb_sf. as business_time,
    case when ZHXGSJ is null then '0' else '1' end as state,
    '待定' as data_rank,
    '待定' as reserve1,
    '待定' as reserve2,
    t_mxjb_sf. as hetat_plan,
    t_mxjb_sf. as stap_food_info,
    t_mxjb_sf. as stap_food_plan,
    t_mxjb_sf. as empt_stom_blgval,
    t_mxjb_sf. as glyc_hemog,
    t_mxjb_sf. as exam_date,
    t_mxjb_sf. as oth_asst_exam,
    字典在哪里找 as hypo_react_code,
    t_mxjb_sf. as hypo_react_name,
    t_mxjb_sf. as yard_exte_dr_name,
    t_mxjb_sf. as insn,
    t_mxjb_sf. as insn_used_and_dos,
    t_mxjb_sf. as refl_memo,
    t_mxjb_sf. as after_meal_bloo_gluc,
    t_mxjb_sf. as patient_type_code,
    t_mxjb_sf. as patient_type_name,
    字典在哪里找 as sput_bacteria_code,
    t_mxjb_sf. as sput_bacteria_name,
    字典在哪里找 as drug_resi_code,
    t_mxjb_sf. as drug_resi_name,
    字典在哪里找 as eval_type_code,
    t_mxjb_sf. as eval_type_name,
    t_mxjb_sf. as alon_of_live_room,
    字典在哪里找 as vent_situ_code,
    t_mxjb_sf. as vent_situ_name,
    t_mxjb_sf. as take_medi_loc,
    t_mxjb_sf. as take_medi_time,
    t_mxjb_sf. as dose_reco_card_of_fillin,
    t_mxjb_sf. as dose_mtd_wth_drug_stor,
    t_mxjb_sf. as tuber_trt_cour_trea,
    t_mxjb_sf. as no_regu_dose_hazr,
    t_mxjb_sf. as dose_new_defs_wth_dspo,
    t_mxjb_sf. as trt_cose_flup_sput,
    t_mxjb_sf. as out_cose_how_adhe_dose,
    t_mxjb_sf. as habi_wth_mnan,
    t_mxjb_sf. as clos_cont_the_exam,
    t_mxjb_sf. as ctrl_whtr_satis,
    字典在哪里找 as interface_mark_code,
    t_mxjb_sf. as interface_mark_name,
    字典在哪里找 as data_source_code,
    t_mxjb_sf. as data_source_name,
    t_mxjb_sf. as equipcode,
    t_mxjb_sf. as urin_rout,
    t_mxjb_sf. as fund,
    t_mxjb_sf. as neuro_chng,
    t_mxjb_sf. as adequ_dors_arter,
    t_mxjb_sf. as cop_exam,
    t_mxjb_sf. as hl_guid,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t
where MXJBBZ = '1'
t_mxjb_sf.REF_ID = t_dwellerfile.DF_ID
t_mxjb_sf.REF_ID = t_cm_dinfo.DF_ID
) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_risk_manage_card partition(dt)
select 
    concat(unified_uscid,hp_pdp_mang_card,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as hp_pdp_mang_card,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as occup_code,
    null as occup_name,
    null as weight,
    null as height,
    null as pdp_riskfactor_code,
    null as pdp_riskfactor_name,
    null as family_his_code,
    null as family_his_name,
    null as past_dis_his_code,
    null as past_dis_his_name,
    null as smoke_code,
    null as smoke_name,
    null as stop_smoke_time,
    null as start_smoke_age,
    null as drnk_frqu_code,
    null as drnk_frqu_name,
    null as overdrink_mark,
    null as sport_rate_code,
    null as sport_rate_name,
    null as bldpresshigh,
    null as dbp,
    null as info_source_code,
    null as info_source_name,
    null as now_mang_org_code,
    null as now_mang_org_name,
    null as now_mang_dor_no,
    null as now_mang_dor_name,
    null as now_mang_team_code,
    null as now_mang_team_name,
    null as build_cards_org_code,
    null as build_cards_dor_no,
    null as build_cards_dor_name,
    null as build_cards_time,
    null as dft_fu_dor_no,
    null as dft_fu_dor_name,
    null as build_cards_dept_code,
    null as build_cards_dept_name,
    null as build_cards_team_code,
    null as build_cards_team_name,
    null as obj_state_code,
    null as obj_state_name,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as addr,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_risk_followup_card partition(dt)
select 
    concat(unified_uscid,hp_pdp_fu_card,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as hp_pdp_fu_card,
    null as hp_pdp_mang_card,
    null as visit_way_code,
    null as visit_way_name,
    null as fu_mang_code,
    null as fu_mang_name,
    null as loss_fu_code,
    null as loss_fu_name,
    null as pdp_riskfactor_code,
    null as pdp_riskfactor_name,
    null as weight,
    null as height,
    null as bldpresshigh,
    null as dbp,
    null as fu_doc_no,
    null as fu_doc_name,
    null as fu_hospital_code,
    null as visit_date,
    null as next_follow_date,
    null as fu_dept_code,
    null as fu_dept_name,
    null as fu_team_id,
    null as fu_team_name,
    null as duty_dor_no,
    null as duty_dor_name,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_auxiliary_exam partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_hpid,
    null as assist_exam_proj,
    null as asst_exam_rslt,
    null as check_dor_name,
    null as check_dor_no,
    null as check_date,
    null as check_org_code,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_followup_med_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_hpid,
    null as drug_name_code,
    null as drug_name,
    null as drug_type_code,
    null as drug_type_name,
    null as drug_use_per_dose,
    null as rfd,
    null as drug_dose_unit,
    null as drug_used_idose,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_used_frqu,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    null as business_time,
    null as take_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_followup_tcm_info partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_card,
    null as syndrome_code,
    null as syndrome_name,
    null as guid_measures_code,
    null as guid_measures_name,
    null as other_guid_code,
    null as other_guid_name,
    null as other_dscr,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmh_35yo_initial_review partition(dt)
select 
    concat(report_hp_card,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as report_hp_card,
    null as unified_uscid,
    null as report_hp_type_card_code,
    null as report_hp_type_card_name,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as rpt_date,
    null as report_sbp,
    null as rpot_dbp,
    null as report_doctor_no,
    null as report_name,
    null as rpt_dept_code,
    null as rpt_dept_name,
    null as whether_hp_mark,
    null as first_hp_mark,
    null as cnfm_hp,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_report_card partition(dt)
select 
    concat(rpt_org_uscid,rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_org_uscid,
    null as rpot_card_id,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as occup_code,
    null as occup_name,
    null as dm_type_code,
    null as dm_type_name,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as cnfm_date,
    null as reporter_id,
    null as report_name,
    null as rpt_date,
    null as rpt_dept_code,
    null as rpt_dept_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_manage_card partition(dt)
select 
    concat(control_uscid,dmid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as control_uscid,
    null as dmid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as tel,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as brdy,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as height,
    null as weight,
    null as edu_background_code,
    null as edu_background_name,
    null as occup_code,
    null as occup_name,
    null as payment_term_code,
    null as payment_term_name,
    null as dm_family_his,
    null as dm_type_code,
    null as dm_type_name,
    null as cnfm_date,
    null as case_source_code,
    null as case_source_name,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as rpot_card_id,
    null as obj_state_code,
    null as obj_state_name,
    null as curr_duty_dor_no,
    null as curr_duty_dor_name,
    null as now_mang_team_code,
    null as now_mang_team_name,
    null as dft_fu_dor_no,
    null as dft_fu_dor_name,
    null as build_cards_org_code,
    null as build_org_name,
    null as build_cards_dor_no,
    null as build_cards_dor_name,
    null as build_cards_time,
    null as build_cards_dept_code,
    null as build_cards_dept_name,
    null as build_cards_team_name,
    null as build_cards_team_id,
    null as addr_type_code,
    null as addr_type_name,
    null as addr_name,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as residential_addr,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_cm_dinfo. as asick_code,
    t_cm_dinfo. as asick_name,
    t_cm_dinfo. as udrug,
    t_cm_dinfo. as cstatus_code,
    t_cm_dinfo. as cstatus_name,
    t_cm_dinfo. as oxygentime,
    t_cm_dinfo. as fbedreason,
    t_cm_dinfo. as fbedcreatedata,
    t_cm_dinfo. as fbedcanceldata,
    t_cm_dinfo. as coalslong,
    t_cm_dinfo. as coalsmark,
    t_cm_dinfo. as family_smoke_mark,
    t_cm_dinfo. as cbehavior_code,
    t_cm_dinfo. as cbehavior_name,
    t_cm_dinfo. as mind_info_code,
    t_cm_dinfo. as mind_info_name,
    t_cm_dinfo. as stop_drink_age,
    t_cm_dinfo. as stop_drink_mark,
    t_cm_dinfo. as drunkenness_mark,
    t_cm_dinfo. as drink_type_code,
    t_cm_dinfo. as drink_type_name,
    t_cm_dinfo. as smok_info_code,
    t_cm_dinfo. as smok_info_name,
    t_cm_dinfo. as habits_diet_code,
    t_cm_dinfo. as habits_diet_name,
    t_cm_dinfo. as weeksport,
    t_cm_dinfo. as holdonsport,
    t_cm_dinfo. as sporttime,
    t_cm_dinfo. as sportfrequency_code,
    t_cm_dinfo. as sportfrequency_name,
    t_cm_dinfo. as sportdesc,
    t_cm_dinfo. as smok_day,
    t_cm_dinfo. as drnk_day,
    t_cm_dinfo. as paper_file_no,
    t_cm_dinfo. as soci_secu_cardno,
    t_cm_dinfo. as whtr_del,
    t_cm_dinfo. as filed_huma_no,
    t_cm_dinfo. as filed_huma_name,
    t_cm_dinfo. as whtr_our_hosp_cnfm,
    t_cm_dinfo. as cnfm_ins,
    t_cm_dinfo. as cnfm_dr_no,
    t_cm_dinfo. as cnfm_dr_name,
    t_cm_dinfo. as updt_emplo_no,
    t_cm_dinfo. as updt_emplo_name,
    t_cm_dinfo. as whtr_spec_mgt,
    t_cm_dinfo. as ctrl_whtr_satis,
    t_cm_dinfo. as info_psh_date,
    t_cm_dinfo. as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_followup_card partition(dt)
select 
    concat(control_uscid,fu_dmid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as control_uscid,
    null as fu_dmid,
    null as dmid,
    null as fu_mang_code,
    null as fu_mang_name,
    null as loss_fu_code,
    null as loss_fu_name,
    null as next_follow_date,
    null as visit_way_code,
    null as visit_way_name,
    null as fu_eval_res_code,
    null as fu_eval_res_name,
    null as dm_clinical_symptoms_code,
    null as clinical_symptoms_name,
    null as foot_dorsal_artery_code,
    null as foot_dorsal_artery_name,
    null as dm_family_his,
    null as regular_activities,
    null as regular_activities_type_code,
    null as regular_activities_type_name,
    null as sport_freq_code,
    null as sport_freq_name,
    null as aim_sport_freq_code,
    null as aim_sport_freq_name,
    null as sport_duration,
    null as aim_sport_duration,
    null as height,
    null as weight,
    null as aim_wt,
    null as bmi,
    null as aim_bmi,
    null as daily_staple_num,
    null as aim_daily_staple_num,
    null as diet_info_code,
    null as diet_info_name,
    null as bldpresshigh,
    null as dbp,
    null as waist_cm,
    null as hip,
    null as waist_hip_whr,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as hba1c,
    null as tc,
    null as hdl_c,
    null as ldl_c,
    null as tg,
    null as acr,
    null as ndbdl,
    null as take_medicine_code,
    null as take_medicine_name,
    null as drug_dys_mark,
    null as drug_dys_dscr,
    null as smok_day,
    null as aim_day_smoke_value,
    null as drnk_day,
    null as other_sign_dscr,
    null as psycho_adjust_res_code,
    null as psycho_adjust_res_name,
    null as fu_compliance_res_code,
    null as fu_compliance_res_name,
    null as referral_reason,
    null as accept_org_name,
    null as accept_dept_name,
    null as fu_eval_prop_code,
    null as fu_eval_prop_name,
    null as fu_doc_no,
    null as fu_doc_name,
    null as duty_dor_no,
    null as duty_dor_name,
    null as visit_date,
    null as fu_dept_code,
    null as fu_dept_name,
    null as fu_team_id,
    null as fu_team_name,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_mxjb_sf. as hetat_plan,
    t_mxjb_sf. as stap_food_info,
    t_mxjb_sf. as stap_food_plan,
    t_mxjb_sf. as empt_stom_blgval,
    t_mxjb_sf. as glyc_hemog,
    t_mxjb_sf. as exam_date,
    t_mxjb_sf. as oth_asst_exam,
    t_mxjb_sf. as hypo_react_code,
    t_mxjb_sf. as hypo_react_name,
    t_mxjb_sf. as yard_exte_dr_name,
    t_mxjb_sf. as insn,
    t_mxjb_sf. as insn_used_and_dos,
    t_mxjb_sf. as refl_memo,
    t_mxjb_sf. as after_meal_bloo_gluc,
    t_mxjb_sf. as patient_type_code,
    t_mxjb_sf. as patient_type_name,
    t_mxjb_sf. as sput_bacteria_code,
    t_mxjb_sf. as sput_bacteria_name,
    t_mxjb_sf. as drug_resi_code,
    t_mxjb_sf. as drug_resi_name,
    t_mxjb_sf. as eval_type_code,
    t_mxjb_sf. as eval_type_name,
    t_mxjb_sf. as alon_of_live_room,
    t_mxjb_sf. as vent_situ_code,
    t_mxjb_sf. as vent_situ_name,
    t_mxjb_sf. as take_medi_loc,
    t_mxjb_sf. as take_medi_time,
    t_mxjb_sf. as dose_reco_card_of_fillin,
    t_mxjb_sf. as dose_mtd_wth_drug_stor,
    t_mxjb_sf. as tuber_trt_cour_trea,
    t_mxjb_sf. as no_regu_dose_hazr,
    t_mxjb_sf. as dose_new_defs_wth_dspo,
    t_mxjb_sf. as trt_cose_flup_sput,
    t_mxjb_sf. as out_cose_how_adhe_dose,
    t_mxjb_sf. as habi_wth_mnan,
    t_mxjb_sf. as clos_cont_the_exam,
    t_mxjb_sf. as ctrl_whtr_satis,
    t_mxjb_sf. as interface_mark_code,
    t_mxjb_sf. as interface_mark_name,
    t_mxjb_sf. as data_source_code,
    t_mxjb_sf. as data_source_name,
    t_mxjb_sf. as equipcode,
    t_mxjb_sf. as urin_rout,
    t_mxjb_sf. as fund,
    t_mxjb_sf. as neuro_chng,
    t_mxjb_sf. as adequ_dors_arter,
    t_mxjb_sf. as cop_exam,
    t_mxjb_sf. as hl_guid,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_igr_report_card partition(dt)
select 
    concat(unified_uscid,abnormal_glucose_regulation_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as abnormal_glucose_regulation_no,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as cert_code,
    null as cert_name,
    null as abnoglu_type_code,
    null as abnoglu_type_name,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as cnfm_date,
    null as reporter_id,
    null as report_name,
    null as rpt_date,
    null as rpt_dept_code,
    null as rpt_dept_name,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_igr_manage_card partition(dt)
select 
    concat(unified_uscid,abnoglu_regulation_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as abnoglu_regulation_id,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as abnoglu_type_code,
    null as abnoglu_type_name,
    null as cnfm_date,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as manage_card_no,
    null as obj_state_code,
    null as obj_state_name,
    null as now_mang_org_code,
    null as now_mang_org_name,
    null as curr_duty_dor_no,
    null as curr_duty_dor_name,
    null as now_mang_team_code,
    null as now_mang_team_name,
    null as dft_fu_dor_no,
    null as dft_fu_dor_name,
    null as build_cards_org_code,
    null as build_cards_dor_no,
    null as build_cards_dor_name,
    null as build_cards_time,
    null as build_cards_dept_code,
    null as build_cards_dept_name,
    null as build_cards_team_name,
    null as build_cards_team_id,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_igr_followup_card partition(dt)
select 
    concat(unified_uscid,fu_abngluc_regulation_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as fu_abngluc_regulation_id,
    null as abnoglu_regulation_id,
    null as visit_way_code,
    null as visit_way_name,
    null as fu_mang_code,
    null as fu_mang_name,
    null as loss_fu_code,
    null as loss_fu_name,
    null as turn_group_code,
    null as turn_group_name,
    null as next_follow_date,
    null as dm_clinical_symptoms_code,
    null as dm_clinical_symptoms_name,
    null as now_symptom_name,
    null as foot_dorsal_artery_code,
    null as foot_dorsal_artery_name,
    null as hypo_react_code,
    null as hypo_react_name,
    null as dm_family_his,
    null as regular_activities,
    null as regular_activities_type_code,
    null as regular_activities_type_name,
    null as sport_freq_code,
    null as sport_freq_name,
    null as aim_sport_freq_code,
    null as aim_sport_freq_name,
    null as sport_duration,
    null as aim_sport_duration,
    null as height,
    null as weight,
    null as aim_wt,
    null as bmi,
    null as aim_bmi,
    null as daily_staple_num,
    null as aim_daily_staple_num,
    null as diet_info_code,
    null as diet_info_name,
    null as bldpresshigh,
    null as dbp,
    null as waist_cm,
    null as hip,
    null as waist_hip_whr,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as hba1c,
    null as tc,
    null as hdl_c,
    null as ldl_c,
    null as tg,
    null as acr,
    null as ndbdl,
    null as take_medicine_code,
    null as take_medicine_name,
    null as drug_dys_mark,
    null as drug_dys_dscr,
    null as smok_day,
    null as aim_day_smoke_value,
    null as drnk_day,
    null as other_positive_names,
    null as psycho_adjust_res_code,
    null as psycho_adjust_res_name,
    null as fu_compliance_res_code,
    null as fu_compliance_res_name,
    null as fu_eval_res_code,
    null as fu_eval_res_name,
    null as referral_reason,
    null as accept_org_name,
    null as accept_dept_name,
    null as fu_eval_prop_code,
    null as fu_eval_prop_name,
    null as fu_doc_no,
    null as fu_doc_name,
    null as duty_dor_no,
    null as duty_dor_name,
    null as fu_hospital_code,
    null as visit_date,
    null as fu_dept_code,
    null as fu_dept_name,
    null as fu_team_id,
    null as fu_team_name,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_high_mana_card partition(dt)
select 
    concat(unified_uscid,bm_mang_cardid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as bm_mang_cardid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as dm_family_his,
    null as diab_risk_code,
    null as diab_risk_name,
    null as fbg_value,
    null as fbg_check_type_code,
    null as fbg_check_type_name,
    null as rbg_value,
    null as rbg_check_type_code,
    null as rbg_check_type_name,
    null as glucose_tolerance_value,
    null as gtct_code,
    null as gtct_name,
    null as pbg_value,
    null as pbg_check_type_code,
    null as pbg_check_type_name,
    null as reporter_id,
    null as report_name,
    null as rpt_date,
    null as rpt_dept_code,
    null as rpt_dept_name,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_aux_exam partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_dmid,
    null as assist_exam_proj,
    null as asst_exam_rslt,
    null as check_dor_name,
    null as check_dor_no,
    null as check_date,
    null as check_org_code,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_complication partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_dmid,
    null as dmid,
    null as disease_cause_code,
    null as disease_cause_name,
    null as cnfm_date,
    null as diag_org_code,
    null as diag_org_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_followup_med_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_dmid,
    null as dmid,
    null as drug_name_code,
    null as drug_name,
    null as drug_type_code,
    null as drug_type_name,
    null as drug_use_per_dose,
    null as rfd,
    null as drug_dose_unit,
    null as drug_used_idose,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_used_frqu,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    null as business_time,
    null as employ_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmd_followup_tcm_info partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as fu_type_code,
    null as fu_type_name,
    null as fu_card,
    null as dmid,
    null as syndrome_code,
    null as syndrome_name,
    null as symptoms_dscr,
    null as guid_measures_code,
    null as guid_measures_name,
    null as other_guid_code,
    null as other_guid_name,
    null as other_dscr,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_report_card partition(dt)
select 
    concat(org_uscid,ca_rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as org_uscid,
    null as ca_rpot_card_id,
    null as personal_health_no,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as patient_name,
    null as gender_code,
    null as gender_name,
    null as nation_code,
    null as nation_name,
    null as brdy,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as job_type,
    null as occup_code,
    null as occup_name,
    null as empr_name,
    null as e_mail,
    null as tel_home,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as resd_addr,
    null as curr_address,
    null as curr_poscode,
    null as residence_migration_time,
    null as otp_no,
    null as ipt_no,
    null as icd9_code,
    null as icd10_code,
    null as icdo_code,
    null as icdom_code,
    null as sixth_code,
    null as demand_will_code,
    null as demand_will_name,
    null as inform_patient_code,
    null as tumor_diag_name,
    null as diag_part_code,
    null as diag_part_name,
    null as palg_no,
    null as pathological_type,
    null as reprot_coty_code,
    null as reprot_coty_name,
    null as rpt_dept_name,
    null as tnm_non_determine_mark,
    null as diagnose_phase_t_code,
    null as diagnose_phase_t_name,
    null as diagnose_phase_n_code,
    null as diagnose_phase_n_name,
    null as diagnose_phase_m_code,
    null as diagnose_phase_m_name,
    null as clinical_stage_code,
    null as clinical_stage_name,
    null as first_diag_time,
    null as cnfm_date,
    null as diag_accord_code,
    null as diag_accord_name,
    null as death_date,
    null as die_reason_code,
    null as die_reason_name,
    null as rpt_org_uscid,
    null as rpt_org_name,
    null as report_org_class_code,
    null as report_org_class_name,
    null as report_dor_no,
    null as report_dor_name,
    null as report_dor_type_code,
    null as report_dor_type_name,
    null as rpt_date,
    null as omit_mark,
    null as dist_dise_con_check_date,
    null as input_org_code,
    null as input_dor_no,
    null as input_dor_name,
    null as input_date,
    null as input_dept_code,
    null as input_dept_name,
    null as remark,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_initial_visit_card partition(dt)
select 
    concat(ca_card,control_uscid,ca_rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ca_card,
    null as control_uscid,
    null as ca_rpot_card_id,
    null as resd_addr_check_mark,
    null as resd_addr_check_code,
    null as resd_addr_check_name,
    null as residential_check_mark,
    null as residential_check_code,
    null as residential_check_name,
    null as paper_card_flag,
    null as height,
    null as smoke_code,
    null as smoke_name,
    null as passive_smoking_place_code,
    null as passive_smoking_place_name,
    null as start_smoke_age,
    null as stop_smoke_age,
    null as smoke_total_years,
    null as daily_smoke_num,
    null as first_sympt_date,
    null as initial_diag_time,
    null as fist_proc_hospital,
    null as ipt_no,
    null as palg_no,
    null as first_oper_time,
    null as tumor_operat_nature_code,
    null as tumor_operat_nature_name,
    null as family_tumor_his_mark,
    null as family_tumor_his_code,
    null as family_tumor_his_name,
    null as family_tumor_his_type_code,
    null as family_tumor_his_type_name,
    null as first_org_code,
    null as frist_dor_no,
    null as frist_dor_name,
    null as frist_date,
    null as input_org_code,
    null as input_org_name,
    null as input_dor_no,
    null as input_dor_name,
    null as input_date,
    null as input_dept_code,
    null as input_dept_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_treatment_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as ca_card,
    null as ca_clinical_code,
    null as ca_clinical_name,
    null as clinical_date,
    null as clinical_org_code,
    null as clinical_org_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_followup_card partition(dt)
select 
    concat(control_uscid,fu_ca_card,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as control_uscid,
    null as fu_ca_card,
    null as paper_card_flag,
    null as ca_rpot_card_id,
    null as follow_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as treat_case_code,
    null as treat_case_name,
    null as now_case_code,
    null as now_case_name,
    null as weight,
    null as height,
    null as content_code,
    null as content_name,
    null as death_mark_code,
    null as death_mark_name,
    null as death_date,
    null as die_reason_code,
    null as die_reason_name,
    null as deathplace_type_code,
    null as deathplace_type_name,
    null as ca_um_mark_code,
    null as ca_um_mark_name,
    null as um_date,
    null as um_reason_code,
    null as um_reason_name,
    null as um_dscr,
    null as karnofsky_score_num,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as fu_doc_no,
    null as fu_doc_name,
    null as visit_date,
    null as next_follow_date,
    null as remark,
    null as input_org_code,
    null as input_dor_no,
    null as input_dor_name,
    null as input_date,
    null as input_dept_code,
    null as input_dept_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_followup_med_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_ca_card,
    null as drug_name_code,
    null as drug_name,
    null as drug_type_code,
    null as drug_use_per_dose,
    null as rfd,
    null as drug_dose_unit,
    null as drug_used_idose,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_used_frqu,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_recurrence_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_ca_card,
    null as recent_recur_time,
    null as recurrent,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmt_metastasis_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_ca_card,
    null as transfer_date,
    null as tumor_pat_metast_part,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmp_manage_card partition(dt)
select 
    concat(psycho_card,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.id0000 as psycho_card,
    '待定' as control_uscid,
    t_dwellerfile.sscardno as card_no,
    '0' as card_type_code,
    '社保卡' as card_type_name,
    null as medcasno,
    to_date(t.tbtime,'%Y%m%d') as build_card_date,
    t_dwellerfile.name as full_name,
    t_dwellerfile.sex as gender_code,
    d_gender_name.dic_val_name as gender_name,
    to_date(t_dwellerfile.birthday,'%Y%m%d') as brdy,
    t_dwellerfile.cdegree as edu_background_code,
    d_edu_background_name.dic_val_name as edu_background_name,
    t_dwellerfile.rprtype as resd_natu_code,
    d_resd_natu_name.dic_val_name as resd_natu_name,
    t.jyqk00 as employmentstatus_code,
    d_employmentstatus_name.dic_val_name as employmentstatus_name,
    t_dwellerfile.mstatus as mrg_stas_code,
    d_mrg_stas_name.dic_val_name as mrg_stas_name,
    t_dwellerfile.folk as nation_code,
    d_nation_name.dic_val_name as nation_name,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t_dwellerfile. as certno,
    null as rescue_source_code,
    null as rescue_source_name,
    null as medfee_paymtd_code,
    null as medfee_paymtd_name,
    '待定' as mang_type_code,
    '待定' as mang_type_name,
    t. as duty_doc_no,
    null as duty_doc_name,
    null as resp_dor_tel,
    t. as guardian_name,
    d_guardian_relation_code.dic_val_code as guardian_relation_code,
    t. as guardian_relation_name,
    t. as guardian_addr,
    t. as guardian_telephone,
    null as committee_name,
    t. as committee_coner_name,
    t. as committee_telephone,
    null as seve_ment_diso_sort_code,
    null as seve_ment_diso_sort_name,
    null as seve_ment_diso_fami_hist,
    to_date(t.ccfbtime,'%Y%m%d') as first_disease_time,
    d_major_psyc_symp_code.dic_val_code as major_psyc_symp_code,
    t. as major_psyc_symp_name,
    t. as major_psyc_symp_dscr,
    t. as outp_trea_code,
    d_outp_trea_name.dic_val_name as outp_trea_name,
    null as psychiatric_hos_num,
    null as recently_ment_illn_diag_code,
    t. as recently_ment_illn_diag_name,
    to_date(t.qztime,'%Y%m%d') as cnfm_date,
    t. as conf_diag_org_name,
    null as conf_diag_org_code,
    null as trea_effe_cate_code,
    null as trea_effe_cate_name,
    null as risk_level_code,
    null as risk_level_name,
    null as anti_medi_sign,
    to_date(t.sczlsj,'%Y%m%d') as first_anti_trea_time,
    t. as latest_treat_effect_code,
    '待定' as latest_treat_effect_name,
    t. as mild_trouble_num,
    t. as cause_trouble_num,
    t. as trouble_num,
    t. as self_injury_num,
    t. as atte_suic_num,
    t. as other_harm_beha_num,
    t. as inform_agree_manage_mark,
    t. as inform_agree_nameer_name,
    t. as inform_agree_name_date,
    t. as economy_status_code,
    d_economy_status_name.dic_val_name as economy_status_name,
    t. as speist_dor_ad,
    t. as locked_case_code,
    d_locked_case_name.dic_val_name as locked_case_name,
    null as lock_start_date,
    null as unlock_start_date,
    t. as reger_no,
    t. as reger_name,
    '待定' as build_cards_org_code,
    '待定' as build_org_name,
    null as addr_districts_code,
    null as addr_districts_name,
    null as curr_addr_prov_code,
    t_dwellerfile. as curr_addr_prov_name,
    null as curr_addr_city_code,
    t_dwellerfile. as curr_addr_city_name,
    null as curr_addr_coty_code,
    t_dwellerfile. as curr_addr_coty_name,
    null as curr_addr_town_code,
    t_dwellerfile. as curr_addr_town_name,
    null as curr_addr_comm_code,
    t_dwellerfile. as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    t_dwellerfile. as residential_housnum,
    null as poscode,
    null as weight,
    null as code_686,
    null as name_686,
    t. as business_time,
    case t.isdel0 when '0' then '1' when '1' then '0' end as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t. as past_hospi_info,
    t. as otp_dr_name,
    t. as soci_secu_cardno_mark,
    case t.isdel0 when '0' then '1' when '1' then '0' end as whtr_del,
    t. as diag_icd10_code,
    default_update_time as upload_time,
    to_timestamp(t.edate) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t. as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' end as deleted,
    case t.isdel0 when '0' then to_timestamp(t.edate) end as deleted_time
from zl3500000000000004_syyzw.t_zxjsjb_info as t
left join t_dwellerfile on t_dwellerfile.df_id=t.df_id
left join data_dic_a as d_gender_name on d_gender_name.dic_type_code='GENDER_CODE' and d_gender_name.dic_val_code=t.sex
left join data_dic_a as d_edu_background_name on d_edu_background_name.dic_type_code='EDU_BACKGROUND_CODE' and d_edu_background_name.dic_val_code=t.cdegree
left join data_dic_a as d_resd_natu_name on d_resd_natu_name.dic_type_code='RESD_NATU_CODE' and d_resd_natu_name.dic_val_code=t.rprtype
left join data_dic_a as d_employmentstatus_name on d_employmentstatus_name.dic_type_code='EMPLOYMENTSTATUS_CODE' and d_employmentstatus_name.dic_val_code=t.jyqk00
left join data_dic_a as d_mrg_stas_name on d_mrg_stas_name.dic_type_code='MRG_STAS_CODE' and d_mrg_stas_name.dic_val_code=t.mstatus
left join data_dic_a as d_nation_name on d_nation_name.dic_type_code='NATION_CODE' and d_nation_name.dic_val_code=t.folk
left join data_dic_a as d_guardian_relation_code on d_guardian_relation_code.dic_type_code='GUARDIAN_RELATION_CODE' and d_guardian_relation_code.dic_val_name=t.rel
left join data_dic_a as d_major_psyc_symp_code on d_major_psyc_symp_code.dic_type_code='MAJOR_PSYC_SYMP_CODE' and d_major_psyc_symp_code.dic_val_name=t.jwszz0
left join data_dic_a as d_outp_trea_name on d_outp_trea_name.dic_type_code='OUTP_TREA_CODE' and d_outp_trea_name.dic_val_code=t.jwmzzlqk
left join data_dic_a as d_economy_status_name on d_economy_status_name.dic_type_code='ECONOMY_STATUS_CODE' and d_economy_status_name.dic_val_code=t.jjzk00
left join data_dic_a as d_locked_case_name on d_locked_case_name.dic_type_code='LOCKED_CASE_CODE' and d_locked_case_name.dic_val_code=t.gsqk
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_dmp_followup_form partition(dt)
select 
    concat(fu_psycho_code,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t. as fu_psycho_code,
    '待定' as control_uscid,
    t. as psycho_card,
    null as case_manage_logo,
    null as the_patient_cond_code,
    null as the_patient_cond_name,
    null as pati_soci_func_revi_code,
    null as pati_soci_func_revi_name,
    case t.sfyy00 is not null then '0' else '1' end as visit_sign,
    t. as visit_way_code,
    d_visit_way_name.dic_val_name as visit_way_name,
    t. as loss_fu_code,
    d_loss_fu_name.dic_val_name as loss_fu_name,
    to_date(t.swsj00,'%Y%m%d') as death_date,
    null as die_reason_code,
    null as die_reason_name,
    t_dwellerfile. as full_name,
    '待定' as fu_doctor_name,
    t. as fu_doctor_no,
    null as now_symptom_code,
    null as now_symptom_name,
    null as treatment_form_code,
    null as treatment_form_name,
    null as recovery_code,
    null as recovery_name,
    t. as risk_level_code,
    d_risk_type_name.dic_val_name as risk_level_name,
    t. as risk_type_code,
    d_risk_type_name.dic_val_name as risk_type_name,
    t. as personal_life_code,
    d_personal_life_name.dic_val_name as personal_life_name,
    t. as housework_code,
    d_housework_name.dic_val_name as housework_name,
    t. as prod_labor_and_work_code,
    d_prod_labor_and_work_name.dic_val_name as prod_labor_and_work_name,
    t. as learning_ability_code,
    d_learning_ability_name.dic_val_name as learning_ability_name,
    t. as soci_inte_comm_code,
    d_soci_inte_comm_name.dic_val_name as soci_inte_comm_name,
    t. as mild_trouble_num,
    t. as cause_trouble_num,
    t. as trouble_num,
    t. as self_injury_num,
    t. as atte_suic_num,
    t. as other_harm_beha_num,
    t. as hosp_betwe_two_fu_code,
    d_hosp_betwe_two_fu_name.dic_val_name as hosp_betwe_two_fu_name,
    null as patn_ipt_cnt,
    null as ment_illn_diag_code,
    null as ment_illn_diag_name,
    null as treatment_code,
    null as treatment_name,
    null as timeliness_code,
    null as timeliness_name,
    null as way_of_treatment_code,
    null as way_of_treatment_name,
    null as outp_spec_hosp,
    null as lock_up_betw_two_fu_code,
    null as lock_up_betw_two_fu_name,
    null as now_status_code,
    null as now_status_name,
    null as marriage_function_code,
    null as marriage_function_name,
    null as parental_functions_code,
    null as parental_functions_name,
    null as social_withdrawal_code,
    null as social_withdrawal_name,
    null as soci_acti_outs_the_home_code,
    null as soci_acti_outs_the_home_name,
    null as family_activ_code,
    null as family_activ_name,
    null as family_functions_code,
    null as family_functions_name,
    null as outs_inte_and_conc_code,
    null as outs_inte_and_conc_name,
    null as resp_and_plan_code,
    null as resp_and_plan_name,
    null as sdss_total,
    null as insi_eval_result_code,
    null as insi_eval_result_name,
    null as physical_disease,
    null as auxi_insp_sign,
    null as asst_exam_rslt,
    null as drug_dys_mark,
    t. as trea_effe_cate_code,
    null as trea_effe_cate_name,
    case t.ccsffl when '1' then '3' when '2' then '2' when '3' then '1' end as ment_diso_fu_eval_code,
    d_ment_diso_fu_eval_name.dic_val_name as ment_diso_fu_eval_name,
    null as take_drug_compliance_code,
    null as take_drug_compliance_name,
    null as speist_dor_ad_code,
    null as speist_dor_ad_name,
    '待定' as emer_medi_trea_sign,
    t. as referral_flag,
    t. as referral_reason,
    t. as referral_org_code,
    t. as referral_org_name,
    null as treatment_opinion,
    to_date(t.sfrq00,'%Y%m%d') as fu_date,
    null as ment_illn_fami_hist_sign,
    null as fami_ment_illn_name,
    d_major_psyc_symp_code.dic_val_code as major_psyc_symp_code,
    t. as major_psyc_symp_name,
    '待定' as fu_hospital_code,
    '待定' as fu_hospital_name,
    t. as business_time,
    '0' as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t. as heav_ment_illn_flup_no,
    t. as self_know_forc_code,
    t. as self_know_forc_name,
    t. as slep_info_code,
    t. as slep_info_name,
    t. as diet_info_code,
    t. as diet_info_name,
    t. as psn_life_cate_code,
    t. as psn_life_cate_name,
    t. as housw_code,
    t. as housw_name,
    t. as gena_lbr_wth_job_code,
    t. as gena_lbr_wth_job_name,
    t. as stdy_ablt_code,
    t. as stdy_ablt_name,
    t. as soca_inter_pers_code,
    t. as soca_inter_pers_name,
    t. as lab_exam,
    t. as medication_compliance_code,
    t. as medication_compliance_name,
    t. as drug_dys_case,
    t. as drug_dys_dscr,
    t. as life_lbr_ablt,
    t. as prfs_trai,
    t. as stdy_ablt,
    t. as soci_inter,
    t. as oth,
    t. as next_follow_date,
    t. as flup_dr,
    t. as otp_dr_name,
    t. as info_memo,
    t. as dange,
    t. as ipt_info,
    t. as last_dscg_time,
    t. as lstvs_rea,
    t. as die_rea_body_dise,
    t. as die_reason,
    t. as death_time,
    t. as crt_flup_obj_code,
    d_crt_flup_obj_name.dic_val_name as crt_flup_obj_name,
    t.sysjc2 as lab_exam_opt,
    t.zhxgsj as upload_time,
    t.zhxgsj as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from zl3500000000000004_syyzw.t_zxjsjb_sf as t
left join data_dic_a as d_visit_way_name on d_visit_way_name.dic_type_code='VISIT_WAY_CODE' and d_visit_way_name.dic_val_code=t.sffs00
left join data_dic_a as d_loss_fu_name on d_loss_fu_name.dic_type_code='LOSS_FU_CODE' and d_loss_fu_name.dic_val_code=t.sfyy00
left join t_dwellerfile on t_dwellerfile.df_id=t.df_id
left join data_dic_a as d_risk_type_name on d_risk_type_name.dic_type_code='RISK_LEVEL_CODE' and d_risk_type_name.dic_val_code=t.wxxing
left join data_dic_a as d_risk_type_name on d_risk_type_name.dic_type_code='RISK_TYPE_CODE' and d_risk_type_name.dic_val_code=t.wxxing
left join data_dic_a as d_personal_life_name on d_personal_life_name.dic_type_code='PERSONAL_LIFE_CODE' and d_personal_life_name.dic_val_code=t.grshll
left join data_dic_a as d_housework_name on d_housework_name.dic_type_code='HOUSEWORK_CODE' and d_housework_name.dic_val_code=t.jwld00
left join data_dic_a as d_prod_labor_and_work_name on d_prod_labor_and_work_name.dic_type_code='PROD_LABOR_AND_WORK_CODE' and d_prod_labor_and_work_name.dic_val_code=t.scldgz
left join data_dic_a as d_learning_ability_name on d_learning_ability_name.dic_type_code='LEARNING_ABILITY_CODE' and d_learning_ability_name.dic_val_code=t.xxnl00
left join data_dic_a as d_soci_inte_comm_name on d_soci_inte_comm_name.dic_type_code='SOCI_INTE_COMM_CODE' and d_soci_inte_comm_name.dic_val_code=t.shrjjw
left join data_dic_a as d_hosp_betwe_two_fu_name on d_hosp_betwe_two_fu_name.dic_type_code='HOSP_BETWE_TWO_FU_CODE' and d_hosp_betwe_two_fu_name.dic_val_code=t.zyqk00
left join data_dic_a as d_ment_diso_fu_eval_name on d_ment_diso_fu_eval_name.dic_type_code='MENT_DISO_FU_EVAL_CODE' and d_ment_diso_fu_eval_name.dic_val_code=t.ccsffl
left join data_dic_a as d_major_psyc_symp_code on d_major_psyc_symp_code.dic_type_code='MAJOR_PSYC_SYMP_CODE' and d_major_psyc_symp_code.dic_val_name=t.jwszz0
left join data_dic_a as d_crt_flup_obj_name on d_crt_flup_obj_name.dic_type_code='CRT_FLUP_OBJ_CODE' and d_crt_flup_obj_name.dic_val_code=t.bcsfdx
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_dmp_followup_med_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_psycho_code,
    null as drug_use_category_code,
    null as drug_use_category_name,
    null as drug_name_code,
    null as drug_name,
    null as drug_type_code,
    null as drug_type_name,
    null as drug_use_per_dose,
    null as rfd,
    null as drug_dose_unit,
    null as drug_used_idose,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_used_frqu,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dmp_emergency_action partition(dt)
select 
    concat(unified_uscid,emer_medi_disp_form_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as emer_medi_disp_form_no,
    null as psycho_card,
    null as disposal_start_time,
    null as end_of_disposal_time,
    null as first_disp_site,
    null as brie_dscr_of_the_scene,
    null as executive_staff_code,
    null as executive_staff_name,
    null as emt_reason_code,
    null as emt_reason_name,
    null as emt_reason_num,
    null as emt_measure_code,
    null as emt_measure_name,
    null as emt_measure_num,
    null as emt_diagnosis_code,
    null as emt_diagnosis_name,
    null as emt_diagnosis_num_code,
    null as emt_diagnosis_num_name,
    null as emt_nature_code,
    null as emt_nature_name,
    null as emt_obj_sour_code,
    null as emt_obj_sour_name,
    null as treatment_effect,
    null as emt_org_name,
    null as crter_no,
    null as update_time,
    null as upder_no,
    null as vali_flag,
    null as create_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dme_special_form partition(dt)
select 
    concat(elderly_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as elderly_id,
    null as control_uscid,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as sign_date,
    null as smok_day,
    null as drnk_day,
    null as living_capacity_code,
    null as living_capacity_name,
    null as lifestyle_support_code,
    null as lifestyle_support_name,
    null as eyesight_index_code,
    null as eyesight_index_name,
    null as health_problem_code,
    null as health_problem_name,
    null as incomplete_teeth_code,
    null as incomplete_teeth_name,
    null as addr_type_code,
    null as addr_type_name,
    null as per_addr_prov_code,
    null as per_addr_prov_name,
    null as per_addr_city_code,
    null as per_addr_city_name,
    null as per_addr_coty_code,
    null as per_addr_coty_name,
    null as per_addr_town_code,
    null as per_addr_town_name,
    null as per_addr_comm_code,
    null as per_addr_comm_name,
    null as per_addr_cotry_name,
    null as per_addr_housnum,
    null as inquirer_date,
    null as inquirer_no,
    null as inquirer_name,
    null as care_code,
    null as care_name,
    null as inspect_find_gyne_type,
    null as inspect_gyne_code,
    null as inspect_gyne_name,
    null as almost_two_years_ge_type,
    null as gynecological_symptoms_code,
    null as gynecological_symptoms_name,
    null as medcasno,
    null as addr_districts_code,
    null as addr_districts_name,
    null as poscode,
    null as duty_doc_name,
    null as duty_doc_no,
    null as symp_code,
    null as health_symp_name,
    null as check_dor_no,
    null as check_dor_name,
    null as check_date,
    null as have_fraction_code,
    null as have_fraction_name,
    null as wash_fraction_code,
    null as wash_fraction_name,
    null as dress_fraction_code,
    null as dress_fraction_name,
    null as toilet_fraction_code,
    null as toilet_fraction_name,
    null as motion_fraction_code,
    null as motion_fraction_name,
    null as total_score,
    null as oxygen_uptake,
    null as builder_dor_no,
    null as builder_dor_name,
    null as builder_dept_code,
    null as builder_dept_name,
    null as builder_org_name,
    null as builder_org_code,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_elderlyhealth.jjsrqk as jjsrqk,
    t_elderlyhealth.bxqk as bxqk,
    t_elderlyhealth.yjs as mena_his,
    t_elderlyhealth.sys as sys,
    t_elderlyhealth.wcqk as wcqk,
    t_elderlyhealth.cjqk as cjqk,
    t_elderlyhealth.mxbqk as mxbqk,
    t_elderlyhealth.fbedreason as fbedreason,
    t_elderlyhealth.fbedcreatedata as fbedcreatedata,
    t_elderlyhealth.fbedcanceldata as fbedcanceldata,
    t_elderlyhealth.coalslong as coalslong,
    t_elderlyhealth.coalsmark as coalsmark,
    t_elderlyhealth.fsmoke as family_smoke_mark,
    t_elderlyhealth.protectmark as protectivemeasures_mark,
    t_elderlyhealth.hazardworktime as hazardworktime,
    t_elderlyhealth.hazardwork as danger_occup,
    t_elderlyhealth.hazardworktype as hazardworktype,
    t_elderlyhealth.hazardworkname as occup_risk_name,
    t_elderlyhealth.hazardworkmark as occup_mark,
    t_elderlyhealth.cbehavior as cbehavior_code,
    t_elderlyhealth.cbehavior as cbehavior_name,
    t_elderlyhealth.psychological as mind_info_code,
    t_elderlyhealth.psychological as mind_info_name,
    t_elderlyhealth.nodrinkage as stop_drink_age,
    t_elderlyhealth.nodrinkmark as stop_drink_mark,
    t_elderlyhealth.drunkmark as drunkenness_mark,
    t_elderlyhealth.bdrinkage as start_drink_age,
    t_elderlyhealth.drinktype as drink_type_code,
    t_elderlyhealth.drinkfrequency as drink_type_name,
    t_elderlyhealth.nosmokeage as stop_smoke_year,
    t_elderlyhealth.bsmokeage as start_smoke_age,
    t_elderlyhealth.smokestatus as smok_info_code,
    t_elderlyhealth.smokestatus as smok_info_name,
    t_elderlyhealth.dietcode as habits_diet_code,
    t_elderlyhealth.dietcode as habits_diet_name,
    t_elderlyhealth.weeksport as weeksport,
    t_elderlyhealth.holdonsport as holdonsport,
    t_elderlyhealth.sporttime as sporttime,
    t_elderlyhealth.sportfrequency as sportfrequency_code,
    t_elderlyhealth. as sportfrequency_name,
    t_elderlyhealth.sportdesc as sportdesc,
    t_elderlyhealth.yyno00 as hosp_intl_no,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dme_hospital_history partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as elderly_id,
    null as admission_date,
    null as leave_date,
    null as reason,
    null as uscn,
    null as inhosp_org_code,
    null as medcasno,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    t_elderlyinhospital.departments as departments,
    t_elderlyinhospital.diagnosis as diagnosis,
    t_elderlyinhospital.surgery as surgery,
    t_elderlyinhospital.result as result,
    t_elderlyinhospital.jktjcs as hl_phys_exam_cnt,
    t_elderlyinhospital.df_id as df_id,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dme_home_bed_history partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as elderly_id,
    null as build_bed_date,
    null as remove_bed_date,
    null as bed_reason,
    null as bed_org_name,
    null as bed_org_code,
    null as medcasno,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dme_followup_form partition(dt)
select 
    concat(fu_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_id,
    null as control_uscid,
    null as elderly_id,
    null as fu_no,
    null as visit_date,
    null as visit_way_code,
    null as visit_way_name,
    null as outcome_state_code,
    null as outcome_state_name,
    null as lstvs_rea,
    null as sympt_code,
    null as sympt_name,
    null as psychological_guidance_code,
    null as psychological_guidance_name,
    null as weight,
    null as smok_day,
    null as stop_smoke_year,
    null as smoke_time,
    null as drnk_day,
    null as stop_drink_time,
    null as stop_drink_year,
    null as sportdesc,
    null as sportfrequency_code,
    null as sportfrequency_name,
    null as sport_time,
    null as insist_sport_year,
    null as weeksport,
    null as psycho_adjust_res_code,
    null as psycho_adjust_res_name,
    null as chd_prevention,
    null as osteoporosis_prevention,
    null as fu_advice,
    null as next_visit_goal,
    null as next_follow_date,
    null as fu_doc_name,
    null as fu_doc_no,
    null as fu_diet_type_code,
    null as fu_diet_type_name,
    null as fu_compliance_res_code,
    null as fu_compliance_res_name,
    null as fu_psyc_guidance_dscr,
    null as medication_compliance_code,
    null as medication_compliance_name,
    null as dbp,
    null as systpre,
    null as bmi,
    null as heart_rate,
    null as other1,
    null as vaccination_status,
    null as other_sympt_dscrr,
    null as salt_intake_profile_code,
    null as salt_intake_profile_name,
    null as asst_exam,
    null as drug_dys_dscr,
    null as fu_up_type_code,
    null as fu_up_type_name,
    null as referral_reason,
    null as accept_org_name,
    null as accept_dept_name,
    null as habits_diet_code,
    null as habits_diet_name,
    null as smok_info_code,
    null as smok_info_name,
    null as drnk_frqu_code,
    null as drnk_frqu_name,
    null as drink_type_code,
    null as drink_type_name,
    null as drunkenness_mark,
    null as stop_drink_mark,
    null as occup_mark,
    null as occup_risk_name,
    null as occup_risk_type_code,
    null as occup_risk_type_name,
    null as danger_occup,
    null as harm_occup_duration_year,
    null as family_smoke_mark,
    null as fu_hospital_code,
    null as fu_hospital_name,
    null as business_time,
    null as data_rank,
    null as state,
    null as reserve1,
    null as reserve2,
    t_elderly_sfxx.tz0000 as wt_emp,
    t_elderly_sfxx.xy0000 as smok_emp,
    t_elderly_sfxx.ysqk00 as diet_info_code,
    t_elderly_sfxx.ysqk00 as diet_info_name,
    t_elderly_sfxx.bz0000 as remark,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_dme_followup_med_info partition(dt)
select 
    concat(ref_no,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as control_uscid,
    null as fu_id,
    null as drug_name_code,
    null as drug_name,
    null as drug_type_code,
    null as drug_type_name,
    null as drug_use_per_dose,
    null as rfd,
    null as drug_dose_unit,
    null as drug_used_idose,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_used_frqu,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    null as business_time,
    null as state,
    null as data_rank,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdv_child_basic_info partition(dt)
select 
    concat(chilid,vaccinate_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as vaccinate_uscid,
    null as chil_card_no,
    null as chil_card_date,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as chil_cert_no,
    null as chil_cert_type_code,
    null as chil_cert_type_name,
    null as birth_certificate_no,
    null as birth_addr_type_code,
    null as birth_addr_type_name,
    null as chil_brdy,
    null as chil_wt,
    null as nation_code,
    null as nation_name,
    null as chil_mother,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as chil_father,
    null as father_cert_no,
    null as father_cert_type_code,
    null as father_cert_type_name,
    null as cur_addr_code,
    null as cur_addr_name,
    null as local_type_code,
    null as local_type_name,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as poscode,
    null as family_addr,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_vil,
    null as resd_addr_housnum,
    null as resd_addr,
    null as tel1,
    null as tel2,
    null as algs_his,
    null as mother_hb_code,
    null as mother_hb_name,
    null as mother_hiv_anti_code,
    null as mother_hiv_anti_name,
    null as hb_vacc_time,
    null as ppd_result_code,
    null as ppd_result_name,
    null as cur_org_code,
    null as cur_org_name,
    null as last_org_code,
    null as last_org_name,
    null as build_coty_code,
    null as build_coty_name,
    null as build_org_code,
    null as build_org_name,
    null as build_date,
    null as build_card_name,
    null as build_card_no,
    null as guardian_name,
    null as jhrybrgx_code,
    null as jhrybrgxmc_name,
    null as jhrlxfs,
    null as into_time,
    null as out_time,
    null as out_reason,
    null as remark,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as chil_fetus,
    null as chil_mothermobile,
    null as chil_fathermobile,
    null as chil_address,
    null as occup_code,
    null as occup_name,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdv_child_vacc_info partition(dt)
select 
    concat(chilid,vaccinate_uscid,inoc_bact_code,inoc_property,inoc_time,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as vaccinate_uscid,
    null as inoc_bact_code,
    null as inoc_property,
    null as inoc_time,
    null as inoc_dose,
    null as dosunt,
    null as inoc_inpl_code,
    null as inoc_inpl_name,
    null as vacc_type_code,
    null as vacc_type_name,
    null as vacc_time,
    null as inoc_date_certainty,
    null as inoc_coty_code,
    null as inoc_coty_name,
    null as inoc_union_code,
    null as inoc_union_name,
    null as input_date,
    null as immunity_type_code,
    null as immunity_type_name,
    null as inoc_type_code,
    null as inoc_type_name,
    null as inoc_react_flag,
    null as inoc_off_part_mark,
    null as price,
    null as vacc_doses_code,
    null as vacc_doses_name,
    null as inoc_batchno,
    null as other_vacc_name,
    null as inoc_corp_code,
    null as inoc_corp_name,
    null as inoc_pay,
    null as uisout,
    null as inoc_free,
    null as inoc_regulatory_code,
    null as vaccinate_org_name,
    null as vaccer_no,
    null as vacc_staff,
    null as reg_org_name,
    null as reger_no,
    null as reger_name,
    null as inoc_validdate,
    null as remark,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdv_child_infect_history partition(dt)
select 
    concat(chilid,vaccinate_uscid,infect_code,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as vaccinate_uscid,
    null as infect_code,
    null as infect_name,
    null as onset_date,
    null as reger_no,
    null as reger_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdv_child_contraindication partition(dt)
select 
    concat(chilid,vaccinate_uscid,reg_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as vaccinate_uscid,
    null as reg_id,
    null as vacc_code,
    null as vacc_name,
    null as taboo_code,
    null as taboo_name,
    null as taboo_dscr,
    null as reger_no,
    null as reger_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdv_aefi_record partition(dt)
select 
    concat(report_id,chilid,vaccinate_uscid,vacc_code,vacc_name,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as report_id,
    null as chilid,
    null as vaccinate_uscid,
    null as vacc_code,
    null as vacc_name,
    null as vacc_org_name,
    null as aefi_date,
    null as vaccinate_inoc_time,
    null as inoc_dose,
    null as dosunt,
    null as inoc_inpl_code,
    null as inoc_inpl_name,
    null as inoc_channel_code,
    null as inoc_channel_name,
    null as vacc_time,
    null as aefi_code,
    null as aefi_name,
    null as primary_diagnostic,
    null as diagnostic_unit,
    null as group_reaction_mark,
    null as group_reaction_code,
    null as adverse_reaction_deal_rslt,
    null as rpt_doc_code,
    null as rpt_doc_name,
    null as rpt_org_uscid,
    null as vacc_batch,
    null as vaccinemfrcode,
    null as vaccinemfrname,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdf_report_card partition(dt)
select 
    concat(rpt_card_no,rpt_org_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_card_no,
    null as rpt_org_uscid,
    null as supply_card_time,
    null as orin_time,
    null as rpt_hosp_area_code,
    null as rpt_hosp_area_name,
    null as poscode,
    null as rpt_card_type_code,
    null as rpt_card_type_code_name,
    null as patient_name,
    null as parent_name,
    null as patient_ascr_code,
    null as patient_ascr_name,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as age_unit_code,
    null as age_unit_name,
    null as age,
    null as patient_work_org_name,
    null as psn_tel,
    null as tel_home,
    null as tel_org,
    null as cur_addr,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as occup_code,
    null as occup_name,
    null as onset_date,
    null as diag_time,
    null as death_date,
    null as case_type_code,
    null as case_type_name,
    null as acute_chronic_code,
    null as acute_chronic_name,
    null as report_dor_no,
    null as report_dor_name,
    null as fill_date,
    null as infect_type_code,
    null as infect_type_name,
    null as infect_code,
    null as infect_name,
    null as mod_diag_name,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as concact_flag,
    null as concact_name,
    null as sample_source,
    null as other_sample,
    null as s_date1,
    null as s_method1,
    null as s_method2,
    null as s_result1,
    null as s_result2,
    null as wb_valid,
    null as wb_test_date,
    null as wb_test_unit,
    null as tight_contact,
    null as remark,
    null as input_date,
    null as auth_date,
    null as record_status,
    null as card_status,
    null as d_aud_time,
    null as c_aud_time,
    null as p_aud_time,
    null as crt_aud_time,
    null as delete_time,
    null as mend_time,
    null as rpt_create_time,
    null as notify_unit_code,
    null as notify_unit_name,
    null as rpt_org_tel,
    null as death_aud_time,
    null as rec_edit_time,
    null as rpt_org_name,
    null as first_sympt_date,
    null as oth_infect_diseases,
    null as remove_card_reason,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_report_card partition(dt)
select 
    concat(rpot_card_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpot_card_id,
    null as unified_uscid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as adequ_age,
    null as age_unit_code,
    null as age_unit_name,
    null as brdy,
    null as nation_code,
    null as nation_name,
    null as occup_code,
    null as occup_name,
    null as resd_local_type_code,
    null as resd_local_type_name,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    null as resd_addr_housnum,
    null as resd_addr,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as cur_addr,
    null as org_addr_prov_code,
    null as org_addr_prov_name,
    null as org_addr_city_code,
    null as org_addr_city_name,
    null as org_addr_coty_code,
    null as org_addr_coty_name,
    null as org_addr_town_code,
    null as org_addr_town_name,
    null as org_addr_steet,
    null as org_addr_housnum,
    null as empr_tel,
    null as empr_name,
    null as psn_tel,
    null as tel_home,
    null as tel_contact,
    null as coner_name,
    null as addr_districts_code,
    null as addr_districts_name,
    null as otp_no,
    null as ipt_no,
    null as reg_no,
    null as detainee_mark,
    null as tb_hole_mark,
    null as like_tb_code,
    null as like_tb_name,
    null as first_symptom_date,
    null as mdtrt_date,
    null as cnfm_date,
    null as start_date,
    null as crt_check_date,
    null as check_rpt_date,
    null as discovery_mode_code,
    null as discovery_mode_name,
    null as sputum_exam_rslt_code,
    null as sputum_exam_rslt_name,
    null as sputum_not_exam_reason,
    null as culture_rslt_code,
    null as culture_rslt_name,
    null as culture_uncheck_rslt,
    null as dr_rslt_code,
    null as dr_rslt_name,
    null as dr_rslt_des,
    null as ct_exam_rslt,
    null as liver_check_rslt_code,
    null as liver_check_rslt_name,
    null as fecal_routine_exam_rslt_code,
    null as fecal_routine_exam_rslt_name,
    null as urinalysis_exam_rslt_code,
    null as urinalysis_exam_rslt_name,
    null as blood_routine_check_rslt_code,
    null as blood_routine_check_rslt_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as accept_anti_trt_mark,
    null as anti_trt_start_date,
    null as accept_smz_tmp_trt_mark,
    null as sms_tmp_trt_start_date,
    null as accept_anti_tb_mark_code,
    null as accept_anti_tb_mark_name,
    null as lab_drug_alle_code,
    null as lab_drug_alle_name,
    null as lab_drug_alle_rslt_code,
    null as lab_drug_alle_rslt_name,
    null as detection_tb_flora_rslt_code,
    null as detection_tb_flora_rslt_name,
    null as tb_out_part_code,
    null as tb_out_part__name,
    null as diag_category_code,
    null as diag_category_name,
    null as diag_category_rslt_code,
    null as diag_category_rslt_name,
    null as phthisical_mark,
    null as hepatitis_mark,
    null as tb_touch_his,
    null as complicat_code,
    null as complicat_name,
    null as trt_category_code,
    null as trt_category_name,
    null as reg_category_code,
    null as reg_category_name,
    null as drug_resi_code,
    null as drug_resi_name,
    null as trt_plan_code,
    null as trt_plan_name,
    null as tb_chemotherapy_plan_code,
    null as tb_chemotherapy_plan_name,
    null as drug_dys_mark,
    null as trt_stop_date,
    null as trt_stop_reason_code,
    null as trt_stop_reason_name,
    null as mang_way_code,
    null as mang_way_name,
    null as supervise_way_code,
    null as supervise_way_name,
    null as normal_use_drug_mark,
    null as first_mang_org_name,
    null as cur_mang_org_name,
    null as first_mang_org_code,
    null as cur_org_code,
    null as diag_org_name,
    null as diage_dor_code,
    null as diage_dor_name,
    null as first_trt_org_name,
    null as cur_trt_org_name,
    null as duty_dor_name,
    null as patient_ascr_code,
    null as patient_ascr_name,
    null as rpt_dor_name,
    null as report_doctor_no,
    null as remark,
    null as input_orgname,
    null as input_date,
    null as enter_dor_name,
    null as reg_dor_no,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_cm_dinfo.oxygentime as oxygentime,
    t_cm_dinfo.fbedreason as fbedreason,
    t_cm_dinfo.fbedcreatedata as fbedcreatedata,
    t_cm_dinfo.fbedcanceldata as fbedcanceldata,
    t_cm_dinfo.coalslong as coalslong,
    t_cm_dinfo.coalsmark as coalsmark,
    t_cm_dinfo.fsmoke as family_smoke_mark,
    t_cm_dinfo.protectmark as protectivemeasures_mark,
    t_cm_dinfo.hazardworktime as harm_occup_duration_year,
    t_cm_dinfo.hazardwork as danger_occup,
    t_cm_dinfo.hazardworktype as occup_risk_type_code,
    t_cm_dinfo.hazardworkname as occup_risk_name,
    t_cm_dinfo.hazardworkmark as occup_mark,
    t_cm_dinfo.cbehavior as cbehavior_code,
    t_cm_dinfo.cbehavior as cbehavior_name,
    t_cm_dinfo.psychological as mind_info_code,
    t_cm_dinfo.psychological as mind_info_name,
    t_cm_dinfo.sfbyqz as whtr_our_hosp_cnfm,
    t_cm_dinfo.sfgg00 as whtr_spec_mgt,
    t_cm_dinfo.kzmy as ctrl_whtr_satis,
    t_cm_dinfo.tsrq00 as info_psh_date,
    t_cm_dinfo.sfts00 as whtr_alre_psh,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_management_card partition(dt)
select 
    concat(mana_card_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as rpot_card_id,
    null as full_name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as edu_background_code,
    null as edu_background_name,
    null as gender_code,
    null as gender_name,
    null as nation_code,
    null as nation_name,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as brdy,
    null as occup_code,
    null as occup_name,
    null as contact_name,
    null as relation_contactor_code,
    null as relation_contactor_name,
    null as resd_local_type_code,
    null as resd_local_type_name,
    null as empr_name,
    null as reg_no,
    null as discovery_mode_code,
    null as discovery_mode_name,
    null as diag_category_code,
    null as diag_category_name,
    null as diag_category_rslt_code,
    null as diag_category_rslt_name,
    null as severe_mark,
    null as reg_category_code,
    null as reg_category_name,
    null as trt_category_code,
    null as trt_category_name,
    null as trt_stop_date,
    null as trt_stop_reason_code,
    null as trt_stop_reason_name,
    null as aim_visit_times,
    null as actual_visit_times,
    null as aim_use_drug_times,
    null as actual_use_drug_times,
    null as use_drug_rate,
    null as eval_dor_name,
    null as mang_way_code,
    null as mang_way_name,
    null as reg_date,
    null as first_symptom_date,
    null as first_time,
    null as cnfm_date,
    null as mdtrt_date,
    null as mang_org_name,
    null as supervise_org_name,
    null as local_trt_org_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as hiv_check_time,
    null as last_cd4_value,
    null as cd4_check_time,
    null as drug_resi_code,
    null as drug_resi_name,
    null as diag_org_name,
    null as diage_dor_code,
    null as diage_dor_name,
    null as diage_area,
    null as medcasno,
    null as age,
    null as age_unit_code,
    null as age_unit_name,
    null as resd_addr,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    null as resd_addr_housnum,
    null as cur_addr,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as empr_addr,
    null as tel_family,
    null as tel_contact,
    null as tel,
    null as mobile_phone,
    null as first_diag_org_name,
    null as first_diag_area_name,
    null as accept_anti_trt_mark,
    null as anti_trt_start_date,
    null as accept_smz_tmp_trt_mark,
    null as sms_tmp_trt_start_date,
    null as is_accept_antitb,
    null as remark,
    null as input_time,
    null as otp_no,
    null as ipt_no,
    null as trt_plan_code,
    null as trt_plan_name,
    null as only_incubation_pos,
    null as tb_out_part_code,
    null as tb_out_part__name,
    null as complicat_code,
    null as complicat_name,
    null as start_date,
    null as like_tb_code,
    null as like_tb_name,
    null as detection_tb_flora_rslt_code,
    null as detection_tb_flora_rslt_name,
    null as phthisical_mark,
    null as hepatitis_mark,
    null as tb_touch_his,
    null as detainee_mark,
    null as before_smear_rslt_code,
    null as before_smear_rslt_name,
    null as before_smear_uncheck_reason,
    null as before_culture_media_rslt_code,
    null as before_culture_media_rslt_name,
    null as befoculture_unchkrea,
    null as bcg_vacc_his_mark,
    null as past_his,
    null as dm_his_mark,
    null as dm_type_code,
    null as dm_type_name,
    null as hepatopathy_his_mark,
    null as nephropathy_his_mark,
    null as alle_his_mark,
    null as chronic_bronchitis_his_mark,
    null as use_immunosuppressants_mark,
    null as other_disease_his,
    null as tb_mang_org_name,
    null as trt_org_name,
    null as trt_dor_code,
    null as trt_dor_name,
    null as filler_no,
    null as fill_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_image_exam partition(dt)
select 
    concat(mana_card_id,control_uscid,seq_times,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as seq_times,
    null as seq_month,
    null as dr_check_org_code,
    null as dr_test_date,
    null as dr_rep_date,
    null as ct_check_org_code,
    null as ct_date,
    null as ct_rpt_date,
    null as dr_abn_mark,
    null as dr_abn_position_code,
    null as dr_rslt_des,
    null as dr_abn_position_name,
    null as ct_exam_rslt,
    null as tb_hole_mark,
    null as tb_hole_position_code,
    null as tb_hole_position_name,
    null as miliary,
    null as business_time,
    null as state,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_lab_info partition(dt)
select 
    concat(mana_card_id,control_uscid,seq_times,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as seq_times,
    null as seq_month,
    null as check_unit_name,
    null as check_org_code,
    null as sgpt_value,
    null as tbi_value_price,
    null as bun_value,
    null as scr_value,
    null as uricacid_res,
    null as wbc_value,
    null as rbc_value,
    null as platelet_value,
    null as lymph_rate,
    null as neut_rate,
    null as c_reactive_protein_rstl,
    null as pro_rslt_code,
    null as pro_rslt_name,
    null as uglu_rslt_code,
    null as uglu_rslt_name,
    null as rbc_urine_rslt_code,
    null as rbc_urine_rslt_name,
    null as wbc_urine_rslt_code,
    null as wbc_urine_rslt_name,
    null as klg_rslt_code,
    null as klg_rslt_name,
    null as urine_check_code,
    null as urine_check_name,
    null as esr_check_res,
    null as blood_suar_check_value,
    null as liver_check_rslt_code,
    null as liver_check_rslt_name,
    null as fecal_routine_exam_rslt_code,
    null as fecal_routine_exam_rslt_name,
    null as urinalysis_exam_rslt_code,
    null as urinalysis_exam_rslt_name,
    null as blood_routine_check_rslt_code,
    null as blood_routine_check_rslt_name,
    null as business_time,
    null as state,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_sputum_info partition(dt)
select 
    concat(mana_card_id,control_uscid,seq_times,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as seq_times,
    null as seq_month,
    null as check_unit_name,
    null as check_org_code,
    null as check_date,
    null as smear_lab_seq,
    null as sputum_exam_rslt_code,
    null as sputum_exam_rslt_name,
    null as sputum_not_exam_reason,
    null as rpt_phlegm_date,
    null as smear_pattern_code,
    null as smear_pattern_name,
    null as s1_afsr_code,
    null as s1_afsr_name,
    null as s1_fsr_code,
    null as s1_fsr_name,
    null as sample1_300,
    null as sample1_50,
    null as s2_afsr_code,
    null as s2_afsr_name,
    null as s2_fsr_code,
    null as s2_fsr_name,
    null as sample2_300,
    null as sample2_50,
    null as s3_afsr_code,
    null as s3_afsr_name,
    null as s3_fsr_code,
    null as s3_fsr_name,
    null as sample3_300,
    null as sample3_50,
    null as culture_rslt_code,
    null as culture_rslt_name,
    null as culture_uncheck_rslt,
    null as medium_positive_res_code,
    null as medium_positive_res_name,
    null as medium_colony_count,
    null as medium_rslt_rep_date,
    null as remark,
    null as receive_org_reg_date,
    null as send_date,
    null as receive_datetime,
    null as send_org_reg_date,
    null as business_time,
    null as state,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_drug_sensitivity partition(dt)
select 
    concat(mana_card_id,control_uscid,seq_times,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as seq_times,
    null as check_unit_name,
    null as check_date,
    null as lab_seq,
    null as sens_inh_code,
    null as sens_inh_name,
    null as sens_sm_code,
    null as sens_sm_name,
    null as sens_rfp_code,
    null as sens_rfp_name,
    null as sens_emb_code,
    null as sens_emb_name,
    null as sens_amikacin_code,
    null as sens_amikacin_name,
    null as sens_levofoxacin_code,
    null as sens_levofoxacin_name,
    null as sens_ofl_code,
    null as sens_ofl_name,
    null as sens_capromycin_code,
    null as sens_capromycin_name,
    null as pas_code,
    null as pas_name,
    null as colony_exam_code,
    null as colony_exam_name,
    null as sterility_control_tube,
    null as drug_alle_category_code,
    null as drug_alle_category_name,
    null as business_time,
    null as state,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_med_pickup partition(dt)
select 
    concat(mana_card_id,control_uscid,seq_times,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as mana_card_id,
    null as control_uscid,
    null as seq_times,
    null as get_drug_org_name,
    null as get_drug_dor_name,
    null as get_drug_date,
    null as total_dose,
    null as get_drug_month,
    null as surplus_days,
    null as next_get_drug_date,
    null as business_time,
    null as state,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_first_home_visit partition(dt)
select 
    concat(tuberculosis_card_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as tuberculosis_card_id,
    null as control_uscid,
    null as mana_card_id,
    null as visit_date,
    null as fu_org_code,
    null as visit_way_code,
    null as visit_way_name,
    null as patient_type_code,
    null as patient_type_name,
    null as sputum_exam_rslt_code,
    null as sputum_exam_rslt_name,
    null as drug_resi_code,
    null as drug_resi_name,
    null as symptom_signs_code,
    null as symptom_signs_name,
    null as other_symptom,
    null as trt_plan_code,
    null as trt_plan_name,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_form_code,
    null as drug_form_name,
    null as supervisorcategorycode,
    null as supervisorcategoryname,
    null as eval_other_type,
    null as alon_of_live_room,
    null as vent_situ_code,
    null as vent_situ_name,
    null as cur_smoke_value,
    null as aim_smoke_value,
    null as cur_drink_value,
    null as aim_drink_value,
    null as take_medi_loc,
    null as take_medi_time,
    null as dose_reco_card_of_fillin,
    null as dose_mtd_wth_drug_stor,
    null as tuber_trt_cour_trea,
    null as no_regu_dose_hazr,
    null as dose_new_defs_wth_dspo,
    null as trt_cose_flup_sput,
    null as out_cose_how_adhe_dose,
    null as habi_wth_mnan,
    null as clos_cont_the_exam,
    null as next_follow_date,
    null as fill_date,
    null as eval_dor_code,
    null as eval_dor_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_mxjb_fjhsf.sfzz as whtr_refl,
    t_mxjb_fjhsf.tzzlsj as stop_trt_time,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_code,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_name,
    t_mxjb_fjhsf.pgysqm as eval_dr_sign,
    t_mxjb_fjhsf.wyysqm as otp_dr_sign,
    t_mxjb_fjhsf.sjxzrq as on_ins_sub_turn_reg_time,
    t_mxjb_sf_yyqk.ywbh_id as drug_name_code,
    t_mxjb_sf_yyqk.ywmc as drug_name,
    t_mxjb_sf_yyqk.ywyf as used,
    t_mxjb_sf_yyqk.ywyl as dos,
    t_mxjb_sf_yyqk.yysj as medication_time,
    t_mxjb_sf_yyqk.fyycx as dose_adhe,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdb_followup_service partition(dt)
select 
    concat(tuberculosis_card_id,control_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as tuberculosis_card_id,
    null as control_uscid,
    null as mana_card_id,
    null as visit_date,
    null as treatment_seq_month,
    null as supervisorcategorycode,
    null as supervisorcategoryname,
    null as eval_other_type,
    null as supervisoname,
    null as mang_way_code,
    null as mang_way_name,
    null as mang_way_change_mark,
    null as change_date,
    null as urine_spot_check_code,
    null as urine_spot_check_name,
    null as check_drug_mark_code,
    null as check_drug_mark_name,
    null as fill_card_mark_code,
    null as fill_card_mark_name,
    null as deal_opinion,
    null as psn_name,
    null as psn_comm_code,
    null as psn_comm_name,
    null as create_time,
    null as tel,
    null as fu_org_code,
    null as visit_way_code,
    null as visit_way_name,
    null as trt_plan_code,
    null as trt_plan_name,
    null as trt_stage_code,
    null as trt_stage_name,
    null as follow_dor_advice_mark,
    null as recheck_mark,
    null as drug_use_way_code,
    null as drug_use_way_name,
    null as drug_form_code,
    null as drug_form_name,
    null as omit_times,
    null as supervise_way_code,
    null as supervise_way_name,
    null as drug_dys_mark,
    null as defs_code,
    null as medn_defs_name,
    null as trt_change_mark,
    null as trt_change_reason_code,
    null as trt_change_reason_name,
    null as trt_change_date,
    null as new_trt_code,
    null as new_trt_name,
    null as new_use_dscr,
    null as omit_times_per_month,
    null as omit_reason,
    null as symptom_signs_code,
    null as symptom_signs_name,
    null as other_symptom,
    null as cur_smoke_value,
    null as aim_smoke_value,
    null as cur_drink_value,
    null as aim_drink_value,
    null as complicat_flag,
    null as complicat_dscr,
    null as weight,
    null as tprt,
    null as breathing,
    null as lung_respiratory_sound_code,
    null as lung_respiratory_sound_name,
    null as abn_sound_position_code,
    null as abn_sound_position_name,
    null as other_positive_names,
    null as smear_afs_code,
    null as smear_afs_name,
    null as sputum_not_exam_reason,
    null as sample_medium_rstl_code,
    null as sample_medium_rstl_name,
    null as drug_sensitivity_rstl_code,
    null as drug_sensitivity_rstl_name,
    null as culture_rslt_code,
    null as culture_rslt_name,
    null as culture_uncheck_rslt,
    null as drug_fast_dscr,
    null as dr_abn_code,
    null as dr_abn_name,
    null as galt,
    null as gast,
    null as gtbil,
    null as sscr,
    null as sbun,
    null as sua,
    null as xhb,
    null as xrbc,
    null as xwbc,
    null as xplt,
    null as xxc,
    null as other_abn_record,
    null as conclude_code,
    null as conclude_name,
    null as referral_flag,
    null as accept_org_name,
    null as accept_depart_name,
    null as referral_reason,
    null as in_date,
    null as out_agency_name,
    null as transferoutdate,
    null as fu_rslt_2week,
    null as next_follow_date,
    null as fill_date,
    null as filler_no,
    null as fill_name,
    null as flu_times,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    t_mxjb_fjhsf.sfzz as whtr_refl,
    t_mxjb_fjhsf.tzzlsj as stop_trt_time,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_code,
    t_mxjb_fjhsf.tzzlyy as trt_stop_reason_name,
    t_mxjb_fjhsf.pgysqm as eval_dr_sign,
    t_mxjb_fjhsf.wyysqm as otp_dr_sign,
    t_mxjb_fjhsf.sjxzrq as on_ins_sub_turn_reg_time,
    t_mxjb_sf_yyqk.ywbh_id as drug_name_code,
    t_mxjb_sf_yyqk.ywmc as drug_name,
    t_mxjb_sf_yyqk.ywyf as used,
    t_mxjb_sf_yyqk.ywyl as dos,
    t_mxjb_sf_yyqk.yysj as medication_time,
    t_mxjb_sf_yyqk.fyycx as dose_adhe,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdd_activity_record partition(dt)
select 
    concat(unified_uscid,record_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as record_no,
    null as act_time,
    null as act_addr,
    null as act_type_code,
    null as act_type_name,
    null as act_topic_code,
    null as act_topic_name,
    null as accept_person_type_code,
    null as accept_person_type_name,
    null as accept_count,
    null as issue_goods_kind_code,
    null as issue_goods_kind_name,
    null as issue_goods_count,
    null as act_content,
    null as act_comment,
    null as save_doc_code,
    null as save_doc_name,
    null as filler_no,
    null as fill_name,
    null as duty_no,
    null as responer_name,
    null as orga_no,
    null as orga_name,
    null as speaker_no,
    null as speaker_name,
    null as formfillingtime,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdc_statistics_death_report partition(dt)
select 
    concat(rpt_org_uscid,rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_org_uscid,
    null as rpot_card_id,
    null as rpt_card_type_code,
    null as rpt_card_type_name,
    null as mang_obj_mark,
    null as full_name,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as nation_code,
    null as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as vocational_type_code,
    null as vocational_type_name,
    null as longest_occup_code,
    null as longest_occup_name,
    null as retarded_mark,
    null as child_mark,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as edu_background_code,
    null as edu_background_name,
    null as pre_lif_org_name,
    null as org_tel,
    null as coner_name,
    null as tel_contact,
    null as contact_org_name,
    null as coner_addr,
    null as coner_poscode,
    null as same_addr_mark,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_vil,
    null as resd_addr_housnum,
    null as resd_addr,
    null as resd_poscode,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_vil,
    null as residential_housnum,
    null as detl_addr,
    null as curr_addr_poscode,
    null as prey_mark,
    null as death_time,
    null as adequ_age,
    null as age_unit_code,
    null as age_unit_name,
    null as die_drt_rea_a_code,
    null as die_drt_rea_a,
    null as happen_date_a,
    null as dura_time_a,
    null as die_drt_rea_b_code,
    null as die_drt_rea_b,
    null as happen_date_b,
    null as dura_time_b,
    null as die_drt_rea_c_code,
    null as die_drt_rea_c,
    null as happen_date_c,
    null as dura_time_c,
    null as die_drt_rea_d_code,
    null as die_drt_rea_d,
    null as happen_date_d,
    null as dura_time_d,
    null as other_situ_1_code,
    null as other_situ_1_name,
    null as other_situ_2_code,
    null as other_situ_2_name,
    null as other_situ_3_code,
    null as other_situ_3_name,
    null as diag_highest_org_code,
    null as diag_highest_org_name,
    null as diag_org_level_code,
    null as diag_org_level_name,
    null as die_org_code,
    null as die_org_name,
    null as ipt_no,
    null as deathplace_type_code,
    null as deathplace_type_name,
    null as die_high_diag_evid_code,
    null as die_high_diag_evid_name,
    null as his_symptom,
    null as death_reason,
    null as informant_name,
    null as informant_relate_code,
    null as informant_relate_name,
    null as informant_addr,
    null as informant_work_place,
    null as inquirer_name,
    null as inquirer_date,
    null as app_rpt_name,
    null as app_rpt_rel,
    null as app_rpt_addr,
    null as app_rpt_org_name,
    null as app_rpt_tel,
    null as infer_name,
    null as infer_date,
    null as confirm_name,
    null as confirm_org_name,
    null as confirm_date,
    null as confirm_evid,
    null as autopsy_record,
    null as death_reason_code,
    null as death_reason_name,
    null as rule_code,
    null as rule_name,
    null as report_doctor_no,
    null as report_name,
    null as rpt_date,
    null as stat_date,
    null as reg_dor_no,
    null as enter_dor_name,
    null as input_org_code,
    null as input_dept_code,
    null as input_dept_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_cdc_under_5yo_statistics_infant_death_info partition(dt)
select 
    concat(rpt_org_uscid,rpot_card_id,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_org_uscid,
    null as rpot_card_id,
    null as birth_gest_weeks,
    null as chil_wt,
    null as malformation_mark,
    null as icd_10_code,
    null as icd_10_name,
    null as birth_addr_code,
    null as birth_addr_name,
    null as chil_mother,
    null as mother_brdy,
    null as chil_father,
    null as father_brdy,
    null as prts_cur_addr_prov_code,
    null as prts_cur_addr_prov_name,
    null as prts_cur_addr_city_code,
    null as prts_cur_addr_city_name,
    null as prts_cur_addr_coty_code,
    null as prts_cur_addr_coty_name,
    null as prts_cur_addr_town_code,
    null as prts_cur_addr_town_name,
    null as prts_cur_addr_comm_code,
    null as prts_cur_addr_comm_name,
    null as cur_addr_cotry,
    null as prts_cur_addr_housnum,
    null as paren_addr,
    null as matn_cnt_times,
    null as stillbirth_times,
    null as die_matn_cnt_times,
    null as abortion_time,
    null as last_birth_rslt_code,
    null as last_birth_rslt_name,
    null as last_birth_date,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_basic_info partition(dt)
select 
    concat(woman_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as woman_id,
    null as unified_uscid,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as nation_code,
    null as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as occup_code,
    null as occup_name,
    null as edu_background_code,
    null as edu_background_name,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as resd_prov_code,
    null as resd_prov_name,
    null as resd_city_code,
    null as resd_city_name,
    null as resd_coty_code,
    null as resd_coty_name,
    null as resd_town_code,
    null as resd_town_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_cotry_name,
    null as resd_addr_housnum,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as cur_addr_poscode,
    null as addr_districts_code,
    null as addr_districts_name,
    null as empr_name,
    null as tel,
    null as mobile,
    null as past_dis_his,
    null as his_breast_cancer_diag_date,
    null as his_breast_cancer_treatment,
    null as his_contact_bleed,
    null as his_cervical_cancer_diag_date,
    null as his_cervical_cancer_treatment,
    null as vulva_disease_his,
    null as sex_bleeding_his,
    null as oprn_his,
    null as gynaecology_proc_his,
    null as dise_now,
    null as family_hereditary_his,
    null as pat_relate_code,
    null as pat_relate_name,
    null as breast_cancer_family_his,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as consanguine_relate_name,
    null as birth_control,
    null as algs_his,
    null as menarche_age,
    null as term_birth_times,
    null as pre_birth_times,
    null as abortion_sum_cnt,
    null as prg_cnt,
    null as matn_cnt,
    null as spontaneous_abortion_times,
    null as induced_abortion_times,
    null as vaginal_midwifery_times,
    null as caesar_times,
    null as dead_fetus_no,
    null as stillbirth_no,
    null as puerperium_infected,
    null as birth_defect_no,
    null as pregnancy_hp,
    null as pregnancy_dm,
    null as pregnancy_dm_other_com,
    null as history_of_macrosomia,
    null as last_gest_end_date,
    null as last_gest_end_code,
    null as last_gest_end_name,
    null as last_deliver_date,
    null as last_deliver_way_code,
    null as last_deliver_way_name,
    null as birth_girl_no,
    null as birth_boy_no,
    null as children_genetic_diseases,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_fnbjsc.scbh00 as manl_no,
    fy_fnbjsc.sbkh00 as ssc_no,
    fy_fnbjsc.jddw00 as filed_emp,
    fy_fnbjsc.sczt00 as manl_stas_code,
    fy_fnbjsc.sczt00 as manl_stas_name,
    fy_fnbjsc.yfzt00 as matn_stas_code,
    fy_fnbjsc.yfzt00 as matn_stas_name,
    fy_fnbjsc.sfgw00 as whtr_hrisk,
    fy_fnbjsc.ynbh00 as inhosp_no,
    fy_fnbjsc.wi_id as imp_data_of_old_sys_no,
    fy_fnbjsc.sffm00 as whtr_brth,
    fy_fnbjsc.sflc00 as whtr_misc,
    fy_fnbjsc.appfs0 as appf_sco_10_m,
    fy_fnbjsc.xgchd0 as postp_recu_plc_modi_flag,
    fy_fnbjsc.mqnl00 as mthr_age,
    fy_fnbjsc.fqnl00 as fthr_age,
    fy_fnbjsc.sfdq00 as whtr_sing_paren,
    fy_fnbjsc.fmxzflag as brth_rcd_dld_mark,
    fy_fnbjsc.scsfxzflag as fst_flup_dld_mark,
    fy_fnbjsc.edwsfxzflag as two_to_fifth_flup_dld_mark,
    fy_fnbjsc.ch42xzflag as postp_42_days_visu_dld_mark,
    fy_fnbjsc.dhzt00 as tel_stas,
    fy_fnbjsc.xczfrq as next_follo_up_date,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_pre_marital_exam partition(dt)
select 
    concat(check_form_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as check_form_id,
    null as unified_uscid,
    null as woman_id,
    null as check_org_code,
    null as card_no,
    null as card_type_code,
    null as card_type_name,
    null as check_othp_id,
    null as oth_name,
    null as name,
    null as certno,
    null as psncert_type_code,
    null as psncert_type_name,
    null as occup_code,
    null as occup_name,
    null as edu_background_code,
    null as edu_background_name,
    null as nation_code,
    null as nation_name,
    null as birth_addr_dscr,
    null as addr_dscr,
    null as kinship_code,
    null as kinship_name,
    null as check_date,
    null as menstrual_duration_day,
    null as menstrual_amount_code,
    null as menstrual_amount_name,
    null as menstrual_cycle,
    null as dysmenorrhea_code,
    null as dysmenorrhea_name,
    null as last_mena_date,
    null as sbp,
    null as dbp,
    null as posture,
    null as special_posture_check_rslt,
    null as mental_state_code,
    null as mental_state_name,
    null as special_face_check_rslt,
    null as facial_feature_check_rslt,
    null as intelligence_dscr,
    null as rhythm_heart_dscr,
    null as heart_rate,
    null as heart_auscultate_res,
    null as lung_auscul_res,
    null as liver_palpation_rslt,
    null as limbs_exam_rslt,
    null as spine_abnorm_exam_rslt,
    null as skin_hair_check_rslt,
    null as thyroid_check_rslt,
    null as other_check_rslt,
    null as physical_exam_phy_name,
    null as adam_apple_check_rslt,
    null as pubic_hair_check_rslt,
    null as penis_check_rslt,
    null as foreskin_check_code,
    null as foreskin_check_name,
    null as left_testis_check_code,
    null as left_testis_check_name,
    null as right_testis_check_code,
    null as right_testis_check_name,
    null as left_epididymis_check_code,
    null as left_epididymis_check_name,
    null as right_epididymis_check_code,
    null as right_epididymis_check_name,
    null as nodules_dscr,
    null as varicocele_mark,
    null as varicocele_part,
    null as varicocele_degree_code,
    null as varicocele_degree_name,
    null as informed_consent_sign,
    null as vulva_check_rslt,
    null as vaginal_check_rslt,
    null as uterus_check_rslt,
    null as left_accessory_check_code,
    null as left_accessory_check_name,
    null as right_accessory_check_code,
    null as right_accessory_check_name,
    null as cervical_check_rslt,
    null as left_breast_exam_code,
    null as left_breast_exam_name,
    null as right_breast_exam_code,
    null as right_breast_exam_name,
    null as vagina_secret_chara_disc,
    null as other_result_of_geni_exami,
    null as genital_examiner_name,
    null as genital_examiner_no,
    null as xray_exam_rslt,
    null as white_blood_cell_class_rslt,
    null as wbc_value_g_l,
    null as rbc_value_gl,
    null as hgb_value,
    null as platelet_value,
    null as urine_sg,
    null as pro_quan_check_value_24h,
    null as uglu_quan_check_value,
    null as urine_ph,
    null as serum_sgpt_value,
    null as hbsag_check_rslt_code,
    null as hbsag_check_rslt_name,
    null as sts_rslt_code,
    null as sts_rslt_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as neisseria_gonor_rslt,
    null as trichomonad_code,
    null as trichomonad_name,
    null as candida_code,
    null as candida_name,
    null as vagina_secret_clean_code,
    null as vagina_secret_clean_name,
    null as check_report_no,
    null as diag_code,
    null as diag_name,
    null as premarit_check_rslt_code,
    null as premarit_check_rslt_name,
    null as premarit_medi_advice_code,
    null as premarit_medi_advice_name,
    null as premar_health_guidance,
    null as premar_health_advice,
    null as consultation_result_code,
    null as consultation_result_name,
    null as other2,
    null as check_doct_name,
    null as check_doct_no,
    null as mold,
    null as galt,
    null as hbsag,
    null as myco_tube_anti_test,
    null as comp_medi_hist_by_the_exam,
    null as sugg_for_furt_insp,
    null as sugg_insp_items,
    null as atti_of_prem_exam_subj,
    null as abnormal_rslt,
    null as medical_measures_recom,
    null as object_of_birth_plan,
    null as contraception_code,
    null as contraception_name,
    null as repr_heal_cons_and_guid_cont,
    null as check_org_name,
    null as fn_report_date,
    null as fv_physician_job_no,
    null as fv_physician_no,
    null as main_inspect_doct_no,
    null as main_inspect_doct_name,
    null as appoint_return_visit_date,
    null as issuance_date,
    null as brdy,
    null as past_dis_his,
    null as disease_code,
    null as disease_name,
    null as is_mental_illness,
    null as no_of_occu,
    null as last_onset_time,
    null as are_you_curr_taki_medi,
    null as curr_life_soci_adap,
    null as oprn_time,
    null as oprn_his,
    null as dise_now,
    null as physician_signature,
    null as family_hereditary_his,
    null as pat_relate_code,
    null as pat_relate_name,
    null as menarche_age,
    null as term_birth_times,
    null as pre_birth_times,
    null as abortion_sum_cnt,
    null as mrg_stas_code,
    null as mrg_stas_name,
    null as birth_girl_no,
    null as birth_boy_no,
    null as children_genetic_diseases,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as consanguine_relate_name,
    null as gyne_exam_method_code,
    null as gyne_exam_method_name,
    null as check_dor_name,
    null as check_dor_no,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_gynec_exam_card partition(dt)
select 
    concat(check_woman_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as check_woman_id,
    null as unified_uscid,
    null as woman_id,
    null as check_org_code,
    null as full_name,
    null as menarche_age,
    null as menopause_age_year,
    null as menstrual_cycle,
    null as period,
    null as menst_situation_code,
    null as menst_situation_name,
    null as have_dysmenorrhea,
    null as chil_fetus,
    null as matn_cnt,
    null as induced_abortion_times,
    null as spontaneous_deliver_times,
    null as last_date_of_birth,
    null as now_son_no,
    null as now_girl_no,
    null as contraception_code,
    null as contraception_name,
    null as oral_drug_name,
    null as name_of_injection,
    null as release_date,
    null as is_sterilization,
    null as sterilization_date,
    null as dscr_of_nc_reasons,
    null as gynecological_diseases,
    null as gyne_cens_inte_years_code,
    null as gyne_cens_inte_years_name,
    null as have_breast_se,
    null as breast_examination_code,
    null as breast_examination_name,
    null as with_or_with_brea_fibr,
    null as brea_fibr_cate_code,
    null as brea_fibr_cate_name,
    null as have_tumor,
    null as tumor_nature_code,
    null as tumor_nature_name,
    null as tumor_site_dscr,
    null as tumor_treatment_methods,
    null as family_tumor_site,
    null as family_tumor_nature_code,
    null as family_tumor_nature_name,
    null as woman_disease_code,
    null as woman_disease_name,
    null as trea_of_women_dise_code,
    null as trea_of_women_dise_name,
    null as build_card_org_name,
    null as build_cards_org_code,
    null as build_card_date,
    null as reger_no,
    null as reger_name,
    null as reg_org_code,
    null as reg_org_name,
    null as last_deliver_way_code,
    null as last_deliver_way_name,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_gynec_exam_record partition(dt)
select 
    concat(check_record_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as check_record_id,
    null as unified_uscid,
    null as check_woman_id,
    null as exam_date,
    null as last_period_stop_date,
    null as check_age,
    null as vulva_check_refer,
    null as vagina_inflammation_dscr,
    null as cervical_dscr,
    null as corpusuteri_check_dscr,
    null as adnex_rslt_name,
    null as breast_check_dscr,
    null as ca_dscr,
    null as tumor_site_dscr,
    null as tumor_nature_code,
    null as tumor_nature_name,
    null as ca_stages_code,
    null as ca_stages_name,
    null as cin_code,
    null as cin_name,
    null as cin_clss_code,
    null as cin,
    null as disease_dscr,
    null as treat_situ_dscr,
    null as reg_dor_name,
    null as reg_dor_code,
    null as census_check_name,
    null as census_check_code,
    null as exam_operator_name,
    null as exam_operator_code,
    null as vulva_unwell_sympt_code,
    null as vulva_unwell_sympt_name,
    null as menstrual_duration,
    null as menstrual_amount_code,
    null as menstrual_amount_name,
    null as menstrual_cycle,
    null as dysmenorrhea_code,
    null as dysmenorrhea_name,
    null as menopause_mark,
    null as menopause_age,
    null as proc_menopause_mark,
    null as hysterectomy_mark,
    null as hysterectomy_date,
    null as menopause_bleed_mark,
    null as leucorrhea_situation,
    null as contraception_code,
    null as contraception_name,
    null as mastodynia_mark,
    null as breast_hyperplasia_mark,
    null as breast_nodule,
    null as abnormal_lactation_mark,
    null as vaginal_check_rslt,
    null as cervical_check_rslt,
    null as vagina_secret_rslt,
    null as breast_dr,
    null as gynecological_check,
    null as vagina_microscopy_rslt,
    null as check_other_rslt,
    null as physical_diagnosis,
    null as risk_factor_screen,
    null as phys_exam_exam_rslt,
    null as process_guide_ad,
    null as kupperman_pf,
    null as depression_score,
    null as anxiety_score,
    null as gynecological_medication,
    null as fill_date,
    null as breast_bscan_rslt,
    null as breast_xray_check_rslt,
    null as state,
    null as data_rank,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_cancer_screening_case partition(dt)
select 
    concat(check_tumour_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as check_tumour_id,
    null as unified_uscid,
    null as woman_id,
    null as census_check_code,
    null as brdy,
    null as adequ_age,
    null as edu_background_code,
    null as edu_background_name,
    null as arpu,
    null as occup_code,
    null as occup_name,
    null as menarche_age,
    null as fist_sex_age,
    null as married_age,
    null as fist_fertility_age,
    null as menopause_age,
    null as menstruation_situation,
    null as marriage_his_code,
    null as marriage_his_name,
    null as number_full_births,
    null as pre_birth_times,
    null as abortion_cnt,
    null as survival_no,
    null as risk_factor_mark,
    null as psychic_factor_mark,
    null as hdl_c_mark,
    null as alcoholic_mark,
    null as smok_mark,
    null as dr_over_5times,
    null as ca_his_mark,
    null as ca_his_name,
    null as ca_family_his,
    null as ca_family_his_name,
    null as estrogen_mark,
    null as estrogen_date,
    null as cervical_erosion_mark,
    null as cin_mark,
    null as menoxenia_mark,
    null as ein_mark,
    null as polycystic_ovaries,
    null as hp_mark,
    null as dm_mark,
    null as obesity_mark,
    null as venereal_his_code,
    null as venereal_his_name,
    null as pelvic_radiation_his_mark,
    null as other_situation,
    null as census_situation,
    null as census_date,
    null as census_type_code,
    null as census_type_name,
    null as census_org_code,
    null as census_org_name,
    null as census_check_name,
    null as exam_org_name,
    null as cin_check_date,
    null as cin_check_rslt,
    null as symptom_duration,
    null as census_positive_sign,
    null as vulva_refer,
    null as vaginal_check_rslt,
    null as cervix_check_rslt,
    null as corpus,
    null as adnex_check_rslt_code,
    null as breast_exam_rslt,
    null as positive_sign_dscr,
    null as diag_situation,
    null as cnfm_date,
    null as diagnose_hospital_code,
    null as diagnose_hospital_name,
    null as cin_diag_mark,
    null as cervical_biopsy_diag_mark,
    null as curett_diag_mark,
    null as b_ultra_diag_mark,
    null as intima_thickness,
    null as mammo_diag_mark,
    null as peri_cytology_diag_mark,
    null as pathological_diag_mark,
    null as oth_meth_diag_mark,
    null as patholog_diag,
    null as pathological_diagnosis_name,
    null as clinical_stage_code,
    null as clinical_stage_name,
    null as treatment_start_date,
    null as treatment_hospital_code,
    null as treatment_hospital_name,
    null as therapies_code,
    null as therapies_name,
    null as therapies_operation_mark,
    null as therapies_radiation_mark,
    null as therapies_chemotherapy_mark,
    null as therapies_entocrine_mark,
    null as therapies_medi_tcm_mark,
    null as therapies_1th_mark_code,
    null as therapies_1th_mark_name,
    null as therapies_2th_mark_code,
    null as therapies_2th_mark_name,
    null as therapies_3th_mark_code,
    null as therapies_3th_mark_name,
    null as therapies_4th_mark_code,
    null as therapies_4th_mark_name,
    null as therapies_5th_mark_code,
    null as therapies_5th_mark_name,
    null as die_reason_code,
    null as die_reason_name,
    null as fn_org,
    null as fill_name,
    null as register_datetime,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_family_planning_service partition(dt)
select 
    concat(plan_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as plan_id,
    null as unified_uscid,
    null as woman_id,
    null as reg_org_code,
    null as otp_no,
    null as ipt_no,
    null as p_proc_code,
    null as p_proc_name,
    null as preoperative_dscr,
    null as operation_dscr,
    null as oprn_oprt_code,
    null as oprn_name,
    null as anst_method_code,
    null as anst_method_name,
    null as proc_complication_mark,
    null as proc_complication_dscr,
    null as special_situat_record,
    null as process_guide_ad,
    null as fu_exam_rslt,
    null as oprn_doc_code,
    null as oprn_doc_name,
    null as oprn_asit_doc_name1,
    null as proc_date,
    null as operation_name,
    null as proc_bleed,
    null as proc_process_mark,
    null as uterus_date,
    null as blastocyst_out_dtime,
    null as drug_use_way,
    null as pathology_rslt,
    null as abdominal_ache_degree_code,
    null as abdominal_ache_degree_name,
    null as diar_cnt,
    null as vomit_times,
    null as take_drug_time,
    null as misc_drug_type_code,
    null as ma_drug_category_name,
    null as abortion_code,
    null as abortion_name,
    null as clean_uterus_operate_mark,
    null as per_blastocyst_dia,
    null as blastocyst_mark,
    null as villus_mark,
    null as cervical_dilation_mark,
    null as right_epididymis_operate_mark,
    null as left_epididymis_operate_mark,
    null as right_var_deferens_cut_length,
    null as left_var_deferens_cut_length,
    null as vasoligation_code,
    null as vasoligation_name,
    null as vasoligation_position_code,
    null as vasoligation_position_name,
    null as tubal_ligation_code,
    null as tubal_ligation_name,
    null as get_implant_exam_res_code,
    null as get_implant_exam_res_name,
    null as implant_part_code,
    null as implant_part_name,
    null as implant_year_limit,
    null as implant_period_code,
    null as implant_period_name,
    null as iud_type_code,
    null as iud_type_name,
    null as iud_get_abnormal_dscr,
    null as iud_get_situat_code,
    null as iud_get_situat_name,
    null as iud_put_code,
    null as iud_put_name,
    null as iud_put_year_limit,
    null as hcg_value,
    null as hcg_res_code,
    null as hcg_res_name,
    null as spermatic_cord_exam_res,
    null as scrotum_exam_res,
    null as right_var_deferens_exam_res,
    null as left_var_deferens_exam_res,
    null as right_ovary_exam_res,
    null as left_ovary_exam_res,
    null as right_oviduct_exam_res,
    null as left_oviduct_exam_res,
    null as uterus_size_code,
    null as uterus_size_name,
    null as uterus_place_code,
    null as uterus_place_name,
    null as chfcomp,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_pregnancy_card_info partition(dt)
select 
    concat(pregnant_card_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as pregnant_card_id,
    null as unified_uscid,
    null as woman_id,
    null as cult_prov_code,
    null as cult_prov_name,
    null as cult_city_code,
    null as cult_city_name,
    null as cult_coty_code,
    null as cult_coty_name,
    null as cult_town_code,
    null as cult_town_name,
    null as cult_comm_code,
    null as cult_comm_name,
    null as cult_cotry_name,
    null as cult_addr_housnum,
    null as cult_addr,
    null as cult_addr_tel,
    null as husband_tel_phone,
    null as husband_health,
    null as husband_ill_code,
    null as husband_ill_name,
    null as last_mena_date,
    null as expect_deliver_date,
    null as married_age,
    null as taking_folic_mark_code,
    null as taking_folic_mark_name,
    null as menstrual_duration_day,
    null as menstrual_amount_code,
    null as menstrual_amount_name,
    null as menstrual_cycle,
    null as dysmenorrhea_code,
    null as dysmenorrhea_name,
    null as pregnancy_complication,
    null as other_abn_pregnancy,
    null as rpr_name,
    null as tpha_name,
    null as risk_factor_name,
    null as risk_score,
    null as whtr_hrisk,
    null as hobby_name,
    null as ad_fertility_his,
    null as bleed_his,
    null as build_card_date,
    null as build_org_code,
    null as build_org_name,
    null as build_gest_weeks,
    null as build_org_tel,
    null as begin_date,
    null as end_date,
    null as build_bed_date,
    null as duty_dor_no,
    null as duty_dor_name,
    null as case_mark,
    null as end_case_date,
    null as reger_no,
    null as reger_name,
    null as blotype_abo_code,
    null as blotype_abo_name,
    null as blotype_rh_code,
    null as blotype_rh_name,
    null as tel_type,
    null as spouse_emp_name,
    null as spouse_occup_code,
    null as spouse_occup_name,
    null as spouse_cert_no,
    null as spouse_psncert_type_code,
    null as spouse_psncert_type_name,
    null as spouse_edu_background_code,
    null as spouse_edu_background_name,
    null as spouse_nation_code,
    null as spouse_nation_name,
    null as spouse_brdy,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_exam_status partition(dt)
select 
    concat(prenatal_examination_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as prenatal_examination_id,
    null as unified_uscid,
    null as pregnant_card_id,
    null as prenatal_examination_date,
    null as prenatal_examination_no,
    null as exam_date,
    null as fetal_move_weeks,
    null as sbp,
    null as dbp,
    null as height,
    null as weight,
    null as reject_item_mark,
    null as nutritional,
    null as nutritional_code,
    null as nutritional_name,
    null as spine_mark,
    null as thyroid_mark,
    null as nipple_mark,
    null as headache_mark,
    null as lungs_norm_mark,
    null as palpitation_mark,
    null as exhausted_mark,
    null as liver_en_mark,
    null as spleen_en_mark,
    null as abde,
    null as cervix_height,
    null as fetus_code,
    null as fetus_name,
    null as fetal_move,
    null as bleeding_time,
    null as clotting_time,
    null as uglu_quan_check_value,
    null as kidney_check_res,
    null as liver_check_res_name,
    null as hbsag_check_res_name,
    null as ecg_exam_rslt,
    null as torch_check,
    null as dropsy,
    null as e3,
    null as bpd,
    null as amniotic_fluid,
    null as fhr,
    null as join_mark,
    null as cervix_uteri_dscr,
    null as cervix_uteri_code,
    null as cervix_uteri_name,
    null as iliac_interspinal_diameter,
    null as ic,
    null as subjects_ec_diam,
    null as ischial_tuberosity_diam,
    null as psadoo,
    null as hemoglobin_value,
    null as pro_quan_check_value,
    null as pro_qual_res_code,
    null as pro_qual_res_name,
    null as disposal_methods_code,
    null as disposal_methods_name,
    null as deal_date,
    null as disposal_dscr,
    null as syphilis_rlst,
    null as screening_org_name,
    null as hiv_anti_name,
    null as hiv_org_code,
    null as reg_org_code,
    null as reg_org_name,
    null as other_gynecology_dscr,
    null as measure_date,
    null as check_gest_weeks,
    null as urinate_dscr,
    null as blood_suar_check_value,
    null as hcg_value,
    null as kidney_check_res_code,
    null as kidney_check_res_name,
    null as liver_check_rslt_code,
    null as liver_check_rslt_name,
    null as swelling_degree_code,
    null as swelling_degree_name,
    null as pelvimetry_gest_weeks,
    null as pelvimetry_date,
    null as intercrestal_diameter,
    null as interspinal_diameter,
    null as high_risk_preg_code,
    null as high_risk_preg_name,
    null as critical_pregnant_mark,
    null as mor_sickness_mark,
    null as mor_sickness_weeks,
    null as pre_abnormal_res,
    null as physical_signs,
    null as sympt_definition,
    null as basic_sbp,
    null as basic_dbp,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_info partition(dt)
select 
    concat(antenatal_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as antenatal_id,
    null as unified_uscid,
    null as pregnant_card_id,
    null as exam_date,
    null as otp_no,
    null as ipt_no,
    null as fetal_move_weeks,
    null as sbp,
    null as dbp,
    null as height,
    null as weight,
    null as reject_item_mark,
    null as nutritional_code,
    null as nutritional_name,
    null as spine_mark,
    null as knee_mark,
    null as tooth_mark,
    null as thyroid_mark,
    null as nipple_mark,
    null as heart_mark,
    null as lungs_norm_mark,
    null as liver_en_mark,
    null as spleen_en_mark,
    null as abde,
    null as cervix_height,
    null as fetus_code,
    null as fetus_name,
    null as fhr,
    null as presentation_situation_code,
    null as presentation_situation_name,
    null as join_mark,
    null as engagement_situation,
    null as vulva_code,
    null as vulva_name,
    null as vagina_code,
    null as vagina_name,
    null as uterine_code,
    null as uterine_name,
    null as uterine_size_code,
    null as uterine_size_name,
    null as adnexa_uteri_mark,
    null as iliac_interspinal_diameter,
    null as ic,
    null as subjects_ec_diam,
    null as ischial_tuberosity_diam,
    null as psadoo,
    null as hemoglobin_value,
    null as pro_quan_check_value,
    null as pro_qual_res_code,
    null as pro_qual_res_name,
    null as leucorrhea_trichomonal_code,
    null as leucorrhea_trichomonal_name,
    null as leucorrhea_mold_code,
    null as leucorrhea_mold_name,
    null as high_risk_gest_weeks,
    null as risk_score,
    null as primary_diagnostic,
    null as disposal_methods_code,
    null as disposal_methods_name,
    null as deal_date,
    null as disposal_dscr,
    null as gest_weeks_score,
    null as estimate_nwb_wt,
    null as est_deliver_way_code,
    null as est_deliver_way_name,
    null as main_deliver_code,
    null as main_deliver_name,
    null as exception_interview_times,
    null as reg_org_code,
    null as reg_org_name,
    null as syphilis_screen_mark,
    null as syphilis_rlst,
    null as screen_preg_week,
    null as screening_org_name,
    null as cure_org_name,
    null as medium_anemia_gest_weeks,
    null as medium_anemia,
    null as medium_anemia_hos,
    null as ultrasound_b_gest_weeks,
    null as ultrasound_b_screen_rslt,
    null as ultrasound_b_screen_org_name,
    null as consultation_hiv,
    null as check_hiv,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as symptom,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_extend_info partition(dt)
select 
    concat(antenatal_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as antenatal_id,
    null as unified_uscid,
    null as pregnant_card_id,
    null as exam_date,
    null as otp_no,
    null as ipt_no,
    fy_fncqjc.scxtbh as manl_sys_no,
    fy_fncqjc.sznl00 as adequ_age,
    fy_fncqjc.zfxm00 as husb_name,
    fy_fncqjc.zfnl00 as husb_age,
    fy_fncqjc.zfgzdw as husb_job_emp,
    fy_fncqjc.zfjkqk as hus_health_code,
    fy_fncqjc.zfjkqk as hus_health_name,
    fy_fncqjc.mcyj00 as mrg_his_last_mena,
    fy_fncqjc.jhnl00 as mrg_his_age_marr,
    fy_fncqjc.qyjh00 as mrg_his_affi_mrg,
    fy_fncqjc.yjs000 as mrg_his_mena_his,
    fy_fncqjc.ycq000 as mrg_his_edc,
    fy_fncqjc.yunci0 as mrg_his_preg_time,
    fy_fncqjc.ccydfm as mrg_his_fetl_birt_vagi_brth,
    fy_fncqjc.ccpgc0 as mrg_his_fetl_birt_cesr_sect,
    fy_fncqjc.rsfy00 as hist_cur_preg_prey_react,
    fy_fncqjc.cgtd00 as curpreg_fsm_code,
    fy_fncqjc.cgtd00 as curpreg_fsm_name,
    fy_fncqjc.jt0000 as hist_cur_preg_seve_vomi,
    fy_fncqjc.ydcx00 as hist_cur_preg_vagi_blee,
    fy_fncqjc.fr0000 as hist_cur_preg_feve,
    fy_fncqjc.gm0000 as hist_cur_preg_alg,
    fy_fncqjc.fy0000 as hist_cur_preg_dose,
    fy_fncqjc.bfgr00 as hist_cur_preg_viri_infe,
    fy_fncqjc.jcyhwz as hist_cur_preg_cont_harm_subs,
    fy_fncqjc.fbyy0 as curpreg_bcp,
    fy_fncqjc.xysqt0 as hist_cur_preg_oth,
    fy_fncqjc.rsdyrq as prey_his_first_chid_date,
    fy_fncqjc.rsdyqk as preyhis_fcnm_code,
    fy_fncqjc.rsdyqk as preyhis_fcnm_name,
    fy_fncqjc.rsderq as prey_his_seco_birt_date,
    fy_fncqjc.rsdeqk as prey_his_seco_birt_info,
    fy_fncqjc.rsdsrq as prey_his_thrd_chid_date,
    fy_fncqjc.rsdsqk as prey_his_thrd_chid_info,
    fy_fncqjc.rsstrq as prey_his_four_trim_date,
    fy_fncqjc.rsstqk as prey_his_four_trim_info,
    fy_fncqjc.jwsx00 as past_hear,
    fy_fncqjc.jwsf00 as past_lung,
    fy_fncqjc.jwsg00 as past_live,
    fy_fncqjc.jwss00 as past_his_kid,
    fy_fncqjc.jwsgxy as past_his_high_bloo_pres,
    fy_fncqjc.jwstnb as past_his_diab,
    fy_fncqjc.jwsjk0 as past_his_hyper,
    fy_fncqjc.jwsgms as past_his_algs,
    fy_fncqjc.jwsjsb as past_his_psych,
    fy_fncqjc.jwsxyb as past_his_hema,
    fy_fncqjc.jwsdx0 as past_his_epile,
    fy_fncqjc.jwssss as past_his_oprn_his,
    fy_fncqjc.jwsqt0 as past_his_oth,
    fy_fncqjc.jzsbr0 as fmhis_in_pers,
    fy_fncqjc.jzsar0 as fmhis_love_ones,
    fy_fncqjc.tjjcxy as phys_exam_bas_bloo_pres,
    fy_fncqjc.tjxy00 as phys_exam_bloo_pres,
    fy_fncqjc.tjtzzs as phys_exam_wt_ind,
    fy_fncqjc.tjs000 as phys_exam_kid,
    fy_fncqjc.tjjzsz as phys_exam_spin_fors,
    fy_fncqjc.tjfz00 as phys_exam_swel,
    fy_fncqjc.tjjfs0 as phys_exam_tend_refl,
    fy_fncqjc.tjjmqz as phys_exam_vari_vein,
    fy_fncqjc.tjqt00 as phys_exam_oth,
    fy_fncqjc.fjwy00 as gyn_exam_vulv_code,
    fy_fncqjc.fjwy00 as gyn_exam_vulv_name,
    fy_fncqjc.fjyd00 as gyn_exam_vagi_code,
    fy_fncqjc.fjyd00 as gyn_exam_vagi_name,
    fy_fncqjc.fjgj00 as gyn_exam_cerv_code,
    fy_fncqjc.fjgj00 as gyn_exam_cerv_name,
    fy_fncqjc.fjgt00 as gyn_exam_uter_code,
    fy_fncqjc.fjgt00 as gyn_exam_uter_name,
    fy_fncqjc.fjfj00 as gyn_exam_att,
    fy_fncqjc.cjgjjj as pelvic_iip,
    fy_fncqjc.cjgj00 as pelvic_icp,
    fy_fncqjc.cjdcwj as pelvic_sfp,
    fy_fncqjc.cjzgjj as pelvic_sip,
    fy_fncqjc.fjxdb0 as asst_exam_hemog,
    fy_fncqjc.fjncg0 as asst_exam_urin_rout_urin_prot,
    fy_fncqjc.fjbcg0 as asst_exam_leuk_cnvl_vagi_secre,
    fy_fncqjc.fjxx00 as asst_exam_blotype_abo,
    fy_fncqjc.fjhiv0 as asst_exam_hiv,
    fy_fncqjc.fjhbsa as asst_exam_hbsag,
    fy_fncqjc.fjcqsc as asst_exam_labr_old_scre,
    fy_fncqjc.fjsg00 as asst_exam_kidn_fun,
    fy_fncqjc.fjgg00 as asstexam_lft_gpt,
    fy_fncqjc.fjbc00 as asst_exam_bmus,
    fy_fncqjc.fjxdt0 as asst_exam_electro,
    fy_fncqjc.bjzd00 as hcare_guid,
    fy_fncqjc.czzdg0 as fstdiag_diag_g,
    fy_fncqjc.czzdp0 as fstdiag_diag_p,
    fy_fncqjc.czzdrs as fstdiag_diag_week_womb_prey,
    fy_fncqjc.czzdnr as fstdiag_diag_diag_cont,
    fy_fncqjc.ryrq00 as admission_date,
    fy_fncqjc.zs0000 as chfcomp,
    fy_fncqjc.ryzd00 as adm_diag,
    fy_fncqjc.cl0000 as dspo,
    fy_fncqjc.zz0000 as refl,
    fy_fncqjc.jcdw00 as exam_emp,
    fy_fncqjc.ckmzbh as obst_otp_no,
    fy_fncqjc.rsdytc as prey_his_fetts_one,
    fy_fncqjc.rsdetc as prey_his_fetts_2,
    fy_fncqjc.rsdstc as prey_his_fetts_thr,
    fy_fncqjc.rssttc as prey_his_fetts_4,
    fy_fncqjc.bsxwz0 as illhis_inqer,
    fy_fncqjc.jcz000 as exam,
    fy_fncqjc.hkzt00 as resd_status_code,
    fy_fncqjc.hkzt00 as resd_status_name,
    fy_fncqjc.qm0000 as sign,
    fy_fncqjc.jcdwna as exam_unit_name,
    fy_fncqjc.jcys00 as exam_dr,
    fy_fncqjc.jcysna as exam_dr_name,
    fy_fncqjc.fjxxrh as asst_exam_blotype_rh,
    fy_fncqjc.ggngc0 as sgot_value,
    fy_fncqjc.ggnbdb as blo_prot,
    fy_fncqjc.ggnzdh as dbil_value,
    fy_fncqjc.ggnjhd as comb_bili,
    fy_fncqjc.sgnxns as blo_urea_nitr,
    fy_fncqjc.tbys00 as fill_form_dr,
    fy_fncqjc.zhbjz0 as last_edit,
    fy_fncqjc.zhbjsj as last_edit_time,
    fy_fncqjc.tjg000 as phys_exam_live,
    fy_fncqjc.tbyz00 as fill_form_geso,
    fy_fncqjc.tbyzts as fill_form_geso_days,
    fy_fncqjc.gwys00 as hrisk_fac,
    fy_fncqjc.gwysbh as hrisk_fac_no,
    fy_fncqjc.gwpf00 as hrisk_sco,
    fy_fncqjc.ncgnt0 as asst_exam_urin_rout_urin_gluc,
    fy_fncqjc.ncgntt as asst_exam_urin_rout_urin_keto,
    fy_fncqjc.ncgnqx as asstexam_urrout_occultblood,
    fy_fncqjc.ncgqt0 as asst_exam_urin_rout_oth,
    fy_fncqjc.ydqjd0 as asst_exam_leuk_cnvl_vagi_clea,
    fy_fncqjc.jzsgxy as fmhis_high_bloo_pres,
    fy_fncqjc.jzstnb as fmhis_diab,
    fy_fncqjc.jzsycb as fmhis_hered_dise,
    fy_fncqjc.jzsjsb as fmhis_psych,
    fy_fncqjc.jzscd0 as fmhis_demen,
    fy_fncqjc.jzsjx0 as fmhis_defo,
    fy_fncqjc.jzsqt0 as fmhis_oth,
    fy_fncqjc.jzsgxya as fmhis_high_bloo_pres_love,
    fy_fncqjc.jzstnba as fmhis_diab_love,
    fy_fncqjc.jzsycba as fmhis_hered_dise_love,
    fy_fncqjc.jzsjsba as fmhis_psych_love,
    fy_fncqjc.jzscd0a as fmhis_demen_love,
    fy_fncqjc.jzsjx0a as fmhis_defo_love,
    fy_fncqjc.jzsqt0a as fmhis_oth_love,
    fy_fncqjc.fjjcgg as gyn_exam_uter_heig,
    fy_fncqjc.fjbg00 as asst_exam_hepa_c,
    fy_fncqjc.scybst as labr_old_scre_18_tris,
    fy_fncqjc.sceyst as labr_old_scre_21_tris,
    fy_fncqjc.sjgqx0 as labr_old_scre_neur_tube_defe,
    fy_fncqjc.rsdwtc as prey_his_fetts_five,
    fy_fncqjc.rsdwrq as prey_his_fifth_fets_date,
    fy_fncqjc.rsdwqk as prey_his_fifth_fets_info,
    fy_fncqjc.gwyyrq as lynn_cocc,
    fy_fncqjc.cjz000 as crter,
    fy_fncqjc.cjrq00 as crte_date,
    fy_fncqjc.zfsfzh as husb_cert_no,
    null as husb_cert_type_code,
    null as husb_cert_type_name,
    fy_fncqjc.td0000 as fetal_move,
    fy_fncqjc.ckjcgg as obst_exam_bott_uter_hgt,
    fy_fncqjc.ckjcfw as obst_exam_abde,
    fy_fncqjc.xl0000 as first_exp_hwb,
    fy_fncqjc.ckjctw as obst_exam_fetl_posi,
    fy_fncqjc.cktxl0 as dropsy,
    fy_fncqjc.fz0000 as err_addr_modi_hosp,
    fy_fncqjc.dzxgyy as err_addr_modi_dr_id,
    fy_fncqjc.dzxgid as err_addr_modi_name,
    fy_fncqjc.dzxgxm as err_addr_modi_time,
    fy_fncqjc.dzxgsj as tel,
    fy_fncqjc.dhhm00 as whtr_notc,
    fy_fncqjc.fjtsc0 as asst_exam_sug_scre,
    fy_fncqjc.fjtsc2 as asst_exam_empt_stom_bloo_gluc,
    fy_fncqjc.bcts00 as last_mena_whtr_bmus_proj,
    fy_fncqjc.sfwz00 as whtr_intact_mark,
    fy_fncqjc.gwyse0 as hrisk_fac_2,
    fy_fncqjc.gwyss0 as hrisk_fac_3,
    fy_fncqjc.gwysbhe as hrisk_fac_no_2,
    fy_fncqjc.gwysbhs as hrisk_fac_no_3,
    fy_fncqjc.gwpfe0 as hrisk_sco_2,
    fy_fncqjc.gwyze0 as high_crit_preg_week_2,
    fy_fncqjc.gwpfs0 as hrisk_sco_3,
    fy_fncqjc.gwyzs0 as high_crit_preg_week_3,
    fy_fncqjc.xcgbxb as asstexam_br_wbc_count,
    fy_fncqjc.xcgxxb as asstexam_br_plateletcount,
    fy_fncqjc.xcgqt0 as asst_exam_bloo_rout_oth,
    fy_fncqjc.ygbmky as asstexam_hbsag,
    fy_fncqjc.ygbmkt as asstexam_hbsab,
    fy_fncqjc.ygeky0 as asstexam_hbeag,
    fy_fncqjc.ygekt0 as asstexam_hbeab,
    fy_fncqjc.yghxkt as asstexam_hbcab,
    fy_fncqjc.fqcsrq as husb_brdy,
    fy_fncqjc.resbz0 as prey_his_memo,
    fy_fncqjc.jwspx0 as past_his_anem,
    fy_fncqjc.fkssbz as gyn_oprn_his_flag,
    fy_fncqjc.fksss0 as gynaecology_proc_his,
    fy_fncqjc.grsxy0 as psn_his_smok,
    fy_fncqjc.grsyj0 as whtr_drnk,
    fy_fncqjc.grsyw0 as psn_his_taki_medn,
    fy_fncqjc.grsyhw as psn_his_cont_toxi_harm_subs,
    fy_fncqjc.grsfsx as psn_his_cont_radi,
    fy_fncqjc.grsqt0 as psn_his_oth,
    fy_fncqjc.rshbzs as prey_copn_his,
    fy_fncqjc.rsbfzs as prey_cop_his,
    fy_fncqjc.yqtz00 as pre_preg_wt,
    fy_fncqjc.fjjjbz as gyn_exam_refu_exam_flag,
    fy_fncqjc.wyjjbz as vulv_refu_exam_flag,
    fy_fncqjc.ydjjbz as vagi_refu_exam_flag,
    fy_fncqjc.gjjjbz as cerv_refu_exam_flag,
    fy_fncqjc.gtjjbz as uter_refu_exam_flag,
    fy_fncqjc.fjflag as att_refu_exam_flag,
    fy_fncqjc.mdxqlb as syph_sero_test_type_code,
    fy_fncqjc.mdxqlb as syph_sero_test_type_name,
    fy_fncqjc.mqgj00 as mthr_ntly_code,
    fy_fncqjc.mqgj00 as mthr_ntly_name,
    fy_fncqjc.fqgj00 as fthr_ntly_code,
    fy_fncqjc.fqgj00 as fthr_ntly_name,
    null as mother_cert_no,
    fy_fncqjc.mqsfzl as mother_psncert_type_code,
    fy_fncqjc.mqsfzl as mother_psncert_type_name,
    null as father_cert_no,
    fy_fncqjc.fqsfzl as father_cert_type_code,
    fy_fncqjc.fqsfzl as father_cert_type_name,
    fy_fncqjc.blflag as add_flag,
    fy_fncqjc.tjrq00 as examination_date,
    fy_fncqjc.sbkh00 as ssc_no,
    fy_fncqjc.ncgbxb as asstexam_urrout_wbc,
    fy_fncqjc.ncgwyc as asstexam_urrout_noabn,
    fy_fncqjc.zstj00 as chfcomp_menop_week,
    fy_fncqjc.zszz00 as chfcomp_symp,
    fy_fncqjc.fkjcqt as gyn_exam_oth,
    fy_fncqjc.zszzqt as chfcomp_symp_oth,
    fy_fncqjc.bcsdj0 as asstexam_bus_bpd,
    fy_fncqjc.bctw00 as asstexam_bus_headcirc,
    fy_fncqjc.bcfw00 as asst_exam_b_colo_ultr_abde,
    fy_fncqjc.bcys00 as asstexam_bus_amnioticfluid,
    fy_fncqjc.bctx00 as asstexam_bus_fetalheart,
    fy_fncqjc.bctpcs as asstexam_bus_placentamaturity,
    fy_fncqjc.bctpfy as asstexam_bus_placentaattach,
    fy_fncqjc.bctpxy as asstexam_bus_placcervdist,
    fy_fncqjc.bcjxl0 as asstexam_bus_umbilicalflow,
    fy_fncqjc.bczgxd as asstexam_bus_uterusthick,
    fy_fncqjc.bcqt00 as asst_exam_b_colo_ultr_oth,
    fy_fncqjc.bcjl00 as asst_exam_b_colo_ultr_ccls,
    fy_fncqjc.ft4000 as asst_exam_thyr_dise_scre_ft4,
    fy_fncqjc.tsh000 as asst_exam_thyr_dise_scre_tsh,
    fy_fncqjc.tpoab0 as asst_exam_thyr_dise_scre_tpoab,
    fy_fncqjc.fjshqt as asstexam_biofull_noabn,
    fy_fncqjc.shkfxt as asstexam_biofull_fbg,
    fy_fncqjc.shgbza as asstexam_biofull_gpt,
    fy_fncqjc.shgcza as asstexam_biofull_got,
    fy_fncqjc.shzdb0 as asstexam_biofull_tp,
    fy_fncqjc.shbdb0 as asst_exam_bioch_full_set_albu,
    fy_fncqjc.shzdzs as asstexam_biofull_tba,
    fy_fncqjc.shzdhs as asstexam_biofull_tbil,
    fy_fncqjc.shjg00 as asst_exam_bioch_full_set_myon,
    fy_fncqjc.shnsd0 as asstexam_biofull_bun,
    fy_fncqjc.shns00 as asstexam_biofull_ua,
    fy_fncqjc.shldh0 as asst_exam_bioch_full_set_ldh,
    fy_fncqjc.shqt00 as asst_exam_bioch_full_set_oth,
    fy_fncqjc.rprdd0 as asst_exam_rpr,
    fy_fncqjc.ggkfxt as asstexam_lf_fbg,
    fy_fncqjc.ggqt00 as asst_exam_live_fun_oth,
    fy_fncqjc.sgqt00 as asst_exam_kidn_fun_oth,
    fy_fncqjc.jzsh00 as asst_exam_er_bioch,
    fy_fncqjc.xdtwyc as asst_exam_electro_un_abn,
    fy_fncqjc.fjnxgn as asst_exam_coag_efcc,
    fy_fncqjc.bcggj0 as asstexam_bus_femurneck,
    fy_fncqjc.jcxmjc as asst_exam_refu_check,
    fy_fncqjc.mz0000 as mthr_naty_code,
    fy_fncqjc.mz0000 as mthr_naty_name,
    fy_fncqjc.fqmz00 as fthr_naty_code,
    fy_fncqjc.fqmz00 as fthr_naty_name,
    fy_fncqjc.fjtppa as asst_exam_tppa,
    fy_fncqjc.sfjh00 as whtr_mrg,
    fy_fncqjc.bjzdqt as hcare_guid_oth,
    fy_fncqjc.fjqt00 as asst_exam_oth,
    fy_fncqjc.wcjysb as asstexam_nipt_tris13,
    fy_fncqjc.wcjyey as asstexam_nipt_tris21,
    fy_fncqjc.jcqt00 as asst_exam_refu_check_oth,
    fy_fncqjc.xcgzxl as asstexam_coag_neutpct,
    fy_fncqjc.nxpt00 as asst_exam_coag_efcc_pt,
    fy_fncqjc.nxinr0 as asst_exam_coag_efcc_inr,
    fy_fncqjc.nxfg00 as asst_exam_coag_efcc_fg,
    fy_fncqjc.nxappt as asst_exam_coag_efcc_aptt,
    fy_fncqjc.nxdejt as asst_exam_coag_efcc_d_dime,
    fy_fncqjc.gwyzy0 as high_crit_preg_week_1,
    fy_fncqjc.yyrqe0 as hrisk_ordr_date_2,
    fy_fncqjc.yyrqs0 as hrisk_ordr_date_3,
    fy_fncqjc.xcgkdrq as bloo_rout_bilg_date,
    fy_fncqjc.xxkdrq as blotype_bilg_date,
    fy_fncqjc.ncgkdrq as urin_rout_bilg_date,
    fy_fncqjc.bdcgkdrq as leuk_cnvl_bilg_date,
    fy_fncqjc.yxgykdrq as hepa_b_five_bilg_date,
    fy_fncqjc.rprkdrq as rpr_bilg_date,
    fy_fncqjc.tppakdrq as tppa_bilg_date,
    fy_fncqjc.hivkdrq as hiv_bilg_date,
    fy_fncqjc.bgkdrq as hepa_c_bilg_date,
    fy_fncqjc.hbsagkdrq as hbsag_bilg_date,
    fy_fncqjc.cqsckdrq as labr_old_scre_bilg_date,
    fy_fncqjc.wcjykdrq as ninve_gene_dect_bilg_date,
    fy_fncqjc.ogttkdrq as oggt_bilg_date,
    fy_fncqjc.sgkdrq as kidn_fun_bilg_date,
    fy_fncqjc.ggkdrq as live_fun_bilg_date,
    fy_fncqjc.shqtkdrq as bioch_full_set_bilg_date,
    fy_fncqjc.bckdrq as b_colo_ultr_bilg_date,
    fy_fncqjc.jzshkdrq as er_bioch_bilg_date,
    fy_fncqjc.jzxkdrq as thyr_dise_scre_bilg_date,
    fy_fncqjc.xdtkdrq as electro_bilg_date,
    fy_fncqjc.nxgnkdrq as coag_efcc_bilg_date,
    fy_fncqjc.shk000 as asst_exam_bioch_full_set_pot,
    fy_fncqjc.shna00 as asst_exam_bioch_full_set_sodi,
    fy_fncqjc.shcl00 as asst_exam_bioch_full_set_chlo,
    fy_fncqjc.shca00 as asst_exam_bioch_full_set_calc,
    fy_fncqjc.shmg00 as asst_exam_bioch_full_set_magn,
    fy_fncqjc.jzk000 as asst_exam_er_bioch_pot,
    fy_fncqjc.jzna00 as asst_exam_er_bioch_sodi,
    fy_fncqjc.jzcl00 as asst_exam_er_bioch_chlo,
    fy_fncqjc.jzca00 as asst_exam_er_bioch_calc,
    fy_fncqjc.jzmg00 as asst_exam_er_bioch_magn,
    fy_fncqjc.jzzdb0 as asst_exam_er_bioch_totl_prot,
    fy_fncqjc.jzbdb0 as asst_exam_er_bioch_albu,
    fy_fncqjc.jzzdhs as asst_exam_er_bioch_drt_bili,
    fy_fncqjc.jzjdhs as asst_exam_er_bioch_indi_bili,
    fy_fncqjc.jzgbzam as asstexam_er_gpt,
    fy_fncqjc.jzgczam as asstexam_er_got,
    fy_fncqjc.jzxt00 as asst_exam_er_bioch_bloo_gluc,
    fy_fncqjc.jzrstam as asst_exam_er_bioch_lacta_deam,
    fy_fncqjc.jzns00 as asst_exam_er_bioch_urea,
    fy_fncqjc.jzjg00 as asst_exam_er_bioch_creat,
    fy_fncqjc.ft3000 as asst_exam_thyr_ft3,
    fy_fncqjc.nxmsj0 as asst_exam_coag_efcc_proth_time,
    fy_fncqjc.ggzdzs as asstexam_lf_tba,
    fy_fncqjc.ggnzdb as asst_exam_live_fun_totl_prot,
    fy_fncqjc.jznsh0 as asst_exam_er_bioch_uric_acid,
    fy_fncqjc.xcglbx as asst_exam_bloo_rout_lymph_prct,
    fy_fncqjc.xcghxb as asstexam_br_mcv,
    fy_fncqjc.xcgxhd as asstexam_br_mch,
    fy_fncqjc.gtqt00 as uter_oth,
    fy_fncqjc.ncgndy as asst_exam_urin_rout_urobi,
    fy_fncqjc.ncgdhs as asst_exam_urin_rout_bili,
    fy_fncqjc.jzzdgc as asst_exam_er_bioch_totl_chol,
    fy_fncqjc.bdcgmj as leuk_cnvl_mold,
    fy_fncqjc.bdcgdc as leuk_cnvl_tric,
    fy_fncqjc.bdcgbv as leuk_cnvl_bacte_vagi_dise_bv,
    fy_fncqjc.bdcgqt as leuk_cnvl_oth,
    fy_fncqjc.fjbdcg as leuk_cnvl_whtr_abn,
    fy_fncqjc.cxgzxl as asstexam_br_neutpct,
    fy_fncqjc.fqhkd0 as husb_domi,
    fy_fncqjc.nz0000 as to_be_diag,
    fy_fncqjc.fjyyrq as retes_ordr_date,
    fy_fncqjc.fjmr00 as retes_def_btn,
    fy_fncqjc.tjmr00 as phys_exam_def_btn,
    fy_fncqjc.ogttqt as ogtt_oth,
    fy_fncqjc.cqscqt as labr_old_scre_oth,
    fy_fncqjc.amyzx0 as aids_syph_b_cslt,
    fy_fncqjc.thxhkdrq as glyc_hemog_bilg_date,
    fy_fncqjc.thxhdb as asst_exam_glyc_hemog,
    fy_fncqjc.lrzxm0 as inpt_the_name,
    fy_fncqjc.jczxm0 as exam_operator_name,
    fy_fncqjc.qxlpi0 as fetus_umbil_bloo_flow_mnit_pi,
    fy_fncqjc.qxlri0 as fetus_umbil_bloo_flow_mnit_ri,
    fy_fncqjc.qxljckdrq as fetus_umbflow_orderdate,
    fy_fncqjc.scbz00 as upld_flag,
    fy_fncqjc.rsstc6 as prey_his_fetts_six,
    fy_fncqjc.rsstc7 as prey_his_fetts_seve,
    fy_fncqjc.xcgjcdw as bloo_rout_exam_emp,
    fy_fncqjc.pttcd0 as bloo_gluc_mes,
    fy_fncqjc.pttkdrq as bloo_gluc_mes_bilg_date,
    fy_fncqjc.kaxjkdrq as anti_a_pote_bilg_date,
    fy_fncqjc.kbxjkdrq as anti_b_pote_bilg_date,
    fy_fncqjc.kaxj00 as anti_a_pote,
    fy_fncqjc.kbxj00 as anti_b_pote,
    fy_fncqjc.yszd00 as amnio_flui_diag,
    fy_fncqjc.gjxbjc as cerv_cyt_exam,
    fy_fncqjc.cszghd as ultra_uterusthick,
    fy_fncqjc.csgjcd as ultra_measu_cerv_leng,
    fy_fncqjc.cscrp0 as rapi_crp,
    fy_fncqjc.pxjy01 as medt_anem_gene_dect,
    fy_fncqjc.coombsiggc as drt_reta_glon_test,
    fy_fncqjc.tdhcgz as fetus_mri,
    fy_fncqjc.rsfyy0 as prey_react_mon,
    fy_fncqjc.cgtdy0 as first_sens_fetl_mov_mon,
    fy_fncqjc.fjtrus as trust,
    fy_fncqjc.trusjcdw as trust_exam_emp,
    fy_fncqjc.truskdrq as trust_bilg_date,
    fy_fncqjc.fjelis as elisa,
    fy_fncqjc.elisjcdw as elisa_exam_emp,
    fy_fncqjc.eliskdrq as elisa_bilg_date,
    fy_fncqjc.fjrt00 as rt,
    fy_fncqjc.rtjcdw as rt_exam_emp,
    fy_fncqjc.rtkdrq as rt_bilg_date,
    fy_fncqjc.ft30dw as asst_exam_thyr_ft3_emp,
    fy_fncqjc.ft40dw as asst_exam_thyr_ft4_emp,
    fy_fncqjc.tsh0dw as asst_exam_thyr_tsh_emp,
    fy_fncqjc.bzyszs as asstexam_bus_afi,
    fy_fncqjc.fjjcbz as asst_exam,
    fy_fncqjc.rprjc0 as syph_whtr_dect,
    fy_fncqjc.rprzdgr as syph_whtr_ifet,
    fy_fncqjc.rprzl as syph_whtr_trt,
    fy_fncqjc.ygjc0 as hepa_b_whtr_dect,
    fy_fncqjc.ygjcjg as hepa_b_dect_rslt,
    fy_fncqjc.hivjcqzrq as hiv_cnfm_date,
    fy_fncqjc.hivjcjg as hiv_dect_rslt,
    fy_fncqjc.hivjc0 as hiv_whtr_dect,
    fy_fncqjc.scqks0 as scre_rslt_info_no_3,
    fy_fncqjc.pgnr00 as eval_cont,
    fy_fncqjc.rspgbh as eval_cont_no,
    fy_fncqjc.pgqk00 as eval_rslt_info_no,
    fy_fncqjc.pgnre0 as eval_cont_2,
    fy_fncqjc.pgnrs0 as eval_cont_3,
    fy_fncqjc.rspgbhe as eval_cont_no_2,
    fy_fncqjc.rspgbhs as eval_cont_no_3,
    fy_fncqjc.pgqke0 as eval_rslt_info_no_2,
    fy_fncqjc.pgqks0 as eval_rslt_info_no_3,
    fy_fncqjc.scnr00 as scre_cont,
    fy_fncqjc.rsscbh as scre_cont_no,
    fy_fncqjc.scqk00 as scre_rslt_info_no,
    fy_fncqjc.scnre0 as scre_cont_2,
    fy_fncqjc.scnrs0 as scre_cont_3,
    fy_fncqjc.rsscbhe as scre_cont_no_2,
    fy_fncqjc.rsscbhs as scre_cont_no_3,
    fy_fncqjc.scqke0 as scre_rslt_info_no_2,
    null as rchk_flag,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_first_prenatal_visit partition(dt)
select 
    concat(unified_uscid,fu_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    null as fu_no,
    null as pregnant_card_id,
    null as fu_org_code,
    null as full_name,
    null as register_datetime,
    null as input_gest_weeks,
    null as pregnancy_age,
    null as husb_name,
    null as husb_age,
    null as husband_tel_phone,
    null as prg_cnt,
    null as deliver_matn_cnt,
    null as pregnancy_matn_cnt,
    null as last_menses_date_mark,
    null as last_mena_date,
    null as expect_deliver_date,
    null as past_history_code,
    null as past_history_name,
    null as family_his_code,
    null as family_his_name,
    null as per_his_code,
    null as per_his_name,
    null as gyn_oprn_his_flag,
    null as deliver_his_code,
    null as deliver_his_name,
    null as height,
    null as weight,
    null as bmi,
    null as sbp,
    null as dbp,
    null as heart_check_rslt_code,
    null as heart_check_rslt_name,
    null as heart_check_rslt_dscr,
    null as lungs_check_rslt_code,
    null as lungs_check_rslt_name,
    null as lungs_check_rslt_dscr,
    null as vulva_rslt_code,
    null as vulva_rslt_name,
    null as vulva_rslt_dscr,
    null as deliver_rslt_code,
    null as deliver_rslt_name,
    null as deliver_rslt_dscr,
    null as cervical_rslt_code,
    null as cervical_rslt_name,
    null as cervical_rslt_dscr,
    null as uterine_rslt_code,
    null as uterine_rslt_name,
    null as uterine_rslt_dscr,
    null as adnexa_uteri_rslt_code,
    null as adnexa_uteri_rslt_name,
    null as adnexa_uteri_rslt_dscr,
    null as hgb_value,
    null as wbc_value,
    null as platelet_value,
    null as other_exam_res,
    null as urinary_protein,
    null as uglu_quan_check_value,
    null as urine_ket_code,
    null as urine_ket_name,
    null as urine_occult_blood_code,
    null as urine_occult_blood_name,
    null as urine_other_rslt,
    null as blotype_abo_code,
    null as blotype_abo_name,
    null as blotype_rh_code,
    null as blotype_rh_name,
    null as serum_transa_value,
    null as serum_sgpt_value,
    null as sgot_value,
    null as tbi_value,
    null as dbil_value,
    null as scr_bilirubin_value,
    null as scr_value,
    null as blood_urea_nitrogen_value,
    null as vagina_secret_code,
    null as vagina_secret_name,
    null as vagina_clean_code,
    null as vagina_clean_name,
    null as hbsag_b_code,
    null as hbsag_b_name,
    null as hbsab_code,
    null as hbsab_name,
    null as hbeag_code,
    null as hbeag_name,
    null as hbeab_code,
    null as hbeab_name,
    null as hbab_code,
    null as hbab_name,
    null as vdrl_code,
    null as vdrl_name,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as bscan_exam_rslt,
    null as check_other_rslt,
    null as overall_ability_code,
    null as overall_ability_name,
    null as overall_ability_dscr,
    null as guidelines_code,
    null as guidelines_name,
    null as transfer_treatment,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as next_follow_date,
    null as fu_doc_no,
    null as fu_doc_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    填表孕周天数. as fill_form_geso_days,
    woman_first_prenatal_followup. as b_us_bpd,
    woman_first_prenatal_followup. as b_ultrasonic_conclusion,
    woman_first_prenatal_followup. as b_ultrasonic_other,
    头围. as head_circ,
    股骨颈. as femur,
    腹围. as abde,
    羊水. as amniotic_fluid,
    胎心. as cardiac,
    胎盘成熟度. as placenta_maturity,
    胎盘附于. as placenta_attached,
    胎盘下缘距子宫颈内口. as placenta_uterus_spacing,
    脐血流. as umbilical_blood_flow,
    子宫下段厚度. as lower_uterus_thickness,
    羊水指数. as amniotic_fluid_index,
    人工流产. as induced_abortion_num,
    总体评估为其他时描述. as overall_evaluation_remark,
    保健指导代码为其他时描述. as health_guide_remark,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_visits_2_5 partition(dt)
select 
    concat(fu_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_no,
    null as pregnant_card_id,
    null as fu_org_code,
    null as fu_times,
    null as full_name,
    null as urge_date,
    null as geso_val,
    null as chfcomp,
    null as weight,
    null as cervix_height,
    null as sbp,
    null as dbp,
    null as abde,
    null as fetalpositionstatus,
    null as fhr,
    null as hgb_value,
    null as urinary_protein,
    null as oth_asst_exam,
    null as fu_rslt_code,
    null as fu_rslt_name,
    null as fu_rslt_dscr,
    null as guide_code,
    null as guide_name,
    null as transfer_treatment,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as next_follow_date,
    null as fu_doc_no,
    null as fu_doc_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_edwcsf.cbbj00 as subdi_mark_code,
    fy_edwcsf.cbbj00 as subdi_mark_name,
    fy_edwcsf.yzts00 as geso,
    fy_edwcsf.wi_id as imp_data_of_old_sys_no,
    fy_edwcsf.drwdw0 as whtr_imp_exte_emp,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_delivery_record partition(dt)
select 
    concat(deliverid,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as deliverid,
    null as unified_uscid,
    null as pregnant_card_id,
    null as deliver_date,
    null as total_reproduct_min,
    null as first_labor_min,
    null as second_labor_min,
    null as third_labor_min,
    null as contractions_start_time,
    null as induce_way_code,
    null as induce_way_name,
    null as membranes_rupture_way_code,
    null as membranes_rupture_way_name,
    null as membranes_rupture_time,
    null as fetus_delivery_time,
    null as placenta_delivery_time,
    null as uterus_full_open_time,
    null as placenta_delivery_way_code,
    null as placenta_delivery_way_name,
    null as membranes_complete_case_flag,
    null as deliver_preg_week,
    null as deliver_way_othdcr,
    null as deliver_way_code,
    null as deliver_way_name,
    null as total_bleeding_no,
    null as birthing_bleeding_no,
    null as blood_reason,
    null as after_birth_2h_bleeding_nu,
    null as use_drug_dscr,
    null as after_birth_30m_dscr,
    null as after_birth_60m_dscr,
    null as after_birth_90m_dscr,
    null as after_birth_120m_dscr,
    null as urine_flag,
    null as amn_fluid_case,
    null as umb_dscr,
    null as placenta_dscr,
    null as birth_canal_injury_dscr,
    null as skin_touch_time,
    null as operation_testify,
    null as oprn_oprt_name,
    null as proc_special_dscr,
    null as is_accompany_childbirth,
    null as is_early_contact_sucking,
    null as nwb_name,
    null as nwb_gend_code,
    null as nwb_gend_name,
    null as birth_weight,
    null as birth_length,
    null as head_circum,
    null as chest_circum,
    null as apgar1_score,
    null as apgar5_score,
    null as apgar10_score,
    null as birth_situat_code,
    null as birth_situat_name,
    null as pregnancy_result_code,
    null as pregnancy_result_name,
    null as birth_defect_code,
    null as birth_defect_name,
    null as birth_defect_dscr,
    null as nwb_hear_screen_case_code,
    null as nwb_hear_screen_case_name,
    null as leave_date,
    null as diag_res,
    null as deliver_org_code,
    null as deliver_org_name,
    null as reg_org_name,
    null as case_mark,
    null as end_case_date,
    null as close_case_org_code,
    null as close_case_org_name,
    null as deliver_emp_code,
    null as deliver_emp_name,
    null as record_emp_name,
    null as record_emp_code,
    null as nurse_emp_name,
    null as fill_date,
    null as amn_fluid_amount,
    null as hcg,
    null as hiv_anti_code,
    null as hiv_anti_name,
    null as pulse,
    null as nwb_rescue_code,
    null as nwb_rescue_name,
    null as nwb_rescue_flag,
    null as nwb_intensive_dscr_code,
    null as nwb_intensive_dscr_name,
    null as nwb_intensive_mark,
    null as diag_way_code,
    null as diag_way_name,
    null as nwb_urine_dscr,
    null as red_hip_flag,
    null as jaundice_level_code,
    null as jaundice_level_name,
    null as head_circ,
    null as bas_wt,
    null as ht,
    null as nwb_heart_rate,
    null as last_gestate_stop_date,
    null as deliver_time,
    null as previous_gest_end_code,
    null as previous_gest_end_name,
    null as previous_deliver_way_code,
    null as previous_deliver_way_name,
    null as preg_name,
    null as brdy,
    null as ipt_no,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as perineum_cut_mark,
    null as perine_suture_no,
    null as perinaeum_case_code,
    null as perinaeum_case_name,
    null as is_critical,
    null as intensive_code,
    null as intensive_name,
    null as blood_pressure,
    null as breast_milk_duration,
    null as mother_res,
    null as fetals,
    null as nwb_body_temperat,
    null as birth_defect_mark,
    null as birth_defect_type_code,
    null as birth_defect_type_name,
    fy_fmqkjl.csqxls as birth_defect_no,
    null as deliver_res,
    null as nwb_death_flag,
    null as nwb_death_reason,
    null as nwb_death_time,
    null as midwifery_psn_name,
    null as midwifery_org_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_fmqkjl.fmdd00 as brth_loc,
    fy_fmqkjl.tfw000 as fets_part,
    fy_fmqkjl.zyrq00 as ipt_date,
    fy_fmqkjl.zhbjz0 as last_edit,
    fy_fmqkjl.zhbjrq as last_edit_date,
    fy_fmqkjl.sftz00 as whtr_notc_code,
    fy_fmqkjl.sftz00 as whtr_notc_name,
    fy_fmqkjl.wi_id as imp_data_of_old_sys_no,
    fy_fmqkjl.gwys00 as hrisk_fac,
    fy_fmqkjl.gwysbh as hrisk_fac_id,
    fy_fmqkjl.gwyyrq as hrisk_ordr_date,
    fy_fmqkjl.gwpf00 as hrisk_sco,
    fy_fmqkjl.jszid1 as midy_the_id2,
    fy_fmqkjl.jsznam2 as midy_the_name_2,
    fy_fmqkjl.cszbh0 as bir_cert_num,
    fy_fmqkjl.sfgw00 as whtr_hrisk,
    fy_fmqkjl.chsscx as postp_thirty_m_blee,
    fy_fmqkjl.chlscx as postp_60_m_blee,
    fy_fmqkjl.chjscx as postp_90_m_blee,
    fy_fmqkjl.chlxscx as postp_120_m_blee,
    fy_fmqkjl.chxyss1 as postpartum_30m_sbp,
    fy_fmqkjl.chxyss2 as postpartum_30m_dbp,
    fy_fmqkjl.chxyls1 as postp_60_m_bloo_pres_systpre,
    fy_fmqkjl.chxyls2 as postpartum_60m_dbp,
    fy_fmqkjl.chxyjs1 as postp_90_m_bloo_pres_systpre,
    fy_fmqkjl.chxyjs2 as postpartum_90m_dbp,
    fy_fmqkjl.ccszsl as oxyto_inj_amt,
    fy_fmqkjl.ccsbw0 as oxyto_inj_part,
    fy_fmqkjl.mjzsl0 as ergot_inj_amt,
    fy_fmqkjl.mjzsbw as ergot_inj_part,
    fy_fmqkjl.mjzsqt as ergot_inj_oth,
    fy_fmqkjl.nfzs00 as in_stit_val,
    fy_fmqkjl.nfxx00 as in_stit_type,
    fy_fmqkjl.wfzs00 as exte_stit_val,
    fy_fmqkjl.wfxx00 as exte_stit_type,
    fy_fmqkjl.hyfhgj as perin_sutur_new_anal_exam,
    fy_fmqkjl.yxkssj as earl_expo_earl_aspi_begn_time,
    fy_fmqkjl.yxcxsj as earl_expo_earl_aspi_dura,
    fy_fmqkjl.yxqk00 as earl_expo_earl_aspi_info_code,
    fy_fmqkjl.yxqk00 as earl_expo_earl_aspi_info_name,
    fy_fmqkjl.wyxyy0 as noskincontact_reason,
    fy_fmqkjl.lkcfsj as matn_leave_labo_deli_time,
    fy_fmqkjl.chxbsj as labr_real_esta_new_uri_time,
    fy_fmqkjl.hyqkzz as perin_incs_indi,
    fy_fmqkjl.pgczj0 as cesr_sect_indi,
    fy_fmqkjl.pgcfzz as cesr_sect_gyn_ind,
    fy_fmqkjl.yzhbz0 as sev_copn,
    fy_fmqkjl.newbz0 as memo,
    fy_fmqkjl.ch0000 as bedno,
    fy_fmqkjl.sfxtgl as matr_syst_admi,
    fy_fmqkjl.ynxc00 as inhosp_prete_labo,
    fy_fmqkjl.sfwzyf as criti_matr_flag,
    fy_fmqkjl.ncys00 as diffic_fac,
    fy_fmqkjl.qdyc00 as umbil_cord_abn,
    fy_fmqkjl.crqbfz as puer_cop,
    fy_fmqkjl.lcsj00 as parturient_time,
    fy_fmqkjl.hyfhzs as perin_sutur_num_ned,
    fy_fmqkjl.hylsqk as perin_fiss_info,
    fy_fmqkjl.gjqk00 as cervical_case,
    fy_fmqkjl.gg0000 as uter_heig,
    fy_fmqkjl.twyc00 as fetl_posi_abn,
    fy_fmqkjl.csycqk as time_deli_abnor,
    fy_fmqkjl.zx0000 as eclam,
    fy_fmqkjl.zxyl00 as inhosp_eclam,
    fy_fmqkjl.jsqk00 as midy_info_ipt_brth_code,
    fy_fmqkjl.jsqk00 as midy_info_ipt_brth_name,
    fy_fmqkjl.blflag as add_flag,
    fy_fmqkjl.sfswfm as whtr_city_exte_brth,
    fy_fmqkjl.chmb01 as postpartum_30m_pulse,
    fy_fmqkjl.chmb02 as postp_pul_60_sco_emp_per_min,
    fy_fmqkjl.chmb03 as postp_pul_90_sco_emp_per_min,
    fy_fmqkjl.chmb04 as postp_pul_120_sco_emp_per_min,
    fy_fmqkjl.lsqk00 as fulfi_info,
    fy_fmqkjl.sfzcjg as whtr_assi_labo_ins,
    fy_fmqkjl.zybah0 as matn_ipt_medcas_no,
    fy_fmqkjl.dwfzr0 as emp_resper,
    fy_fmqkjl.jglxdh as tel,
    fy_fmqkjl.fmjgnw as brth_loc_code,
    fy_fmqkjl.fmjgnw as brth_loc_name,
    fy_fmqkjl.fyscbz as prov_matr_child_upld_flag,
    fy_fmqkjl.cxrq00 as bloo_collec_date,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_postpartum_visit_basic partition(dt)
select 
    concat(postp_interview_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as postp_interview_no,
    null as unified_uscid,
    null as pregnant_card_id,
    null as interview_org_code,
    null as cotry_code,
    null as cotry_name,
    null as comm_code,
    null as resd_local_type_code,
    null as resd_local_type_name,
    null as preg_name,
    null as matn_age,
    null as work_org_name,
    null as matn_tel,
    null as husb_name,
    null as husb_age,
    null as husband_org_name,
    null as husband_tel_phone,
    null as resd_addr_code,
    null as resd_addr_name,
    null as resd_addr_prov_code,
    null as resd_addr_prov_name,
    null as resd_addr_city_code,
    null as resd_addr_city_name,
    null as resd_addr_coty_code,
    null as resd_addr_coty_name,
    null as resd_addr_subd_code,
    null as resd_addr_subd_name,
    null as resd_addr_comm_code,
    null as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    null as resd_addr_housnum,
    null as resd_addr,
    null as resd_addr_poscode,
    null as last_mena_date,
    null as expect_deliver_date,
    null as risk_score,
    null as hrisk_fac,
    null as build_date,
    null as build_gest_weeks,
    null as check_times,
    null as deliver_preg_week,
    null as delivery_time,
    null as deliver_way_code,
    null as deliver_way_name,
    null as parity_times_code,
    null as parity_times_name,
    null as nwb_bir_wt,
    null as apgar_sco,
    null as sp_info,
    null as leave_date,
    null as build_bed_date,
    null as end_date,
    null as appoint_flup_date,
    null as appoint_42day_date,
    null as build_org_name,
    null as build_name,
    null as build_prey_weeks,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_postpartum_visit_record partition(dt)
select 
    concat(interview_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as interview_no,
    null as unified_uscid,
    null as postp_base_interview_no,
    null as interview_org_code,
    null as exam_date,
    null as delivery_time,
    null as leave_date,
    null as after_birth_days,
    null as matn_age,
    null as self_intensive_care_start_date,
    null as sbp,
    null as dbp,
    null as tprt,
    null as milk_volume_code,
    null as milk_volume_name,
    null as breast_pain,
    null as breast_red,
    null as nipple_code,
    null as nipple_name,
    null as nipple_dscr,
    null as uterine_ht_dscr,
    null as uterine_tenderness_mark,
    null as uterine_soft_mark_code,
    null as uterine_soft_mark_name,
    null as uterine_contraction_code,
    null as uterine_contraction_name,
    null as lochia_color_dscr,
    null as lochia_quantity_dscr,
    null as lochia_gaminess_mark,
    null as incision_heal_mark,
    null as perineum_red_swell_mark,
    null as ep_dscr,
    null as other_perineum_dscr,
    null as high_risk_rslt,
    null as after_birth_guide_mark_code,
    null as after_birth_guide_mark_name,
    null as together_mark,
    null as check_sum_dscr,
    null as mother_dscr,
    null as nb_dscr,
    null as disposal_dscr,
    null as appoint_date,
    null as chkpsn_name,
    null as exam_operator_code,
    null as reg_org_code,
    null as visits_org_name,
    null as mamma_abn_code,
    null as mamma_abn_name,
    null as mamma_abn_dscr,
    null as lochia_abn_code,
    null as lochia_abn_name,
    null as lochia_abn_dscr,
    null as uterine_abn_code,
    null as uterine_abn_name,
    null as uterine_abn_dscr,
    null as cut_abn_code,
    null as cut_abn_name,
    null as cut_abn_dscr,
    null as fu_clinic_date,
    null as exam_emp,
    null as interview_date,
    null as publicity_content_code,
    null as publicity_content_name,
    null as special_situat_record,
    null as common_dscr,
    null as common_mentality_dscr,
    null as other_interview_rslt,
    null as type_abn_mark,
    null as type_abn_dscr,
    null as guide_code,
    null as guide_name,
    null as transfer_treatment,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as next_follow_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_postpartum_42d_checkup partition(dt)
select 
    concat(interview42_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as interview42_no,
    null as unified_uscid,
    null as pregnant_card_id,
    null as check_org_code,
    null as full_name,
    null as visit_date,
    null as delivery_time,
    null as leave_date,
    null as health_case_dscr,
    null as psycho_case_dscr,
    null as sbp,
    null as dbp,
    null as left_breast_exam_code,
    null as left_breast_exam_name,
    null as left_breast_check_rslt_name,
    null as right_breast_exam_code,
    null as right_breast_exam_name,
    null as right_breast_check_rslt_name,
    null as corpusuteri_abnorm_mark,
    null as corpusuteri_abnorm_dscr,
    null as lochia_abnorm_mark,
    null as lochia_abnorm_dscr,
    null as wound_healing_code,
    null as wound_healing_name,
    null as mamma_abn_code,
    null as mamma_abn_name,
    null as breast_name,
    null as uterine_abn_code,
    null as uterine_abn_name,
    null as uterine_abn_dscr,
    null as cut_abn_code,
    null as cut_abn_name,
    null as cut_abn_dscr,
    null as preg_recovery_mark,
    null as preg_recovery_mark_,
    null as preg_health_case_dscr,
    null as other_interview_rslt,
    null as preg_health_guide_code,
    null as preg_health_guide_name,
    null as processing_mode_code,
    null as processing_mode_name,
    null as referral_reason,
    null as transfer_treatment_org_code,
    null as transfer_treatment_org_name,
    null as accept_dept_code,
    null as referral_dept_name,
    null as interview_dor_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_high_risk_referral partition(dt)
select 
    concat(transfer_treatment_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as transfer_treatment_no,
    null as unified_uscid,
    null as woman_id,
    null as reg_org_code,
    null as full_name,
    null as age,
    null as resd_flag_code,
    null as resd_flag_name,
    null as prg_cnt,
    null as matn_cnt,
    null as geso_val,
    null as expect_deliver_date,
    null as resd_addr,
    null as curr_addr,
    null as tel,
    null as hometelephonenumber,
    null as referral_reason,
    null as transfer_treatment_date,
    null as out_org_name,
    null as out_agency_code,
    null as dor_name,
    null as mdtrt_date_time,
    null as cur_diag,
    null as deal_opinion,
    null as pregrisk_warncode,
    null as pregrisk_warnname,
    null as inta_org_name,
    null as accept_org_uscid,
    null as accept_dor_name,
    null as cur_disease_code,
    null as transfer_treament_desr,
    null as high_risk_preg_outc_code,
    null as high_risk_preg_outc_name,
    null as referral_rec,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_high_risk_followup_register partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as woman_id,
    null as reg_org_code,
    null as reg_date,
    null as full_name,
    null as age,
    null as resd_flag_code,
    null as resd_flag_name,
    null as prg_cnt,
    null as matn_cnt,
    null as expect_deliver_date,
    null as curr_addr,
    null as tel,
    null as main_diag_code,
    null as main_diag_name,
    null as main_diag_risk_category,
    null as main_diag_cnfm_date,
    null as main_diag_cnfm_org_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_high_risk_followup_detail partition(dt)
select 
    concat(fu_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_no,
    null as unified_uscid,
    null as fuid,
    null as visit_date,
    null as full_name,
    null as prey_weeks,
    null as cur_symptom_deal,
    null as fu_doctor_no,
    null as fu_doctor_name,
    null as fu_org_code,
    null as fu_org_name,
    null as eval_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_screening_diagnosis partition(dt)
select 
    concat(screen_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as screen_id,
    null as unified_uscid,
    null as woman_id,
    null as reg_org_code,
    null as last_mena_date,
    null as weight,
    null as fetals,
    null as ant_diagnosis_ad,
    null as pregnancy_result_code,
    null as pregnancy_result_name,
    null as full_name,
    null as brdy,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as diag_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_prenatal_screening_detail partition(dt)
select 
    concat(detail_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as detail_id,
    null as unified_uscid,
    null as screen_id,
    null as reg_org_code,
    null as prenatal_prey_weeks,
    null as screen_item_code,
    null as screen_way_code,
    null as screen_way_name,
    null as screen_rstl,
    null as diag_preg_weeks,
    null as diag_item,
    null as diag_way_dscr,
    null as diag_res,
    null as exam_date,
    null as screen_dor_code,
    null as screen_dor_name,
    null as screen_org_code,
    null as screen_org_name,
    null as diag_date,
    null as diag_dor_code,
    null as diag_dor_name,
    null as diag_org_code,
    null as diag_org_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_maternal_death_report partition(dt)
select 
    concat(rpt_card_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_card_no,
    null as unified_uscid,
    null as woman_id,
    null as death_date,
    null as death_age,
    null as die_diag_code,
    null as die_dise_name,
    null as death_record_summary,
    null as death_tylp_code,
    null as death_tylp_name,
    null as deathplace_type_code,
    null as death_place_name,
    null as death_diag_code,
    null as death_diag_name,
    null as deliver_position_code,
    null as deliver_position_name,
    null as death_cotry_code,
    null as death_cotry_name,
    null as new_delivery_mark,
    null as deliver_type_code,
    null as deliver_type_name,
    null as preg_exam_flag,
    null as early_gest_weeks,
    null as check_times,
    null as prov_health_org_res_mark_code,
    null as prov_health_org_res_mark_name,
    null as nat_eval_res_mark_code,
    null as nat_eval_res_mark_name,
    null as fill_org_name,
    null as report_org_code,
    null as reporter_name,
    null as report_date,
    null as full_name,
    null as rpt_time,
    null as districts_code,
    null as districts_name,
    null as per_addr_prov_code,
    null as per_addr_prov_name,
    null as per_addr_city_code,
    null as per_addr_city_name,
    null as per_addr_coty_code,
    null as per_addr_coty_name,
    null as per_addr_town_code,
    null as per_addr_town_name,
    null as per_addr_comm_code,
    null as per_addr_comm_name,
    null as per_addr_cotry_name,
    null as per_addr_housnum,
    null as per_addr,
    null as temp_addr_prov_code,
    null as temp_addr_prov_name,
    null as temp_addr_city_code,
    null as temp_addr_city_name,
    null as temp_addr_coty_code,
    null as temp_addr_coty_name,
    null as temp_addr_town_code,
    null as temp_addr_town_name,
    null as temp_addr_comm_code,
    null as temp_addr_comm_name,
    null as temp_addr_cotry_name,
    null as temp_addr_housnum,
    null as temp_addr,
    null as temp_districts_code,
    null as temp_districts_name,
    null as birth_addr_dscr,
    null as plan,
    null as age,
    null as nation_code,
    null as nation_name,
    null as edu_background_code,
    null as edu_background_name,
    null as per_family_income_type_code,
    null as per_family_income_type_name,
    null as curr_addr_name,
    null as prg_cnt,
    null as last_mena_date,
    null as deliver_date,
    null as deliver_way_code,
    null as deliver_way_name,
    null as main_disease_code,
    null as main_disease_name,
    null as main_death_factor,
    null as death_type,
    null as maternal_death_type_code,
    null as maternal_death_type_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_folic_acid_register partition(dt)
select 
    concat(unified_uscid,sys_no,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as unified_uscid,
    fy_yslqdj.xtbh00 as sys_no,
    fy_yslqdj.iccard as card_no,
    fy_yslqdj.scxtbh as manl_sys_no,
    null as woman_id,
    fy_yslqdj.yzz000 as preg_week_w,
    fy_yslqdj.yzt000 as preg_week_d,
    fy_yslqdj.lqrq00 as rece_date,
    fy_yslqdj.lqs000 as rece_val,
    fy_yslqdj.lqr000 as rece_huma,
    fy_yslqdj.sfzbhy as whtr_prpa_prg,
    fy_yslqdj.sfgwdy as whtr_high_crit_preg,
    fy_yslqdj.ffr000 as issu_huma,
    fy_yslqdj.ffrxm0 as issu_huma_name,
    fy_yslqdj.ffdw00 as issu_emp,
    fy_yslqdj.remark as remark,
    fy_yslqdj.cjz000 as add_the,
    fy_yslqdj.cjrq00 as add_date,
    fy_yslqdj.zhbjz0 as last_edit,
    fy_yslqdj.zhbjrq as last_edit_date,
    fy_yslqdj.yyid00 as hosp_no,
    fy_yslqdj.name as full_name,
    fy_yslqdj.birthday as brdy,
    fy_yslqdj.age as age,
    null as psncert_type_code,
    null as psncert_type_name,
    fy_yslqdj.idcardno as certno,
    fy_yslqdj. as tel,
    fy_yslqdj. as adress_pro,
    fy_yslqdj. as adress_city,
    fy_yslqdj. as adress_county,
    fy_yslqdj. as adress_rural,
    fy_yslqdj. as adress_village,
    fy_yslqdj. as location_detl_addr,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_postpartum_42d_visit_record partition(dt)
select 
    concat(fu_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as visit_date,
    null as fu_no,
    null as woman_id,
    null as unified_uscid,
    null as manual_no,
    null as delivery_time,
    null as leave_date,
    null as tprt,
    null as common_dscr,
    null as common_mentality_dscr,
    null as systolic_pressure,
    null as dbp,
    null as breast_abnormal_code,
    null as breast_abnormal_name,
    null as lochia_abnormal_code,
    null as lochia_abnormal_name,
    null as uterus_abnormal_code,
    null as uterus_abnormal_name,
    null as cut_abnormal_code,
    null as cut_abnormal_name,
    null as other_abnormal_remark,
    null as class_recovery_code,
    null as class_recovery_name,
    null as guidelines_code,
    null as guidelines_name,
    null as health_guide_remark,
    null as treat_result_code,
    null as treat_result_name,
    null as referral_hospital_dept,
    null as fu_doc_no,
    null as fu_doc_name,
    null as next_follow_date,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_marital_exam_record partition(dt)
select 
    concat(unified_uscid,sort,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    fy_hqyxjc. as woman_id,
    fy_hqyxjc. as unified_uscid,
    fy_hqyxjc. as sort,
    fy_hqyxjc. as fillin_date,
    fy_hqyxjc. as full_name,
    fy_hqyxjc. as gender_code,
    fy_hqyxjc. as gender_name,
    fy_hqyxjc. as brdy,
    null as psncert_type_code,
    null as psncert_type_name,
    fy_hqyxjc. as certno,
    fy_hqyxjc. as occup_code,
    fy_hqyxjc. as occup_name,
    fy_hqyxjc. as edu_background_code,
    fy_hqyxjc. as edu_background_name,
    fy_hqyxjc. as nation_code,
    fy_hqyxjc. as nation_name,
    fy_hqyxjc. as prenup_prov,
    fy_hqyxjc. as prenup_city,
    fy_hqyxjc. as prenup_county,
    fy_hqyxjc. as prenup_subdistrict,
    fy_hqyxjc. as filli_prenup_check_time_of_vil,
    fy_hqyxjc. as prenup_housenum,
    fy_hqyxjc. as resd_addr_prov_code,
    fy_hqyxjc. as resd_addr_prov_name,
    fy_hqyxjc. as resd_addr_city_code,
    fy_hqyxjc. as resd_addr_city_name,
    fy_hqyxjc. as resd_coty_code,
    fy_hqyxjc. as resd_coty_name,
    fy_hqyxjc. as resd_town_code,
    fy_hqyxjc. as resd_town_name,
    fy_hqyxjc. as resd_addr_comm_code,
    fy_hqyxjc. as resd_addr_comm_name,
    fy_hqyxjc. as curr_addr_prov,
    fy_hqyxjc. as curr_addr_city,
    fy_hqyxjc. as cur_addr_coty_code,
    fy_hqyxjc. as cur_addr_coty_name,
    fy_hqyxjc. as curr_addr_subd,
    fy_hqyxjc. as curr_addr_vil,
    fy_hqyxjc. as curr_addr_hsnum,
    fy_hqyxjc. as poscode,
    fy_hqyxjc. as workplace,
    fy_hqyxjc. as tel,
    fy_hqyxjc. as oth_name,
    fy_hqyxjc. as ref_no,
    fy_hqyxjc. as oppo_no,
    fy_hqyxjc. as exam_date,
    fy_hqyxjc. as bloo_rela,
    fy_hqyxjc. as past_illhis,
    fy_hqyxjc. as oprn_his,
    fy_hqyxjc. as past_illhis_oth,
    fy_hqyxjc. as dise_now,
    fy_hqyxjc. as mena_menar_age,
    fy_hqyxjc. as menst_prd_of_divi,
    fy_hqyxjc. as menst_prd_divi,
    fy_hqyxjc. as mens_flow,
    fy_hqyxjc. as dysme,
    fy_hqyxjc. as last_mena_date,
    fy_hqyxjc. as past_mrg_his,
    fy_hqyxjc. as child_psncnt,
    fy_hqyxjc. as adequ_mon_cnt,
    fy_hqyxjc. as pret_cnt,
    fy_hqyxjc. as misc_cnt,
    fy_hqyxjc. as with_hered_rel_of_fmhis,
    fy_hqyxjc. as relation_patient_code,
    fy_hqyxjc. as relation_patient_name,
    fy_hqyxjc. as fm_clos_rela_marr,
    fy_hqyxjc. as prenup_illhis_exam_sign,
    fy_hqyxjc. as prenup_illhis_exam_dr_id,
    fy_hqyxjc. as prenup_illhis_exam_dr_sign,
    fy_hqyxjc. as bloo_pres_of_divid,
    fy_hqyxjc. as bloo_pres_of_divis,
    fy_hqyxjc. as sp_body_cond,
    fy_hqyxjc. as ment_sta,
    fy_hqyxjc. as sp_face,
    fy_hqyxjc. as inte,
    fy_hqyxjc. as skin_hai,
    fy_hqyxjc. as five_sens,
    fy_hqyxjc. as thyr,
    fy_hqyxjc. as heart_rate,
    fy_hqyxjc. as rhythm_heart_dscr,
    fy_hqyxjc. as murm,
    fy_hqyxjc. as lung,
    fy_hqyxjc. as live,
    fy_hqyxjc. as fors_spin,
    fy_hqyxjc. as physexm_oth,
    fy_hqyxjc. as prenup_physexm_dr_id,
    fy_hqyxjc. as prenup_physexm_dr_sign,
    fy_hqyxjc. as laryn,
    fy_hqyxjc. as male_pubi_hair,
    fy_hqyxjc. as peni,
    fy_hqyxjc. as foresk,
    fy_hqyxjc. as testi_bilat_ask_wth,
    fy_hqyxjc. as testi_volu_left,
    fy_hqyxjc. as testi_volu_rght,
    fy_hqyxjc. as testi_un_ask_wth,
    fy_hqyxjc. as epidi,
    fy_hqyxjc. as nodu_left,
    fy_hqyxjc. as nodu_rght,
    fy_hqyxjc. as sperm_cord_vari_vein,
    fy_hqyxjc. as sperm_cord_vari_vein_deg,
    fy_hqyxjc. as male_second_sex_char_oth,
    fy_hqyxjc. as malessc_examdrid,
    fy_hqyxjc. as malessc_examdrsign,
    fy_hqyxjc. as fml_pubi_hair,
    fy_hqyxjc. as breast_exam_rslt,
    fy_hqyxjc. as anal_chec_vulv,
    fy_hqyxjc. as anal_chec_secre,
    fy_hqyxjc. as anal_chec_uter,
    fy_hqyxjc. as anal_chec_att,
    fy_hqyxjc. as vagi_exam_vulv,
    fy_hqyxjc. as vagi_exam_vagi,
    fy_hqyxjc. as vagi_exam_cerv,
    fy_hqyxjc. as vagi_exam_uter,
    fy_hqyxjc. as vagi_exam_att,
    fy_hqyxjc. as fml_second_sex_char_oth,
    fy_hqyxjc. as fema_vagi_exam_sign,
    fy_hqyxjc. as fml_second_sex_char_exam_dr_id,
    fy_hqyxjc. as femalessc_examdrsign,
    fy_hqyxjc. as chest_xray,
    fy_hqyxjc. as transa,
    fy_hqyxjc. as bloo_rout,
    fy_hqyxjc. as hbsag_check_res_name,
    fy_hqyxjc. as urin_rout,
    fy_hqyxjc. as syph_fitr,
    fy_hqyxjc. as vagi_secre,
    fy_hqyxjc. as gonoc,
    fy_hqyxjc. as oth_sp_exam,
    fy_hqyxjc. as exam_rslt,
    fy_hqyxjc. as disediag,
    fy_hqyxjc. as disediag_codg,
    fy_hqyxjc. as medi_opnn,
    fy_hqyxjc. as medi_opnn_exam_mal_id,
    fy_hqyxjc. as medi_opnn_exam_mal_sign,
    fy_hqyxjc. as medi_opnn_exam_fml_id,
    fy_hqyxjc. as medi_opnn_exam_fml_sign,
    fy_hqyxjc. as prenup_hc_cslt,
    fy_hqyxjc. as cslt_guid_rslt,
    fy_hqyxjc. as cslt_guid_rslt_exam_mal_id,
    fy_hqyxjc. as cslt_guid_rslt_exam_mal_sign,
    fy_hqyxjc. as cslt_guid_rslt_exam_fml_id,
    fy_hqyxjc. as cslt_guid_rslt_exam_fml_sign,
    fy_hqyxjc. as referral_flag,
    fy_hqyxjc. as refl_hosp_id,
    fy_hqyxjc. as refl_hosp,
    fy_hqyxjc. as transfer_treatment_date,
    fy_hqyxjc. as appoint_flup_date,
    fy_hqyxjc. as issuance_date,
    fy_hqyxjc. as cnvl_main_test_dr_id,
    fy_hqyxjc. as cnvl_main_test_dr_sign,
    fy_hqyxjc. as hosp_id,
    fy_hqyxjc. as remark,
    fy_hqyxjc. as prenupexam_drid,
    fy_hqyxjc. as prenupexam_drsign,
    fy_hqyxjc. as prenupexam_certissudate,
    fy_hqyxjc. as rcd_of_stas,
    fy_hqyxjc. as hiv_anti_code,
    fy_hqyxjc. as hiv_anti_name,
    fy_hqyxjc. as whtr_fore_rela,
    fy_hqyxjc. as cert_code,
    fy_hqyxjc. as cert_name,
    fy_hqyxjc. as age,
    fy_hqyxjc. as prt_stas,
    fy_hqyxjc. as son_psncnt,
    fy_hqyxjc. as adequ_age,
    fy_hqyxjc. as adequ_agem,
    fy_hqyxjc. as bloo_rout_val,
    fy_hqyxjc. as transa_val,
    fy_hqyxjc. as urinary_protein,
    fy_hqyxjc. as urin_gluc,
    fy_hqyxjc. as occu_bloo,
    fy_hqyxjc. as abnormal_rslt,
    fy_hqyxjc. as folat_issu,
    fy_hqyxjc. as euge_dect,
    fy_hqyxjc. as whit_bloo_cells,
    fy_hqyxjc. as last_modi_date,
    fy_hqyxjc. as bas_info_inpt_psn,
    fy_hqyxjc. as illhis_inpt_psn,
    fy_hqyxjc. as physexm_inpt_psn,
    fy_hqyxjc. as cnvl_exam_inpt_psn,
    fy_hqyxjc. as whtr_flup,
    fy_hqyxjc. as whtr_earl_preg,
    fy_hqyxjc. as upld_flag,
    fy_hqyxjc. as hemog_a2,
    fy_hqyxjc. as hemog_a_+_f,
    fy_hqyxjc. as hemog_bart_'_s,
    fy_hqyxjc. as hemog_c,
    fy_hqyxjc. as hemog_f,
    fy_hqyxjc. as hemog_h,
    fy_hqyxjc. as hemog_s,
    fy_hqyxjc. as prom_thyr_horm_mes,
    fy_hqyxjc. as pre_preg_euge_exam_untes,
    fy_hqyxjc. as gian_cell_viru_igg_anti_mes,
    fy_hqyxjc. as rube_viru_igg_anti_mes,
    fy_hqyxjc. as toxo_igm_anti_mes,
    fy_hqyxjc. as cnfm_diab_child_matn_his,
    fy_hqyxjc. as diab_fmhis,
    fy_hqyxjc. as diab_face,
    fy_hqyxjc. as diab_scre,
    fy_hqyxjc. as confdmchild_stillbirthcnt,
    fy_hqyxjc. as confdmchild_natmisccnt,
    fy_hqyxjc. as hsv-ii_igg_test,
    fy_hqyxjc. as hsv-ii_igm_test,
    fy_hqyxjc. as dyspla,
    fy_hqyxjc. as jaun,
    fy_hqyxjc. as resd_natu_code,
    fy_hqyxjc. as resd_natu_name,
    fy_hqyxjc. as gian_cell_viru_igm_anti_mes,
    fy_hqyxjc. as whol_bod_edem,
    fy_hqyxjc. as bld_his,
    fy_hqyxjc. as matn_certno,
    fy_hqyxjc. as face_atro_pall,
    fy_hqyxjc. as hepa_b_surf_anti,
    fy_hqyxjc. as hepa_b_e_hbeab,
    fy_hqyxjc. as hepa_b_e_hbeag,
    fy_hqyxjc. as hepa_b_cor_anti,
    fy_hqyxjc. as toxo_igg_anti_mes,
    fy_hqyxjc. as rube_viru_igm_anti_mes,
    fy_hqyxjc. as are_you_curr_taki_medi,
    fy_hqyxjc. as whtr_inj_excs_vac,
    fy_hqyxjc. as currcontraceptive,
    fy_hqyxjc. as contr_cont_used_time,
    fy_hqyxjc. as years_discontinue,
    fy_hqyxjc. as months_discontinue,
    fy_hqyxjc. as whtr_into_meat_eat_eggs,
    fy_hqyxjc. as whtr_anor_vege,
    fy_hqyxjc. as whtr_yes_consu_fles_hob,
    fy_hqyxjc. as whtr_smok,
    fy_hqyxjc. as whtr_exis_passi_smok,
    fy_hqyxjc. as whtr_drnk,
    fy_hqyxjc. as whtr_used_coca_etc._toxo_drug,
    fy_hqyxjc. as whtr_bad_brea,
    fy_hqyxjc. as whtr_gums_blee,
    fy_hqyxjc. as envexposure,
    fy_hqyxjc. as whtr_feel_life_job_pre,
    fy_hqyxjc. as relationstension,
    fy_hqyxjc. as whtr_feel_econ_pre,
    fy_hqyxjc. as whtr_do_well_prg_prpa,
    fy_hqyxjc. as other1,
    fy_hqyxjc. as height,
    fy_hqyxjc. as weight,
    fy_hqyxjc. as wt_ind,
    fy_hqyxjc. as liverkidney_gpt,
    fy_hqyxjc. as creat_umoll,
    fy_hqyxjc. as blotype_abo_code,
    fy_hqyxjc. as blotype_abo_name,
    fy_hqyxjc. as bloo_gluc,
    fy_hqyxjc. as gynecological_check,
    fy_hqyxjc. as cnvl_exam_oth,
    fy_hqyxjc. as exam_type_code,
    fy_hqyxjc. as exam_type_name,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_newborn_basic_info partition(dt)
select 
    concat(unified_uscid,nebo_guid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    newborninfo. as unified_uscid,
    newborninfo. as nebo_guid,
    newborninfo. as nebo_birthno,
    newborninfo. as nb_cert_no,
    null as baby_psncert_type_code,
    null as baby_psncert_type_name,
    newborninfo. as chil_mother,
    newborninfo. as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    newborninfo. as chil_father,
    newborninfo. as father_cert_no,
    null as father_cert_type_code,
    null as father_cert_type_name,
    newborninfo. as tel,
    newborninfo. as mobile,
    newborninfo. as nebo_mhbsag_code,
    newborninfo. as nebo_mhbsag_name,
    newborninfo. as nebo_fhbsag_code,
    newborninfo. as nebo_fhbsag_name,
    newborninfo. as cur_addr_coty_code,
    newborninfo. as cur_addr_coty_name,
    newborninfo. as nebo_address_depa_id,
    newborninfo. as nebo_depa_id,
    newborninfo. as nebo_address,
    newborninfo. as nwb_name,
    newborninfo. as gender_code,
    newborninfo. as gender_name,
    newborninfo. as nebo_birthtime,
    newborninfo. as chil_wt,
    newborninfo. as nebo_nati_code,
    newborninfo. as nebo_nati_name,
    newborninfo. as resd_coty_code,
    newborninfo. as resd_coty_name,
    newborninfo. as resd_town_code,
    newborninfo. as resd_town_name,
    newborninfo. as resd_addr,
    newborninfo. as mthr_naty_code,
    newborninfo. as mthr_naty_name,
    newborninfo. as nebo_childcode,
    newborninfo. as nebo_mothercode,
    newborninfo. as nebo_fathercode,
    newborninfo. as chil_fetus,
    newborninfo. as birth_gest_weeks,
    newborninfo. as nebo_birthstate,
    newborninfo. as nebo_defects,
    newborninfo. as nebo_defects_desc,
    newborninfo. as nebo_screen,
    newborninfo. as nebo_apgar1,
    newborninfo. as nebo_apgar2,
    newborninfo. as nebo_apgar3,
    newborninfo. as nebo_doctor,
    newborninfo. as fillin_date,
    newborninfo. as sys_no,
    newborninfo. as nebo_bcg,
    newborninfo. as nebi_bcg_reason,
    newborninfo. as nebo_hepb,
    newborninfo. as nebi_hepb_reason,
    newborninfo. as nebi_hepb_reasonother,
    newborninfo. as nebo_hepbig,
    newborninfo. as uploadtime,
    newborninfo. as wom_hcare_manl_sys_no,
    newborninfo. as crte_date,
    newborninfo. as crter,
    newborninfo. as last_edit_date,
    newborninfo. as last_edit,
    newborninfo. as modify_datetime,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hwm_newborn_vaccin_record partition(dt)
select 
    concat(upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    newborninoculate. as nebi_nebo_id,
    newborninoculate. as vacc_code,
    newborninoculate. as vacc_name,
    newborninoculate. as vaccinate_inoc_time,
    newborninoculate. as nebi_free,
    newborninoculate. as nebi_date,
    newborninoculate. as nebi_dose,
    newborninoculate. as vacc_batch,
    newborninoculate. as nebi_corp_code,
    newborninoculate. as nebi_nurse,
    newborninoculate. as nebi_inpl_id,
    newborninoculate. as nebi_inoculateway,
    newborninoculate. as nebi_editdate,
    newborninoculate. as vcnt_info_id,
    newborninoculate. as hosp_id,
    newborninoculate. as patn_id,
    newborninoculate. as uploadtime,
    newborninoculate. as crte_date,
    newborninoculate. as crter,
    newborninoculate. as last_edit_date,
    newborninoculate. as last_edit,
    newborninoculate. as modify_datetime,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_basic_info partition(dt)
select 
    concat(chilid,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as chilid,
    null as unified_uscid,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as birth_certificate_no,
    null as chil_mother,
    null as nwb_name,
    null as gender_code,
    null as gender_name,
    null as preg_matn_cnt,
    null as geso_val,
    null as chil_wt,
    null as brdy,
    null as birth_prov_code,
    null as birth_prov_name,
    null as birth_city_code,
    null as birth_city_name,
    null as birth_coty_code,
    null as birth_coty_name,
    null as birth_town_code,
    null as birth_town_name,
    null as birth_comm_code,
    null as birth_comm_name,
    null as birth_cotry_name,
    null as birth_addr_housnum,
    null as birth_addr,
    null as first_time,
    null as otp_no,
    null as no_consult,
    null as remark,
    null as family_hereditary_his,
    null as relation_patient_baby_code,
    null as relation_patient_baby_name,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as relation_inbreed_name,
    null as pregnancy_card_id,
    null as gravidity,
    null as matn_cnt,
    null as abn_fert_his,
    null as matn_edu_background_code,
    null as matn_edu_background_name,
    null as per_family_income_code,
    null as per_family_income_name,
    null as family_addr,
    null as family_cur_prov_code,
    null as family_cur_prov_name,
    null as family_cur_city_code,
    null as family_cur_city_name,
    null as family_cur_coty_code,
    null as family_cur_coty_name,
    null as family_cur_town_code,
    null as family_cur_town_name,
    null as family_cur_comm_code,
    null as family_cur_comm_name,
    null as family_cur_cotry_name,
    null as family_cur_addr_housnum,
    null as family_cur_postalcode,
    null as hometelephonenumber,
    null as resd_flag_code,
    null as resd_flag_name,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_etbjsc. as card_type_code,
    fy_etbjsc. as card_type_name,
    fy_etbjsc. as card_no,
    fy_etbjsc. as fthr_no,
    fy_etbjsc. as nwb_asph,
    fy_etbjsc. as hear_scre_asoc_chara_sec,
    fy_etbjsc. as dise_scre_asoc_chara_sec,
    fy_etbjsc. as resd_status_code,
    fy_etbjsc. as resd_status_name,
    fy_etbjsc. as resd_natu_code,
    fy_etbjsc. as resd_natu_name,
    fy_etbjsc. as mob,
    fy_etbjsc. as whtr_hrisk,
    fy_etbjsc. as whtr_adm,
    fy_etbjsc. as whtr_hepa_b_vac_vcnt,
    fy_etbjsc. as whtr_bcg_vac_vcnt,
    fy_etbjsc. as manl_stas_code,
    fy_etbjsc. as manl_stas_name,
    fy_etbjsc. as live_stas_code,
    fy_etbjsc. as live_stas_name,
    fy_etbjsc. as coun,
    fy_etbjsc. as whtr_die,
    fy_etbjsc. as vill_for_emp_set_file_no,
    fy_etbjsc. as imp_data_of_old_sys_no,
    fy_etbjsc. as appf_sco_1_m,
    fy_etbjsc. as appf_sco_5_m,
    fy_etbjsc. as whtr_emig,
    fy_etbjsc. as whtr_stt,
    fy_etbjsc. as whtr_sing_paren,
    fy_etbjsc. as appf_sco_10_m,
    fy_etbjsc. as whtr_pret,
    fy_etbjsc. as whtr_twin_preg,
    fy_etbjsc. as newbornscreen_negcode,
    fy_etbjsc. as newbornscreen_negname,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_birth_certificate partition(dt)
select 
    concat(birth_certificate_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as birth_certificate_no,
    null as unified_uscid,
    null as chilid,
    null as visa_org_code,
    null as nwb_name,
    null as nwb_gend_code,
    null as nwb_gend_name,
    null as nwb_brdy,
    null as birth_addr_type_code,
    null as birth_addr_type_name,
    null as birth_situat_code,
    null as birth_situat_name,
    null as birth_gest_weeks,
    null as chil_wt,
    null as birth_ht,
    null as birth_head_circ,
    null as birth_chest_circ,
    null as birth_health_code,
    null as birth_health_name,
    null as childbirth_code,
    null as childbirth_name,
    null as apgar1_score,
    null as apgar5_score,
    null as apgar10_score,
    null as chil_mother,
    null as mother_childbirth_age,
    null as mthr_ntly_code,
    null as mthr_ntly_name,
    null as mthr_naty_code,
    null as mthr_naty_name,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as mother_brdy,
    null as mother_occup_type_code,
    null as mother_occup_type_name,
    null as mother_empr_name,
    null as chil_father,
    null as father_childbirth_age,
    null as fthr_ntly_code,
    null as fthr_ntly_name,
    null as fthr_naty_code,
    null as fthr_naty_name,
    null as father_cert_no,
    null as father_cert_type_code,
    null as father_cert_type_name,
    null as father_brdy,
    null as father_occup_type_code,
    null as father_occup_type_name,
    null as father_empr_name,
    null as birth_addr,
    null as birth_prov_code,
    null as birth_prov_name,
    null as birth_city_code,
    null as birth_city_name,
    null as birth_coty_code,
    null as birth_coty_name,
    null as birth_town_code,
    null as birth_town_name,
    null as birth_comm_code,
    null as birth_comm_name,
    null as birth_cotry_name,
    null as birth_addr_housnum,
    null as deliver_code,
    null as deliver_name,
    null as midwifery_psn_name,
    null as deliver_org_code,
    null as deliver_org_name,
    null as issue_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_critical_newborn_transfer partition(dt)
select 
    concat(sort,uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as sort,
    null as uscid,
    null as chilid,
    null as transport_org_code,
    null as cotry_code,
    null as cotry_name,
    null as quarter,
    null as tran_reg_no,
    null as chil_mother,
    null as gender_code,
    null as gender_name,
    null as transfer_treatment_date,
    null as brdy,
    null as adequ_age,
    null as out_org_code,
    null as accept_hosp,
    null as diag_code,
    null as diag_name,
    null as is_out_cons_code,
    null as warm_box_mark,
    null as resd_flag_code,
    null as resd_flag_name,
    null as dis_pro_code,
    null as dis_pro_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_newborn_screening_record partition(dt)
select 
    concat(screen_rec_no,uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as screen_rec_no,
    null as uscid,
    null as chilid,
    null as check_org_code,
    null as nwb_name,
    null as mother_name,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as nwb_gend_code,
    null as nwb_gend_name,
    null as nwb_brdy,
    null as specimen_id,
    null as ipt_no,
    null as ree_mark,
    null as blood_col_dtime,
    null as blood_col_code,
    null as blood_col_name,
    null as blood_col_position_code,
    null as blood_col_position_name,
    null as blood_col_org_code,
    null as blood_col_org_name,
    null as blood_col_dor_no,
    null as blood_col_dor_name,
    null as screen_proj_code1,
    null as screen_proj_name1,
    null as screen_way_code1,
    null as screen_way_name1,
    null as screen_rslt1,
    null as screen_proj_code2,
    null as screen_proj_name2,
    null as screen_way_code2,
    null as screen_way_name2,
    null as screen_rslt2,
    null as screen_proj_code3,
    null as screen_proj_name3,
    null as screen_way_code3,
    null as screen_way_name3,
    null as screen_rslt3,
    null as screen_proj_code4,
    null as screen_proj_name4,
    null as screen_way_code4,
    null as screen_way_name4,
    null as screen_rslt4,
    null as screen_proj_code5,
    null as screen_proj_name5,
    null as screen_way_code5,
    null as screen_way_name5,
    null as screen_rslt5,
    null as screen_proj_code6,
    null as screen_proj_name6,
    null as screen_way_code6,
    null as screen_way_name6,
    null as screen_rslt6,
    null as check_date,
    null as check_dor_no,
    null as check_dor_name,
    null as screen_rslt_date,
    null as recall_date,
    null as check_rslt_code,
    null as check_rslt_name,
    null as service_name,
    null as relation_service_code,
    null as relation_service_name,
    null as informer_name,
    null as diag_date,
    null as diag_item,
    null as diag_way_dscr,
    null as diag_res,
    null as diag_org_name,
    null as diage_dor_code,
    null as diage_dor_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_newborn_screening_followup partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as urge_time,
    null as screen_rec_no,
    null as fu_org_code,
    null as chil_mother,
    null as birth_hos,
    null as baby_name,
    null as gender_code,
    null as gender_name,
    null as screen_rslt_ch,
    null as screen_rslt_pku,
    null as screen_rslt_g_6_pd,
    null as screen_rslt_cah,
    null as cnfm_rslt,
    null as cnfm_hos,
    null as fu_rslt,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_42d_hearing_recheck_report partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as screen_rec_no,
    null as rpt_org_uscid,
    null as birth_coty_code,
    null as birth_coty_name,
    null as birth_org_code,
    null as birth_org_name,
    null as chil_mother,
    null as children_name,
    null as brdy,
    null as vacation_addr,
    null as tel,
    null as screen_rslt_left,
    null as screen_rslt_right,
    null as diag_rslt_left,
    null as diag_rslt_right,
    null as cnfm_months,
    null as cnfm_hos,
    null as remark,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_health_exam_register partition(dt)
select 
    concat(examination_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as examination_id,
    null as unified_uscid,
    null as chilid,
    null as reg_org_code,
    null as full_name,
    null as gender_code,
    null as gender_name,
    null as brdy,
    null as birth_gest_weeks,
    null as chil_father,
    null as chil_mother,
    null as is_take_drug_mark,
    null as bc_pill_name,
    null as fetals,
    null as matn_cnt,
    null as childbirth_explain,
    null as childbirth_code,
    null as childbirth_name,
    null as allergic_his,
    null as chil_wt,
    null as birth_ht,
    null as tel,
    null as mdtrt_souc,
    null as local_type_code,
    null as local_type_name,
    null as birthe_defective_mark,
    null as nebo_screen,
    null as blotype_abo_code,
    null as blotype_abo_name,
    null as blotype_rh_code,
    null as blotype_rh_name,
    null as build_date,
    null as builder_no,
    null as build_name,
    null as build_org_name,
    null as deliver_way_code,
    null as deliver_way_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_exam_inquiry_record partition(dt)
select 
    concat(examination_refer_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as examination_refer_no,
    null as unified_uscid,
    null as examination_id,
    null as check_org_code,
    null as check_org_name,
    null as refer_date,
    null as adequ_age,
    null as age_unit_code,
    null as age_unit_name,
    null as human_milk_mark_code,
    null as human_milk_mark_name,
    null as human_milk_times,
    null as eat_human_milk_mark,
    null as milk,
    null as powdered_milk,
    null as rice_flour,
    null as noodle,
    null as congee,
    null as rice,
    null as pungent_foods,
    null as egg,
    null as bean_products,
    null as vegetables,
    null as fruit,
    null as vitamin_ad,
    null as calcic,
    null as calcium_ablet,
    null as other_situation_state,
    null as appetite_mark_code,
    null as appetite_mark_name,
    null as copious_sweat,
    null as irritable_situation,
    null as sleep_situation,
    null as outdoor_act_mark,
    null as outdoor_act_duration,
    null as life_style_mark_code,
    null as life_style_mark_name,
    null as shit_times,
    null as nwb_shit_code,
    null as nwb_shit_name,
    null as last_dis_situation,
    null as questioner,
    null as vitamin_d_mark,
    null as vitamind_name,
    null as vitamind_dose,
    null as sup_food_code,
    null as sup_food_name,
    null as creeper_months,
    null as sit_months,
    null as turn_months,
    null as rise_months,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_physical_exam_record partition(dt)
select 
    concat(physique_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as physique_id,
    null as unified_uscid,
    null as examination_id,
    null as check_org_code,
    null as age_code,
    null as age_name,
    null as examination_date,
    null as adequ_age,
    null as age_unit_code,
    null as age_unit_name,
    null as weight,
    null as wt_code,
    null as wt_name,
    null as height_cm,
    null as ht_code,
    null as ht_name,
    null as sit_ht,
    null as head_circ,
    null as chest_circ,
    null as subcutaneous_fat,
    null as complexion_code,
    null as complexion_name,
    null as skin_check_abnorm_code,
    null as skin_check_abnorm_name,
    null as hair,
    null as lymph_node_rslt_code,
    null as lymph_node_rslt_name,
    null as bregma_close_mark,
    null as bregma_size,
    null as posterior_fontanelle,
    null as neck_mass_mark,
    null as eye_check_abn_code,
    null as eye_check_abn_name,
    null as eye_check_abn_dscr,
    null as nose_appear_check_abnorm_code,
    null as nose_appear_check_abnorm_name,
    null as nose_appear_check_abnorm_dscr,
    null as ear_code,
    null as ear_name,
    null as ear_dscr,
    null as oral_code,
    null as oral_name,
    null as oral_dscr,
    null as vision_check_dscr,
    null as left_original_hyperopia_value,
    null as right_eye_uncorrected_value,
    null as left_redress_hyperopia_value,
    null as right_eye_corrected_value,
    null as hearing_check_rslt_mark_code,
    null as hearing_check_rslt_mark_name,
    null as teething_no,
    null as teethingcari_no,
    null as caries_experi_no,
    null as chest_check_rslt_code,
    null as chest_check_rslt_name,
    null as chest_check_rslt_dscr,
    null as belly_check_rslt_code,
    null as belly_check_rslt_name,
    null as belly_check_rslt_dscr,
    null as navel_check_rslt_code,
    null as navel_check_rslt_name,
    null as navel_check_rslt_dscr,
    null as live,
    null as spleen_dscr,
    null as genitals_check_rslt_code,
    null as genitals_check_rslt_name,
    null as genitals_check_rslt_dscr,
    null as anus_check_rslt_code,
    null as anus_check_rslt_name,
    null as anus_check_rslt_dscr,
    null as skeleton_dscr,
    null as hip_test,
    null as spirit_dev_code,
    null as spirit_dev_name,
    null as action_dev_code,
    null as action_dev_name,
    null as gait_mark,
    null as gait_dscr,
    null as rickets_symptom_code,
    null as rickets_symptom_name,
    null as rickets_sign_code,
    null as rickets_sign_name,
    null as physique_eval_code,
    null as physique_eval_name,
    null as chil_dev_eval_pass_mark,
    null as chil_dev_eval_mark_code,
    null as chil_dev_eval_mark_name,
    null as hb_mark_code,
    null as hb_mark_name,
    null as rbc,
    null as outdoor_act_mark,
    null as outdoor_act_duration,
    null as vitamin_d_mark,
    null as vitamind_name,
    null as vitamind_dose,
    null as diagnosis,
    null as next_check_time,
    null as check_doct_name,
    null as phys_exam_exam_rslt,
    null as frail_chil_mark,
    null as spirit_dev_screen_rslt,
    null as ht_wg_eval_rslt_code,
    null as ht_wg_eval_rslt_name,
    null as age_ht_eval_rslt_code,
    null as age_ht_eval_rslt_name,
    null as age_wt_eval_rslt_code,
    null as age_wt_eval_rslt_name,
    null as hgb_value,
    null as tonsil_check_rslt_name,
    null as phary_check_rslt_code,
    null as phary_check_rslt_name,
    null as trachoma_mark,
    null as spine_abnorm_exam_rslt,
    null as limbs_check_rslt_code,
    null as limbs_check_rslt_name,
    null as limbs_check_rslt_dscr,
    null as lymph_check_rslt_dscr,
    null as fu2_disease_mark_code,
    null as fu2_disease_mark_name,
    null as fu2_disease_dscr,
    null as other_case_info,
    null as transfer_treatment_mark,
    null as transfer_treatment_dscr,
    null as accept_org_name,
    null as accept_depart_name,
    null as referral_reason,
    null as guide_code,
    null as guide_name,
    null as next_follow_date,
    null as fu_doc_no,
    null as fu_doc_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_anniversary_summary partition(dt)
select 
    concat(one_summary_id,uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as one_summary_id,
    null as uscid,
    null as examination_id,
    null as reg_org_code,
    null as month0_6_add_wt,
    null as month7_12_add_wt,
    null as birth_ht,
    null as ht12,
    null as embryo_month,
    null as month12_teeth_no,
    null as head_circ_first_cross_month,
    null as cur_cross,
    null as month3_wt,
    null as month3_ht,
    null as month6_wt,
    null as month6_ht,
    null as month9_wt,
    null as month9_ht,
    null as month12_wt,
    null as ddst_mark,
    null as hearing_abn_mark,
    null as vision_mark,
    null as tooth_no,
    null as breast_milk_code,
    null as breast_milk_name,
    null as month2_hb,
    null as month2_rbc,
    null as month6_hb,
    null as month6_rbc,
    null as month12_hb,
    null as month12_rbc,
    null as upper_res_infec_mark,
    null as bronchitis_mark,
    null as diarrhea_mark,
    null as pneumonia_disease,
    null as ricket_mark,
    null as ricket_drug,
    null as anemia_mark,
    null as anemia_drug,
    null as summarize_dscr,
    null as guide_dscr,
    null as summary_no,
    null as summary_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_treatment_record partition(dt)
select 
    concat(correct_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as correct_id,
    null as unified_uscid,
    null as examination_id,
    null as correct_org_code,
    null as correct_date,
    null as medical_history_sum,
    null as guide_disposal,
    null as handler_no,
    null as handler_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_weak_child_management partition(dt)
select 
    concat(frail_chilid,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as frail_chilid,
    null as unified_uscid,
    null as chilid,
    null as control_uscid,
    null as feed_way_type_code,
    null as feed_way_type_name,
    null as delicate_reason_type_code,
    null as delicate_reason_type_name,
    null as disease_outcome_code,
    null as disease_outcome_name,
    null as end_case_date,
    null as case_close_dor_no,
    null as case_close_dor_name,
    null as close_case_org_code,
    null as case_close_org_name,
    null as build_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_weak_child_followup partition(dt)
select 
    concat(fu_clinic_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as fu_clinic_id,
    null as unified_uscid,
    null as frail_chilid,
    null as fu_clinic_date,
    null as fu_month,
    null as sympt_definition,
    null as positive_names,
    null as check_rslt,
    null as process_guide_ad,
    null as appoint_flup_date,
    null as check_dor_no,
    null as check_dor_name,
    null as check_org_code,
    null as check_org_name,
    null as full_name,
    null as delicate_reason_type_code,
    null as delicate_reason_type_name,
    null as physical_signs,
    null as assist_check_proj_name,
    null as assist_check_rslt,
    null as appoint_date,
    null as check_date,
    null as disease_outcome_code,
    null as disease_outcome_name,
    null as end_case_date,
    null as case_close_dor_name,
    null as case_close_dor_no,
    null as case_close_org_name,
    null as build_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_birth_defects_monitoring partition(dt)
select 
    concat(birth_defect_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as birth_defect_id,
    null as unified_uscid,
    null as chilid,
    null as reg_org_code,
    null as re_org_name,
    null as chil_father,
    null as chil_mother,
    null as resd_flag_code,
    null as resd_flag_name,
    null as prg_cnt,
    null as matn_cnt,
    null as tel,
    null as children_name,
    null as gendercode,
    null as gendername,
    null as brdy,
    null as birth_certificate_no,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as birth_gest_weeks,
    null as chil_wt,
    null as fetals,
    null as pregnancy_code,
    null as pregnancy_name,
    null as birth_defect_outcome_code,
    null as birth_defect_outcome_name,
    null as trt__mark,
    null as defect_diag_basis_code,
    null as diag_type_name,
    null as type_code,
    null as type_name,
    null as defect_type_code1,
    null as defect_type_name1,
    null as defect_type_code2,
    null as defect_type_name2,
    null as defect_type_code3,
    null as defect_type_name3,
    null as defect_type_code4,
    null as defect_type_name4,
    null as defect_type_code5,
    null as defect_type_name5,
    null as defect_type_code6,
    null as defect_type_name6,
    null as defect_type_code7,
    null as defect_type_name7,
    null as defect_type_code8,
    null as defect_type_name8,
    null as defect_type_code9,
    null as defect_type_name9,
    null as defect_type_code10,
    null as defect_type_name10,
    null as other_defect_type_code,
    null as other_defect_type_name,
    null as prime_disease_mark,
    null as prime_disease_code,
    null as prime_disease_name,
    null as prime_drug_type_code,
    null as prime_drug_type_name,
    null as prime_touch_type_code,
    null as prime_touch_type_name,
    null as dead_fetus_no,
    null as spontaneous_abortion_no,
    null as birth_defect_no,
    null as defect_name,
    null as family_hereditary_his_code,
    null as family_hereditary_his_name,
    null as consanguine_mar_mark,
    null as consanguine_relate_code,
    null as consanguine_relate_name,
    null as handicapped_chil_diag_dscr,
    null as handicapped_chil_diag_age,
    null as vision_disability_type_code,
    null as vision_disability_type_name,
    null as hear_disability_type_code,
    null as hear_disability_type_name,
    null as mentally_disability_type_code,
    null as mentally_disability_type_name,
    null as body_disability_type_code,
    null as body_disability_type_name,
    null as register_datetime,
    null as filler_no,
    null as fill_name,
    null as input_occup_code,
    null as input_occup_name,
    null as input_orgcode,
    null as hosp_auth_date,
    null as hosp_auth_no,
    null as hosp_auth_name,
    null as hosp_auth_title_code,
    null as hosp_auth_title_name,
    null as prov_auth_date,
    null as prov_auth_no,
    null as prov_auth_title_code,
    null as prov_auth_title_name,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_birth_defects_report partition(dt)
select 
    concat(ref_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as ref_no,
    null as unified_uscid,
    null as chilid,
    null as rpt_org_uscid,
    null as rpt_org_name,
    null as chil_father,
    null as fthr_age,
    null as fthr_naty_code,
    null as fthr_naty_name,
    null as chil_mother,
    null as mthr_age,
    null as mthr_naty_code,
    null as mthr_naty_name,
    null as prg_cnt,
    null as matn_cnt,
    null as resd_flag_code,
    null as resd_flag_name,
    null as family_addr,
    null as poscode,
    null as tel,
    null as children_name,
    null as gendercode,
    null as gendername,
    null as chil_brdy,
    null as birth_certificate_no,
    null as psncert_type_code,
    null as psncert_type_name,
    null as certno,
    null as birth_gest_weeks,
    null as chil_wt,
    null as fetals,
    null as outcome_code,
    null as outcome_name,
    null as birth_defect_name,
    null as diag_hos_name,
    null as diag_accord_code,
    null as diag_accord_name,
    null as birth_defect_diag,
    null as early_pregnancy_dscr,
    null as family_history,
    null as handicapped_chil_diag_dscr,
    null as fill_name,
    null as input_occup_code,
    null as input_occup_name,
    null as fn_org,
    null as register_datetime,
    null as prov_auth_title_code,
    null as prov_auth_title_name,
    null as prov_auth_no,
    null as prov_auth_date,
    null as hosp_auth_title_code,
    null as hosp_auth_title_name,
    null as hosp_auth_name,
    null as hosp_auth_date,
    null as ep_harmful_factors_dscr,
    null as prime_touch_type_code,
    null as prime_touch_type_name,
    null as prime_drug_type_code,
    null as prime_drug_type_name,
    null as ep_illness_code,
    null as ep_illness_name,
    null as prime_disease_mark,
    null as type_code,
    null as type_name,
    null as trt__mark,
    null as defective_child_end_code,
    null as defective_child_end_name,
    null as pregnancy_code,
    null as pregnancy_name,
    null as gestational_age,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_death_report partition(dt)
select 
    concat(rpt_card_no,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as rpt_card_no,
    null as unified_uscid,
    null as report_org_name,
    null as chilid,
    null as rpt_org_uscid,
    null as dead_org_name,
    null as death_date,
    null as children_name,
    null as gender_code,
    null as gender_name,
    null as geso_val,
    null as resd_flag_code,
    null as resd_flag_name,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    null as curr_addr_prov_name,
    null as curr_addr_city_code,
    null as curr_addr_city_name,
    null as curr_addr_coty_code,
    null as curr_addr_coty_name,
    null as curr_addr_town_code,
    null as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    null as curr_addr_cotry_name,
    null as residential_housnum,
    null as curr_addr,
    null as chil_father,
    null as chil_mother,
    null as tel,
    null as brdy,
    null as bdd_within_seven_days,
    null as bdd_7_days_away,
    null as before_death_diag_code,
    null as before_death_diag_name,
    null as diag_org_name,
    null as illness_summary,
    null as death_diag_code,
    null as death_diag_name,
    null as death_age,
    null as death_diag,
    null as death_code,
    null as death_tylp_name,
    null as deathplace_type_code,
    null as death_place_name,
    null as pre_death_treatment_code,
    null as pre_death_treatment_name,
    null as no_treat_reason_code,
    null as no_treat_reason_name,
    null as birth_addr_type_code,
    null as birth_addr_type_name,
    null as death_cotry_code,
    null as death_cotry_name,
    null as rpt_doc_code,
    null as rpt_doc_name,
    null as rpt_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_newborn_visit_basic_info partition(dt)
select 
    concat(children_visit_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as children_visit_id,
    null as unified_uscid,
    null as chilid,
    null as interview_org_code,
    null as nwb_name,
    null as nwb_gend_code,
    null as nwb_gend_name,
    null as nwb_brdy,
    null as baby_psncert_type_code,
    null as baby_psncert_type_name,
    null as nb_cert_no,
    null as birth_addr_type_code,
    null as birth_addr_type_name,
    null as birth_situat_code,
    null as birth_situat_name,
    null as other_birth_situat,
    null as birth_gest_weeks,
    null as chil_wt,
    null as birth_ht,
    null as birth_head_circ,
    null as birth_chest_circ,
    null as birth_health_code,
    null as birth_health_name,
    null as childbirth_code,
    null as childbirth_name,
    null as last_deliver_way_code,
    null as last_deliver_way_name,
    null as apgar1_score,
    null as apgar5_score,
    null as apgar10_score,
    null as mother_preg_affect_code,
    null as mother_preg_affect_name,
    null as chil_mother,
    null as mother_childbirth_age,
    null as mthr_ntly_code,
    null as mthr_ntly_name,
    null as mthr_naty_code,
    null as mthr_naty_name,
    null as mother_cert_no,
    null as mother_psncert_type_code,
    null as mother_psncert_type_name,
    null as mother_brdy,
    null as mother_occup_type_code,
    null as mother_occup_type_name,
    null as mother_emp_name,
    null as mother_tel,
    null as chil_father,
    null as father_childbirth_age,
    null as fthr_ntly_code,
    null as fthr_ntly_name,
    null as fthr_naty_code,
    null as fthr_naty_name,
    null as father_cert_no,
    null as father_cert_type_code,
    null as father_cert_type_name,
    null as father_brdy,
    null as father_occup_type_code,
    null as father_occup_type_name,
    null as father_emp_name,
    null as father_tel,
    null as birth_addr,
    null as birth_prov_code,
    null as birth_prov_name,
    null as birth_city_code,
    null as birth_city_name,
    null as birth_coty_code,
    null as birth_coty_name,
    null as birth_town_code,
    null as birth_town_name,
    null as birth_comm_code,
    null as birth_comm_name,
    null as birth_cotry_name,
    null as birth_addr_housnum,
    null as cur_addr_prov_code,
    null as cur_addr_prov_name,
    null as cur_addr_city_code,
    null as cur_addr_city_name,
    null as cur_addr_coty_code,
    null as cur_addr_coty_name,
    null as cur_addr_town_code,
    null as cur_addr_town_name,
    null as cur_addr_comm_code,
    null as cur_addr_comm_name,
    null as cur_addr_street,
    null as cur_addr_housnum,
    null as cur_addr,
    null as deliver_code,
    null as deliver_name,
    null as midwifery_psn_name,
    null as deliver_org_code,
    null as deliver_org_name,
    null as build_org_name,
    null as build_name,
    null as build_date,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_newborn_visit_record partition(dt)
select 
    concat(nb_visit_id,unified_uscid,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    null as nb_visit_id,
    null as unified_uscid,
    null as visit_no,
    null as children_visit_id,
    null as interview_org_code,
    null as together_mark,
    null as nb_hear_screen_case_code,
    null as nb_hear_screen_case_name,
    null as nb_dis_screen_proj_code,
    null as nb_dis_screen_proj_name,
    null as nebo_screen,
    null as feedway_type_code,
    null as feedway_type_name,
    null as daily_suck_no,
    null as daily_suck_times,
    null as diet_case_code,
    null as diet_case_name,
    null as sleep_case_code,
    null as sleep_case_name,
    null as nb_shit_code,
    null as nb_shit_name,
    null as nb_shit_dscr,
    null as baby_shit_times,
    null as vomit_mark,
    null as not_breastfeed_dscr,
    null as baby_tprt,
    null as baby_wt,
    null as baby_pulse,
    null as heart_rate,
    null as br,
    null as jaundice_part_mark,
    null as jaundice_part_code,
    null as jaundice_part_name,
    null as other_jaundice_part,
    null as bregma_horiz_diam,
    null as bregma_vert_diam,
    null as bregma_tension_code,
    null as bregma_tension_name,
    null as infant_spirit_type,
    null as child_complexion_code,
    null as child_complexion_name,
    null as child_head_hematoma_size,
    null as skin_check_res_code,
    null as skin_check_res_name,
    null as eye_appear_check_abnorm_mark,
    null as eye_appear_check_abnorm_dscr,
    null as ear_appear_check_abnorm_mark,
    null as ear_appear_check_abnorm_dscr,
    null as nose_abnorm_check_mark,
    null as nose_abnorm_check_res_dscr,
    null as oral_check_abnorm_mark,
    null as oral_check_abnorm_res_dscr,
    null as head_check_abnorm_mark,
    null as head_check_abnorm_res_dscr,
    null as heart_auscultate_abnorm_mark,
    null as heart_auscultate_abnorm_dscr,
    null as lung_auscultate_abnorm_mark,
    null as lung_auscultate_abnorm_dscr,
    null as chest_check_abnorm_mark,
    null as chest_check_abnorm_res_dscr,
    null as abdominal_check_abnorm_mark,
    null as abdominal_check_abnorm_dscr,
    null as hips_check_abnorm_mark,
    null as hips_check_abnorm_res_dscr,
    null as aedea_check_abnorm_mark,
    null as aedea_check_abnorm_dscr,
    null as cm_check_abnorm_mark,
    null as umbilical_check_res_code,
    null as umbilical_check_res_name,
    null as red_hip_flag,
    null as limbs_act_abnorm_mark,
    null as limbs_act_check_abnorm_rscr,
    null as baby_neck_mass_mark,
    null as neck_mass_check_res_dscr,
    null as anus_check_abnorm_mark,
    null as anus_check_abnorm_res_dscr,
    null as spine_check_abnorm_mark,
    null as spine_check_abnorm_res_dscr,
    null as referral_flag,
    null as accept_org_name,
    null as accept_depart_name,
    null as referral_reason,
    null as transfer_treatment_mark,
    null as referral_rec_dscr,
    null as nb_health_guide_type_code,
    null as nb_health_guide_type_name,
    null as check_sum_dscr,
    null as nb_dscr,
    null as chkpsn_name,
    null as exam_operator_code,
    null as reg_org_code,
    null as visits_org_name,
    null as appoint_nwb_sys_date,
    null as exam_emp,
    null as interview_date,
    null as next_visit_date,
    null as next_visit_place,
    null as special_situat_record,
    null as process_guide_ad,
    null as data_rank,
    null as state,
    null as business_time,
    null as reserve1,
    null as reserve2,
    fy_etfsjl.ssnnz0 as belo_to_age_gro_code,
    fy_etfsjl.ssnnz0 as belo_to_age_gro_name,
    fy_etfsjl.xmxl00 as exam_item_time_peri_code,
    fy_etfsjl.xmxl00 as exam_item_time_peri_name,
    fy_etfsjl.sgqk00 as hgt_info_code,
    fy_etfsjl.sgqk00 as hgt_info_name,
    fy_etfsjl.tzqk00 as wt_code,
    fy_etfsjl.tzqk00 as wt_name,
    fy_etfsjl.wi_id as imp_data_of_old_sys_no,
    fy_etfsjl.yscys as one_yea_old_teethi,
    fy_etfsjl.tzpjxx as wt_eval_detl_code,
    fy_etfsjl.tzpjxx as wt_eval_detl_name,
    fy_etfsjl.sgpjxx as hgt_eval_detl_code,
    fy_etfsjl.sgpjxx as hgt_eval_detl_name,
    fy_etfsjl.twpj00 as head_circ_eval,
    fy_etfsjl.zdjgyy as diag_rslt_nurt_code,
    fy_etfsjl.zdjgyy as diag_rslt_nurt_name,
    fy_etfsjl.zdjgbt as diag_rslt_tonsi_code,
    fy_etfsjl.zdjgbt as diag_rslt_tonsi_name,
    fy_etfsjl.zdjgqt as diag_rslt_oth_code,
    fy_etfsjl.zdjgqt as diag_rslt_oth_name,
    fy_etfsjl.zdjggw as diag_rslt_hrisk_code,
    fy_etfsjl.zdjggw as diag_rslt_hrisk_name,
    fy_etfsjl.zhpj00 as com_eval_code,
    fy_etfsjl.zhpj00 as com_eval_name,
    fy_etfsjl.yypj00 as nurt_eval_code,
    fy_etfsjl.yypj00 as nurt_eval_name,
    fy_etfsjl.yypjna as nurt_eval_na,
    fy_etfsjl.issc00 as measu_way,
    fy_etfsjl.sctzpj as wt_hgt_stdv_code,
    fy_etfsjl.sctzpj as wt_hgt_stdv_name,
    fy_etfsjl.yygs00 as over_nutr_eval_code,
    fy_etfsjl.yygs00 as over_nutr_eval_name,
    fy_etfsjl.gwidyy as malnu_hrisk_file_id,
    fy_etfsjl.gwidpx as anem_hrisk_file_id,
    fy_etfsjl.gwidgl as ricke_hrisk_file_id,
    fy_etfsjl.gwiddj as reg_hrisk_file_id,
    fy_etfsjl.gwpg00 as hrisk_eval,
    fy_etfsjl.czwt00 as hrisk_exis_prb,
    fy_etfsjl.gwzd00 as hrisk_guid,
    fy_etfsjl.gwzl00 as trt,
    fy_etfsjl.gwvdzl as vitd_trt,
    fy_etfsjl.czwtpx as exis_prb_anem,
    fy_etfsjl.gwzdpx as hrisk_guid_anem,
    fy_etfsjl.czwtgl as exis_prb_ricke,
    fy_etfsjl.gwzdgl as hrisk_guid_ricke,
    fy_etfsjl.yeyid0 as kinde_id,
    fy_etfsjl.bjid00 as clss_id,
    fy_etfsjl.ysty00 as tcm_diet_condi,
    fy_etfsjl.qjts00 as tcm_rise_fall_regu,
    fy_etfsjl.xwarff as pass_paren_pt_by_rubb_mtd,
    fy_etfsjl.zwqk00 as parenttcm_knowledge,
    fy_etfsjl.myd000 as tcmadvice_satisfaction,
    fy_etfsjl.gwidza as hrisk_chld_spec_cas_mgt_rcd_id,
    fy_etfsjl.pgjg00 as eval_rslt,
    fy_etfsjl.pgjgcl as dspo,
    fy_etfsjl.pgffxl as highriskchild_evalmethod,
    fy_etfsjl.pgjgxl as highriskchild_evalresult,
    fy_etfsjl.gwzdxl as highriskchild_guidance,
    fy_etfsjl.gwzdcl as highriskchild_handling,
    fy_etfsjl.gwidxl as mind_devt_abn_hrisk_file_id,
    fy_etfsjl.sfwc00 as whtr_out,
    fy_etfsjl.zd00za as highriskcase_guidance,
    fy_etfsjl.cl00za as highriskcase_handling,
    fy_etfsjl.scxtbhtemp as guid_data_used,
    fy_etfsjl.qdpx00 as mil_anem,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
insert overwrite table ods_hcs_hch_high_risk_register partition(dt)
select 
    concat(unified_uscid,manl_sys_inte_code,upload_time,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    fy_gweda. as chilid,
    fy_gweda. as unified_uscid,
    fy_gweda.scxtnm as manl_sys_inte_code,
    fy_gweda.rdate as casefil_time,
    fy_gweda.laszz as caseopen_childsymptoms,
    fy_gweda.bcategory as norm_labo_diffic,
    fy_gweda.feed as feed_his,
    fy_gweda.rage as adequ_age,
    fy_gweda.pdweeks as pret_week,
    fy_gweda.diagnoses as infi_child_diag,
    fy_gweda.lastrecord as infi_child_past_his,
    fy_gweda.height as birth_ht,
    fy_gweda.weight as chil_wt,
    fy_gweda.times as chil_fetus,
    fy_gweda.onetwo as sin_twin_preg_code,
    fy_gweda.onetwo as sin_twin_preg_name,
    fy_gweda.closingdate as end_case_date,
    fy_gweda.closingzz as caseclose_childsymptoms,
    fy_gweda.closinger as case_close_dor_name,
    fy_gweda.closingorg as case_close_org_name,
    fy_gweda.creatdate as build_date,
    fy_gweda.jddw00 as filed_emp,
    fy_gweda.zdjgbh as diag_rslt_no,
    fy_gweda.sfja00 as whtr_clscase,
    fy_gweda.wi_id as imp_data_of_old_sys_no,
    fy_gweda.cjrq00 as crte_date,
    fy_gweda.cjz000 as crter,
    fy_gweda.zhxgrq as last_modi_date,
    fy_gweda.zhxgz0 as last_modi_the,
    fy_gweda.dalx00 as file_type_code,
    fy_gweda.dalx00 as file_type_name,
    fy_gweda.zglx00 as trans_type_code,
    fy_gweda.zglx00 as trans_type_name,
    fy_gweda.css000 as bir_his_code,
    fy_gweda.css000 as bir_his_name,
    fy_gweda.lgywys as 6_indi_mon_in_feed_his_code,
    fy_gweda.lgywys as 6_indi_mon_in_feed_his_name,
    fy_gweda.mpxyz0 as moth_prg_anem_weeks,
    fy_gweda.mpxxdb as moth_prg_anem_hemoglobin,
    fy_gweda.mpxtj0 as moth_prg_anem_info,
    fy_gweda.mpxyw0 as moth_prg_anem_medication,
    fy_gweda.mpxjl0 as moth_prg_anem_dosage,
    fy_gweda.mpxlc0 as moth_prg_anem_cure,
    fy_gweda.swzhnl as child_ironfoodstartage,
    fy_gweda.yqbrvd as moth_prg_and_lacta_code,
    fy_gweda.yqbrvd as moth_prg_and_lacta_name,
    fy_gweda.fyvd00 as chld_taki_vitd,
    fy_gweda.fyvdyl as begn_taki_vitd_age_mon,
    fy_gweda.fyvdtl as begn_taki_vitd_age_day,
    fy_gweda.fyvdjl as begn_taki_vitd,
    fy_gweda.glbtz0 as rickets_sign_code,
    fy_gweda.glbtz0 as rickets_sign_name,
    fy_gweda.xg0000 as calc,
    fy_gweda.xl0000 as blo_phos,
    fy_gweda.xakp00 as blo_akp,
    fy_gweda.xsh000 as blo_25d,
    fy_gweda.xxjc00 as x_line_exam,
    fy_gweda.gwpg00 as hrisk_eval_code,
    fy_gweda.gwpg00 as hrisk_eval_name,
    fy_gweda.gwfd00 as hrisk_sec,
    fy_gweda.djrq00 as reg_date,
    fy_gweda.gwys00 as hrisk_fac_or_abnor,
    fy_gweda.zfjg00 as follo_up_rslt,
    fy_gweda.gwczwt as hrisk_spec_cas_mgt_exis_prb,
    fy_gweda.gwzdyj as hrisk_spec_cas_mgt_guid,
    fy_gweda.zzdwid as refl_emp_id,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from zl3500000000000004_syyzw. as t

) as tab

-- ================================================
