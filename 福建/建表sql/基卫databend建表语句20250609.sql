-- 生成时间：2025-06-09 09:58:40
CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_basic` (
    `rid` STRING COMMENT '数据唯一记录号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `addr_city` STRING COMMENT '属地地区',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `full_name` STRING COMMENT '姓名',
    `patient_souc_code` STRING COMMENT '患者来源代码',
    `patient_souc_name` STRING COMMENT '患者来源名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `brdy` DATE COMMENT '出生日期',
    `birth_addr` STRING COMMENT '出生详细地址',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `ntly_code` STRING COMMENT '国籍代码',
    `ntly_name` STRING COMMENT '国籍名称',
    `mobile` STRING COMMENT '手机号码',
    `tel` STRING COMMENT '联系电话',
    `empr_poscode` STRING COMMENT '工作单位邮编',
    `empr_name` STRING COMMENT '工作单位名称',
    `empr_addr` STRING COMMENT '工作单位地址',
    `residential_code` STRING COMMENT '居住地行政区划代码',
    `residential_name` STRING COMMENT '居住地行政区划名称',
    `curr_addr_prov_code` STRING COMMENT '居住地省自治区直辖市代码',
    `curr_addr_prov_name` STRING COMMENT '居住地省自治区直辖市名称',
    `curr_addr_city_code` STRING COMMENT '居住地市地区代码',
    `curr_addr_city_name` STRING COMMENT '居住地市地区名称',
    `curr_addr_coty_code` STRING COMMENT '居住地县区代码',
    `curr_addr_coty_name` STRING COMMENT '居住地县区名称',
    `curr_addr_town_code` STRING COMMENT '居住地乡镇街道代码',
    `curr_addr_town_name` STRING COMMENT '居住地乡镇街道名称',
    `curr_addr_comm_code` STRING COMMENT '居住地居委会村代码',
    `curr_addr_comm_name` STRING COMMENT '居住地居委会村名称',
    `curr_addr_cotry_name` STRING COMMENT '居住地村街路弄等',
    `curr_addr_housnum` STRING COMMENT '居住地门牌号包括“室”',
    `residential_addr` STRING COMMENT '居住地址详细',
    `resd_addr_code` STRING COMMENT '户籍地行政区划代码',
    `resd_addr_name` STRING COMMENT '户籍地行政区划名称',
    `resd_addr_prov_code` STRING COMMENT '户籍地省自治区直辖市代码',
    `resd_addr_prov_name` STRING COMMENT '户籍地省自治区直辖市名称',
    `resd_addr_coty_code` STRING COMMENT '户籍地县区代码',
    `resd_addr_coty_name` STRING COMMENT '户籍地县区名称',
    `resd_addr_subd_code` STRING COMMENT '户籍地乡镇街道代码',
    `resd_addr_subd_name` STRING COMMENT '户籍地乡镇街道名称',
    `resd_addr_comm_code` STRING COMMENT '户籍地居委会村代码',
    `resd_addr_comm_name` STRING COMMENT '户籍地居委会村名称',
    `resd_addr_cotry_name` STRING COMMENT '户籍地村街路弄等',
    `resd_addr_housnum` STRING COMMENT '户籍地门牌号包括“室”',
    `resd_addr` STRING COMMENT '户籍地址详细地址',
    `resd_addr_poscode` STRING COMMENT '户籍地址邮编',
    `coner_name` STRING COMMENT '联系人监护人姓名',
    `relation_code` STRING COMMENT '联系人关系代码',
    `relation_name` STRING COMMENT '联系人关系名称',
    `coner_addr` STRING COMMENT '联系人监护人地址',
    `coner_org_name` STRING COMMENT '联系人单位名称',
    `coner_poscode` STRING COMMENT '联系人监护人邮编',
    `coner_tel` STRING COMMENT '联系人监护人电话号码',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='个人健康档案基本信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_home` (
    `rid` STRING COMMENT '数据唯一记录号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `emp_status_code` STRING COMMENT '从业状况代码',
    `emp_status_name` STRING COMMENT '从业状况名称',
    `per_addr_code` STRING COMMENT '常住类型代码',
    `per_addr_name` STRING COMMENT '常住类型名称',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `blotype_abo_code` STRING COMMENT 'ABO血型代码',
    `blotype_abo_name` STRING COMMENT 'ABO血型名称',
    `blotype_rh_code` STRING COMMENT 'Rh血型代码',
    `blotype_rh_name` STRING COMMENT 'Rh血型名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `medfee_paymtd_code` STRING COMMENT '医疗付款方式代码',
    `medfee_paymtd_name` STRING COMMENT '医疗付款方式名称',
    `expose_his_code` STRING COMMENT '暴露史代码',
    `expose_his_name` STRING COMMENT '暴露史名称',
    `hereditary_mark` STRING COMMENT '遗传病史标志',
    `hereditary_code` STRING COMMENT '遗传病史代码',
    `hereditary_name` STRING COMMENT '遗传病史名称',
    `chronic_code` STRING COMMENT '慢性病患病情况代码',
    `chronic_name` STRING COMMENT '慢性病患病情况名称',
    `disa_info_code` STRING COMMENT '残疾情况代码',
    `disa_info_name` STRING COMMENT '残疾情况名称',
    `disable_certificate_no` STRING COMMENT '残疾证号',
    `brf_code` STRING COMMENT '行为危险因素情况代码',
    `brf_name` STRING COMMENT '行为危险因素情况名称',
    `kitchen_exhaust_mark` STRING COMMENT '厨房排风设施标识',
    `kitchen_exhaust_code` STRING COMMENT '厨房排风设施代码',
    `kitchen_exhaust_name` STRING COMMENT '厨房排风设施名称',
    `fuel_type_code` STRING COMMENT '燃料类型代码',
    `fuel_type_name` STRING COMMENT '燃料类型名称',
    `drink_water_type_code` STRING COMMENT '饮水类别代码',
    `drink_water_type_name` STRING COMMENT '饮水类别名称',
    `wc_type_code` STRING COMMENT '厕所类别代码',
    `wc_type_name` STRING COMMENT '厕所类别名称',
    `avian_corral_type_code` STRING COMMENT '禽畜栏类别代码',
    `avian_corral_type_name` STRING COMMENT '禽畜栏类别名称',
    `build_date` DATE COMMENT '建档日期',
    `build_org_code` STRING COMMENT '建档机构统一社会信用代码',
    `build_org_name` STRING COMMENT '建档机构名称',
    `build_org_tel` STRING COMMENT '建档机构联系电话',
    `mang_org_code` STRING COMMENT '健康档案管理机构名称',
    `duty_dor_no` STRING COMMENT '责任医生工号',
    `duty_dor_name` STRING COMMENT '责任医生姓名',
    `dscr` STRING COMMENT '其他说明',
    `duty_dor_tel` STRING COMMENT '责任医生电话',
    `registerhiscode` STRING COMMENT '登记人工号',
    `registerhisname` STRING COMMENT '登记人姓名',
    `reg_dor_no` STRING COMMENT '录入医生工号',
    `enter_dor_name` STRING COMMENT '录入医生姓名',
    `reg_date` TIMESTAMP COMMENT '登记日期',
    `inquirer_name` STRING COMMENT '调查者姓名',
    `inquirer_no` STRING COMMENT '调查者工号',
    `inquirer_date` DATE COMMENT '调查日期',
    `health_rec_status_code` STRING COMMENT '档案状态代码',
    `health_rec_status_name` STRING COMMENT '档案状态名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `workplace` STRING COMMENT '工作单位',
    `feepay_type_code` STRING COMMENT '费用支付方式代码',
    `feepay_type_name` STRING COMMENT '费用支付方式名称',
    `pobservation_type_code` STRING COMMENT '既往观察项目分类代码',
    `pobservation_type_name` STRING COMMENT '既往观察项目分类名称',
    `pobservation_code` STRING COMMENT '既往观察项目代码',
    `pobservation_name` STRING COMMENT '既往观察项目名称',
    `pobservationmethods_code` STRING COMMENT '既往观察方法代码',
    `pobservationmethods_name` STRING COMMENT '既往观察方法名称',
    `pobservationresult_code` STRING COMMENT '既往观察结果代码',
    `pobservationresult_name` STRING COMMENT '既往观察结果名称',
    `observations_date` DATE COMMENT '观察结果开始(发现)日期',
    `observatione_date` DATE COMMENT '观察结果停止(治愈)日期',
    `medpay_uebmi` STRING COMMENT '医疗费用支付方式城镇职工基本医疗保险',
    `medpay_trpbmi` STRING COMMENT '医疗费用支付方式城镇居民基本医疗保险',
    `medpay_nrcmc` STRING COMMENT '医疗费用支付方式新型农村合作医疗',
    `med_fee_pay_way_poor_assi` STRING COMMENT '医疗费用支付方式贫困救助',
    `med_fee_pay_way_busi_hi` STRING COMMENT '医疗费用支付方式商业医疗保险',
    `medpay_fape` STRING COMMENT '医疗费用支付方式全公费',
    `med_fee_pay_way_full_ownpay` STRING COMMENT '医疗费用支付方式全自费',
    `med_fee_pay_way_oth_way` STRING COMMENT '医疗费用支付方式其他方式',
    `fmhis_fthr` STRING COMMENT '家族史父亲',
    `fmhis_mthr` STRING COMMENT '家族史母亲',
    `fmhis_brot_sist` STRING COMMENT '家族史兄妹',
    `fmhis_child` STRING COMMENT '家族史子女',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='个人健康档案首页表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_lifestyle` (
    `rid` STRING COMMENT '数据唯一记录号',
    `patientid` STRING COMMENT '个人档案标识符',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `ref_no` STRING COMMENT '编号',
    `phys_exer_adhe_excs_time` STRING COMMENT '体育锻炼坚持锻炼时间',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `start_smoke_way_age` STRING COMMENT '吸烟情况吸烟年龄',
    `stop_smoke_way_age` STRING COMMENT '吸烟情况戒烟年龄',
    `stop_drink_way_age` STRING COMMENT '饮酒情况戒酒年龄',
    `start_drink_way_age` STRING COMMENT '饮酒情况开始饮酒年龄',
    `last_year_drunkenness_mark` STRING COMMENT '近一年是否有醉酒',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `drink_type_others` STRING COMMENT '饮酒情况饮酒种类其他',
    `prfs_expo_info` STRING COMMENT '职业暴露情况是否',
    `ins_id` STRING COMMENT '机构ID',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `prfs_expo_chem_prot_mes_cont` STRING COMMENT '职业暴露化学品防护措施内容',
    `prfs_expo_toxi_prot_mes_cont` STRING COMMENT '职业暴露毒物防护措施内容',
    `prfs_expo_rdat_prot_mes_cont` STRING COMMENT '职业暴露射线防护措施内容',
    `excs_frqu_code` STRING COMMENT '锻炼频率代码',
    `excs_frqu_name` STRING COMMENT '锻炼频率名称',
    `each_excs_time` STRING COMMENT '每次锻炼时间分钟',
    `excs_way` STRING COMMENT '锻炼方式',
    `smoke_mark_code` STRING COMMENT '吸烟情况吸烟状况代码',
    `smoke_mark_name` STRING COMMENT '吸烟情况吸烟状况名称',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `stop_drink_code` STRING COMMENT '饮酒情况是否戒酒代码',
    `stop_drink_name` STRING COMMENT '饮酒情况是否戒酒名称',
    `prfs_expo_dust` STRING COMMENT '职业暴露粉尘',
    `prfs_expo_dust_prot_mes` STRING COMMENT '职业暴露粉尘防护措施',
    `prfs_expo_oth` STRING COMMENT '职业暴露其他',
    `prfs_expo_oth_prot_mes` STRING COMMENT '职业暴露其他防护措施',
    `prfs_expo_dust_prot_mes_cont` STRING COMMENT '职业暴露粉尘防护措施内容',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='生活方式';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_illness` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `dise_his_type_code` STRING COMMENT '既往疾病种类代码',
    `dise_his_type_name` STRING COMMENT '既往疾病种类名称',
    `cnfm_date` DATE COMMENT '确诊日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往疾病史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_surgery` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `proc_name` STRING COMMENT '手术名称',
    `proc_date` DATE COMMENT '手术日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往手术史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_injury` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `trauma_name` STRING COMMENT '外伤名称',
    `trauma_date` DATE COMMENT '外伤日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往外伤史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_transfusion` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `transfuse_reason` STRING COMMENT '输血原因',
    `transfuse_datetime` TIMESTAMP COMMENT '输血日期时间',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往输血史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_family` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `relation_patient_code` STRING COMMENT '患者与本人关系代码',
    `relation_patient_name` STRING COMMENT '患者与本人关系名称',
    `disease_code` STRING COMMENT '疾病代码',
    `disease_name` STRING COMMENT '疾病名称',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家族疾病史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_sign` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `signid` STRING COMMENT '签约ID',
    `sign_dor_no` STRING COMMENT '签约医生工号',
    `sign_dor_name` STRING COMMENT '签约医生姓名',
    `sign_team_code` STRING COMMENT '签约团队编号',
    `sign_team_name` STRING COMMENT '签约团队名称',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `sign_datetime` TIMESTAMP COMMENT '签约时间',
    `unsign_date` TIMESTAMP COMMENT '解约时间',
    `unsign_reason` STRING COMMENT '解约原因',
    `sign_status_code` STRING COMMENT '签约状态代码',
    `sign_status_name` STRING COMMENT '签约状态名称',
    `reg_dor_code` STRING COMMENT '登记医生工号',
    `reg_dor_name` STRING COMMENT '登记医生姓名',
    `reg_time` TIMESTAMP COMMENT '登记时间',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `presideid_card_type_code` STRING COMMENT '负责人身份证件类别代码',
    `presideid_card_type_name` STRING COMMENT '负责人身份证件类别名称',
    `presideid_card_no` STRING COMMENT '负责人身份证件号码',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭签约关系表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `householder_psncert_type_code` STRING COMMENT '户主身份证件类别代码',
    `householder_psncert_type_name` STRING COMMENT '户主身份证件类别名称',
    `householder_cert_no` STRING COMMENT '户主身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `patientid` STRING COMMENT '个人档案标识符',
    `family_tel` STRING COMMENT '家庭电话',
    `family_addr_code` STRING COMMENT '家庭所在地行政区划代码',
    `family_addr_name` STRING COMMENT '家庭所在地行政区划名称',
    `family_addr_prov_code` STRING COMMENT '家庭所在地省自治区直辖市代码',
    `family_addr_prov_name` STRING COMMENT '家庭所在地省自治区直辖市名称',
    `family_addr_city_code` STRING COMMENT '家庭所在地市地区代码',
    `family_addr_city_name` STRING COMMENT '家庭所在地市地区名称',
    `family_addr_coty_code` STRING COMMENT '家庭所在地县区代码',
    `family_addr_coty_name` STRING COMMENT '家庭所在地县区名称',
    `family_addr_town_code` STRING COMMENT '家庭所在地乡镇街道代码',
    `family_addr_town_name` STRING COMMENT '家庭所在地乡镇街道名称',
    `family_addr_comm_code` STRING COMMENT '家庭所在地居委会村代码',
    `family_addr_comm_name` STRING COMMENT '家庭所在地居委会村名称',
    `family_addr_cotry_name` STRING COMMENT '家庭所在地村街路弄等',
    `family_addr_housnum` STRING COMMENT '家庭所在地门牌号包括“室”',
    `family_addr` STRING COMMENT '家庭住址',
    `poscode` STRING COMMENT '邮政编码',
    `family_rec_status_code` STRING COMMENT '家庭档案状态代码',
    `family_rec_status_name` STRING COMMENT '家庭档案状态名称',
    `inquirer_staff_no` STRING COMMENT '调查人工号',
    `inquirer_staff_name` STRING COMMENT '调查人姓名',
    `inquirer_date` DATE COMMENT '调查日期',
    `registerhiscode` STRING COMMENT '登记人工号',
    `registerhisname` STRING COMMENT '登记人姓名',
    `reg_date` TIMESTAMP COMMENT '登记日期',
    `data_rank` STRING COMMENT '密级',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭健康档案表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_member` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `householder_mark` STRING COMMENT '户主标志',
    `householder_relation_code` STRING COMMENT '与户主关系代码',
    `householder_relation_name` STRING COMMENT '与户主关系名称',
    `data_rank` STRING COMMENT '密级',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭成员关系表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_staff` (
    `rid` STRING COMMENT '数据唯一记录号',
    `job_no` STRING COMMENT '工号',
    `uscid` STRING COMMENT '医疗机构统一社会信用代码',
    `rgst_name` STRING COMMENT '注册名称',
    `full_name` STRING COMMENT '姓名',
    `certno` STRING COMMENT '身份证件号码',
    `dept_code` STRING COMMENT '科室代码',
    `team` STRING COMMENT '所属团队',
    `pro_tech_duty_code` STRING COMMENT '专业技术职务代码',
    `pro_tech_duty_name` STRING COMMENT '专业技术职务名称',
    `job_title_code` STRING COMMENT '职称代码',
    `job_title_name` STRING COMMENT '职称名称',
    `brdy` DATE COMMENT '出生日期',
    `psn_type_code` STRING COMMENT '人员类别代码',
    `psn_type_name` STRING COMMENT '人员类别名称',
    `staff_nature_code` STRING COMMENT '员工性质代码',
    `staff_nature_name` STRING COMMENT '员工性质名称',
    `practice_type_code` STRING COMMENT '医师执业类别代码',
    `practice_type_name` STRING COMMENT '医师执业类别名称',
    `gp_flag` STRING COMMENT '全科医师标志',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `professional_code` STRING COMMENT '专业代码',
    `professional_name` STRING COMMENT '专业名称',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='医护人员字典表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_team` (
    `rid` STRING COMMENT '数据唯一记录号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `team_code` STRING COMMENT '团队代码',
    `team_name` STRING COMMENT '团队名称',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='医护团队字典表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_record_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `patientid` STRING COMMENT '个人档案标识符',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `appoint_id` STRING COMMENT '预约编号',
    `plan_code` STRING COMMENT '计划编号',
    `full_name` STRING COMMENT '姓名',
    `examination_date` DATE COMMENT '体检日期',
    `main_dor_no` STRING COMMENT '主检医生工号',
    `main_dor_name` STRING COMMENT '主检医生姓名',
    `sympt_code` STRING COMMENT '症状代码标准',
    `sympt_name` STRING COMMENT '症状名称',
    `tprt` DECIMAL(20,4) COMMENT '体温℃',
    `pule` INT(16) COMMENT '脉率次min',
    `vent_frqu` INT(16) COMMENT '呼吸频率次min',
    `left_dbp` INT(16) COMMENT '血压左舒张压mmHg',
    `left_sbp` INT(16) COMMENT '血压左收缩压mmHg',
    `right_dbp` INT(16) COMMENT '血压右舒张压mmHg',
    `right_sbp` INT(16) COMMENT '血压右收缩压mmHg',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `waist_cm` DECIMAL(20,4) COMMENT '腰围(cm)',
    `hipline` DECIMAL(20,4) COMMENT '臀围',
    `whr` DECIMAL(20,4) COMMENT '腰臀围比值',
    `elder_cognition_res_code` STRING COMMENT '老年人认识功能代码',
    `elder_cognition_res_name` STRING COMMENT '老年人认识功能名称',
    `elder_wit_score` INT(16) COMMENT '智力状态检查总分',
    `elder_health_status_code` STRING COMMENT '老年人健康状态自我评估代码',
    `elder_health_status_name` STRING COMMENT '老年人健康状态自我评估名称',
    `elder_self_eval_code` STRING COMMENT '老年人自理能力自我评估代码',
    `elder_self_eval_name` STRING COMMENT '老年人自理能力自我评估名称',
    `elder_emotional_status_code` STRING COMMENT '老年人情感状态代码',
    `elder_emotional_status_name` STRING COMMENT '老年人情感状态名称',
    `elder_depression_score` INT(16) COMMENT '老年人抑郁评分检查总分',
    `excs_frqu_code` STRING COMMENT '锻炼频率代码',
    `excs_frqu_name` STRING COMMENT '锻炼频率名称',
    `exercise_each` INT(16) COMMENT '每次锻炼时间',
    `insist_exercise_month` INT(16) COMMENT '坚持锻炼时间',
    `exercise_code` STRING COMMENT '锻炼方式说明',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `start_smoke_age` INT(16) COMMENT '开始吸烟年龄',
    `stop_smoke_age` INT(16) COMMENT '戒烟年龄',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `stop_drink_code` STRING COMMENT '饮酒情况是否戒酒代码',
    `stop_drink_name` STRING COMMENT '饮酒情况是否戒酒名称',
    `stop_drink_age` INT(16) COMMENT '戒酒年龄',
    `start_drink_age` INT(16) COMMENT '开始饮酒年龄',
    `last_year_drunkenness_mark` STRING COMMENT '近一年是否有醉酒',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `oral_exterior_code` STRING COMMENT '口腔口唇外观代码',
    `oral_exterior_name` STRING COMMENT '口腔口唇外观名称',
    `dentition_type_code` STRING COMMENT '口腔齿列类别代码',
    `dentition_type_name` STRING COMMENT '口腔齿列类别名称',
    `dentition_explain` STRING COMMENT '口腔齿列描述',
    `phary_exam_res_code` STRING COMMENT '口腔咽部检查结果代码',
    `phary_exam_res_name` STRING COMMENT '口腔咽部检查结果名称',
    `left_original_value` DECIMAL(20,4) COMMENT '裸眼视力左眼',
    `right_original_hyperopia_value` DECIMAL(20,4) COMMENT '裸眼视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `left_vision_code` STRING COMMENT '左眼视力代码',
    `left_vision_name` STRING COMMENT '左眼视力名称',
    `right_vision_code` STRING COMMENT '右眼视力代码',
    `right_vision_name` STRING COMMENT '右眼视力名称',
    `eyeground_abnorm_mark` STRING COMMENT '眼底异常标志',
    `fundoscopy_abnorm_descr` STRING COMMENT '眼底异常描述',
    `hear_check_res_code` STRING COMMENT '听力检测结果代码',
    `hear_check_res_name` STRING COMMENT '听力检测结果名称',
    `sport_function_status_code` STRING COMMENT '运动功能状态代码',
    `sport_function_status_name` STRING COMMENT '运动功能状态名称',
    `skin_check_abnorm_code` STRING COMMENT '皮肤检查结果代码',
    `skin_check_abnorm_name` STRING COMMENT '皮肤检查结果名称',
    `scleral_check_res_code` STRING COMMENT '巩膜检查结果代码',
    `scleral_check_res_name` STRING COMMENT '巩膜检查结果名称',
    `lymph_check_res_code` STRING COMMENT '淋巴结检查结果代码',
    `lymph_check_res_name` STRING COMMENT '淋巴结检查结果名称',
    `barrel_chest_mark` STRING COMMENT '肺桶状胸标志',
    `lung_abnorm_breath_mark` STRING COMMENT '肺部异常呼吸音标志',
    `lung_abnorm_breath_descr` STRING COMMENT '肺呼吸音异常描述',
    `lung_rale_code` STRING COMMENT '肺罗音代码',
    `lung_rale_name` STRING COMMENT '肺罗音名称',
    `lung_rale_describe` STRING COMMENT '肺罗音描述',
    `heart_rate` INT(16) COMMENT '心率次min',
    `heart_rate_type_code` STRING COMMENT '心脏心律类别代码',
    `heart_rate_type_name` STRING COMMENT '心脏心律类别名称',
    `heart_murmur_mark` STRING COMMENT '心脏杂音标志',
    `heart_murmur_describe` STRING COMMENT '心脏杂音描述',
    `abdominal_tend_mark` STRING COMMENT '腹部压痛标志',
    `abdominal_tend_descr` STRING COMMENT '腹部压痛描述',
    `abdominal_mass_mark` STRING COMMENT '腹部包块标志',
    `abdominal_mass_descr` STRING COMMENT '腹部包块描述',
    `abdominal_hepatauxe_mark` STRING COMMENT '腹部肝大标志',
    `abdominal_hepatauxe_descr` STRING COMMENT '腹部肝大描述',
    `splenauxe_mark` STRING COMMENT '腹部脾大标志',
    `splenauxe_descr` STRING COMMENT '腹部脾大描述',
    `abdominal_dullness_mark` STRING COMMENT '腹部移动性浊音标志',
    `abdominal_dullness_descr` STRING COMMENT '腹部移动性浊音描述',
    `legs_edema_check_res_code` STRING COMMENT '下肢水肿代码',
    `legs_edema_check_res_name` STRING COMMENT '下肢水肿名称',
    `foot_dorsal_artery_code` STRING COMMENT '足背动脉搏动代码',
    `foot_dorsal_artery_name` STRING COMMENT '足背动脉搏动名称',
    `anus_check_res_type_code` STRING COMMENT '肛门指诊代码',
    `anus_check_res_type_name` STRING COMMENT '肛门指诊名称',
    `breast_check_res_code` STRING COMMENT '乳腺检查结果代码',
    `breast_check_res_name` STRING COMMENT '乳腺检查结果名称',
    `vulva_abnorm_mark` STRING COMMENT '妇科外阴异常标志',
    `vulva_abnorm_descr` STRING COMMENT '妇科外阴描述',
    `vagina_abnorm_mark` STRING COMMENT '妇科阴道异常标志',
    `vagina_abnorm_descr` STRING COMMENT '妇科阴道描述',
    `cervix_abnorm_mark` STRING COMMENT '妇科宫颈异常标志',
    `cervix_abnorm_descr` STRING COMMENT '妇科宫颈描述',
    `corpusuteri_abnorm_mark` STRING COMMENT '妇科宫体异常标志',
    `corpusuteri_abnorm_descr` STRING COMMENT '妇科宫体描述',
    `adnex_abnorm_mark` STRING COMMENT '妇科附件异常标志',
    `gyn_adnex_abnorm_descr` STRING COMMENT '妇科附件异常描述',
    `other_health_check_res` STRING COMMENT '查体其他',
    `ecg_abnorm_mark` STRING COMMENT '心电图异常标志',
    `ecg_abnorm_descr` STRING COMMENT '心电图异常描述',
    `xray_abnorm_mark` STRING COMMENT '胸部X线片异常标志',
    `xray_abnorm_descr` STRING COMMENT '胸部X线片异常描述',
    `bscan_abnorm_mark` STRING COMMENT 'B超异常标志',
    `bscan_abnorm_descr` STRING COMMENT 'B超异常描述',
    `cps_abnorm_mark` STRING COMMENT '宫颈涂片异常标志',
    `cps_abnorm_descr` STRING COMMENT '宫颈涂片异常描述',
    `other_assist_check` STRING COMMENT '辅助检查其他',
    `cardiovascular_code` STRING COMMENT '脑血管疾病代码',
    `cardiovascular_name` STRING COMMENT '脑血管疾病名称',
    `chronic_kidney_code` STRING COMMENT '肾脏疾病代码',
    `chronic_kidney_name` STRING COMMENT '肾脏疾病名称',
    `cardiopathy_code` STRING COMMENT '心脏疾病代码',
    `cardiopathy_name` STRING COMMENT '心脏疾病名称',
    `vas_code` STRING COMMENT '血管疾病代码',
    `vas_name` STRING COMMENT '血管疾病名称',
    `oculopathy_code` STRING COMMENT '眼部疾病代码',
    `oculopathy_name` STRING COMMENT '眼部疾病名称',
    `neuro_exam_abnormal_mark` STRING COMMENT '神经系统疾病标志',
    `neuro_exam_abnormal_descr` STRING COMMENT '神经系统疾病描述',
    `systemic_disease_mark` STRING COMMENT '其他系统疾病标志',
    `systemic_disease_descr` STRING COMMENT '其他系统疾病描述',
    `hl_eval_abnorm_flag` STRING COMMENT '健康评价异常标志',
    `abnormal_descr` STRING COMMENT '健康评价异常描述',
    `health_guide_code` STRING COMMENT '健康指导代码',
    `health_guide_name` STRING COMMENT '健康指导名称',
    `risk_control_ad_code` STRING COMMENT '危险因素控制建议代码',
    `risk_control_ad_name` STRING COMMENT '危险因素控制建议名称',
    `aim_weight` DECIMAL(20,4) COMMENT '减体重目标',
    `offer_vacc_code` STRING COMMENT '建议接种疫苗代码',
    `offer_vacc_name` STRING COMMENT '建议接种疫苗名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `updt_emplo_no` STRING COMMENT '更新员工编号',
    `updt_emplo_name` STRING COMMENT '更新员工姓名',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='体检记录信息';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_med_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `drug_name` STRING COMMENT '药品名称',
    `drugusage` STRING COMMENT '使用频率',
    `drug_used_dosunt` STRING COMMENT '药物使用剂量单位',
    `drug_used_sdose` DECIMAL(20,4) COMMENT '药物使用次剂量',
    `drug_used_idose` DECIMAL(20,4) COMMENT '药物使用总剂量',
    `drug_used_way_code` STRING COMMENT '给药途径用法代码',
    `drug_used_way_name` STRING COMMENT '给药途径用法名称',
    `medication_time` STRING COMMENT '用药时间',
    `tcmdrug_type_code` STRING COMMENT '中药类别代码',
    `tcmdrug_type_name` STRING COMMENT '中药类别名称',
    `medication_compliance_code` STRING COMMENT '服药依从性代码',
    `medication_compliance_name` STRING COMMENT '服药依从性名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='主要用药信息';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_home_bed_history` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `build_bed_date` DATE COMMENT '建床日期',
    `remove_bed_date` DATE COMMENT '撤床日期',
    `bed_reason` STRING COMMENT '建床原因',
    `bed_org_name` STRING COMMENT '建床医疗机构名称',
    `medcasno` STRING COMMENT '病案号',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭病床史';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_nonimm_vacc_history` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `vacc_code` STRING COMMENT '疫苗代码',
    `vacc_name` STRING COMMENT '疫苗名称',
    `vacc_time` TIMESTAMP COMMENT '接种日期时间',
    `vaccinate_uscid` STRING COMMENT '接种机构统一社会信用代码',
    `vaccinate_org_name` STRING COMMENT '接种机构名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='非免疫规划预防接种史';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_elderly_care_assess` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `dinner_score` INT(16) COMMENT '进餐分数',
    `freshen_score` INT(16) COMMENT '梳洗分数',
    `dress_score` INT(16) COMMENT '穿衣分数',
    `toilet_score` INT(16) COMMENT '如厕分数',
    `acty_score` INT(16) COMMENT '活动分数',
    `total_score` STRING COMMENT '总分',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='老年人生活自理能力评估表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_lab_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `hl_phys_exam_asst_exam_id` STRING COMMENT '健康体检辅助检查ID',
    `ins_id` STRING COMMENT '机构ID',
    `hl_file_id` STRING COMMENT '健康档案ID',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `asst_exam_empt_stom_bloo_gluc` STRING COMMENT '辅助检查空腹血糖',
    `asst_exam_empt_stom_bloo_gluc_mg` STRING COMMENT '辅助检查空腹血糖mg',
    `asst_exam_bloo_rout_hemog` STRING COMMENT '辅助检查血常规血红蛋白',
    `asst_exam_bloo_rout_whit_bloo_cells` STRING COMMENT '辅助检查血常规白细胞',
    `asst_exam_bloo_rout_plate` STRING COMMENT '辅助检查血常规血小板',
    `asst_exam_bloo_rout_oth` STRING COMMENT '辅助检查血常规其他',
    `asst_exam_urin_rout_urin_prot` STRING COMMENT '辅助检查尿常规尿蛋白',
    `asst_exam_urin_rout_urin_gluc` STRING COMMENT '辅助检查尿常规尿糖',
    `asst_exam_urin_rout_urin_keto` STRING COMMENT '辅助检查尿常规尿酮体',
    `asst_exam_urin_rout_urin_occu_bloo` STRING COMMENT '辅助检查尿常规尿潜血',
    `asst_exam_urin_rout_oth` STRING COMMENT '辅助检查尿常规其他',
    `asst_exam_urin_micro_albu` STRING COMMENT '辅助检查尿微量白蛋白',
    `asst_exam_sto_occu_bloo` STRING COMMENT '辅助检查大便潜血',
    `asst_exam_live_fun_sero_gluta_transa` STRING COMMENT '辅助检查肝功能血清谷丙转氨酶',
    `asst_exam_live_fun_sero_grain_grass_transa` STRING COMMENT '辅助检查肝功能血清谷草转氨酶',
    `asst_exam_live_fun_albu` STRING COMMENT '辅助检查肝功能白蛋白',
    `asst_exam_live_fun_totl_bili` STRING COMMENT '辅助检查肝功能总胆红素',
    `asst_exam_live_fun_comb_bili` STRING COMMENT '辅助检查肝功能结合胆红素',
    `asst_exam_kdn_fun_sero_creat` STRING COMMENT '辅助检查肾功能血清肌酐',
    `asst_exam_kdn_fun_blo_urea_nitr` STRING COMMENT '辅助检查肾功能血尿素氮',
    `asst_exam_kdn_fun_k_conc` STRING COMMENT '辅助检查肾功能血钾浓度',
    `asst_exam_kdn_fun_na_conc` STRING COMMENT '辅助检查肾功能血钠浓度',
    `asst_exam_pl_totl_chol` STRING COMMENT '辅助检查血脂总胆固醇',
    `asst_exam_pl_trigl` STRING COMMENT '辅助检查血脂甘油三酯',
    `asst_exam_pl_sero_low_den_lipopr_chol` STRING COMMENT '辅助检查血脂血清低密度脂蛋白胆固醇',
    `asst_exam_pl_sero_high_den_lipopr_chol` STRING COMMENT '辅助检查血脂血清高密度脂蛋白胆固醇',
    `asst_exam_glyc_hemog` STRING COMMENT '辅助检查糖化血红蛋白',
    `asst_exam_hepa_b_surf_anti` STRING COMMENT '辅助检查乙型肝炎表面抗原',
    `asst_exam_fund` STRING COMMENT '辅助检查眼底',
    `asst_exam_fund_abn` STRING COMMENT '辅助检查眼底异常',
    `asst_exam_electro` STRING COMMENT '辅助检查心电图',
    `asst_exam_electro_abn` STRING COMMENT '辅助检查心电图异常',
    `asst_exam_chst_x_line_piec` STRING COMMENT '辅助检查胸部x线片',
    `asst_exam_chst_x_line_piec_abn` STRING COMMENT '辅助检查胸部x线片异常',
    `asst_exam_b_over` STRING COMMENT '辅助检查b超',
    `asst_exam_b_over_oth` STRING COMMENT '辅助检查b超其他',
    `asst_exam_cerv_smea` STRING COMMENT '辅助检查宫颈涂片',
    `asst_exam_cerv_smea_abn` STRING COMMENT '辅助检查宫颈涂片异常',
    `asst_exam_oth` STRING COMMENT '辅助检查其他',
    `urin_rout_urin_whit_bloo_cells` STRING COMMENT '尿常规尿白细胞',
    `urin_rout_urin_bili` STRING COMMENT '尿常规尿胆红素',
    `urin_rout_urin_nit` STRING COMMENT '尿常规尿亚硝酸盐',
    `live_fun_albu_glon_rat` STRING COMMENT '肝功能白蛋白球蛋白比值(*)',
    `live_fun_indi_bili` STRING COMMENT '肝功能间接胆红素(*)',
    `live_fun_alp` STRING COMMENT '肝功能碱性磷酸酶(alp)',
    `live_fun_r_ggt` STRING COMMENT '肝功能r谷氨酰转移酶',
    `kdn_fun_uric_acid` STRING COMMENT '肾功能尿酸',
    `asst_exam_electro1` STRING COMMENT '辅助检查心电图1',
    `asst_exam_electro2` STRING COMMENT '辅助检查心电图2',
    `asst_exam_electro3` STRING COMMENT '辅助检查心电图3',
    `abd_b_over_lgp_sple` INT(6) COMMENT '腹部b超肝胆胰脾',
    `abd_b_over_lgp_sple_oth` STRING COMMENT '腹部b超肝胆胰脾其他',
    `asst_exam_rcd_sav_time` TIMESTAMP COMMENT '辅助检查记录保存时间',
    `asst_exam_abd_b_over1` STRING COMMENT '辅助检查腹部b超1',
    `asst_exam_abd_b_over2` STRING COMMENT '辅助检查腹部b超2',
    `asst_exam_abd_b_over3` STRING COMMENT '辅助检查腹部b超3',
    `asst_exam_abd_b_over4` STRING COMMENT '辅助检查腹部b超4',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='体检检验记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_physical_exam` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `check_id` STRING COMMENT '查体ID',
    `skin_codition_code` STRING COMMENT '皮肤状况代码',
    `skin_codition_name` STRING COMMENT '皮肤状况名称',
    `skin_condition_remark` STRING COMMENT '皮肤状况为其他时描述',
    `sclera_code` STRING COMMENT '巩膜代码',
    `sclera_name` STRING COMMENT '巩膜名称',
    `sclera_remark` STRING COMMENT '巩膜为其他时描述',
    `lymph_node_code` STRING COMMENT '淋巴结代码',
    `lymph_node_name` STRING COMMENT '淋巴结名称',
    `lymph_node_remark` STRING COMMENT '淋巴结为其他时描述',
    `barrel_chest_flag` STRING COMMENT '肺桶状胸标志',
    `breath_sound_code` STRING COMMENT '肺呼吸音代码',
    `breath_sound_name` STRING COMMENT '肺呼吸音名称',
    `breath_sound_abnormal_remark` STRING COMMENT '肺呼吸音异常描述',
    `rale_code` STRING COMMENT '肺罗音代码',
    `rale_name` STRING COMMENT '肺罗音名称',
    `rale_remark` STRING COMMENT '肺罗音为其他时描述',
    `heart_rate` INT(16) COMMENT '心率次min',
    `heart_rhythm_code` STRING COMMENT '心脏心律代码',
    `heart_rhythm_name` STRING COMMENT '心脏心律名称',
    `heart_noise_flag` STRING COMMENT '心脏杂音标志标志',
    `heart_noise_remark` STRING COMMENT '心脏杂音异常时描述',
    `tenderness_flag` STRING COMMENT '腹部压痛标志',
    `tenderness_remark` STRING COMMENT '腹部压痛异常时描述',
    `bag_piece_flag` STRING COMMENT '腹部包块标志',
    `bag_piece_remark` STRING COMMENT '腹部包块异常时描述',
    `hepatomegaly_flag` STRING COMMENT '腹部肝大标志',
    `hepatomegaly_remark` STRING COMMENT '腹部肝大异常时描述',
    `splenomegaly_flag` STRING COMMENT '腹部脾大标志',
    `splenomegaly_remark` STRING COMMENT '腹部脾大异常时描述',
    `move_dullness_flag` STRING COMMENT '腹部移动性浊音标志',
    `move_dullness_remark` STRING COMMENT '腹部移动性浊音异常时描述',
    `legs_edema_check_res_code` STRING COMMENT '下肢水肿代码',
    `legs_edema_check_res_name` STRING COMMENT '下肢水肿名称',
    `foot_dorsal_artery_code` STRING COMMENT '足背动脉搏动代码',
    `foot_dorsal_artery_name` STRING COMMENT '足背动脉搏动名称',
    `anus_check_res_type_code` STRING COMMENT '肛门指诊代码',
    `anus_check_res_type_name` STRING COMMENT '肛门指诊名称',
    `anus_dre_remark` STRING COMMENT '肛门指诊为其他时描述',
    `mammary_gland_code` STRING COMMENT '乳腺情况代码',
    `mammary_gland_name` STRING COMMENT '乳腺情况名称',
    `mammary_gland_remark` STRING COMMENT '乳腺为其他时描述',
    `vulva_code` STRING COMMENT '妇科外阴代码',
    `vulva_name` STRING COMMENT '妇科外阴名称',
    `vulva_remark` STRING COMMENT '妇科外阴为其他时描述',
    `vagina_code` STRING COMMENT '妇科阴道代码',
    `vagina_name` STRING COMMENT '妇科阴道名称',
    `vagina_remark` STRING COMMENT '妇科阴道为其他时描述',
    `cervical_code` STRING COMMENT '妇科宫颈代码',
    `cervical_name` STRING COMMENT '妇科宫颈名称',
    `cervical_remark` STRING COMMENT '妇科宫颈为其他时描述',
    `uterine_body_code` STRING COMMENT '妇科宫体代码',
    `uterine_body_name` STRING COMMENT '妇科宫体名称',
    `uterine_body_remark` STRING COMMENT '妇科宫体为其他时描述',
    `attachment_code` STRING COMMENT '妇科附件代码',
    `attachment_name` STRING COMMENT '妇科附件名称',
    `attachment_remark` STRING COMMENT '妇科附件为其他时描述',
    `fundus_code` STRING COMMENT '眼底代码',
    `fundus_name` STRING COMMENT '眼底名称',
    `fundus_remark` STRING COMMENT '眼底为其他时描述',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--查体';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_physical_exam_extend` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `check_id` STRING COMMENT '查体ID',
    `oral_lips_code` STRING COMMENT '口腔口唇代码',
    `oral_lips_name` STRING COMMENT '口腔口唇名称',
    `oral_dentition_code` STRING COMMENT '口腔齿列代码',
    `oral_dentition_name` STRING COMMENT '口腔齿列名称',
    `oral_pharyngeal_code` STRING COMMENT '口腔咽部代码',
    `oral_pharyngeal_name` STRING COMMENT '口腔咽部名称',
    `oral_dentition_missing_tooth` STRING COMMENT '口腔齿列缺齿',
    `oral_dentition_decayed_tooth` STRING COMMENT '口腔齿列龋齿',
    `oral_dentition_false_tooth` STRING COMMENT '口腔齿列义齿',
    `eyesight_left` STRING COMMENT '视力左眼',
    `eyesight_right` STRING COMMENT '视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `hearing_code` STRING COMMENT '听力代码',
    `hearing_name` STRING COMMENT '听力名称',
    `motor_function_code` STRING COMMENT '运动功能代码',
    `motor_function_name` STRING COMMENT '运动功能名称',
    `other_remark` STRING COMMENT '其他查体结果描述',
    `modify_datetime` TIMESTAMP COMMENT '最后修改时间',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--查体扩展表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_main_issues` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `cardiovascular_code` STRING COMMENT '脑血管疾病代码',
    `cardiovascular_name` STRING COMMENT '脑血管疾病名称',
    `cerebrovascular_disease_remark` STRING COMMENT '脑血管疾病为其他时描述',
    `chronic_kidney_code` STRING COMMENT '肾脏疾病代码',
    `chronic_kidney_name` STRING COMMENT '肾脏疾病名称',
    `kidney_disease_remark` STRING COMMENT '肾脏疾病为其他时描述',
    `cardiopathy_code` STRING COMMENT '心脏疾病代码',
    `cardiopathy_name` STRING COMMENT '心脏疾病名称',
    `heart_disease_remark` STRING COMMENT '心脏疾病为其他时描述',
    `vas_code` STRING COMMENT '血管疾病代码',
    `vas_name` STRING COMMENT '血管疾病名称',
    `vascular_disease_remark` STRING COMMENT '血管疾病为其他时描述',
    `oculopathy_code` STRING COMMENT '眼部疾病代码',
    `oculopathy_name` STRING COMMENT '眼部疾病名称',
    `eyes_disease_remark` STRING COMMENT '眼部疾病为其他时描述',
    `neuro_exam_abnormal_mark` STRING COMMENT '神经系统疾病标志',
    `neuro_exam_abnormal_descr` STRING COMMENT '神经系统疾病描述',
    `systemic_disease_mark` STRING COMMENT '其他系统疾病标志',
    `systemic_disease_descr` STRING COMMENT '其他系统疾病描述',
    `gentle_constitution` STRING COMMENT '中医体质辨识平和质',
    `tcm_vdc` STRING COMMENT '中医体质辨识气虚质',
    `yang_deficiency_constitution` STRING COMMENT '中医体质辨识阳虚质',
    `dryness_constitution` STRING COMMENT '中医体质辨识阴虚质',
    `phlegm_damp_constitution` STRING COMMENT '中医体质辨识痰湿质',
    `damp_heat_constitution` STRING COMMENT '中医体质辨识湿热质',
    `blood_stasis_constitution` STRING COMMENT '中医体质辨识血瘀质',
    `look_depressed_constitution` STRING COMMENT '中医体质辨识气郁质',
    `sensitive_constitution` STRING COMMENT '中医体质辨识特秉质',
    `medical_abnormal_flag` STRING COMMENT '体检异常标志',
    `medical_abnormal_remark1` STRING COMMENT '体检异常1',
    `medical_abnormal_remark2` STRING COMMENT '体检异常2',
    `medical_abnormal_remark3` STRING COMMENT '体检异常3',
    `medical_abnormal_remark4` STRING COMMENT '体检异常4',
    `health_guide_code` STRING COMMENT '健康指导代码',
    `health_guide_name` STRING COMMENT '健康指导名称',
    `risk_factor_control_code` STRING COMMENT '危险因素控制代码',
    `risk_factor_control_name` STRING COMMENT '危险因素控制名称',
    `weight_target` STRING COMMENT '危险因素控制减体重目标',
    `vaccination` STRING COMMENT '危险因素控制疫苗接种',
    `risk_factor_control_remark` STRING COMMENT '危险因素控制为其他时描述',
    `health_guide_content` STRING COMMENT '健康指导内容',
    `healthy_teaching` STRING COMMENT '健教处方',
    `register_datetime` TIMESTAMP COMMENT '填表日期',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--主要健康问题';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_organ_function` (
    `rid` STRING COMMENT '数据唯一记录号',
    `hl_phys_exam_orga_efcc_id` STRING COMMENT '健康体检脏器功能ID',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `oral_lips_code` STRING COMMENT '口腔口唇代码',
    `oral_lips_name` STRING COMMENT '口腔口唇名称',
    `oral_dentition_code` STRING COMMENT '口腔齿列代码',
    `oral_dentition_name` STRING COMMENT '口腔齿列名称',
    `oral_pharyngeal_code` STRING COMMENT '口腔咽部代码',
    `oral_pharyngeal_name` STRING COMMENT '口腔咽部名称',
    `eyesight_left` STRING COMMENT '视力左眼',
    `eyesight_right` STRING COMMENT '视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `hearing_code` STRING COMMENT '听力代码',
    `hearing_name` STRING COMMENT '听力名称',
    `motor_function_code` STRING COMMENT '运动功能代码',
    `motor_function_name` STRING COMMENT '运动功能名称',
    `ora_tooth_row_mis` STRING COMMENT '口腔齿列缺齿',
    `ora_tooth_row_cari` STRING COMMENT '口腔齿列龋齿',
    `ora_tooth_row_denti` STRING COMMENT '口腔齿列义齿',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='脏器功能';

