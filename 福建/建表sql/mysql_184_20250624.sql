-- 生成时间：2025-06-24 14:13:59
-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建


-- 创建日志表的存储过程
DROP PROCEDURE IF EXISTS create_log_table;
DELIMITER //
CREATE PROCEDURE create_log_table()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果日志表创建失败，输出错误信息
        GET DIAGNOSTICS CONDITION 1 @err_msg = MESSAGE_TEXT;
        SELECT CONCAT('错误：创建日志表失败 - ', @err_msg) AS message;
    END;

    CREATE TABLE IF NOT EXISTS `table_creation_log` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `table_name` VARCHAR(255) NOT NULL COMMENT '表名',
        `status` VARCHAR(10) NOT NULL COMMENT '状态：成功/失败',
        `message` TEXT COMMENT '详细信息',
        `error_code` VARCHAR(10) COMMENT '错误代码',
        `create_time` DATETIME NOT NULL COMMENT '创建时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表创建结果记录';
    
    -- 清空记录表
    TRUNCATE TABLE `table_creation_log`;
END //
DELIMITER ;

-- 创建日志表
CALL create_log_table();
DROP PROCEDURE IF EXISTS create_log_table;

DROP PROCEDURE IF EXISTS create_tj_charge_table;
DELIMITER //
CREATE PROCEDURE create_tj_charge_table()
BEGIN
    DECLARE success BOOLEAN DEFAULT TRUE;
    DECLARE error_msg TEXT;
    DECLARE error_code VARCHAR(10);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            error_msg = MESSAGE_TEXT,
            error_code = MYSQL_ERRNO;

        INSERT INTO `table_creation_log` 
            (`table_name`, `status`, `message`, `error_code`, `create_time`)
        VALUES (
            'tj_charge',
            '失败',
            error_msg,
            error_code,
            NOW()
        );
    END;

    CREATE TABLE IF NOT EXISTS `tj_charge` (
        `rid` VARCHAR(255) COMMENT '数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码uscid+数据上传时间upload_time+系统建设厂商代码sys_prdr_code+收/退费日期fee_date+收/退费编号fee_no+退费标志refd_mark',
        `org_name` VARCHAR(100) COMMENT '医疗机构名称',
        `uscid` VARCHAR(100) COMMENT '医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明',
        `upload_time` DATETIME COMMENT '数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明',
        `sys_prdr_code` VARCHAR(50) COMMENT '系统建设厂商代码 复合主键，系统建设厂商名称首字母大写',
        `sys_prdr_name` VARCHAR(255) COMMENT '系统建设厂商名称 见公共字段【系统建设厂商名称】说明',
        `fee_date` DATETIME COMMENT '收/退费日期 复合主键；格式为YYYY-MM-DD hh:mm:ss',
        `fee_no` VARCHAR(50) COMMENT '收/退费编号 复合主键；见门诊收费表说明（1）',
        `refd_mark` VARCHAR(1) COMMENT '退费标志 复合主键；1：收费；2：退费',
        `comb_type` VARCHAR(1) COMMENT '体检类别代码 1个人体检2单位体检',
        `tj_comb_sno` VARCHAR(50) COMMENT '体检流水号 体检类别为个人体检时必填',
        `emp_code` VARCHAR(50) COMMENT '体检单位编码 院内表示一个体检单位的唯一编码，体检类别为单位体检时必填',
        `mp_name` VARCHAR(100) COMMENT '体检单位名称 体检类别为单位体检时必填',
        `medfee_sumamt` VARCHAR(255) COMMENT '收/退费总额 收退费均以正数表达。',
        `payment_amt` VARCHAR(255) COMMENT '实收金额',
        `offer_amt` VARCHAR(255) COMMENT '优惠金额',
        `invo_no` VARCHAR(64) COMMENT '发票号 超过一张发票时，以“；”间隔',
        `invono_print_time` DATETIME COMMENT '发票打印日期时间',
        `state` VARCHAR(1) COMMENT '修改标志 1 是 0 否',
        `reserve1` VARCHAR(255) COMMENT '预留一 为系统处理该数据而预留',
        `reserve2` VARCHAR(255) COMMENT '预留二 为系统处理该数据而预留',
        `data_clct_prdr_name` VARCHAR(255) COMMENT '数据改造厂商名称 见公共字段【数据改造厂商名称】说明',
        `crte_time` DATETIME COMMENT '数据创建时间 见公共字段【数据创建时间】说明',
        `updt_time` DATETIME COMMENT '数据更新时间 见公共字段【数据更新时间】说明',
        `deleted` VARCHAR(1) COMMENT '数据删除状态 见公共字段【数据删除状态】说明',
        `deleted_time` DATETIME COMMENT '数据删除时间 见公共字段【数据删除时间】说明',
        CONSTRAINT `pk_tc` PRIMARY KEY (`uscid`, `upload_time`, `sys_prdr_code`, `fee_date`, `fee_no`, `refd_mark`),
        UNIQUE INDEX `idx_rid_upload_time` (`rid`, `upload_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='体检收费表';
    
    
    INSERT INTO `table_creation_log` 
        (`table_name`, `status`, `message`, `error_code`, `create_time`)
    VALUES (
        'tj_charge',
        '成功',
        '表创建成功',
        NULL,
        NOW()
    );
    
END //
DELIMITER ;

CALL create_tj_charge_table();
DROP PROCEDURE IF EXISTS create_tj_charge_table;


-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = '成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = '失败' THEN 1 ELSE 0 END) as failed_count
FROM `table_creation_log`;


-- 显示失败的表详情
SELECT 
    table_name, 
    message, 
    error_code,
    create_time 
FROM `table_creation_log` 
WHERE status = '失败' 
ORDER BY create_time;
