-- 创建表 drug_trac_code
CREATE TABLE drug_trac_code (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VA<PERSON>HAR2(255),
    trac_code VA<PERSON>HAR2(50),
    apply_no VARCHAR2(100),
    apply_serial_no VARCHAR2(100),
    oper_type VARCHAR2(30),
    oper_tname VARCHAR2(100),
    oper_time DATE,
    drug_storage_code VARCHAR2(100),
    drug_storage_name VARCHAR2(255),
    drug_code VARCHAR2(100),
    drug_name VARCHAR2(4000),
    batch_no VARCHAR2(255),
    drug_approve_no VARCHAR2(255),
    manufacturer_code VARCHAR2(255),
    manufacturer_name VARCHAR2(255),
    spler_code VARCHAR2(255),
    spler_name VARCHAR2(255),
    patient_no VARCHAR2(100),
    otp_ipt_flag VARCHAR2(1),
    otp_ipt_name VA<PERSON>HAR2(255),
    mdtrt_sn VARCHAR2(100),
    dept_code VA<PERSON>HAR2(100),
    dept_name VA<PERSON>HAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_127_dru PRIMARY KEY (uscid, sys_prdr_code, trac_code, apply_no, apply_serial_no, oper_type)
);

-- 添加注释
COMMENT ON TABLE drug_trac_code IS '药品追溯码表';
COMMENT ON COLUMN drug_trac_code.rid IS '数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+追溯码trac_code+单据号apply_no+单据子序号apply_serial_no+操作类型代码oper_type';
COMMENT ON COLUMN drug_trac_code.org_name IS '医疗机构名称';
COMMENT ON COLUMN drug_trac_code.uscid IS '医疗机构统一社会信用代码 复合主键，见公共字段【医疗机构统一社会信用代码】说明';
COMMENT ON COLUMN drug_trac_code.upload_time IS '数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明';
COMMENT ON COLUMN drug_trac_code.sys_prdr_code IS '系统建设厂商代码 复合主键，系统建设厂商名称首字母大写';
COMMENT ON COLUMN drug_trac_code.sys_prdr_name IS '系统建设厂商名称 见公共字段【系统建设厂商名称】说明';
COMMENT ON COLUMN drug_trac_code.trac_code IS '追溯码 复合主键';
COMMENT ON COLUMN drug_trac_code.apply_no IS '单据号 复合主键；采购入库时为采购单据号，销售给患者时为销售单据号';
COMMENT ON COLUMN drug_trac_code.apply_serial_no IS '单据子序号 复合主键';
COMMENT ON COLUMN drug_trac_code.oper_type IS '操作类型代码 复合主键；例如：药品采购入库、药品采购退货出库、患者领药、患者退药';
COMMENT ON COLUMN drug_trac_code.oper_tname IS '操作类型名称';
COMMENT ON COLUMN drug_trac_code.oper_time IS '业务操作时间 入库时为采购时间，销售给患者为销售时间';
COMMENT ON COLUMN drug_trac_code.drug_storage_code IS '药品药库单元代码 药库和药房两个类别的收货方。包括药库的库存单元(科室代码)、药房的库存单元(科室代码)';
COMMENT ON COLUMN drug_trac_code.drug_storage_name IS '药品药库单元名称';
COMMENT ON COLUMN drug_trac_code.drug_code IS '药品代码';
COMMENT ON COLUMN drug_trac_code.drug_name IS '药品名称 举例药品：阿莫西林胶囊 0.25g/6粒/1盒';
COMMENT ON COLUMN drug_trac_code.batch_no IS '批次';
COMMENT ON COLUMN drug_trac_code.drug_approve_no IS '药品批号 药品生产批号';
COMMENT ON COLUMN drug_trac_code.manufacturer_code IS '生产厂商代码';
COMMENT ON COLUMN drug_trac_code.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN drug_trac_code.spler_code IS '供应厂商代码';
COMMENT ON COLUMN drug_trac_code.spler_name IS '供应厂商名称';
COMMENT ON COLUMN drug_trac_code.patient_no IS '患者编号 入库时为空，销售给患者为必填。';
COMMENT ON COLUMN drug_trac_code.otp_ipt_flag IS '门诊/住院标志 入库时为空，销售给患者为必填；例如：0门诊，1住院，2体检，3家床，9其他';
COMMENT ON COLUMN drug_trac_code.otp_ipt_name IS '门诊/住院标志名称 入库时为空，销售给患者为必填；例如：0门诊，1住院，2体检，3家床，9其他';
COMMENT ON COLUMN drug_trac_code.mdtrt_sn IS '就诊流水号 入库时为空，销售给患者为必填；就诊时填写就诊流水号，住院时可填写住院流水号';
COMMENT ON COLUMN drug_trac_code.dept_code IS '科室代码 采购入库时可为空，销售出库时为开立科室';
COMMENT ON COLUMN drug_trac_code.dept_name IS '科室名称';
COMMENT ON COLUMN drug_trac_code.data_clct_prdr_name IS '数据改造厂商名称 见公共字段【数据改造厂商名称】说明';
COMMENT ON COLUMN drug_trac_code.crte_time IS '数据创建时间 见公共字段【数据创建时间】说明';
COMMENT ON COLUMN drug_trac_code.updt_time IS '数据更新时间 见公共字段【数据更新时间】说明';
COMMENT ON COLUMN drug_trac_code.deleted IS '数据删除状态 见公共字段【数据删除状态】说明';
COMMENT ON COLUMN drug_trac_code.deleted_time IS '数据删除时间 见公共字段【数据删除时间】说明';

-- 创建索引
CREATE UNIQUE INDEX IDX_U_127_dru ON drug_trac_code (rid, upload_time);

----------------------------------------------20250413-----------------------------------------------------------------
-- 第7分册，2.1.1用血申请单（blood_apply）  blood_apply_id的注释  修改为 用血申请单流水号
-- 修改列注释
COMMENT ON COLUMN blood_apply.blood_apply_id IS '用血申请单流水号';

-- 第7分册，2.1.2用血申请单明细（blood_apply_dtl）    blood_apply_id的注释  修改为 用血申请单流水号
-- 修改列注释
COMMENT ON COLUMN blood_apply_dtl.blood_apply_id IS '用血申请单流水号';

-- 第7分册，2.1.3用血申请单指征项目表（blood_applyiteminfo）    blood_apply_id的注释  修改为 用血申请单流水号
-- 修改列注释
COMMENT ON COLUMN blood_applyiteminfo.blood_apply_id IS '用血申请单流水号';

----------------------------------------------20250416-----------------------------------------------------------------
-- 第17分册，医院字典表（dic_hospital）
-- 隶属关系代码  organizer_class_name  调整为  ownership_code
-- 隶属关系名称  ownership_code 调整为  ownership_name
-- 修改列名
ALTER TABLE dic_hospital RENAME COLUMN "ORGANIZER_CLASS_NAME" TO "ownership_code";
ALTER TABLE dic_hospital RENAME COLUMN "OWNERSHIP_CODE" TO "ownership_name";

----------------------------------------------20250422-----------------------------------------------------------------
-- 第14分册，物资库存信息（material_inv）,增加字段 数据创建时间  crte_time  时间  YYYY-MM-DD hh:mm:ss  必填   见公共字段【数据创建时间】说明
-- 增加列
ALTER TABLE material_inv ADD crte_time DATE;
-- 添加列注释
COMMENT ON COLUMN material_inv.crte_time IS '数据创建时间，格式为 YYYY-MM-DD hh:mm:ss，必填，见公共字段【数据创建时间】说明';

-- 第16分册，设备登记信息（equip_reg_info）,用途 use 字段名由use变更为use_code
-- 修改列名
ALTER TABLE equip_reg_info RENAME COLUMN "USE" TO "use_code";