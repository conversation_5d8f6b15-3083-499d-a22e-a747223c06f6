-- 生成时间：2025-05-28 14:45:16
CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_basic` (
    `rid` STRING COMMENT '数据唯一记录号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `addr_city` STRING COMMENT '属地地区',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `full_name` STRING COMMENT '姓名',
    `patient_souc_code` STRING COMMENT '患者来源代码',
    `patient_souc_name` STRING COMMENT '患者来源名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `brdy` DATE COMMENT '出生日期',
    `birth_addr` STRING COMMENT '出生详细地址',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `ntly_code` STRING COMMENT '国籍代码',
    `ntly_name` STRING COMMENT '国籍名称',
    `mobile` STRING COMMENT '手机号码',
    `tel` STRING COMMENT '联系电话',
    `empr_poscode` STRING COMMENT '工作单位邮编',
    `empr_name` STRING COMMENT '工作单位名称',
    `empr_addr` STRING COMMENT '工作单位地址',
    `residential_code` STRING COMMENT '居住地行政区划代码',
    `residential_name` STRING COMMENT '居住地行政区划名称',
    `curr_addr_prov_code` STRING COMMENT '居住地省自治区直辖市代码',
    `curr_addr_prov_name` STRING COMMENT '居住地省自治区直辖市名称',
    `curr_addr_city_code` STRING COMMENT '居住地市地区代码',
    `curr_addr_city_name` STRING COMMENT '居住地市地区名称',
    `curr_addr_coty_code` STRING COMMENT '居住地县区代码',
    `curr_addr_coty_name` STRING COMMENT '居住地县区名称',
    `curr_addr_town_code` STRING COMMENT '居住地乡镇街道代码',
    `curr_addr_town_name` STRING COMMENT '居住地乡镇街道名称',
    `curr_addr_comm_code` STRING COMMENT '居住地居委会村代码',
    `curr_addr_comm_name` STRING COMMENT '居住地居委会村名称',
    `curr_addr_cotry_name` STRING COMMENT '居住地村街路弄等',
    `curr_addr_housnum` STRING COMMENT '居住地门牌号包括“室”',
    `residential_addr` STRING COMMENT '居住地址详细',
    `resd_addr_code` STRING COMMENT '户籍地行政区划代码',
    `resd_addr_name` STRING COMMENT '户籍地行政区划名称',
    `resd_addr_prov_code` STRING COMMENT '户籍地省自治区直辖市代码',
    `resd_addr_prov_name` STRING COMMENT '户籍地省自治区直辖市名称',
    `resd_addr_coty_code` STRING COMMENT '户籍地县区代码',
    `resd_addr_coty_name` STRING COMMENT '户籍地县区名称',
    `resd_addr_subd_code` STRING COMMENT '户籍地乡镇街道代码',
    `resd_addr_subd_name` STRING COMMENT '户籍地乡镇街道名称',
    `resd_addr_comm_code` STRING COMMENT '户籍地居委会村代码',
    `resd_addr_comm_name` STRING COMMENT '户籍地居委会村名称',
    `resd_addr_cotry_name` STRING COMMENT '户籍地村街路弄等',
    `resd_addr_housnum` STRING COMMENT '户籍地门牌号包括“室”',
    `resd_addr` STRING COMMENT '户籍地址详细地址',
    `resd_addr_poscode` STRING COMMENT '户籍地址邮编',
    `coner_name` STRING COMMENT '联系人监护人姓名',
    `relation_code` STRING COMMENT '联系人关系代码',
    `relation_name` STRING COMMENT '联系人关系名称',
    `coner_addr` STRING COMMENT '联系人监护人地址',
    `coner_org_name` STRING COMMENT '联系人单位名称',
    `coner_poscode` STRING COMMENT '联系人监护人邮编',
    `coner_tel` STRING COMMENT '联系人监护人电话号码',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='个人健康档案基本信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_home` (
    `rid` STRING COMMENT '数据唯一记录号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `emp_status_code` STRING COMMENT '从业状况代码',
    `emp_status_name` STRING COMMENT '从业状况名称',
    `per_addr_code` STRING COMMENT '常住类型代码',
    `per_addr_name` STRING COMMENT '常住类型名称',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `blotype_abo_code` STRING COMMENT 'ABO血型代码',
    `blotype_abo_name` STRING COMMENT 'ABO血型名称',
    `blotype_rh_code` STRING COMMENT 'Rh血型代码',
    `blotype_rh_name` STRING COMMENT 'Rh血型名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `medfee_paymtd_code` STRING COMMENT '医疗付款方式代码',
    `medfee_paymtd_name` STRING COMMENT '医疗付款方式名称',
    `expose_his_code` STRING COMMENT '暴露史代码',
    `expose_his_name` STRING COMMENT '暴露史名称',
    `hereditary_mark` STRING COMMENT '遗传病史标志',
    `hereditary_code` STRING COMMENT '遗传病史代码',
    `hereditary_name` STRING COMMENT '遗传病史名称',
    `chronic_code` STRING COMMENT '慢性病患病情况代码',
    `chronic_name` STRING COMMENT '慢性病患病情况名称',
    `disa_info_code` STRING COMMENT '残疾情况代码',
    `disa_info_name` STRING COMMENT '残疾情况名称',
    `disable_certificate_no` STRING COMMENT '残疾证号',
    `brf_code` STRING COMMENT '行为危险因素情况代码',
    `brf_name` STRING COMMENT '行为危险因素情况名称',
    `kitchen_exhaust_mark` STRING COMMENT '厨房排风设施标识',
    `kitchen_exhaust_code` STRING COMMENT '厨房排风设施代码',
    `kitchen_exhaust_name` STRING COMMENT '厨房排风设施名称',
    `fuel_type_code` STRING COMMENT '燃料类型代码',
    `fuel_type_name` STRING COMMENT '燃料类型名称',
    `drink_water_type_code` STRING COMMENT '饮水类别代码',
    `drink_water_type_name` STRING COMMENT '饮水类别名称',
    `wc_type_code` STRING COMMENT '厕所类别代码',
    `wc_type_name` STRING COMMENT '厕所类别名称',
    `avian_corral_type_code` STRING COMMENT '禽畜栏类别代码',
    `avian_corral_type_name` STRING COMMENT '禽畜栏类别名称',
    `build_date` DATE COMMENT '建档日期',
    `build_org_code` STRING COMMENT '建档机构统一社会信用代码',
    `build_org_name` STRING COMMENT '建档机构名称',
    `build_org_tel` STRING COMMENT '建档机构联系电话',
    `mang_org_code` STRING COMMENT '健康档案管理机构名称',
    `duty_dor_no` STRING COMMENT '责任医生工号',
    `duty_dor_name` STRING COMMENT '责任医生姓名',
    `dscr` STRING COMMENT '其他说明',
    `duty_dor_tel` STRING COMMENT '责任医生电话',
    `registerhiscode` STRING COMMENT '登记人工号',
    `registerhisname` STRING COMMENT '登记人姓名',
    `reg_dor_no` STRING COMMENT '录入医生工号',
    `enter_dor_name` STRING COMMENT '录入医生姓名',
    `reg_date` TIMESTAMP COMMENT '登记日期',
    `inquirer_name` STRING COMMENT '调查者姓名',
    `inquirer_no` STRING COMMENT '调查者工号',
    `inquirer_date` DATE COMMENT '调查日期',
    `health_rec_status_code` STRING COMMENT '档案状态代码',
    `health_rec_status_name` STRING COMMENT '档案状态名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `workplace` STRING COMMENT '工作单位',
    `feepay_type_code` STRING COMMENT '费用支付方式代码',
    `feepay_type_name` STRING COMMENT '费用支付方式名称',
    `pobservation_type_code` STRING COMMENT '既往观察项目分类代码',
    `pobservation_type_name` STRING COMMENT '既往观察项目分类名称',
    `pobservation_code` STRING COMMENT '既往观察项目代码',
    `pobservation_name` STRING COMMENT '既往观察项目名称',
    `pobservationmethods_code` STRING COMMENT '既往观察方法代码',
    `pobservationmethods_name` STRING COMMENT '既往观察方法名称',
    `pobservationresult_code` STRING COMMENT '既往观察结果代码',
    `pobservationresult_name` STRING COMMENT '既往观察结果名称',
    `observations_date` DATE COMMENT '观察结果开始(发现)日期',
    `observatione_date` DATE COMMENT '观察结果停止(治愈)日期',
    `medpay_uebmi` STRING COMMENT '医疗费用支付方式城镇职工基本医疗保险',
    `medpay_trpbmi` STRING COMMENT '医疗费用支付方式城镇居民基本医疗保险',
    `medpay_nrcmc` STRING COMMENT '医疗费用支付方式新型农村合作医疗',
    `med_fee_pay_way_poor_assi` STRING COMMENT '医疗费用支付方式贫困救助',
    `med_fee_pay_way_busi_hi` STRING COMMENT '医疗费用支付方式商业医疗保险',
    `medpay_fape` STRING COMMENT '医疗费用支付方式全公费',
    `med_fee_pay_way_full_ownpay` STRING COMMENT '医疗费用支付方式全自费',
    `med_fee_pay_way_oth_way` STRING COMMENT '医疗费用支付方式其他方式',
    `fmhis_fthr` STRING COMMENT '家族史父亲',
    `fmhis_mthr` STRING COMMENT '家族史母亲',
    `fmhis_brot_sist` STRING COMMENT '家族史兄妹',
    `fmhis_child` STRING COMMENT '家族史子女',
    `phys_exer_adhe_excs_time` STRING COMMENT '体育锻炼坚持锻炼时间',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `start_smoke_way_age` STRING COMMENT '吸烟情况吸烟年龄',
    `stop_smoke_way_age` STRING COMMENT '吸烟情况戒烟年龄',
    `stop_drink_way_age` STRING COMMENT '饮酒情况戒酒年龄',
    `start_drink_way_age` STRING COMMENT '饮酒情况开始饮酒年龄',
    `last_year_drunkenness_mark` STRING COMMENT '近一年是否有醉酒',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `drink_type_others` STRING COMMENT '饮酒情况饮酒种类其他',
    `prfs_expo_info` STRING COMMENT '职业暴露情况是否',
    `ins_id` STRING COMMENT '机构ID',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `prfs_expo_chem_prot_mes_cont` STRING COMMENT '职业暴露化学品防护措施内容',
    `prfs_expo_toxi_prot_mes_cont` STRING COMMENT '职业暴露毒物防护措施内容',
    `prfs_expo_rdat_prot_mes_cont` STRING COMMENT '职业暴露射线防护措施内容',
    `excs_frqu_code` STRING COMMENT '锻炼频率代码',
    `excs_frqu_name` STRING COMMENT '锻炼频率名称',
    `each_excs_time` STRING COMMENT '每次锻炼时间分钟',
    `excs_way` STRING COMMENT '锻炼方式',
    `smoke_mark_code` STRING COMMENT '吸烟情况吸烟状况代码',
    `smoke_mark_name` STRING COMMENT '吸烟情况吸烟状况名称',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `stop_drink_code` STRING COMMENT '饮酒情况是否戒酒代码',
    `stop_drink_name` STRING COMMENT '饮酒情况是否戒酒名称',
    `prfs_expo_dust` STRING COMMENT '职业暴露粉尘',
    `prfs_expo_dust_prot_mes` STRING COMMENT '职业暴露粉尘防护措施',
    `prfs_expo_oth` STRING COMMENT '职业暴露其他',
    `prfs_expo_oth_prot_mes` STRING COMMENT '职业暴露其他防护措施',
    `prfs_expo_dust_prot_mes_cont` STRING COMMENT '职业暴露粉尘防护措施内容',
    `prfs_expo_oth_prot_mes_cont` STRING COMMENT '职业暴露其他防护措施内容',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='个人健康档案首页表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_illness` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `disease_code` STRING COMMENT '疾病代码',
    `disease_name` STRING COMMENT '疾病名称',
    `cnfm_date` DATE COMMENT '确诊日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往疾病史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_surgery` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `proc_name` STRING COMMENT '手术名称',
    `proc_date` DATE COMMENT '手术日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往手术史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_injury` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `trauma_name` STRING COMMENT '外伤名称',
    `trauma_date` DATE COMMENT '外伤日期',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往外伤史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_transfusion` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `transfuse_reason` STRING COMMENT '输血原因',
    `transfuse_datetime` TIMESTAMP COMMENT '输血日期时间',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `provice_district_code` STRING COMMENT '省级行政区划代码',
    `provice_district_name` STRING COMMENT '省级行政区划名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='既往输血史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phd_health_family` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `files_uscid` STRING COMMENT '档案管理机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `relation_patient_code` STRING COMMENT '患者与本人关系代码',
    `relation_patient_name` STRING COMMENT '患者与本人关系名称',
    `disease_code` STRING COMMENT '疾病代码',
    `disease_name` STRING COMMENT '疾病名称',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家族疾病史表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_sign` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `signid` STRING COMMENT '签约ID',
    `sign_dor_no` STRING COMMENT '签约医生工号',
    `sign_dor_name` STRING COMMENT '签约医生姓名',
    `sign_team_code` STRING COMMENT '签约团队编号',
    `sign_team_name` STRING COMMENT '签约团队名称',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `sign_datetime` TIMESTAMP COMMENT '签约时间',
    `unsign_date` TIMESTAMP COMMENT '解约时间',
    `unsign_reason` STRING COMMENT '解约原因',
    `sign_status_code` STRING COMMENT '签约状态代码',
    `sign_status_name` STRING COMMENT '签约状态名称',
    `reg_dor_code` STRING COMMENT '登记医生工号',
    `reg_dor_name` STRING COMMENT '登记医生姓名',
    `reg_time` TIMESTAMP COMMENT '登记时间',
    `remark` STRING COMMENT '备注',
    `state` STRING COMMENT '修改标志',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `presideid_card_type_code` STRING COMMENT '负责人身份证件类别代码',
    `presideid_card_type_name` STRING COMMENT '负责人身份证件类别名称',
    `presideid_card_no` STRING COMMENT '负责人身份证件号码',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭签约关系表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `householder_psncert_type_code` STRING COMMENT '户主身份证件类别代码',
    `householder_psncert_type_name` STRING COMMENT '户主身份证件类别名称',
    `householder_cert_no` STRING COMMENT '户主身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `patientid` STRING COMMENT '个人档案标识符',
    `family_tel` STRING COMMENT '家庭电话',
    `family_addr_code` STRING COMMENT '家庭所在地行政区划代码',
    `family_addr_name` STRING COMMENT '家庭所在地行政区划名称',
    `family_addr_prov_code` STRING COMMENT '家庭所在地省自治区直辖市代码',
    `family_addr_prov_name` STRING COMMENT '家庭所在地省自治区直辖市名称',
    `family_addr_city_code` STRING COMMENT '家庭所在地市地区代码',
    `family_addr_city_name` STRING COMMENT '家庭所在地市地区名称',
    `family_addr_coty_code` STRING COMMENT '家庭所在地县区代码',
    `family_addr_coty_name` STRING COMMENT '家庭所在地县区名称',
    `family_addr_town_code` STRING COMMENT '家庭所在地乡镇街道代码',
    `family_addr_town_name` STRING COMMENT '家庭所在地乡镇街道名称',
    `family_addr_comm_code` STRING COMMENT '家庭所在地居委会村代码',
    `family_addr_comm_name` STRING COMMENT '家庭所在地居委会村名称',
    `family_addr_cotry_name` STRING COMMENT '家庭所在地村街路弄等',
    `family_addr_housnum` STRING COMMENT '家庭所在地门牌号包括“室”',
    `family_addr` STRING COMMENT '家庭住址',
    `poscode` STRING COMMENT '邮政编码',
    `family_rec_status_code` STRING COMMENT '家庭档案状态代码',
    `family_rec_status_name` STRING COMMENT '家庭档案状态名称',
    `inquirer_staff_no` STRING COMMENT '调查人工号',
    `inquirer_staff_name` STRING COMMENT '调查人姓名',
    `inquirer_date` DATE COMMENT '调查日期',
    `registerhiscode` STRING COMMENT '登记人工号',
    `registerhisname` STRING COMMENT '登记人姓名',
    `reg_date` TIMESTAMP COMMENT '登记日期',
    `data_rank` STRING COMMENT '密级',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭健康档案表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_member` (
    `rid` STRING COMMENT '数据唯一记录号',
    `sign_up_uscid` STRING COMMENT '签约医疗机构统一社会信用代码',
    `health_rec_id` STRING COMMENT '家庭档案ID',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `resident_name` STRING COMMENT '居民姓名',
    `householder_mark` STRING COMMENT '户主标志',
    `householder_relation_code` STRING COMMENT '与户主关系代码',
    `householder_relation_name` STRING COMMENT '与户主关系名称',
    `data_rank` STRING COMMENT '密级',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭成员关系表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_staff` (
    `rid` STRING COMMENT '数据唯一记录号',
    `job_no` STRING COMMENT '工号',
    `uscid` STRING COMMENT '医疗机构统一社会信用代码',
    `rgst_name` STRING COMMENT '注册名称',
    `full_name` STRING COMMENT '姓名',
    `certno` STRING COMMENT '身份证件号码',
    `dept_code` STRING COMMENT '科室代码',
    `team` STRING COMMENT '所属团队',
    `proftechttl_code` STRING COMMENT '职务代码',
    `proftechttl_name` STRING COMMENT '职务名称',
    `job_title_code` STRING COMMENT '职称代码',
    `job_title_name` STRING COMMENT '职称名称',
    `brdy` DATE COMMENT '出生日期',
    `psn_type_code` STRING COMMENT '人员类别代码',
    `psn_type_name` STRING COMMENT '人员类别名称',
    `practice_type_code` STRING COMMENT '医师执业类别代码',
    `practice_type_name` STRING COMMENT '医师执业类别名称',
    `gp_flag` STRING COMMENT '全科医师标志',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `professional_code` STRING COMMENT '专业代码',
    `professional_name` STRING COMMENT '专业名称',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='医护人员字典表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phf_health_team` (
    `rid` STRING COMMENT '数据唯一记录号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `team_code` STRING COMMENT '团队代码',
    `team_name` STRING COMMENT '团队名称',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='医护团队字典表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_record_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `patientid` STRING COMMENT '个人档案标识符',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `appoint_id` STRING COMMENT '预约编号',
    `plan_code` STRING COMMENT '计划编号',
    `full_name` STRING COMMENT '姓名',
    `examination_date` DATE COMMENT '体检日期',
    `main_dor_no` STRING COMMENT '主检医生工号',
    `main_dor_name` STRING COMMENT '主检医生姓名',
    `sympt_code` STRING COMMENT '症状代码标准',
    `sympt_name` STRING COMMENT '症状名称',
    `tprt` DECIMAL(20,4) COMMENT '体温℃',
    `pule` INT(16) COMMENT '脉率次min',
    `vent_frqu` INT(16) COMMENT '呼吸频率次min',
    `left_dbp` INT(16) COMMENT '血压左舒张压mmHg',
    `left_sbp` INT(16) COMMENT '血压左收缩压mmHg',
    `right_dbp` INT(16) COMMENT '血压右舒张压mmHg',
    `right_sbp` INT(16) COMMENT '血压右收缩压mmHg',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `waist_cm` DECIMAL(20,4) COMMENT '腰围(cm)',
    `hipline` DECIMAL(20,4) COMMENT '臀围',
    `whr` DECIMAL(20,4) COMMENT '腰臀围比值',
    `elder_cognition_res_code` STRING COMMENT '老年人认识功能代码',
    `elder_cognition_res_name` STRING COMMENT '老年人认识功能名称',
    `elder_wit_score` INT(16) COMMENT '智力状态检查总分',
    `elder_health_status_code` STRING COMMENT '老年人健康状态自我评估代码',
    `elder_health_status_name` STRING COMMENT '老年人健康状态自我评估名称',
    `elder_self_eval_code` STRING COMMENT '老年人自理能力自我评估代码',
    `elder_self_eval_name` STRING COMMENT '老年人自理能力自我评估名称',
    `elder_emotional_status_code` STRING COMMENT '老年人情感状态代码',
    `elder_emotional_status_name` STRING COMMENT '老年人情感状态名称',
    `elder_depression_score` INT(16) COMMENT '老年人抑郁评分检查总分',
    `excs_frqu_code` STRING COMMENT '锻炼频率代码',
    `excs_frqu_name` STRING COMMENT '锻炼频率名称',
    `exercise_each` INT(16) COMMENT '每次锻炼时间',
    `insist_exercise_month` INT(16) COMMENT '坚持锻炼时间',
    `exercise_code` STRING COMMENT '锻炼方式说明',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `start_smoke_age` INT(16) COMMENT '开始吸烟年龄',
    `stop_smoke_age` INT(16) COMMENT '戒烟年龄',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `stop_drink_code` STRING COMMENT '饮酒情况是否戒酒代码',
    `stop_drink_name` STRING COMMENT '饮酒情况是否戒酒名称',
    `stop_drink_age` INT(16) COMMENT '戒酒年龄',
    `start_drink_age` INT(16) COMMENT '开始饮酒年龄',
    `last_year_drunkenness_mark` STRING COMMENT '近一年是否有醉酒',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `oral_exterior_code` STRING COMMENT '口腔口唇外观代码',
    `oral_exterior_name` STRING COMMENT '口腔口唇外观名称',
    `dentition_type_code` STRING COMMENT '口腔齿列类别代码',
    `dentition_type_name` STRING COMMENT '口腔齿列类别名称',
    `dentition_explain` STRING COMMENT '口腔齿列描述',
    `phary_exam_res_code` STRING COMMENT '口腔咽部检查结果代码',
    `phary_exam_res_name` STRING COMMENT '口腔咽部检查结果名称',
    `left_original_value` DECIMAL(20,4) COMMENT '裸眼视力左眼',
    `right_original_hyperopia_value` DECIMAL(20,4) COMMENT '裸眼视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `left_vision_code` STRING COMMENT '左眼视力代码',
    `left_vision_name` STRING COMMENT '左眼视力名称',
    `right_vision_code` STRING COMMENT '右眼视力代码',
    `right_vision_name` STRING COMMENT '右眼视力名称',
    `eyeground_abnorm_mark` STRING COMMENT '眼底异常标志',
    `fundoscopy_abnorm_descr` STRING COMMENT '眼底异常描述',
    `hear_check_res_code` STRING COMMENT '听力检测结果代码',
    `hear_check_res_name` STRING COMMENT '听力检测结果名称',
    `sport_function_status_code` STRING COMMENT '运动功能状态代码',
    `sport_function_status_name` STRING COMMENT '运动功能状态名称',
    `skin_check_abnorm_code` STRING COMMENT '皮肤检查结果代码',
    `skin_check_abnorm_name` STRING COMMENT '皮肤检查结果名称',
    `scleral_check_res_code` STRING COMMENT '巩膜检查结果代码',
    `scleral_check_res_name` STRING COMMENT '巩膜检查结果名称',
    `lymph_check_res_code` STRING COMMENT '淋巴结检查结果代码',
    `lymph_check_res_name` STRING COMMENT '淋巴结检查结果名称',
    `barrel_chest_mark` STRING COMMENT '肺桶状胸标志',
    `lung_abnorm_breath_mark` STRING COMMENT '肺部异常呼吸音标志',
    `lung_abnorm_breath_descr` STRING COMMENT '肺呼吸音异常描述',
    `lung_rale_code` STRING COMMENT '肺罗音代码',
    `lung_rale_name` STRING COMMENT '肺罗音名称',
    `lung_rale_describe` STRING COMMENT '肺罗音描述',
    `heart_rate` INT(16) COMMENT '心率次min',
    `heart_rate_type_code` STRING COMMENT '心脏心律类别代码',
    `heart_rate_type_name` STRING COMMENT '心脏心律类别名称',
    `heart_murmur_mark` STRING COMMENT '心脏杂音标志',
    `heart_murmur_describe` STRING COMMENT '心脏杂音描述',
    `abdominal_tend_mark` STRING COMMENT '腹部压痛标志',
    `abdominal_tend_descr` STRING COMMENT '腹部压痛描述',
    `abdominal_mass_mark` STRING COMMENT '腹部包块标志',
    `abdominal_mass_descr` STRING COMMENT '腹部包块描述',
    `abdominal_hepatauxe_mark` STRING COMMENT '腹部肝大标志',
    `abdominal_hepatauxe_descr` STRING COMMENT '腹部肝大描述',
    `splenauxe_mark` STRING COMMENT '腹部脾大标志',
    `splenauxe_descr` STRING COMMENT '腹部脾大描述',
    `abdominal_dullness_mark` STRING COMMENT '腹部移动性浊音标志',
    `abdominal_dullness_descr` STRING COMMENT '腹部移动性浊音描述',
    `legs_edema_check_res_code` STRING COMMENT '下肢水肿代码',
    `legs_edema_check_res_name` STRING COMMENT '下肢水肿名称',
    `foot_dorsal_artery_code` STRING COMMENT '足背动脉搏动代码',
    `foot_dorsal_artery_name` STRING COMMENT '足背动脉搏动名称',
    `anus_check_res_type_code` STRING COMMENT '肛门指诊代码',
    `anus_check_res_type_name` STRING COMMENT '肛门指诊名称',
    `breast_check_res_code` STRING COMMENT '乳腺检查结果代码',
    `breast_check_res_name` STRING COMMENT '乳腺检查结果名称',
    `vulva_abnorm_mark` STRING COMMENT '妇科外阴异常标志',
    `vulva_abnorm_descr` STRING COMMENT '妇科外阴描述',
    `vagina_abnorm_mark` STRING COMMENT '妇科阴道异常标志',
    `vagina_abnorm_descr` STRING COMMENT '妇科阴道描述',
    `cervix_abnorm_mark` STRING COMMENT '妇科宫颈异常标志',
    `cervix_abnorm_descr` STRING COMMENT '妇科宫颈描述',
    `corpusuteri_abnorm_mark` STRING COMMENT '妇科宫体异常标志',
    `corpusuteri_abnorm_descr` STRING COMMENT '妇科宫体描述',
    `adnex_abnorm_mark` STRING COMMENT '妇科附件异常标志',
    `gyn_adnex_abnorm_descr` STRING COMMENT '妇科附件异常描述',
    `other_health_check_res` STRING COMMENT '查体其他',
    `ecg_abnorm_mark` STRING COMMENT '心电图异常标志',
    `ecg_abnorm_descr` STRING COMMENT '心电图异常描述',
    `xray_abnorm_mark` STRING COMMENT '胸部X线片异常标志',
    `xray_abnorm_descr` STRING COMMENT '胸部X线片异常描述',
    `bscan_abnorm_mark` STRING COMMENT 'B超异常标志',
    `bscan_abnorm_descr` STRING COMMENT 'B超异常描述',
    `cps_abnorm_mark` STRING COMMENT '宫颈涂片异常标志',
    `cps_abnorm_descr` STRING COMMENT '宫颈涂片异常描述',
    `other_assist_check` STRING COMMENT '辅助检查其他',
    `cardiovascular_code` STRING COMMENT '脑血管疾病代码',
    `cardiovascular_name` STRING COMMENT '脑血管疾病名称',
    `chronic_kidney_code` STRING COMMENT '肾脏疾病代码',
    `chronic_kidney_name` STRING COMMENT '肾脏疾病名称',
    `cardiopathy_code` STRING COMMENT '心脏疾病代码',
    `cardiopathy_name` STRING COMMENT '心脏疾病名称',
    `vas_code` STRING COMMENT '血管疾病代码',
    `vas_name` STRING COMMENT '血管疾病名称',
    `oculopathy_code` STRING COMMENT '眼部疾病代码',
    `oculopathy_name` STRING COMMENT '眼部疾病名称',
    `neuro_exam_abnormal_mark` STRING COMMENT '神经系统疾病标志',
    `neuro_exam_abnormal_descr` STRING COMMENT '神经系统疾病描述',
    `systemic_disease` STRING COMMENT '其他系统疾病',
    `systemic_disease_descr` STRING COMMENT '其他系统疾病描述',
    `hl_eval_abnorm_flag` STRING COMMENT '健康评价异常标志',
    `abnormal_descr` STRING COMMENT '健康评价异常描述',
    `health_guide_code` STRING COMMENT '健康指导代码',
    `health_guide_name` STRING COMMENT '健康指导名称',
    `risk_control_ad_code` STRING COMMENT '危险因素控制建议代码',
    `risk_control_ad_name` STRING COMMENT '危险因素控制建议名称',
    `aim_weight` DECIMAL(20,4) COMMENT '减体重目标',
    `offer_vacc_code` STRING COMMENT '建议接种疫苗代码',
    `offer_vacc_name` STRING COMMENT '建议接种疫苗名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `updt_emplo_no` STRING COMMENT '更新员工编号',
    `updt_emplo_name` STRING COMMENT '更新员工姓名',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='体检记录信息';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_med_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `drug_name` STRING COMMENT '药品名称',
    `drugusage` STRING COMMENT '使用频率',
    `drug_used_dosunt` STRING COMMENT '药物使用剂量单位',
    `drug_used_sdose` DECIMAL(20,4) COMMENT '药物使用次剂量',
    `drug_used_idose` DECIMAL(20,4) COMMENT '药物使用总剂量',
    `drug_used_way_code` STRING COMMENT '给药途径用法代码',
    `drug_used_way_name` STRING COMMENT '给药途径用法名称',
    `medication_time` STRING COMMENT '用药时间',
    `tcmdrug_type_code` STRING COMMENT '中药类别代码',
    `tcmdrug_type_name` STRING COMMENT '中药类别名称',
    `medication_compliance_code` STRING COMMENT '服药依从性代码',
    `medication_compliance_name` STRING COMMENT '服药依从性名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='主要用药信息';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_home_bed_history` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `build_bed_date` DATE COMMENT '建床日期',
    `remove_bed_date` DATE COMMENT '撤床日期',
    `bed_reason` STRING COMMENT '建床原因',
    `bed_org_name` STRING COMMENT '建床医疗机构名称',
    `medcasno` STRING COMMENT '病案号',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='家庭病床史';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_nonimm_vacc_history` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `vacc_code` STRING COMMENT '疫苗代码',
    `vacc_name` STRING COMMENT '疫苗名称',
    `vacc_time` TIMESTAMP COMMENT '接种日期时间',
    `vaccinate_uscid` STRING COMMENT '接种机构统一社会信用代码',
    `vaccinate_org_name` STRING COMMENT '接种机构名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='非免疫规划预防接种史';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_elderly_care_assess` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `examination_no` STRING COMMENT '体检编号',
    `dinner_score` INT(16) COMMENT '进餐分数',
    `freshen_score` INT(16) COMMENT '梳洗分数',
    `dress_score` INT(16) COMMENT '穿衣分数',
    `toilet_score` INT(16) COMMENT '如厕分数',
    `acty_score` INT(16) COMMENT '活动分数',
    `total_score` STRING COMMENT '总分',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='老年人生活自理能力评估表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_lab_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `hl_phys_exam_asst_exam_id` STRING COMMENT '健康体检辅助检查ID',
    `ins_id` STRING COMMENT '机构ID',
    `hl_file_id` STRING COMMENT '健康档案ID',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `asst_exam_empt_stom_bloo_gluc` STRING COMMENT '辅助检查空腹血糖',
    `asst_exam_empt_stom_bloo_gluc_mg` STRING COMMENT '辅助检查空腹血糖mg',
    `asst_exam_bloo_rout_hemog` STRING COMMENT '辅助检查血常规血红蛋白',
    `asst_exam_bloo_rout_whit_bloo_cells` STRING COMMENT '辅助检查血常规白细胞',
    `asst_exam_bloo_rout_plate` STRING COMMENT '辅助检查血常规血小板',
    `asst_exam_bloo_rout_oth` STRING COMMENT '辅助检查血常规其他',
    `asst_exam_urin_rout_urin_prot` STRING COMMENT '辅助检查尿常规尿蛋白',
    `asst_exam_urin_rout_urin_gluc` STRING COMMENT '辅助检查尿常规尿糖',
    `asst_exam_urin_rout_urin_keto` STRING COMMENT '辅助检查尿常规尿酮体',
    `asst_exam_urin_rout_urin_occu_bloo` STRING COMMENT '辅助检查尿常规尿潜血',
    `asst_exam_urin_rout_oth` STRING COMMENT '辅助检查尿常规其他',
    `asst_exam_urin_micro_albu` STRING COMMENT '辅助检查尿微量白蛋白',
    `asst_exam_sto_occu_bloo` STRING COMMENT '辅助检查大便潜血',
    `asst_exam_live_fun_sero_gluta_transa` STRING COMMENT '辅助检查肝功能血清谷丙转氨酶',
    `asst_exam_live_fun_sero_grain_grass_transa` STRING COMMENT '辅助检查肝功能血清谷草转氨酶',
    `asst_exam_live_fun_albu` STRING COMMENT '辅助检查肝功能白蛋白',
    `asst_exam_live_fun_totl_bili` STRING COMMENT '辅助检查肝功能总胆红素',
    `asst_exam_live_fun_comb_bili` STRING COMMENT '辅助检查肝功能结合胆红素',
    `asst_exam_kdn_fun_sero_creat` STRING COMMENT '辅助检查肾功能血清肌酐',
    `asst_exam_kdn_fun_blo_urea_nitr` STRING COMMENT '辅助检查肾功能血尿素氮',
    `asst_exam_kdn_fun_k_conc` STRING COMMENT '辅助检查肾功能血钾浓度',
    `asst_exam_kdn_fun_na_conc` STRING COMMENT '辅助检查肾功能血钠浓度',
    `asst_exam_pl_totl_chol` STRING COMMENT '辅助检查血脂总胆固醇',
    `asst_exam_pl_trigl` STRING COMMENT '辅助检查血脂甘油三酯',
    `asst_exam_pl_sero_low_den_lipopr_chol` STRING COMMENT '辅助检查血脂血清低密度脂蛋白胆固醇',
    `asst_exam_pl_sero_high_den_lipopr_chol` STRING COMMENT '辅助检查血脂血清高密度脂蛋白胆固醇',
    `asst_exam_glyc_hemog` STRING COMMENT '辅助检查糖化血红蛋白',
    `asst_exam_hepa_b_surf_anti` STRING COMMENT '辅助检查乙型肝炎表面抗原',
    `asst_exam_fund` STRING COMMENT '辅助检查眼底',
    `asst_exam_fund_abn` STRING COMMENT '辅助检查眼底异常',
    `asst_exam_electro` STRING COMMENT '辅助检查心电图',
    `asst_exam_electro_abn` STRING COMMENT '辅助检查心电图异常',
    `asst_exam_chst_x_line_piec` STRING COMMENT '辅助检查胸部x线片',
    `asst_exam_chst_x_line_piec_abn` STRING COMMENT '辅助检查胸部x线片异常',
    `asst_exam_b_over` STRING COMMENT '辅助检查b超',
    `asst_exam_b_over_oth` STRING COMMENT '辅助检查b超其他',
    `asst_exam_cerv_smea` STRING COMMENT '辅助检查宫颈涂片',
    `asst_exam_cerv_smea_abn` STRING COMMENT '辅助检查宫颈涂片异常',
    `asst_exam_oth` STRING COMMENT '辅助检查其他',
    `urin_rout_urin_whit_bloo_cells` STRING COMMENT '尿常规尿白细胞',
    `urin_rout_urin_bili` STRING COMMENT '尿常规尿胆红素',
    `urin_rout_urin_nit` STRING COMMENT '尿常规尿亚硝酸盐',
    `live_fun_albu_glon_rat` STRING COMMENT '肝功能白蛋白球蛋白比值(*)',
    `live_fun_indi_bili` STRING COMMENT '肝功能间接胆红素(*)',
    `live_fun_alp` STRING COMMENT '肝功能碱性磷酸酶(alp)',
    `live_fun_r_ggt` STRING COMMENT '肝功能r谷氨酰转移酶',
    `kdn_fun_uric_acid` STRING COMMENT '肾功能尿酸',
    `asst_exam_electro1` STRING COMMENT '辅助检查心电图1',
    `asst_exam_electro2` STRING COMMENT '辅助检查心电图2',
    `asst_exam_electro3` STRING COMMENT '辅助检查心电图3',
    `abd_b_over_lgp_sple` INT(6) COMMENT '腹部b超肝胆胰脾',
    `abd_b_over_lgp_sple_oth` STRING COMMENT '腹部b超肝胆胰脾其他',
    `asst_exam_rcd_sav_time` TIMESTAMP COMMENT '辅助检查记录保存时间',
    `asst_exam_abd_b_over1` STRING COMMENT '辅助检查腹部b超1',
    `asst_exam_abd_b_over2` STRING COMMENT '辅助检查腹部b超2',
    `asst_exam_abd_b_over3` STRING COMMENT '辅助检查腹部b超3',
    `asst_exam_abd_b_over4` STRING COMMENT '辅助检查腹部b超4',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='体检检验记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_physical_exam` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `check_id` STRING COMMENT '查体ID',
    `skin_codition_code` STRING COMMENT '皮肤状况代码',
    `skin_codition_name` STRING COMMENT '皮肤状况名称',
    `skin_condition_remark` STRING COMMENT '皮肤状况为其他时描述',
    `sclera_code` STRING COMMENT '巩膜代码',
    `sclera_name` STRING COMMENT '巩膜名称',
    `sclera_remark` STRING COMMENT '巩膜为其他时描述',
    `lymph_node_code` STRING COMMENT '淋巴结代码',
    `lymph_node_name` STRING COMMENT '淋巴结名称',
    `lymph_node_remark` STRING COMMENT '淋巴结为其他时描述',
    `barrel_chest_flag` STRING COMMENT '肺桶状胸标志',
    `breath_sound_code` STRING COMMENT '肺呼吸音代码',
    `breath_sound_name` STRING COMMENT '肺呼吸音名称',
    `breath_sound_abnormal_remark` STRING COMMENT '肺呼吸音异常描述',
    `rale_code` STRING COMMENT '肺罗音代码',
    `rale_name` STRING COMMENT '肺罗音名称',
    `rale_remark` STRING COMMENT '肺罗音为其他时描述',
    `heart_rate` INT(16) COMMENT '心率次min',
    `heart_rhythm_code` STRING COMMENT '心脏心律代码',
    `heart_rhythm_name` STRING COMMENT '心脏心律名称',
    `heart_noise_flag` STRING COMMENT '心脏杂音标志标志',
    `heart_noise_remark` STRING COMMENT '心脏杂音异常时描述',
    `tenderness_flag` STRING COMMENT '腹部压痛标志',
    `tenderness_remark` STRING COMMENT '腹部压痛异常时描述',
    `bag_piece_flag` STRING COMMENT '腹部包块标志',
    `bag_piece_remark` STRING COMMENT '腹部包块异常时描述',
    `hepatomegaly_flag` STRING COMMENT '腹部肝大标志',
    `hepatomegaly_remark` STRING COMMENT '腹部肝大异常时描述',
    `splenomegaly_flag` STRING COMMENT '腹部脾大标志',
    `splenomegaly_remark` STRING COMMENT '腹部脾大异常时描述',
    `move_dullness_flag` STRING COMMENT '腹部移动性浊音标志',
    `move_dullness_remark` STRING COMMENT '腹部移动性浊音异常时描述',
    `legs_edema_check_res_code` STRING COMMENT '下肢水肿代码',
    `legs_edema_check_res_name` STRING COMMENT '下肢水肿名称',
    `foot_dorsal_artery_code` STRING COMMENT '足背动脉搏动代码',
    `foot_dorsal_artery_name` STRING COMMENT '足背动脉搏动名称',
    `anus_check_res_type_code` STRING COMMENT '肛门指诊代码',
    `anus_check_res_type_name` STRING COMMENT '肛门指诊名称',
    `anus_dre_remark` STRING COMMENT '肛门指诊为其他时描述',
    `mammary_gland_code` STRING COMMENT '乳腺情况代码',
    `mammary_gland_name` STRING COMMENT '乳腺情况名称',
    `mammary_gland_remark` STRING COMMENT '乳腺为其他时描述',
    `vulva_code` STRING COMMENT '妇科外阴代码',
    `vulva_name` STRING COMMENT '妇科外阴名称',
    `vulva_remark` STRING COMMENT '妇科外阴为其他时描述',
    `vagina_code` STRING COMMENT '妇科阴道代码',
    `vagina_name` STRING COMMENT '妇科阴道名称',
    `vagina_remark` STRING COMMENT '妇科阴道为其他时描述',
    `cervical_code` STRING COMMENT '妇科宫颈代码',
    `cervical_name` STRING COMMENT '妇科宫颈名称',
    `cervical_remark` STRING COMMENT '妇科宫颈为其他时描述',
    `uterine_body_code` STRING COMMENT '妇科宫体代码',
    `uterine_body_name` STRING COMMENT '妇科宫体名称',
    `uterine_body_remark` STRING COMMENT '妇科宫体为其他时描述',
    `attachment_code` STRING COMMENT '妇科附件代码',
    `attachment_name` STRING COMMENT '妇科附件名称',
    `attachment_remark` STRING COMMENT '妇科附件为其他时描述',
    `fundus_code` STRING COMMENT '眼底代码',
    `fundus_name` STRING COMMENT '眼底名称',
    `fundus_remark` STRING COMMENT '眼底为其他时描述',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--查体';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_physical_exam_extend` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `check_id` STRING COMMENT '查体ID',
    `oral_lips_code` STRING COMMENT '口腔口唇代码',
    `oral_lips_name` STRING COMMENT '口腔口唇名称',
    `oral_dentition_code` STRING COMMENT '口腔齿列代码',
    `oral_dentition_name` STRING COMMENT '口腔齿列名称',
    `oral_pharyngeal_code` STRING COMMENT '口腔咽部代码',
    `oral_pharyngeal_name` STRING COMMENT '口腔咽部名称',
    `oral_dentition_missing_tooth` STRING COMMENT '口腔齿列缺齿',
    `oral_dentition_decayed_tooth` STRING COMMENT '口腔齿列龋齿',
    `oral_dentition_false_tooth` STRING COMMENT '口腔齿列义齿',
    `eyesight_left` STRING COMMENT '视力左眼',
    `eyesight_right` STRING COMMENT '视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `hearing_code` STRING COMMENT '听力代码',
    `hearing_name` STRING COMMENT '听力名称',
    `motor_function_code` STRING COMMENT '运动功能代码',
    `motor_function_name` STRING COMMENT '运动功能名称',
    `other_remark` STRING COMMENT '其他查体结果描述',
    `modify_datetime` TIMESTAMP COMMENT '最后修改时间',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--查体扩展表';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_main_issues` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `exam_datetime` TIMESTAMP COMMENT '检查日期时间',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `examination_no` STRING COMMENT '体检编号',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `cardiovascular_code` STRING COMMENT '脑血管疾病代码',
    `cardiovascular_name` STRING COMMENT '脑血管疾病名称',
    `cerebrovascular_disease_remark` STRING COMMENT '脑血管疾病为其他时描述',
    `chronic_kidney_code` STRING COMMENT '肾脏疾病代码',
    `chronic_kidney_name` STRING COMMENT '肾脏疾病名称',
    `kidney_disease_remark` STRING COMMENT '肾脏疾病为其他时描述',
    `cardiopathy_code` STRING COMMENT '心脏疾病代码',
    `cardiopathy_name` STRING COMMENT '心脏疾病名称',
    `heart_disease_remark` STRING COMMENT '心脏疾病为其他时描述',
    `vas_code` STRING COMMENT '血管疾病代码',
    `vas_name` STRING COMMENT '血管疾病名称',
    `vascular_disease_remark` STRING COMMENT '血管疾病为其他时描述',
    `oculopathy_code` STRING COMMENT '眼部疾病代码',
    `oculopathy_name` STRING COMMENT '眼部疾病名称',
    `eyes_disease_remark` STRING COMMENT '眼部疾病为其他时描述',
    `nervous_disease_code` STRING COMMENT '神经系统疾病代码',
    `nervous_disease_name` STRING COMMENT '神经系统疾病名称',
    `nervous_disease_remark` STRING COMMENT '神经系统疾病为其他时描述',
    `other_disease_code` STRING COMMENT '其他系统疾病代码',
    `other_disease_name` STRING COMMENT '其他系统疾病名称',
    `other_disease_remark` STRING COMMENT '其他系统疾病为其他时描述',
    `gentle_constitution` STRING COMMENT '中医体质辨识平和质',
    `tcm_vdc` STRING COMMENT '中医体质辨识气虚质',
    `yang_deficiency_constitution` STRING COMMENT '中医体质辨识阳虚质',
    `dryness_constitution` STRING COMMENT '中医体质辨识阴虚质',
    `phlegm_damp_constitution` STRING COMMENT '中医体质辨识痰湿质',
    `damp_heat_constitution` STRING COMMENT '中医体质辨识湿热质',
    `blood_stasis_constitution` STRING COMMENT '中医体质辨识血瘀质',
    `look_depressed_constitution` STRING COMMENT '中医体质辨识气郁质',
    `sensitive_constitution` STRING COMMENT '中医体质辨识特秉质',
    `medical_abnormal_flag` STRING COMMENT '体检异常标志',
    `medical_abnormal_remark1` STRING COMMENT '体检异常1',
    `medical_abnormal_remark2` STRING COMMENT '体检异常2',
    `medical_abnormal_remark3` STRING COMMENT '体检异常3',
    `medical_abnormal_remark4` STRING COMMENT '体检异常4',
    `health_guide_code` STRING COMMENT '健康指导代码',
    `health_guide_name` STRING COMMENT '健康指导名称',
    `risk_factor_control_code` STRING COMMENT '危险因素控制代码',
    `risk_factor_control_name` STRING COMMENT '危险因素控制名称',
    `weight_target` STRING COMMENT '危险因素控制减体重目标',
    `vaccination` STRING COMMENT '危险因素控制疫苗接种',
    `risk_factor_control_remark` STRING COMMENT '危险因素控制为其他时描述',
    `health_guide_content` STRING COMMENT '健康指导内容',
    `healthy_teaching` STRING COMMENT '健教处方',
    `register_datetime` TIMESTAMP COMMENT '填表日期',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='健康体检--主要健康问题';

CREATE TABLE IF NOT EXISTS `ods_hcs_phe_organ_function` (
    `rid` STRING COMMENT '数据唯一记录号',
    `hl_phys_exam_orga_efcc_id` STRING COMMENT '健康体检脏器功能ID',
    `test_uscid` STRING COMMENT '体检机构统一社会信用代码',
    `patientid` STRING COMMENT '个人档案标识符',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `oral_lips_code` STRING COMMENT '口腔口唇代码',
    `oral_lips_name` STRING COMMENT '口腔口唇名称',
    `oral_dentition_code` STRING COMMENT '口腔齿列代码',
    `oral_dentition_name` STRING COMMENT '口腔齿列名称',
    `oral_pharyngeal_code` STRING COMMENT '口腔咽部代码',
    `oral_pharyngeal_name` STRING COMMENT '口腔咽部名称',
    `eyesight_left` STRING COMMENT '视力左眼',
    `eyesight_right` STRING COMMENT '视力右眼',
    `left_redress_value` DECIMAL(20,4) COMMENT '矫正视力左眼',
    `right_redress_hyperopia_value` DECIMAL(20,4) COMMENT '矫正视力右眼',
    `hearing_code` STRING COMMENT '听力代码',
    `hearing_name` STRING COMMENT '听力名称',
    `motor_function_code` STRING COMMENT '运动功能代码',
    `motor_function_name` STRING COMMENT '运动功能名称',
    `ora_tooth_row_mis` STRING COMMENT '口腔齿列缺齿',
    `ora_tooth_row_cari` STRING COMMENT '口腔齿列龋齿',
    `ora_tooth_row_denti` STRING COMMENT '口腔齿列义齿',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='脏器功能';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmh_report_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `rpt_org_uscid` STRING COMMENT '报告机构统一社会信用代码',
    `rpot_card_id` STRING COMMENT '报卡编号',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `rpt_date` TIMESTAMP COMMENT '报告日期',
    `report_sbp` INT(16) COMMENT '报告时收缩压',
    `rpot_dbp` INT(16) COMMENT '报告时舒张压',
    `reporter_id` STRING COMMENT '报告者工号',
    `report_name` STRING COMMENT '报告者姓名',
    `rpt_dept_code` STRING COMMENT '报告科室代码',
    `rpt_dept_name` STRING COMMENT '报告科室名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='高血压患者报病卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmh_manage_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `hpid` STRING COMMENT '高血压管理卡ID',
    `medcasno` STRING COMMENT '病案号',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `tel` STRING COMMENT '联系电话',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `family_his_code` STRING COMMENT '家族史代码',
    `family_his_name` STRING COMMENT '家族史名称',
    `exist_smoke_code` STRING COMMENT '生活习惯_吸烟代码',
    `exist_smoke_name` STRING COMMENT '生活习惯_吸烟名称',
    `stop_smoke_time` DATE COMMENT '戒烟开始日期',
    `start_smoke_age` INT(16) COMMENT '开始吸烟年龄',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `start_drink_year` INT(16) COMMENT '开始饮酒年龄(岁)',
    `overdrink_mark` STRING COMMENT '是否饮酒过量',
    `habits_exercise_code` STRING COMMENT '生活习惯锻炼代码',
    `habits_exercise_name` STRING COMMENT '生活习惯锻炼名称',
    `not_medication_sbp` INT(16) COMMENT '未服药血压水平收缩压',
    `un_dose_dbp` INT(16) COMMENT '未服药血压水平舒张压',
    `self_help_ability_code` STRING COMMENT '生活自理能力代码',
    `self_help_ability_name` STRING COMMENT '生活自理能力名称',
    `occup_mark` STRING COMMENT '职业暴露标志',
    `occup_risk_name` STRING COMMENT '职业暴露危险因素名称',
    `occup_risk_type_code` STRING COMMENT '职业暴露危险因素种类代码',
    `occup_risk_type_name` STRING COMMENT '职业暴露危险因素种类名称',
    `danger_occup` STRING COMMENT '有危害因素的具体职业',
    `danger_occup_year` INT(16) COMMENT '从事有危害因素职业时长年',
    `protectivemeasures_mark` STRING COMMENT '防护措施标志',
    `stop_mang_date` DATE COMMENT '终止管理日期',
    `risk_factor_layer_code` STRING COMMENT '危险分层代码',
    `risk_factor_layer_name` STRING COMMENT '危险分层名称',
    `info_source_code` STRING COMMENT '信息来源代码',
    `info_source_name` STRING COMMENT '信息来源名称',
    `rpot_card_id` STRING COMMENT '报卡编号',
    `comm_resp_dor_no` STRING COMMENT '社区责任医师工号',
    `comm_resp_dor_name` STRING COMMENT '社区责任医师姓名',
    `now_mang_dor_no` STRING COMMENT '目前管理医生工号',
    `now_mang_dor_name` STRING COMMENT '目前管理医生姓名',
    `now_mang_team_code` STRING COMMENT '目前管理团队ID',
    `now_mang_team_name` STRING COMMENT '目前管理团队名称',
    `dft_fu_dor_no` STRING COMMENT '默认随访医生工号',
    `dft_fu_dor_name` STRING COMMENT '默认随访医生姓名',
    `build_cards_dor_no` STRING COMMENT '建卡医生工号',
    `build_cards_dor_name` STRING COMMENT '建卡医生姓名',
    `build_cards_dept_code` STRING COMMENT '建卡科室代码',
    `build_cards_dept_name` STRING COMMENT '建卡科室名称',
    `build_cards_team_code` STRING COMMENT '建卡团队编码',
    `build_cards_team_name` STRING COMMENT '建卡团队名称',
    `build_cards_org_code` STRING COMMENT '建卡机构统一社会信用代码',
    `build_cards_org_name` STRING COMMENT '建卡医疗机构名称',
    `build_cards_time` DATE COMMENT '建卡时间',
    `obj_state_code` STRING COMMENT '管理对象状态代码',
    `obj_state_name` STRING COMMENT '管理对象状态名称',
    `addr_type_code` STRING COMMENT '常住地址类别代码',
    `addr_type_name` STRING COMMENT '常住地址类别名称',
    `addr_name` STRING COMMENT '常住地址名称',
    `residential_code` STRING COMMENT '居住地行政区划代码',
    `residential_name` STRING COMMENT '居住地行政区划名称',
    `curr_addr_prov_code` STRING COMMENT '居住地省自治区直辖市代码',
    `curr_addr_prov_name` STRING COMMENT '居住地省自治区直辖市名称',
    `curr_addr_city_code` STRING COMMENT '居住地市地区代码',
    `curr_addr_city_name` STRING COMMENT '居住地市地区名称',
    `curr_addr_coty_code` STRING COMMENT '居住地县区代码',
    `curr_addr_coty_name` STRING COMMENT '居住地县区名称',
    `curr_addr_town_code` STRING COMMENT '居住地乡镇街道代码',
    `curr_addr_town_name` STRING COMMENT '居住地乡镇街道名称',
    `curr_addr_comm_code` STRING COMMENT '居住地居委会村代码',
    `curr_addr_comm_name` STRING COMMENT '居住地居委会村名称',
    `curr_addr_cotry_name` STRING COMMENT '居住地村街路弄等',
    `residential_housnum` STRING COMMENT '居住地门牌号(包括“室”)',
    `residential_addr` STRING COMMENT '居住地址详细',
    `poscode` STRING COMMENT '邮政编码',
    `complication_code` STRING COMMENT '并发症情况代码',
    `complication_name` STRING COMMENT '并发症情况名称',
    `hp_type_code` STRING COMMENT '高血压类型代码',
    `hp_type_name` STRING COMMENT '高血压类型名称',
    `mang_level_code` STRING COMMENT '管理级别代码',
    `mang_level_name` STRING COMMENT '管理级别名称',
    `case_discussion` STRING COMMENT '病例讨论',
    `discuss_reason` STRING COMMENT '讨论原因',
    `discuss_rslt` STRING COMMENT '讨论结果',
    `detail_mang` STRING COMMENT '是否纳入细节管理',
    `detail_mang_no` STRING COMMENT '细节管理对象编号',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `asick_code` STRING COMMENT '相关疾病代码',
    `asick_name` STRING COMMENT '相关疾病名称',
    `udrug` INT(16) COMMENT '常用药物',
    `cstatus_code` STRING COMMENT '目前状况代码',
    `cstatus_name` STRING COMMENT '目前状况名称',
    `oxygentime` STRING COMMENT '吸氧时间(h)',
    `fbedreason` STRING COMMENT '家庭病床建立原因',
    `fbedcreatedata` DATE COMMENT '家庭病床建床日期',
    `fbedcanceldata` DATE COMMENT '家庭病床撤床日期',
    `coalslong` STRING COMMENT '家中煤火取暖时间(年)',
    `coalsmark` STRING COMMENT '家中煤火取暖标志',
    `family_smoke_mark` STRING COMMENT '家庭成员吸烟标志',
    `cbehavior_code` STRING COMMENT '遵医行为代码',
    `cbehavior_name` STRING COMMENT '遵医行为名称',
    `mind_info_code` STRING COMMENT '心理状态代码',
    `mind_info_name` STRING COMMENT '心理状态名称',
    `stop_drink_age` INT(16) COMMENT '戒酒年龄',
    `stop_drink_mark` STRING COMMENT '戒酒标志',
    `drunkenness_mark` STRING COMMENT '醉酒标志',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `weeksport` INT(16) COMMENT '周运动次数',
    `holdonsport` INT(16) COMMENT '坚持运动时长',
    `sporttime` STRING COMMENT '运动时间',
    `sportfrequency_code` STRING COMMENT '运动频率类别代码',
    `sportfrequency_name` STRING COMMENT '运动频率类别名称',
    `sportdesc` STRING COMMENT '运动方式说明',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `paper_file_no` STRING COMMENT '纸质档案号',
    `soci_secu_cardno` STRING COMMENT '社会保障卡号',
    `whtr_del` STRING COMMENT '是否删除',
    `filed_huma_no` STRING COMMENT '建档人编号',
    `filed_huma_name` STRING COMMENT '建档人姓名',
    `whtr_our_hosp_cnfm` STRING COMMENT '是否本院确诊',
    `cnfm_ins` STRING COMMENT '确诊机构',
    `cnfm_dr_no` STRING COMMENT '确诊医生编号',
    `cnfm_dr_name` STRING COMMENT '确诊医生姓名',
    `updt_emplo_no` STRING COMMENT '更新员工编号',
    `updt_emplo_name` STRING COMMENT '更新员工姓名',
    `whtr_spec_mgt` STRING COMMENT '是否规范管理',
    `ctrl_whtr_satis` STRING COMMENT '控制是否满意',
    `info_psh_date` DATE COMMENT '信息推送日期',
    `whtr_alre_psh` STRING COMMENT '是否已推送',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='高血压患者管理卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmh_assess_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `evaid` STRING COMMENT '评估卡编号',
    `hpid` STRING COMMENT '高血压管理卡ID',
    `diag_class_code` STRING COMMENT '高血压分级代码',
    `diag_class_name` STRING COMMENT '高血压分级名称',
    `highest_diag_class_code` STRING COMMENT '历史最高高血压分级代码',
    `highest_diag_class_name` STRING COMMENT '历史最高高血压分级名称',
    `risk_factor_layer_code` STRING COMMENT '危险分层代码',
    `risk_factor_layer_name` STRING COMMENT '危险分层名称',
    `highest_risk_factor_layer_code` STRING COMMENT '历史最高危险分层代码',
    `highest_risk_factor_layer_name` STRING COMMENT '历史最高危险分层名称',
    `risk_change_code` STRING COMMENT '危险分层变化情况代码',
    `risk_change_name` STRING COMMENT '危险分层变化情况名称',
    `bp_control_code` STRING COMMENT '本年度血压控制情况代码',
    `bp_control_name` STRING COMMENT '本年度血压控制情况名称',
    `aim_fu` INT(16) COMMENT '计划内随访次数',
    `aim_fu_norm` INT(16) COMMENT '计划内随访血压正常次数',
    `fu_total_year` INT(16) COMMENT '本年度随访总次数',
    `bldpresshigh` INT(16) COMMENT '收缩压',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `risk_factor_code` STRING COMMENT '危险因素代码',
    `danger_name` STRING COMMENT '危险因素名称',
    `target_organ_damage_code` STRING COMMENT '靶器官损害代码',
    `target_organ_damage_name` STRING COMMENT '靶器官损害名称',
    `complication_code` STRING COMMENT '并发症情况代码',
    `complication_name` STRING COMMENT '并发症情况名称',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `serum_uric_acid` STRING COMMENT '血清尿酸1',
    `check_time1` DATE COMMENT '检测日期1',
    `check_org1` STRING COMMENT '检测机构1',
    `bun2` DECIMAL(20,4) COMMENT '血尿素氮检测值2(mmolL)',
    `check_time2` DATE COMMENT '检测日期2',
    `check_org2` STRING COMMENT '检测机构2',
    `cr3` DECIMAL(20,4) COMMENT '血清肌酐检测值3µmolL',
    `check_time3` DATE COMMENT '检测日期3',
    `check_org3` STRING COMMENT '检测机构3',
    `pro4_code` STRING COMMENT '尿蛋白定性检测结果4代码',
    `pro4_name` STRING COMMENT '尿蛋白定性检测结果4名称',
    `check_date4` DATE COMMENT '检测日期4',
    `check_org4` STRING COMMENT '检测机构4',
    `albuminuria5` DECIMAL(20,4) COMMENT '尿微量白蛋白mgdL5',
    `check_date5` DATE COMMENT '检测日期5',
    `check_org5` STRING COMMENT '检测机构5',
    `c_reactive_protein6` STRING COMMENT 'C反应蛋白值6',
    `check_date6` DATE COMMENT '检测日期6',
    `check_org6` STRING COMMENT '检测机构6',
    `hs_crp7` STRING COMMENT '高敏C反应蛋白值7',
    `check_date7` DATE COMMENT '检测日期7',
    `check_org7` STRING COMMENT '检测机构7',
    `tg8` DECIMAL(20,4) COMMENT '甘油三酯值8(mmolL)',
    `check_date8` DATE COMMENT '检测日期8',
    `check_org8` STRING COMMENT '检测机构8',
    `ldl_c9` DECIMAL(20,4) COMMENT '血清低密度脂蛋白胆固醇检测值9(mmolL)',
    `check_date9` DATE COMMENT '检测日期9',
    `check_org9` STRING COMMENT '检测机构9',
    `hdl_c10` DECIMAL(20,4) COMMENT '血清高密度脂蛋白胆固醇检测值10(mmolL)',
    `check_date10` DATE COMMENT '检测日期10',
    `check_org10` STRING COMMENT '检测机构10',
    `tc11` DECIMAL(20,4) COMMENT '总胆固醇值11(mmolL)',
    `check_date11` DATE COMMENT '检测日期11',
    `check_org11` STRING COMMENT '检测机构11',
    `glu12` DECIMAL(20,4) COMMENT '血糖检测值12mmolL',
    `check_date12` DATE COMMENT '检测日期12',
    `check_org12` STRING COMMENT '检测机构12',
    `check_fundus13` STRING COMMENT '眼底检查结果13',
    `check_date13` DATE COMMENT '检测日期13',
    `check_org13` STRING COMMENT '检测机构13',
    `carotid_artery_ultrasound14` STRING COMMENT '颈动脉超声14',
    `check_date14` DATE COMMENT '检测日期14',
    `check_org14` STRING COMMENT '检测机构14',
    `electrocardiography15` STRING COMMENT '心电图检查结果15',
    `check_date15` DATE COMMENT '检测日期15',
    `check_org15` STRING COMMENT '检测机构15',
    `chest_x_ray16` STRING COMMENT '胸部X线检查结果16',
    `check_date16` DATE COMMENT '检测日期16',
    `check_org16` STRING COMMENT '检测机构16',
    `echocardiogram17` STRING COMMENT '超声心动图17',
    `check_date17` DATE COMMENT '检测日期17',
    `check_org17` STRING COMMENT '检测机构17',
    `eval_year` INT(16) COMMENT '评估年度',
    `eval_code` STRING COMMENT '评估者工号',
    `eval_name` STRING COMMENT '评估者姓名',
    `eval_org_code` STRING COMMENT '评估机构统一社会信用代码',
    `assessment_date` DATE COMMENT '评估日期',
    `eval_dept_code` STRING COMMENT '评估科室代码',
    `eval_dept_name` STRING COMMENT '评估科室名称',
    `eval_team_code` STRING COMMENT '评估团队编码',
    `eval_team_name` STRING COMMENT '评估团队名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='高血压患者评估卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmh_followup_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `fu_hpid` STRING COMMENT '高血压随访卡ID',
    `hpid` STRING COMMENT '高血压管理卡ID',
    `fu_hp_code` STRING COMMENT '高血压随访编号',
    `risk_factor_layer_code` STRING COMMENT '危险分层代码',
    `risk_factor_layer_name` STRING COMMENT '危险分层名称',
    `hp_mang_group_code` STRING COMMENT '高血压管理分组代码',
    `hp_mang_group_name` STRING COMMENT '高血压管理分组名称',
    `group_dscr_code` STRING COMMENT '定转组情况代码',
    `group_dscr_name` STRING COMMENT '定转组情况名称',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `fu_mang_code` STRING COMMENT '本次随访管理状态代码',
    `fu_mang_name` STRING COMMENT '本次随访管理状态名称',
    `loss_fu_code` STRING COMMENT '失访原因代码',
    `loss_fu_name` STRING COMMENT '失访原因名称',
    `now_symptom_code` STRING COMMENT '目前症状代码',
    `now_symptom_name` STRING COMMENT '目前症状名称',
    `reger_no` STRING COMMENT '登记人员工号',
    `reger_name` STRING COMMENT '登记人员姓名',
    `non_druggery_treatment_code` STRING COMMENT '目前非药物治疗措施代码',
    `non_druggery_treatment_name` STRING COMMENT '目前非药物治疗措施名称',
    `medication_compliance_code` STRING COMMENT '服药依从性代码',
    `medication_compliance_name` STRING COMMENT '服药依从性名称',
    `medicine_irreg_code` STRING COMMENT '不规律服药原因代码',
    `medicine_irreg_name` STRING COMMENT '不规律服药原因名称',
    `non_medicine_code` STRING COMMENT '不服药原因代码',
    `non_medicine_name` STRING COMMENT '不服药原因名称',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `bldpresshigh` INT(16) COMMENT '收缩压',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `aim_wt` DECIMAL(20,4) COMMENT '目标体重kg',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `aim_bmi` DECIMAL(20,4) COMMENT '目标体质指数',
    `sportdesc` STRING COMMENT '运动方式说明',
    `sport_freq_code` STRING COMMENT '运动频率代码',
    `sport_freq_name` STRING COMMENT '运动频率名称',
    `aim_sport_freq_code` STRING COMMENT '目标运动频率代码',
    `aim_sport_freq_name` STRING COMMENT '目标运动频率名称',
    `sport_min_length` INT(16) COMMENT '运动时长(min)',
    `aim_sport_duration` INT(16) COMMENT '目标运动时长min',
    `heart_rate` INT(16) COMMENT '心率次min',
    `positive_names` STRING COMMENT '阳性体征',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `tagt_day_smoke_value` INT(16) COMMENT '目标日吸烟量(支)',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `salt_level_code` STRING COMMENT '摄盐量分级代码',
    `salt_level_name` STRING COMMENT '摄盐量分级名称',
    `aim_salt_level_code` STRING COMMENT '目标摄盐量分级代码',
    `aim_salt_level_name` STRING COMMENT '目标摄盐量分级名称',
    `fu_diet_type_code` STRING COMMENT '随访饮食合理性评价类别代码',
    `fu_diet_type_name` STRING COMMENT '随访饮食合理性评价类别名称',
    `psycho_adjust_res_code` STRING COMMENT '心理调整评价结果代码',
    `psycho_adjust_res_name` STRING COMMENT '心理调整评价结果名称',
    `fu_compliance_res_code` STRING COMMENT '随访遵医行为评价结果代码',
    `fu_compliance_res_name` STRING COMMENT '随访遵医行为评价结果名称',
    `drug_dys_mark` STRING COMMENT '药物不良反应标志',
    `drug_dys_dscr` STRING COMMENT '药物不良反应描述',
    `complication_code` STRING COMMENT '并发症情况代码',
    `complication_name` STRING COMMENT '并发症情况名称',
    `health_edu_mark` STRING COMMENT '是否接受过健康教育',
    `advices_code` STRING COMMENT '健康处方建议代码',
    `advices_name` STRING COMMENT '健康处方建议名称',
    `accept_code` STRING COMMENT '健康处方建议患者接受程度代码',
    `accept_name` STRING COMMENT '健康处方建议患者接受程度名称',
    `fu_eval_res_code` STRING COMMENT '随访评价结果代码',
    `fu_eval_res_name` STRING COMMENT '随访评价结果名称',
    `fu_doc_no` STRING COMMENT '随访医生工号',
    `fu_doc_name` STRING COMMENT '随访医生姓名',
    `visit_date` DATE COMMENT '随访日期',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fu_org_code` STRING COMMENT '随访机构统一社会信用代码',
    `fu_dept_code` STRING COMMENT '随访科室代码',
    `fu_dept_name` STRING COMMENT '随访科室名称',
    `fu_team_code` STRING COMMENT '随访团队编码',
    `fu_team_name` STRING COMMENT '随访团队名称',
    `duty_dor_no` STRING COMMENT '责任医生工号',
    `duty_dor_name` STRING COMMENT '责任医生姓名',
    `referral_reason` STRING COMMENT '转诊原因',
    `accept_org_name` STRING COMMENT '转入医疗机构名称',
    `accept_dept_code` STRING COMMENT '转诊科室代码',
    `referral_dept_name` STRING COMMENT '转诊科室名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `hetat_plan` STRING COMMENT '心率计划',
    `stap_food_info` STRING COMMENT '主食情况目前',
    `stap_food_plan` STRING COMMENT '主食情况计划',
    `empt_stom_blgval` STRING COMMENT '空腹血糖值',
    `glyc_hemog` STRING COMMENT '糖化血红蛋白',
    `exam_date` DATE COMMENT '检查日期',
    `oth_asst_exam` STRING COMMENT '其他辅助检查',
    `hypo_react_code` STRING COMMENT '低血糖反应代码',
    `hypo_react_name` STRING COMMENT '低血糖反应名称',
    `yard_exte_dr_name` STRING COMMENT '院外医生姓名',
    `insn` STRING COMMENT '胰岛素',
    `insn_used_and_dos` STRING COMMENT '胰岛素用法和用量',
    `refl_memo` STRING COMMENT '转诊备注',
    `after_meal_bloo_gluc` STRING COMMENT '餐后血糖',
    `patient_type_code` STRING COMMENT '患者类型代码',
    `patient_type_name` STRING COMMENT '患者类型名称',
    `sput_bacteria_code` STRING COMMENT '痰菌情况代码',
    `sput_bacteria_name` STRING COMMENT '痰菌情况名称',
    `drug_resi_code` STRING COMMENT '耐药情况代码',
    `drug_resi_name` STRING COMMENT '耐药情况名称',
    `eval_type_code` STRING COMMENT '督导人员选择代码',
    `eval_type_name` STRING COMMENT '督导人员选择名称',
    `alon_of_live_room` STRING COMMENT '单独的居室',
    `vent_situ_code` STRING COMMENT '通风情况代码',
    `vent_situ_name` STRING COMMENT '通风情况名称',
    `take_medi_loc` STRING COMMENT '取药地点',
    `take_medi_time` STRING COMMENT '取药时间',
    `dose_reco_card_of_fillin` STRING COMMENT '服药记录卡的填写',
    `dose_mtd_wth_drug_stor` STRING COMMENT '服药方法及药品存放',
    `tuber_trt_cour_trea` STRING COMMENT '肺结核治疗疗程',
    `no_regu_dose_hazr` STRING COMMENT '不规律服药危害',
    `dose_new_defs_wth_dspo` STRING COMMENT '服药后不良反应及处理',
    `trt_cose_flup_sput` STRING COMMENT '治疗期间复诊查痰',
    `out_cose_how_adhe_dose` STRING COMMENT '外出期间如何坚持服药',
    `habi_wth_mnan` STRING COMMENT '生活习惯及注意事项',
    `clos_cont_the_exam` STRING COMMENT '密切接触者检查',
    `ctrl_whtr_satis` STRING COMMENT '控制是否满意',
    `interface_mark_code` STRING COMMENT '接口标记代码',
    `interface_mark_name` STRING COMMENT '接口标记名称',
    `data_source_code` STRING COMMENT '数据来源代码',
    `data_source_name` STRING COMMENT '数据来源名称',
    `equipcode` STRING COMMENT '设备编号',
    `urin_rout` STRING COMMENT '尿常规',
    `fund` STRING COMMENT '眼底',
    `neuro_chng` STRING COMMENT '神经病变',
    `adequ_dors_arter` STRING COMMENT '足背动脉',
    `cop_exam` STRING COMMENT '并发症检查',
    `hl_guid` STRING COMMENT '健康指导',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='高血压患者随访卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmd_manage_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `dmid` STRING COMMENT '糖尿病管理卡ID',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `tel` STRING COMMENT '联系电话',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `brdy` DATE COMMENT '出生日期',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `payment_term_code` STRING COMMENT '医疗付费方式代码',
    `payment_term_name` STRING COMMENT '医疗付费方式名称',
    `dm_family_his` STRING COMMENT '糖尿病家族史',
    `dm_type_code` STRING COMMENT '糖尿病类型代码',
    `dm_type_name` STRING COMMENT '糖尿病类型名称',
    `cnfm_date` DATE COMMENT '确诊日期',
    `case_source_code` STRING COMMENT '病例来源代码',
    `case_source_name` STRING COMMENT '病例来源名称',
    `fbg_value` DECIMAL(20,4) COMMENT '空腹血糖值mmolL',
    `fbg_check_type_code` STRING COMMENT '空腹血糖测量方式代码',
    `fbg_check_type_name` STRING COMMENT '空腹血糖测量方式名称',
    `rbg_value` DECIMAL(20,4) COMMENT '随机血糖值mmolL',
    `rbg_check_type_code` STRING COMMENT '随机血糖测量方式代码',
    `rbg_check_type_name` STRING COMMENT '随机血糖测量方式名称',
    `glucose_tolerance_value` STRING COMMENT '糖耐量测试值',
    `gtct_code` STRING COMMENT '糖耐量血糖测量方式代码',
    `gtct_name` STRING COMMENT '糖耐量血糖测量方式名称',
    `pbg_value` DECIMAL(20,4) COMMENT '餐后两小时血糖值mmolL',
    `pbg_check_type_code` STRING COMMENT '餐后2小时血糖测量方式代码',
    `pbg_check_type_name` STRING COMMENT '餐后2小时血糖测量方式名称',
    `rpot_card_id` STRING COMMENT '报卡编号',
    `obj_state_code` STRING COMMENT '管理对象状态代码',
    `obj_state_name` STRING COMMENT '管理对象状态名称',
    `curr_duty_dor_no` STRING COMMENT '目前责任医生工号',
    `curr_duty_dor_name` STRING COMMENT '目前责任医生姓名',
    `now_mang_team_code` STRING COMMENT '目前管理团队ID',
    `now_mang_team_name` STRING COMMENT '目前管理团队名称',
    `dft_fu_dor_no` STRING COMMENT '默认随访医生工号',
    `dft_fu_dor_name` STRING COMMENT '默认随访医生姓名',
    `build_cards_org_code` STRING COMMENT '建卡机构统一社会信用代码',
    `build_org_name` STRING COMMENT '建档机构名称',
    `build_cards_dor_no` STRING COMMENT '建卡医生工号',
    `build_cards_dor_name` STRING COMMENT '建卡医生姓名',
    `build_cards_time` DATE COMMENT '建卡时间',
    `build_cards_dept_code` STRING COMMENT '建卡科室代码',
    `build_cards_dept_name` STRING COMMENT '建卡科室名称',
    `build_cards_team_name` STRING COMMENT '建卡团队名称',
    `build_cards_team_id` STRING COMMENT '建卡团队ID',
    `addr_type_code` STRING COMMENT '常住地址类别代码',
    `addr_type_name` STRING COMMENT '常住地址类别名称',
    `addr_name` STRING COMMENT '常住地址名称',
    `residential_code` STRING COMMENT '居住地行政区划代码',
    `residential_name` STRING COMMENT '居住地行政区划名称',
    `curr_addr_prov_code` STRING COMMENT '居住地省自治区直辖市代码',
    `curr_addr_prov_name` STRING COMMENT '居住地省自治区直辖市名称',
    `curr_addr_city_code` STRING COMMENT '居住地市地区代码',
    `curr_addr_city_name` STRING COMMENT '居住地市地区名称',
    `curr_addr_coty_code` STRING COMMENT '居住地县区代码',
    `curr_addr_coty_name` STRING COMMENT '居住地县区名称',
    `curr_addr_town_code` STRING COMMENT '居住地乡镇街道代码',
    `curr_addr_town_name` STRING COMMENT '居住地乡镇街道名称',
    `curr_addr_comm_code` STRING COMMENT '居住地居委会村代码',
    `curr_addr_comm_name` STRING COMMENT '居住地居委会村名称',
    `curr_addr_cotry_name` STRING COMMENT '居住地村街路弄等',
    `residential_housnum` STRING COMMENT '居住地门牌号(包括“室”)',
    `residential_addr` STRING COMMENT '居住地址详细',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `asick_code` STRING COMMENT '相关疾病代码',
    `asick_name` STRING COMMENT '相关疾病名称',
    `udrug` INT(16) COMMENT '常用药物',
    `cstatus_code` STRING COMMENT '目前状况代码',
    `cstatus_name` STRING COMMENT '目前状况名称',
    `oxygentime` STRING COMMENT '吸氧时间(h)',
    `fbedreason` STRING COMMENT '家庭病床建立原因',
    `fbedcreatedata` DATE COMMENT '家庭病床建床日期',
    `fbedcanceldata` DATE COMMENT '家庭病床撤床日期',
    `coalslong` STRING COMMENT '家中煤火取暖时间(年)',
    `coalsmark` STRING COMMENT '家中煤火取暖标志',
    `family_smoke_mark` STRING COMMENT '家庭成员吸烟标志',
    `cbehavior_code` STRING COMMENT '遵医行为代码',
    `cbehavior_name` STRING COMMENT '遵医行为名称',
    `mind_info_code` STRING COMMENT '心理状态代码',
    `mind_info_name` STRING COMMENT '心理状态名称',
    `stop_drink_age` INT(16) COMMENT '戒酒年龄',
    `stop_drink_mark` STRING COMMENT '戒酒标志',
    `drunkenness_mark` STRING COMMENT '醉酒标志',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `weeksport` INT(16) COMMENT '周运动次数',
    `holdonsport` INT(16) COMMENT '坚持运动时长',
    `sporttime` STRING COMMENT '运动时间',
    `sportfrequency_code` STRING COMMENT '运动频率类别代码',
    `sportfrequency_name` STRING COMMENT '运动频率类别名称',
    `sportdesc` STRING COMMENT '运动方式说明',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `paper_file_no` STRING COMMENT '纸质档案号',
    `soci_secu_cardno` STRING COMMENT '社会保障卡号',
    `whtr_del` STRING COMMENT '是否删除',
    `filed_huma_no` STRING COMMENT '建档人编号',
    `filed_huma_name` STRING COMMENT '建档人姓名',
    `whtr_our_hosp_cnfm` STRING COMMENT '是否本院确诊',
    `cnfm_ins` STRING COMMENT '确诊机构',
    `cnfm_dr_no` STRING COMMENT '确诊医生编号',
    `cnfm_dr_name` STRING COMMENT '确诊医生姓名',
    `updt_emplo_no` STRING COMMENT '更新员工编号',
    `updt_emplo_name` STRING COMMENT '更新员工姓名',
    `whtr_spec_mgt` STRING COMMENT '是否规范管理',
    `ctrl_whtr_satis` STRING COMMENT '控制是否满意',
    `info_psh_date` DATE COMMENT '信息推送日期',
    `whtr_alre_psh` STRING COMMENT '是否已推送',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='糖尿病患者管理卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmd_followup_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `fu_dmid` STRING COMMENT '糖尿病随访卡ID',
    `dmid` STRING COMMENT '糖尿病管理卡ID',
    `fu_mang_code` STRING COMMENT '本次随访管理状态代码',
    `fu_mang_name` STRING COMMENT '本次随访管理状态名称',
    `loss_fu_code` STRING COMMENT '失访原因代码',
    `loss_fu_name` STRING COMMENT '失访原因名称',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `fu_eval_res_code` STRING COMMENT '随访评价结果代码',
    `fu_eval_res_name` STRING COMMENT '随访评价结果名称',
    `dm_clinical_symptoms_code` STRING COMMENT '糖尿病临床症状代码',
    `clinical_symptoms_name` STRING COMMENT '临床症状名称',
    `foot_dorsal_artery_code` STRING COMMENT '足背动脉搏动代码',
    `foot_dorsal_artery_name` STRING COMMENT '足背动脉搏动名称',
    `dm_family_his` STRING COMMENT '糖尿病家族史',
    `regular_activities` STRING COMMENT '有无规律活动',
    `regular_activities_type_code` STRING COMMENT '规律活动种类代码',
    `regular_activities_type_name` STRING COMMENT '规律活动种类名称',
    `sport_freq_code` STRING COMMENT '运动频率代码',
    `sport_freq_name` STRING COMMENT '运动频率名称',
    `aim_sport_freq_code` STRING COMMENT '目标运动频率代码',
    `aim_sport_freq_name` STRING COMMENT '目标运动频率名称',
    `sport_duration` INT(16) COMMENT '运动时长',
    `aim_sport_duration` INT(16) COMMENT '目标运动时长min',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `aim_wt` DECIMAL(20,4) COMMENT '目标体重kg',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `aim_bmi` DECIMAL(20,4) COMMENT '目标体质指数',
    `daily_staple_num` INT(16) COMMENT '日主食量g',
    `aim_daily_staple_num` INT(16) COMMENT '目标日主食量g',
    `diet_info_code` STRING COMMENT '饮食情况代码',
    `diet_info_name` STRING COMMENT '饮食情况名称',
    `bldpresshigh` INT(16) COMMENT '收缩压',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `waist_cm` DECIMAL(20,4) COMMENT '腰围(cm)',
    `hip` INT(16) COMMENT '臀围(cm)',
    `waist_hip_whr` DECIMAL(20,4) COMMENT '腰臀比WHR',
    `fbg_value` DECIMAL(20,4) COMMENT '空腹血糖值mmolL',
    `fbg_check_type_code` STRING COMMENT '空腹血糖测量方式代码',
    `fbg_check_type_name` STRING COMMENT '空腹血糖测量方式名称',
    `rbg_value` DECIMAL(20,4) COMMENT '随机血糖值mmolL',
    `rbg_check_type_code` STRING COMMENT '随机血糖测量方式代码',
    `rbg_check_type_name` STRING COMMENT '随机血糖测量方式名称',
    `glucose_tolerance_value` STRING COMMENT '糖耐量测试值',
    `gtct_code` STRING COMMENT '糖耐量血糖测量方式代码',
    `gtct_name` STRING COMMENT '糖耐量血糖测量方式名称',
    `pbg_value` DECIMAL(20,4) COMMENT '餐后两小时血糖值mmolL',
    `pbg_check_type_code` STRING COMMENT '餐后2小时血糖测量方式代码',
    `pbg_check_type_name` STRING COMMENT '餐后2小时血糖测量方式名称',
    `hba1c` DECIMAL(20,4) COMMENT '糖化血红蛋白值(%)',
    `tc` DECIMAL(20,4) COMMENT '总胆固醇值mmolL',
    `hdl_c` DECIMAL(20,4) COMMENT '血清高密度脂蛋白胆固醇检测值mmolL',
    `ldl_c` DECIMAL(20,4) COMMENT '血清低密度脂蛋白胆固醇检测值mmolL',
    `tg` DECIMAL(20,4) COMMENT '甘油三酯值mmolL',
    `acr` STRING COMMENT '尿微量白蛋白尿肌酐(mgmmol)',
    `ndbdl` DECIMAL(20,4) COMMENT '24小时尿蛋白定量mg24h',
    `take_medicine_code` STRING COMMENT '服药情况代码',
    `take_medicine_name` STRING COMMENT '服药情况名称',
    `drug_dys_mark` STRING COMMENT '药物不良反应标志',
    `drug_dys_dscr` STRING COMMENT '药物不良反应描述',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `aim_day_smoke_value` INT(16) COMMENT '目标日吸烟量',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `other_sign_dscr` STRING COMMENT '体征其他描述',
    `psycho_adjust_res_code` STRING COMMENT '心理调整评价结果代码',
    `psycho_adjust_res_name` STRING COMMENT '心理调整评价结果名称',
    `fu_compliance_res_code` STRING COMMENT '随访遵医行为评价结果代码',
    `fu_compliance_res_name` STRING COMMENT '随访遵医行为评价结果名称',
    `referral_reason` STRING COMMENT '转诊原因',
    `accept_org_name` STRING COMMENT '转入医疗机构名称',
    `accept_dept_name` STRING COMMENT '转入机构科室名称',
    `fu_eval_prop_code` STRING COMMENT '随访建议代码',
    `fu_eval_prop_name` STRING COMMENT '随访建议名称',
    `fu_doc_no` STRING COMMENT '随访医生工号',
    `fu_doc_name` STRING COMMENT '随访医生姓名',
    `duty_dor_no` STRING COMMENT '责任医生工号',
    `duty_dor_name` STRING COMMENT '责任医生姓名',
    `visit_date` DATE COMMENT '随访日期',
    `fu_dept_code` STRING COMMENT '随访科室代码',
    `fu_dept_name` STRING COMMENT '随访科室名称',
    `fu_team_id` STRING COMMENT '随访团队ID',
    `fu_team_name` STRING COMMENT '随访团队名称',
    `fu_hospital_code` STRING COMMENT '随访医疗机构统一社会信用代码',
    `fu_hospital_name` STRING COMMENT '随访医疗机构名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `hetat_plan` STRING COMMENT '心率计划',
    `stap_food_info` STRING COMMENT '主食情况目前',
    `stap_food_plan` STRING COMMENT '主食情况计划',
    `empt_stom_blgval` STRING COMMENT '空腹血糖值',
    `glyc_hemog` STRING COMMENT '糖化血红蛋白',
    `exam_date` DATE COMMENT '检查日期',
    `oth_asst_exam` STRING COMMENT '其他辅助检查',
    `hypo_react_code` STRING COMMENT '低血糖反应代码',
    `hypo_react_name` STRING COMMENT '低血糖反应名称',
    `yard_exte_dr_name` STRING COMMENT '院外医生姓名',
    `insn` STRING COMMENT '胰岛素',
    `insn_used_and_dos` STRING COMMENT '胰岛素用法和用量',
    `refl_memo` STRING COMMENT '转诊备注',
    `after_meal_bloo_gluc` STRING COMMENT '餐后血糖',
    `patient_type_code` STRING COMMENT '患者类型代码',
    `patient_type_name` STRING COMMENT '患者类型名称',
    `sput_bacteria_code` STRING COMMENT '痰菌情况代码',
    `sput_bacteria_name` STRING COMMENT '痰菌情况名称',
    `drug_resi_code` STRING COMMENT '耐药情况代码',
    `drug_resi_name` STRING COMMENT '耐药情况名称',
    `eval_type_code` STRING COMMENT '督导人员选择代码',
    `eval_type_name` STRING COMMENT '督导人员选择名称',
    `alon_of_live_room` STRING COMMENT '单独的居室',
    `vent_situ_code` STRING COMMENT '通风情况代码',
    `vent_situ_name` STRING COMMENT '通风情况名称',
    `take_medi_loc` STRING COMMENT '取药地点',
    `take_medi_time` STRING COMMENT '取药时间',
    `dose_reco_card_of_fillin` STRING COMMENT '服药记录卡的填写',
    `dose_mtd_wth_drug_stor` STRING COMMENT '服药方法及药品存放',
    `tuber_trt_cour_trea` STRING COMMENT '肺结核治疗疗程',
    `no_regu_dose_hazr` STRING COMMENT '不规律服药危害',
    `dose_new_defs_wth_dspo` STRING COMMENT '服药后不良反应及处理',
    `trt_cose_flup_sput` STRING COMMENT '治疗期间复诊查痰',
    `out_cose_how_adhe_dose` STRING COMMENT '外出期间如何坚持服药',
    `habi_wth_mnan` STRING COMMENT '生活习惯及注意事项',
    `clos_cont_the_exam` STRING COMMENT '密切接触者检查',
    `ctrl_whtr_satis` STRING COMMENT '控制是否满意',
    `interface_mark_code` STRING COMMENT '接口标记代码',
    `interface_mark_name` STRING COMMENT '接口标记名称',
    `data_source_code` STRING COMMENT '数据来源代码',
    `data_source_name` STRING COMMENT '数据来源名称',
    `equipcode` STRING COMMENT '设备编号',
    `urin_rout` STRING COMMENT '尿常规',
    `fund` STRING COMMENT '眼底',
    `neuro_chng` STRING COMMENT '神经病变',
    `adequ_dors_arter` STRING COMMENT '足背动脉',
    `cop_exam` STRING COMMENT '并发症检查',
    `hl_guid` STRING COMMENT '健康指导',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='糖尿病患者随访卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmp_manage_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `psycho_card` STRING COMMENT '严重精神障碍患者管理卡ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `medcasno` STRING COMMENT '病案号',
    `build_card_date` DATE COMMENT '建卡日期',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `resd_natu_code` STRING COMMENT '户籍性质类型代码',
    `resd_natu_name` STRING COMMENT '户籍性质类型名称',
    `employmentstatus_code` STRING COMMENT '就业情况代码',
    `employmentstatus_name` STRING COMMENT '就业情况名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `rescue_source_code` STRING COMMENT '医疗补救助来源代码',
    `rescue_source_name` STRING COMMENT '医疗补救助来源名称',
    `medfee_paymtd_code` STRING COMMENT '医疗付款方式代码',
    `medfee_paymtd_name` STRING COMMENT '医疗付款方式名称',
    `mang_type_code` STRING COMMENT '管理类型代码',
    `mang_type_name` STRING COMMENT '管理类型名称',
    `duty_doc_no` STRING COMMENT '责任医师工号',
    `duty_doc_name` STRING COMMENT '责任医师姓名',
    `resp_dor_tel` STRING COMMENT '责任医师电话号码',
    `guardian_name` STRING COMMENT '监护人姓名',
    `guardian_relation_code` STRING COMMENT '监护人与本人患者关系代码',
    `guardian_relation_name` STRING COMMENT '监护人与本人患者关系名称',
    `guardian_addr` STRING COMMENT '监护人住址',
    `guardian_telephone` STRING COMMENT '监护人电话号码',
    `committee_name` STRING COMMENT '居委会名称',
    `committee_coner_name` STRING COMMENT '居委会联系人姓名',
    `committee_telephone` STRING COMMENT '居委会联系人电话',
    `seve_ment_diso_sort_code` STRING COMMENT '严重精神障碍患者分类代码',
    `seve_ment_diso_sort_name` STRING COMMENT '严重精神障碍患者分类名称',
    `seve_ment_diso_fami_hist` INT(16) COMMENT '严重精神障碍家族史',
    `first_disease_time` DATE COMMENT '初次发病时间',
    `major_psyc_symp_code` STRING COMMENT '既往主要精神症状代码',
    `major_psyc_symp_name` STRING COMMENT '既往主要精神症状名称',
    `major_psyc_symp_dscr` STRING COMMENT '既往主要症状其他描述',
    `outp_trea_code` STRING COMMENT '既往门诊治疗情况代码',
    `outp_trea_name` STRING COMMENT '既往门诊治疗情况名称',
    `psychiatric_hos_num` INT(16) COMMENT '既往精神专科医院综合医院精神专科次数',
    `recently_ment_illn_diag_code` STRING COMMENT '最近诊断精神类疾病诊断代码',
    `recently_ment_illn_diag_name` STRING COMMENT '最近诊断精神类疾病诊断名称',
    `cnfm_date` DATE COMMENT '确诊日期',
    `conf_diag_org_name` STRING COMMENT '确诊医疗机构名称',
    `conf_diag_org_code` STRING COMMENT '确诊医疗机构统一社会信用代码',
    `trea_effe_cate_code` STRING COMMENT '既往治疗效果类别代码',
    `trea_effe_cate_name` STRING COMMENT '既往治疗效果类别名称',
    `risk_level_code` STRING COMMENT '危险行为类别代码',
    `risk_level_name` STRING COMMENT '危险行为类别名称',
    `anti_medi_sign` STRING COMMENT '抗精神病药物治疗标志',
    `first_anti_trea_time` DATE COMMENT '首次抗精神病药治疗时间',
    `latest_treat_effect_code` STRING COMMENT '最近一次治疗效果代码',
    `latest_treat_effect_name` STRING COMMENT '最近一次治疗效果名称',
    `mild_trouble_num` INT(16) COMMENT '轻度滋事次数',
    `cause_trouble_num` INT(16) COMMENT '肇事次数',
    `trouble_num` INT(16) COMMENT '肇祸次数',
    `self_injury_num` INT(16) COMMENT '自伤次数',
    `atte_suic_num` INT(16) COMMENT '自杀未遂次数',
    `other_harm_beha_num` INT(16) COMMENT '其他危害行为次数',
    `inform_agree_manage_mark` STRING COMMENT '知情同意管理标志',
    `inform_agree_nameer_name` STRING COMMENT '知情人姓名',
    `inform_agree_name_date` DATE COMMENT '签字时间',
    `economy_status_code` STRING COMMENT '经济状况代码',
    `economy_status_name` STRING COMMENT '经济状况名称',
    `speist_dor_ad` STRING COMMENT '医生意见',
    `locked_case_code` STRING COMMENT '既往关锁情况代码',
    `locked_case_name` STRING COMMENT '既往关锁情况名称',
    `lock_start_date` DATE COMMENT '关锁启动日期',
    `unlock_start_date` DATE COMMENT '解锁启动日期',
    `reger_no` STRING COMMENT '登记人员工号',
    `reger_name` STRING COMMENT '登记人员姓名',
    `build_cards_org_code` STRING COMMENT '建卡机构统一社会信用代码',
    `build_org_name` STRING COMMENT '建档机构名称',
    `addr_districts_code` STRING COMMENT '行政区划代码',
    `addr_districts_name` STRING COMMENT '行政区划名称',
    `curr_addr_prov_code` STRING COMMENT '居住地省自治区直辖市代码',
    `curr_addr_prov_name` STRING COMMENT '居住地省自治区直辖市名称',
    `curr_addr_city_code` STRING COMMENT '居住地市地区代码',
    `curr_addr_city_name` STRING COMMENT '居住地市地区名称',
    `curr_addr_coty_code` STRING COMMENT '居住地县区代码',
    `curr_addr_coty_name` STRING COMMENT '居住地县区名称',
    `curr_addr_town_code` STRING COMMENT '居住地乡镇街道代码',
    `curr_addr_town_name` STRING COMMENT '居住地乡镇街道名称',
    `curr_addr_comm_code` STRING COMMENT '居住地居委会村代码',
    `curr_addr_comm_name` STRING COMMENT '居住地居委会村名称',
    `curr_addr_cotry_name` STRING COMMENT '居住地村街路弄等',
    `residential_housnum` STRING COMMENT '居住地门牌号(包括“室”)',
    `poscode` STRING COMMENT '邮政编码',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `code_686` STRING COMMENT '国家686项目编号',
    `name_686` DATE COMMENT '进入686项目时间',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `past_hospi_info` INT(16) COMMENT '既往住院治疗情况',
    `otp_dr_name` STRING COMMENT '外院医生姓名',
    `soci_secu_cardno_mark` STRING COMMENT '社会保障卡号(智业上传标志)',
    `whtr_del` STRING COMMENT '是否删除',
    `diag_icd10_code` STRING COMMENT '诊断icd10码西医诊断',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='严重精神障碍患者管理卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_dmp_followup_form` (
    `rid` STRING COMMENT '数据唯一记录号',
    `fu_psycho_code` STRING COMMENT '严重精神障碍患者随访记录ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `psycho_card` STRING COMMENT '严重精神障碍患者管理卡ID',
    `case_manage_logo` STRING COMMENT '个案管理标志',
    `the_patient_cond_code` STRING COMMENT '个案管理患者病情总评代码',
    `the_patient_cond_name` STRING COMMENT '个案管理患者病情总评名称',
    `pati_soci_func_revi_code` STRING COMMENT '个案管理患者社会功能总评代码',
    `pati_soci_func_revi_name` STRING COMMENT '个案管理患者社会功能总评名称',
    `visit_sign` STRING COMMENT '访到标志',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `loss_fu_code` STRING COMMENT '失访原因代码',
    `loss_fu_name` STRING COMMENT '失访原因名称',
    `death_date` DATE COMMENT '死亡日期',
    `die_reason_code` STRING COMMENT '死亡原因代码',
    `die_reason_name` STRING COMMENT '死亡原因名称',
    `full_name` STRING COMMENT '姓名',
    `fu_doctor_name` STRING COMMENT '随访医师姓名',
    `fu_doctor_no` STRING COMMENT '随访医师工号',
    `now_symptom_code` STRING COMMENT '目前症状代码',
    `now_symptom_name` STRING COMMENT '目前症状名称',
    `treatment_form_code` STRING COMMENT '治疗形式代码',
    `treatment_form_name` STRING COMMENT '治疗形式名称',
    `recovery_code` STRING COMMENT '康复措施代码',
    `recovery_name` STRING COMMENT '康复措施名称',
    `risk_level_code` STRING COMMENT '危险行为类别代码',
    `risk_level_name` STRING COMMENT '危险行为类别名称',
    `risk_type_code` STRING COMMENT '危险性级别代码',
    `risk_type_name` STRING COMMENT '危险性级别名称',
    `personal_life_code` STRING COMMENT '社会功能情况个人生活自理代码',
    `personal_life_name` STRING COMMENT '社会功能情况个人生活自理名称',
    `housework_code` STRING COMMENT '社会功能情况家务劳动代码',
    `housework_name` STRING COMMENT '社会功能情况家务劳动名称',
    `prod_labor_and_work_code` STRING COMMENT '社会功能情况生产劳动及工作代码',
    `prod_labor_and_work_name` STRING COMMENT '社会功能情况生产劳动及工作名称',
    `learning_ability_code` STRING COMMENT '社会功能情况学习能力代码',
    `learning_ability_name` STRING COMMENT '社会功能情况学习能力名称',
    `soci_inte_comm_code` STRING COMMENT '社会功能情况社会人际交往代码',
    `soci_inte_comm_name` STRING COMMENT '社会功能情况社会人际交往名称',
    `mild_trouble_num` INT(16) COMMENT '轻度滋事次数',
    `cause_trouble_num` INT(16) COMMENT '肇事次数',
    `trouble_num` INT(16) COMMENT '肇祸次数',
    `self_injury_num` INT(16) COMMENT '自伤次数',
    `atte_suic_num` INT(16) COMMENT '自杀未遂次数',
    `other_harm_beha_num` INT(16) COMMENT '其他危害行为次数',
    `hosp_betwe_two_fu_code` STRING COMMENT '两次随访间住院情况代码',
    `hosp_betwe_two_fu_name` STRING COMMENT '两次随访间住院情况名称',
    `patn_ipt_cnt` INT(16) COMMENT '住院次数',
    `ment_illn_diag_code` STRING COMMENT '精神类疾病诊断代码',
    `ment_illn_diag_name` STRING COMMENT '精神类疾病诊断名称',
    `treatment_code` STRING COMMENT '治疗方式代码',
    `treatment_name` STRING COMMENT '治疗方式名称',
    `timeliness_code` STRING COMMENT '及时性代码',
    `timeliness_name` STRING COMMENT '及时性名称',
    `way_of_treatment_code` STRING COMMENT '就诊方式代码',
    `way_of_treatment_name` STRING COMMENT '就诊方式名称',
    `outp_spec_hosp` STRING COMMENT '门诊具体医院',
    `lock_up_betw_two_fu_code` STRING COMMENT '两次随访间关锁情况代码',
    `lock_up_betw_two_fu_name` STRING COMMENT '两次随访间关锁情况名称',
    `now_status_code` STRING COMMENT '目前功能状况职业和工作代码',
    `now_status_name` STRING COMMENT '目前功能状况职业和工作名称',
    `marriage_function_code` STRING COMMENT '目前功能状况婚姻职能代码',
    `marriage_function_name` STRING COMMENT '目前功能状况婚姻职能名称',
    `parental_functions_code` STRING COMMENT '目前功能状况父母职能代码',
    `parental_functions_name` STRING COMMENT '目前功能状况父母职能名称',
    `social_withdrawal_code` STRING COMMENT '目前功能状况社会性退缩代码',
    `social_withdrawal_name` STRING COMMENT '目前功能状况社会性退缩名称',
    `soci_acti_outs_the_home_code` STRING COMMENT '目前功能状况家庭外的社会活动代码',
    `soci_acti_outs_the_home_name` STRING COMMENT '目前功能状况家庭外的社会活动名称',
    `family_activ_code` STRING COMMENT '目前功能状况家庭内活动代码',
    `family_activ_name` STRING COMMENT '目前功能状况家庭内活动名称',
    `family_functions_code` STRING COMMENT '目前功能状况家庭职能代码',
    `family_functions_name` STRING COMMENT '目前功能状况家庭职能名称',
    `outs_inte_and_conc_code` STRING COMMENT '目前功能状况对外界兴趣和关心代码',
    `outs_inte_and_conc_name` STRING COMMENT '目前功能状况对外界兴趣和关心名称',
    `resp_and_plan_code` STRING COMMENT '目前功能状况责任心和计划性代码',
    `resp_and_plan_name` STRING COMMENT '目前功能状况责任心和计划性名称',
    `sdss_total` INT(16) COMMENT 'SDSS总分',
    `insi_eval_result_code` STRING COMMENT '自知力评价结果代码',
    `insi_eval_result_name` STRING COMMENT '自知力评价结果名称',
    `physical_disease` STRING COMMENT '躯体疾病',
    `auxi_insp_sign` STRING COMMENT '辅助检查标志',
    `asst_exam_rslt` STRING COMMENT '辅助检查结果',
    `drug_dys_mark` STRING COMMENT '药物不良反应标志',
    `trea_effe_cate_code` STRING COMMENT '既往治疗效果类别代码',
    `trea_effe_cate_name` STRING COMMENT '既往治疗效果类别名称',
    `ment_diso_fu_eval_code` STRING COMMENT '严重精神障碍患者随访评价结果代码',
    `ment_diso_fu_eval_name` STRING COMMENT '严重精神障碍患者随访评价结果名称',
    `take_drug_compliance_code` STRING COMMENT '用药依从性代码',
    `take_drug_compliance_name` STRING COMMENT '用药依从性名称',
    `speist_dor_ad_code` STRING COMMENT '专科医生意见代码',
    `speist_dor_ad_name` STRING COMMENT '专科医生意见名称',
    `emer_medi_trea_sign` STRING COMMENT '应急医疗处置标志',
    `referral_flag` STRING COMMENT '转诊标志',
    `referral_reason` STRING COMMENT '转诊原因',
    `referral_org_code` STRING COMMENT '转诊医疗机构名称',
    `referral_org_name` STRING COMMENT '转诊医疗机构科室名称',
    `treatment_opinion` STRING COMMENT '治疗意见',
    `fu_date` DATE COMMENT '本次随访日期',
    `ment_illn_fami_hist_sign` STRING COMMENT '精神疾患家族史标志',
    `fami_ment_illn_name` STRING COMMENT '家族性精神疾病名称',
    `major_psyc_symp_code` STRING COMMENT '既往主要精神症状代码',
    `major_psyc_symp_name` STRING COMMENT '既往主要精神症状名称',
    `fu_hospital_code` STRING COMMENT '随访医疗机构统一社会信用代码',
    `fu_hospital_name` STRING COMMENT '随访医疗机构名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `heav_ment_illn_flup_no` STRING COMMENT '重性精神疾病随访编号',
    `self_know_forc_code` STRING COMMENT '自知力代码',
    `self_know_forc_name` STRING COMMENT '自知力名称',
    `slep_info_code` STRING COMMENT '睡眠情况代码',
    `slep_info_name` STRING COMMENT '睡眠情况名称',
    `diet_info_code` STRING COMMENT '饮食情况代码',
    `diet_info_name` STRING COMMENT '饮食情况名称',
    `psn_life_cate_code` STRING COMMENT '个人生活料理代码',
    `psn_life_cate_name` STRING COMMENT '个人生活料理名称',
    `housw_code` STRING COMMENT '家务劳动代码',
    `housw_name` STRING COMMENT '家务劳动名称',
    `gena_lbr_wth_job_code` STRING COMMENT '生成劳动及工作代码',
    `gena_lbr_wth_job_name` STRING COMMENT '生成劳动及工作名称',
    `stdy_ablt_code` STRING COMMENT '学习能力代码',
    `stdy_ablt_name` STRING COMMENT '学习能力名称',
    `soca_inter_pers_code` STRING COMMENT '社会人际交往代码',
    `soca_inter_pers_name` STRING COMMENT '社会人际交往名称',
    `lab_exam` STRING COMMENT '实验室检查',
    `medication_compliance_code` STRING COMMENT '服药依从性代码',
    `medication_compliance_name` STRING COMMENT '服药依从性名称',
    `drug_dys_case` STRING COMMENT '药物不良反应情况',
    `drug_dys_dscr` STRING COMMENT '药物不良反应描述',
    `life_lbr_ablt` STRING COMMENT '生活劳动能力康复措施',
    `prfs_trai` STRING COMMENT '职业训练康复措施',
    `stdy_ablt` STRING COMMENT '学习能力康复措施',
    `soci_inter` STRING COMMENT '社会交往康复措施',
    `oth` STRING COMMENT '其他康复措施',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `flup_dr` INT(16) COMMENT '随访医生院内',
    `otp_dr_name` STRING COMMENT '外院医生姓名',
    `info_memo` STRING COMMENT '情况备注',
    `dange` STRING COMMENT '危险性',
    `ipt_info` STRING COMMENT '住院情况',
    `last_dscg_time` STRING COMMENT '末次出院时间',
    `lstvs_rea` STRING COMMENT '失访原因',
    `die_rea_body_dise` STRING COMMENT '死亡原因躯体疾病',
    `die_reason` STRING COMMENT '死亡原因',
    `death_time` TIMESTAMP COMMENT '死亡时间',
    `crt_flup_obj_code` STRING COMMENT '本次随访对象代码',
    `crt_flup_obj_name` STRING COMMENT '本次随访对象名称',
    `lab_exam_opt` STRING COMMENT '实验室检查选项(血常规血糖)',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='严重精神障碍患者随访表';

CREATE TABLE IF NOT EXISTS `ods_hcs_dme_special_form` (
    `rid` STRING COMMENT '数据唯一记录号',
    `elderly_id` STRING COMMENT '老年人专项表ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `card_no` STRING COMMENT '卡号',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `sign_date` DATE COMMENT '签名日期',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `living_capacity_code` STRING COMMENT '生活能力评价结果代码',
    `living_capacity_name` STRING COMMENT '生活能力评价结果名称',
    `lifestyle_support_code` STRING COMMENT '生活赡养方式代码',
    `lifestyle_support_name` STRING COMMENT '生活赡养方式名称',
    `eyesight_index_code` STRING COMMENT '视力指数情况代码',
    `eyesight_index_name` STRING COMMENT '视力指数情况名称',
    `health_problem_code` STRING COMMENT '现存主要健康问题代码',
    `health_problem_name` STRING COMMENT '现存主要健康问题名称',
    `incomplete_teeth_code` STRING COMMENT '牙齿残缺情况代码',
    `incomplete_teeth_name` STRING COMMENT '牙齿残缺情况名称',
    `addr_type_code` STRING COMMENT '常住地址类别代码',
    `addr_type_name` STRING COMMENT '常住地址类别名称',
    `per_addr_prov_code` STRING COMMENT '常住地省自治区直辖市代码',
    `per_addr_prov_name` STRING COMMENT '常住地省自治区直辖市名称',
    `per_addr_city_code` STRING COMMENT '常住地市地区代码',
    `per_addr_city_name` STRING COMMENT '常住地市地区名称',
    `per_addr_coty_code` STRING COMMENT '常住地县区代码',
    `per_addr_coty_name` STRING COMMENT '常住地县区名称',
    `per_addr_town_code` STRING COMMENT '常住地乡镇街道代码',
    `per_addr_town_name` STRING COMMENT '常住地乡镇街道名称',
    `per_addr_comm_code` STRING COMMENT '常住地居委会村代码',
    `per_addr_comm_name` STRING COMMENT '常住地居委会村名称',
    `per_addr_cotry_name` STRING COMMENT '常住地村街路弄等',
    `per_addr_housnum` STRING COMMENT '常住地门牌号(包括“室”)',
    `inquirer_date` DATE COMMENT '调查日期',
    `inquirer_no` STRING COMMENT '调查者工号',
    `inquirer_name` STRING COMMENT '调查者姓名',
    `care_code` STRING COMMENT '护理照顾情况代码',
    `care_name` STRING COMMENT '护理照顾情况名称',
    `inspect_find_gyne_type` STRING COMMENT '检查中是否发现妇科疾病',
    `inspect_gyne_code` STRING COMMENT '检查中发现的妇科疾病代码',
    `inspect_gyne_name` STRING COMMENT '检查中发现的妇科疾病名称',
    `almost_two_years_ge_type` STRING COMMENT '近两年内是否做过妇科检查',
    `gynecological_symptoms_code` STRING COMMENT '目前妇科症状情况代码',
    `gynecological_symptoms_name` STRING COMMENT '目前妇科症状情况名称',
    `medcasno` STRING COMMENT '病案号',
    `addr_districts_code` STRING COMMENT '行政区划代码',
    `addr_districts_name` STRING COMMENT '行政区划名称',
    `poscode` STRING COMMENT '邮政编码',
    `duty_doc_name` STRING COMMENT '责任医师姓名',
    `duty_doc_no` STRING COMMENT '责任医师工号',
    `symp_code` STRING COMMENT '症状(健康检查)代码',
    `health_symp_name` STRING COMMENT '症状(健康检查)名称',
    `check_dor_no` STRING COMMENT '检查测人员工号',
    `check_dor_name` STRING COMMENT '检查测人员姓名',
    `check_date` DATE COMMENT '检测日期',
    `have_fraction_code` STRING COMMENT '进餐(分数)代码',
    `have_fraction_name` STRING COMMENT '进餐(分数)名称',
    `wash_fraction_code` STRING COMMENT '梳洗分数代码',
    `wash_fraction_name` STRING COMMENT '梳洗分数名称',
    `dress_fraction_code` STRING COMMENT '穿衣分数代码',
    `dress_fraction_name` STRING COMMENT '穿衣分数名称',
    `toilet_fraction_code` STRING COMMENT '如厕分数代码',
    `toilet_fraction_name` STRING COMMENT '如厕分数名称',
    `motion_fraction_code` STRING COMMENT '活动分数代码',
    `motion_fraction_name` STRING COMMENT '活动分数名称',
    `total_score` STRING COMMENT '总分',
    `oxygen_uptake` INT(16) COMMENT '吸氧时长(h)',
    `builder_dor_no` STRING COMMENT '建表医生工号',
    `builder_dor_name` STRING COMMENT '建表医生姓名',
    `builder_dept_code` STRING COMMENT '建表科室代码',
    `builder_dept_name` STRING COMMENT '建表科室名称',
    `builder_org_name` STRING COMMENT '建表机构统一社会信用代码',
    `builder_org_code` STRING COMMENT '建表机构名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `jjsrqk` STRING COMMENT '经济收入情况',
    `bxqk` STRING COMMENT '报销情况',
    `mena_his` STRING COMMENT '月经史',
    `sys` INT(16) COMMENT '生育史',
    `wcqk` INT(16) COMMENT '卧床情况',
    `cjqk` INT(16) COMMENT '残疾情况',
    `mxbqk` STRING COMMENT '慢性病情况',
    `fbedreason` STRING COMMENT '家庭病床建立原因',
    `fbedcreatedata` DATE COMMENT '家庭病床建床日期',
    `fbedcanceldata` DATE COMMENT '家庭病床撤床日期',
    `coalslong` STRING COMMENT '家中煤火取暖时间(年)',
    `coalsmark` STRING COMMENT '家中煤火取暖标志',
    `family_smoke_mark` STRING COMMENT '家庭成员吸烟标志',
    `protectivemeasures_mark` STRING COMMENT '防护措施标志',
    `hazardworktime` STRING COMMENT '从事有危害因素职业时长',
    `danger_occup` STRING COMMENT '有危害因素的具体职业',
    `hazardworktype` STRING COMMENT '职业暴露危险因素种类',
    `occup_risk_name` STRING COMMENT '职业暴露危险因素名称',
    `occup_mark` STRING COMMENT '职业暴露标志',
    `cbehavior_code` STRING COMMENT '遵医行为代码',
    `cbehavior_name` STRING COMMENT '遵医行为名称',
    `mind_info_code` STRING COMMENT '心理状态代码',
    `mind_info_name` STRING COMMENT '心理状态名称',
    `stop_drink_age` INT(16) COMMENT '戒酒年龄',
    `stop_drink_mark` STRING COMMENT '戒酒标志',
    `drunkenness_mark` STRING COMMENT '醉酒标志',
    `start_drink_age` INT(16) COMMENT '开始饮酒年龄',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `stop_smoke_year` STRING COMMENT '戒烟年龄(岁)',
    `start_smoke_age` INT(16) COMMENT '开始吸烟年龄',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `weeksport` INT(16) COMMENT '周运动次数',
    `holdonsport` INT(16) COMMENT '坚持运动时长',
    `sporttime` STRING COMMENT '运动时间',
    `sportfrequency_code` STRING COMMENT '运动频率类别代码',
    `sportfrequency_name` STRING COMMENT '运动频率类别名称',
    `sportdesc` STRING COMMENT '运动方式说明',
    `hosp_intl_no` STRING COMMENT '医院内部编号',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='老年人专项表';

CREATE TABLE IF NOT EXISTS `ods_hcs_dme_hospital_history` (
    `rid` STRING COMMENT '数据唯一记录号',
    `ref_no` STRING COMMENT '编号',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `elderly_id` STRING COMMENT '老年人专项表ID',
    `admission_date` DATE COMMENT '入院日期',
    `leave_date` DATE COMMENT '出院日期',
    `reason` STRING COMMENT '住院原因',
    `uscn` STRING COMMENT '住院医疗机构名称',
    `inhosp_org_code` STRING COMMENT '住院机构统一社会信用代码',
    `medcasno` STRING COMMENT '病案号',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `departments` STRING COMMENT '科别',
    `diagnosis` STRING COMMENT '诊断',
    `surgery` STRING COMMENT '手术',
    `result` STRING COMMENT '结果值',
    `hl_phys_exam_cnt` STRING COMMENT '健康体检次数',
    `df_id` STRING COMMENT '居民编号',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='老年人住院史';

CREATE TABLE IF NOT EXISTS `ods_hcs_dme_followup_form` (
    `rid` STRING COMMENT '数据唯一记录号',
    `fu_id` STRING COMMENT '老年人随访记录ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `elderly_id` STRING COMMENT '老年人专项表ID',
    `fu_no` STRING COMMENT '随访编号',
    `visit_date` DATE COMMENT '随访日期',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `outcome_state_code` STRING COMMENT '转归状态代码',
    `outcome_state_name` STRING COMMENT '转归状态名称',
    `lstvs_rea` STRING COMMENT '失访原因',
    `sympt_code` STRING COMMENT '症状代码标准',
    `sympt_name` STRING COMMENT '症状名称',
    `psychological_guidance_code` STRING COMMENT '心理状态与指导代码',
    `psychological_guidance_name` STRING COMMENT '心理状态与指导名称',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `smok_day` INT(16) COMMENT '日吸烟量支',
    `stop_smoke_year` STRING COMMENT '戒烟年龄(岁)',
    `smoke_time` DATE COMMENT '戒烟日期',
    `drnk_day` INT(16) COMMENT '日饮酒量ml',
    `stop_drink_time` DATE COMMENT '戒酒日期',
    `stop_drink_year` INT(16) COMMENT '戒酒年龄(岁)',
    `sportdesc` STRING COMMENT '运动方式说明',
    `sportfrequency_code` STRING COMMENT '运动频率类别代码',
    `sportfrequency_name` STRING COMMENT '运动频率类别名称',
    `sport_time` INT(16) COMMENT '运动时间(min)',
    `insist_sport_year` INT(16) COMMENT '坚持运动时长(月)',
    `weeksport` INT(16) COMMENT '周运动次数',
    `psycho_adjust_res_code` STRING COMMENT '心理调整评价结果代码',
    `psycho_adjust_res_name` STRING COMMENT '心理调整评价结果名称',
    `chd_prevention` STRING COMMENT '冠心病预防情况',
    `osteoporosis_prevention` STRING COMMENT '骨质疏松预防情况',
    `fu_advice` STRING COMMENT '随访建议',
    `next_visit_goal` STRING COMMENT '下次随访目标',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fu_doc_name` STRING COMMENT '随访医生姓名',
    `fu_doc_no` STRING COMMENT '随访医生工号',
    `fu_diet_type_code` STRING COMMENT '随访饮食合理性评价类别代码',
    `fu_diet_type_name` STRING COMMENT '随访饮食合理性评价类别名称',
    `fu_compliance_res_code` STRING COMMENT '随访遵医行为评价结果代码',
    `fu_compliance_res_name` STRING COMMENT '随访遵医行为评价结果名称',
    `fu_psyc_guidance_dscr` STRING COMMENT '随访心理指导详细描述',
    `medication_compliance_code` STRING COMMENT '服药依从性代码',
    `medication_compliance_name` STRING COMMENT '服药依从性名称',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `systpre` INT(16) COMMENT '血压收缩压',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `heart_rate` INT(16) COMMENT '心率次min',
    `other1` STRING COMMENT '其他',
    `vaccination_status` STRING COMMENT '疫苗接种情况',
    `other_sympt_dscrr` STRING COMMENT '其他症状描述',
    `salt_intake_profile_code` STRING COMMENT '摄盐情况代码',
    `salt_intake_profile_name` STRING COMMENT '摄盐情况名称',
    `asst_exam` STRING COMMENT '辅助检查',
    `drug_dys_dscr` STRING COMMENT '药物不良反应描述',
    `fu_up_type_code` STRING COMMENT '随访分类代码',
    `fu_up_type_name` STRING COMMENT '随访分类名称',
    `referral_reason` STRING COMMENT '转诊原因',
    `accept_org_name` STRING COMMENT '转入医疗机构名称',
    `accept_dept_name` STRING COMMENT '转入机构科室名称',
    `habits_diet_code` STRING COMMENT '饮食习惯代码',
    `habits_diet_name` STRING COMMENT '饮食习惯名称',
    `smok_info_code` STRING COMMENT '吸烟状况代码',
    `smok_info_name` STRING COMMENT '吸烟状况名称',
    `drnk_frqu_code` STRING COMMENT '饮酒频率代码',
    `drnk_frqu_name` STRING COMMENT '饮酒频率名称',
    `drink_type_code` STRING COMMENT '饮酒种类代码',
    `drink_type_name` STRING COMMENT '饮酒种类名称',
    `drunkenness_mark` STRING COMMENT '醉酒标志',
    `stop_drink_mark` STRING COMMENT '戒酒标志',
    `occup_mark` STRING COMMENT '职业暴露标志',
    `occup_risk_name` STRING COMMENT '职业暴露危险因素名称',
    `occup_risk_type_code` STRING COMMENT '职业暴露危险因素种类代码',
    `occup_risk_type_name` STRING COMMENT '职业暴露危险因素种类名称',
    `danger_occup` STRING COMMENT '有危害因素的具体职业',
    `harm_occup_duration_year` INT(16) COMMENT '从事有危害因素职业时长(年)',
    `family_smoke_mark` STRING COMMENT '家庭成员吸烟标志',
    `fu_hospital_code` STRING COMMENT '随访医疗机构统一社会信用代码',
    `fu_hospital_name` STRING COMMENT '随访医疗机构名称',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `wt_emp` STRING COMMENT '体重单位',
    `smok_emp` STRING COMMENT '吸烟单位',
    `diet_info_code` STRING COMMENT '饮食情况代码',
    `diet_info_name` STRING COMMENT '饮食情况名称',
    `remark` STRING COMMENT '备注',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='老年人随访表';

CREATE TABLE IF NOT EXISTS `ods_hcs_cdb_report_card` (
    `rid` STRING COMMENT '数据唯一记录号',
    `rpot_card_id` STRING COMMENT '报卡编号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `certno` STRING COMMENT '身份证件号码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `adequ_age` INT(16) COMMENT '实足年龄',
    `age_unit_code` STRING COMMENT '年龄单位代码',
    `age_unit_name` STRING COMMENT '年龄单位名称',
    `brdy` DATE COMMENT '出生日期',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `resd_local_type_code` STRING COMMENT '患者户籍类型代码',
    `resd_local_type_name` STRING COMMENT '患者户籍类型名称',
    `resd_addr_prov_code` STRING COMMENT '户籍地省自治区直辖市代码',
    `resd_addr_prov_name` STRING COMMENT '户籍地省自治区直辖市名称',
    `resd_addr_city_code` STRING COMMENT '户籍地市地区代码',
    `resd_addr_city_name` STRING COMMENT '户籍地市地区名称',
    `resd_addr_coty_code` STRING COMMENT '户籍地县区代码',
    `resd_addr_coty_name` STRING COMMENT '户籍地县区名称',
    `resd_addr_subd_code` STRING COMMENT '户籍地乡镇街道代码',
    `resd_addr_subd_name` STRING COMMENT '户籍地乡镇街道名称',
    `resd_addr_comm_code` STRING COMMENT '户籍地居委会村代码',
    `resd_addr_comm_name` STRING COMMENT '户籍地居委会村名称',
    `resd_addr_cotry_name` STRING COMMENT '户籍地村街路弄等',
    `resd_addr_housnum` STRING COMMENT '户籍地门牌号包括“室”',
    `resd_addr` STRING COMMENT '户籍地址详细地址',
    `cur_addr_prov_code` STRING COMMENT '现居住地址省自治区直辖市代码',
    `cur_addr_prov_name` STRING COMMENT '现居住地址省自治区直辖市名称',
    `cur_addr_city_code` STRING COMMENT '现居住地市地区代码',
    `cur_addr_city_name` STRING COMMENT '现居住地市地区名称',
    `cur_addr_coty_code` STRING COMMENT '现居住地址县区代码',
    `cur_addr_coty_name` STRING COMMENT '现居住地址县区名称',
    `cur_addr_town_code` STRING COMMENT '现居住地址乡镇街道代码',
    `cur_addr_town_name` STRING COMMENT '现居住地址乡镇街道名称',
    `cur_addr_comm_code` STRING COMMENT '现居住地址居委会村代码',
    `cur_addr_comm_name` STRING COMMENT '现居住地址居委会村名称',
    `cur_addr_street` STRING COMMENT '现居住地址村街路弄等',
    `cur_addr_housnum` STRING COMMENT '现居住地门牌号(包括“室”)',
    `cur_addr` STRING COMMENT '现住地详细地址',
    `org_addr_prov_code` STRING COMMENT '工作单位地址省自治区直辖市代码',
    `org_addr_prov_name` STRING COMMENT '工作单位地址省自治区直辖市名称',
    `org_addr_city_code` STRING COMMENT '工作单位地址市地区代码',
    `org_addr_city_name` STRING COMMENT '工作单位地址市地区名称',
    `org_addr_coty_code` STRING COMMENT '工作单位地址县区代码',
    `org_addr_coty_name` STRING COMMENT '工作单位地址县区名称',
    `org_addr_town_code` STRING COMMENT '工作单位地址乡镇街道代码',
    `org_addr_town_name` STRING COMMENT '工作单位地址乡镇街道名称',
    `org_addr_steet` STRING COMMENT '工作单位地址村街路弄等',
    `org_addr_housnum` STRING COMMENT '工作单位地址门牌号(包括“室”)',
    `empr_tel` STRING COMMENT '工作单位电话号码',
    `empr_name` STRING COMMENT '工作单位名称',
    `psn_tel` STRING COMMENT '本人电话号码',
    `tel_home` STRING COMMENT '家人电话号码',
    `tel_contact` STRING COMMENT '联系人电话',
    `coner_name` STRING COMMENT '联系人监护人姓名',
    `addr_districts_code` STRING COMMENT '行政区划代码',
    `addr_districts_name` STRING COMMENT '行政区划名称',
    `otp_no` STRING COMMENT '门急诊号',
    `ipt_no` STRING COMMENT '住院号',
    `reg_no` STRING COMMENT '登记号',
    `detainee_mark` STRING COMMENT '羁押人员标志',
    `tb_hole_mark` STRING COMMENT '肺结核空洞',
    `like_tb_code` STRING COMMENT '疑似结核患者症状代码',
    `like_tb_name` STRING COMMENT '疑似结核患者症状名称',
    `first_symptom_date` DATE COMMENT '第一症状产生日期',
    `mdtrt_date` DATE COMMENT '本次就诊日期',
    `cnfm_date` DATE COMMENT '确诊日期',
    `start_date` DATE COMMENT '始治日期',
    `crt_check_date` DATE COMMENT '本次检查日期',
    `check_rpt_date` DATE COMMENT '本次检查报告日期',
    `discovery_mode_code` STRING COMMENT '发现方式代码',
    `discovery_mode_name` STRING COMMENT '发现方式名称',
    `sputum_exam_rslt_code` STRING COMMENT '痰检涂片结果代码',
    `sputum_exam_rslt_name` STRING COMMENT '痰检涂片结果名称',
    `sputum_not_exam_reason` STRING COMMENT '痰涂片未查原因',
    `culture_rslt_code` STRING COMMENT '培养结果代码',
    `culture_rslt_name` STRING COMMENT '培养结果名称',
    `culture_uncheck_rslt` STRING COMMENT '痰培养未查原因',
    `dr_rslt_code` STRING COMMENT 'X线检查结果代码',
    `dr_rslt_name` STRING COMMENT 'X线检查结果名称',
    `dr_rslt_des` STRING COMMENT 'X线检查结果描述',
    `ct_exam_rslt` STRING COMMENT 'CT检查结果',
    `liver_check_rslt_code` STRING COMMENT '肝功能检测结果代码',
    `liver_check_rslt_name` STRING COMMENT '肝功能检测结果名称',
    `fecal_routine_exam_rslt_code` STRING COMMENT '粪常规检测结果代码',
    `fecal_routine_exam_rslt_name` STRING COMMENT '粪常规检测结果名称',
    `urinalysis_exam_rslt_code` STRING COMMENT '尿常规检测结果代码',
    `urinalysis_exam_rslt_name` STRING COMMENT '尿常规检测结果名称',
    `blood_routine_check_rslt_code` STRING COMMENT '血常规检测结果代码',
    `blood_routine_check_rslt_name` STRING COMMENT '血常规检测结果名称',
    `hiv_anti_code` STRING COMMENT 'HIV抗体检测结果代码',
    `hiv_anti_name` STRING COMMENT 'HIV抗体检测结果名称',
    `accept_anti_trt_mark` STRING COMMENT '是否接受抗病毒治疗',
    `anti_trt_start_date` DATE COMMENT '抗病毒治疗开始日期',
    `accept_smz_tmp_trt_mark` STRING COMMENT '是否接受复方新诺明治疗',
    `sms_tmp_trt_start_date` DATE COMMENT '复方新诺明治疗开始日期',
    `accept_anti_tb_mark_code` STRING COMMENT '是否接受抗结核治疗代码',
    `accept_anti_tb_mark_name` STRING COMMENT '是否接受抗结核治疗名称',
    `lab_drug_alle_code` STRING COMMENT '药敏实验所用药物代码',
    `lab_drug_alle_name` STRING COMMENT '药敏实验所用药物名称',
    `lab_drug_alle_rslt_code` STRING COMMENT '药敏实验结果代码',
    `lab_drug_alle_rslt_name` STRING COMMENT '药敏实验结果名称',
    `detection_tb_flora_rslt_code` STRING COMMENT '结核菌群检测结果代码',
    `detection_tb_flora_rslt_name` STRING COMMENT '结核菌群检测结果名称',
    `tb_out_part_code` STRING COMMENT '肺外结核部位代码',
    `tb_out_part__name` STRING COMMENT '肺外结核部位名称',
    `diag_category_code` STRING COMMENT '诊断结核病类型代码',
    `diag_category_name` STRING COMMENT '诊断分型名称',
    `diag_category_rslt_code` STRING COMMENT '诊断结果代码',
    `diag_category_rslt_name` STRING COMMENT '诊断结果名称',
    `phthisical_mark` STRING COMMENT '肺结核标志',
    `hepatitis_mark` STRING COMMENT '肝炎标志',
    `tb_touch_his` STRING COMMENT '结核病接触史标志',
    `complicat_code` STRING COMMENT '合并症代码',
    `complicat_name` STRING COMMENT '合并症名称',
    `trt_category_code` STRING COMMENT '治疗分类代码',
    `trt_category_name` STRING COMMENT '治疗分类名称',
    `reg_category_code` STRING COMMENT '登记分类代码',
    `reg_category_name` STRING COMMENT '登记分类名称',
    `drug_resi_code` STRING COMMENT '耐药情况代码',
    `drug_resi_name` STRING COMMENT '耐药情况名称',
    `trt_plan_code` STRING COMMENT '治疗方案代码',
    `trt_plan_name` STRING COMMENT '治疗方案名称',
    `tb_chemotherapy_plan_code` STRING COMMENT '结核病患者化疗方案代码',
    `tb_chemotherapy_plan_name` STRING COMMENT '结核病患者化疗方案名称',
    `drug_dys_mark` STRING COMMENT '药物不良反应标志',
    `trt_stop_date` DATE COMMENT '停止治疗日期',
    `trt_stop_reason_code` STRING COMMENT '停止治疗原因代码',
    `trt_stop_reason_name` STRING COMMENT '停止治疗原因名称',
    `mang_way_code` STRING COMMENT '管理方式代码',
    `mang_way_name` STRING COMMENT '管理方式名称',
    `supervise_way_code` STRING COMMENT '督导方式代码',
    `supervise_way_name` STRING COMMENT '督导方式名称',
    `normal_use_drug_mark` STRING COMMENT '规律服药标志',
    `first_mang_org_name` STRING COMMENT '结核病首管理机构名称',
    `cur_mang_org_name` STRING COMMENT '结核病现管理机构名称',
    `first_mang_org_code` STRING COMMENT '首管理机构统一社会信用代码',
    `cur_org_code` STRING COMMENT '现管理机构统一社会信用代码',
    `diag_org_name` STRING COMMENT '诊断机构名称',
    `diage_dor_code` STRING COMMENT '诊断医师工号',
    `diage_dor_name` STRING COMMENT '诊断医师姓名',
    `first_trt_org_name` STRING COMMENT '首治疗机构名称',
    `cur_trt_org_name` STRING COMMENT '现治疗机构名称',
    `duty_dor_name` STRING COMMENT '责任医生姓名',
    `patient_ascr_code` STRING COMMENT '病人归属代码',
    `patient_ascr_name` STRING COMMENT '病人归属名称',
    `rpt_dor_name` STRING COMMENT '报告医生姓名',
    `report_doctor_no` STRING COMMENT '报告医生工号',
    `remark` STRING COMMENT '备注',
    `input_orgname` STRING COMMENT '录入机构',
    `input_date` DATE COMMENT '录入日期',
    `enter_dor_name` STRING COMMENT '录入医生姓名',
    `reg_dor_no` STRING COMMENT '录入医生工号',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `oxygentime` STRING COMMENT '吸氧时间(h)',
    `fbedreason` STRING COMMENT '家庭病床建立原因',
    `fbedcreatedata` DATE COMMENT '家庭病床建床日期',
    `fbedcanceldata` DATE COMMENT '家庭病床撤床日期',
    `coalslong` STRING COMMENT '家中煤火取暖时间(年)',
    `coalsmark` STRING COMMENT '家中煤火取暖标志',
    `family_smoke_mark` STRING COMMENT '家庭成员吸烟标志',
    `protectivemeasures_mark` STRING COMMENT '防护措施标志',
    `harm_occup_duration_year` INT(16) COMMENT '从事有危害因素职业时长(年)',
    `danger_occup` STRING COMMENT '有危害因素的具体职业',
    `occup_risk_type_code` STRING COMMENT '职业暴露危险因素种类代码',
    `occup_risk_name` STRING COMMENT '职业暴露危险因素名称',
    `occup_mark` STRING COMMENT '职业暴露标志',
    `cbehavior_code` STRING COMMENT '遵医行为代码',
    `cbehavior_name` STRING COMMENT '遵医行为名称',
    `mind_info_code` STRING COMMENT '心理状态代码',
    `mind_info_name` STRING COMMENT '心理状态名称',
    `whtr_our_hosp_cnfm` STRING COMMENT '是否本院确诊',
    `whtr_spec_mgt` STRING COMMENT '是否规范管理',
    `ctrl_whtr_satis` STRING COMMENT '控制是否满意',
    `info_psh_date` DATE COMMENT '信息推送日期',
    `whtr_alre_psh` STRING COMMENT '是否已推送',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='结核病报告卡';

CREATE TABLE IF NOT EXISTS `ods_hcs_cdb_first_home_visit` (
    `rid` STRING COMMENT '数据唯一记录号',
    `tuberculosis_card_id` STRING COMMENT '结核病随访卡ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `mana_card_id` STRING COMMENT '管理卡ID',
    `visit_date` DATE COMMENT '随访日期',
    `fu_org_code` STRING COMMENT '随访机构统一社会信用代码',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `patient_type_code` STRING COMMENT '患者类型代码',
    `patient_type_name` STRING COMMENT '患者类型名称',
    `sputum_exam_rslt_code` STRING COMMENT '痰检涂片结果代码',
    `sputum_exam_rslt_name` STRING COMMENT '痰检涂片结果名称',
    `drug_resi_code` STRING COMMENT '耐药情况代码',
    `drug_resi_name` STRING COMMENT '耐药情况名称',
    `symptom_signs_code` STRING COMMENT '症状及体征代码',
    `symptom_signs_name` STRING COMMENT '症状及体征名称',
    `other_symptom` STRING COMMENT '其他主要症状',
    `trt_plan_code` STRING COMMENT '治疗方案代码',
    `trt_plan_name` STRING COMMENT '治疗方案名称',
    `drug_use_way_code` STRING COMMENT '药物使用途径代码',
    `drug_use_way_name` STRING COMMENT '药物使用途径名称',
    `drug_form_code` STRING COMMENT '治疗药品剂型代码',
    `drug_form_name` STRING COMMENT '治疗药品剂型名称',
    `supervisorcategorycode` STRING COMMENT '督导人员类别代码',
    `supervisorcategoryname` STRING COMMENT '督导人员类别名称',
    `eval_other_type` STRING COMMENT '督导人员其他类别',
    `alon_of_live_room` STRING COMMENT '单独的居室',
    `vent_situ_code` STRING COMMENT '通风情况代码',
    `vent_situ_name` STRING COMMENT '通风情况名称',
    `cur_smoke_value` INT(16) COMMENT '目前吸烟量',
    `aim_smoke_value` INT(16) COMMENT '目标吸烟量',
    `cur_drink_value` INT(16) COMMENT '目前饮酒量ml',
    `aim_drink_value` INT(16) COMMENT '目标饮酒量ml',
    `take_medi_loc` STRING COMMENT '取药地点',
    `take_medi_time` STRING COMMENT '取药时间',
    `dose_reco_card_of_fillin` STRING COMMENT '服药记录卡的填写',
    `dose_mtd_wth_drug_stor` STRING COMMENT '服药方法及药品存放',
    `tuber_trt_cour_trea` STRING COMMENT '肺结核治疗疗程',
    `no_regu_dose_hazr` STRING COMMENT '不规律服药危害',
    `dose_new_defs_wth_dspo` STRING COMMENT '服药后不良反应及处理',
    `trt_cose_flup_sput` STRING COMMENT '治疗期间复诊查痰',
    `out_cose_how_adhe_dose` STRING COMMENT '外出期间如何坚持服药',
    `habi_wth_mnan` STRING COMMENT '生活习惯及注意事项',
    `clos_cont_the_exam` STRING COMMENT '密切接触者检查',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fill_date` DATE COMMENT '填报日期',
    `eval_dor_code` STRING COMMENT '评估医生工号',
    `eval_dor_name` STRING COMMENT '评估医生姓名',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `whtr_refl` STRING COMMENT '是否转诊',
    `stop_trt_time` STRING COMMENT '停止治疗时间',
    `trt_stop_reason_code` STRING COMMENT '停止治疗原因代码',
    `trt_stop_reason_name` STRING COMMENT '停止治疗原因名称',
    `eval_dr_sign` STRING COMMENT '评估医生签名',
    `otp_dr_sign` STRING COMMENT '外院医生签名',
    `on_ins_sub_turn_reg_time` STRING COMMENT '上机构下级转登记时间',
    `drug_name_code` STRING COMMENT '药物代码',
    `drug_name` STRING COMMENT '药品名称',
    `used` STRING COMMENT '用法',
    `dos` STRING COMMENT '用量',
    `medication_time` STRING COMMENT '用药时间',
    `dose_adhe` STRING COMMENT '服药依从性',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='结核病第一次入户随访服务记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_cdb_followup_service` (
    `rid` STRING COMMENT '数据唯一记录号',
    `tuberculosis_card_id` STRING COMMENT '结核病随访卡ID',
    `control_uscid` STRING COMMENT '管理机构统一社会信用代码',
    `mana_card_id` STRING COMMENT '管理卡ID',
    `visit_date` DATE COMMENT '随访日期',
    `treatment_seq_month` INT(16) COMMENT '治疗月序',
    `supervisorcategorycode` STRING COMMENT '督导人员类别代码',
    `supervisorcategoryname` STRING COMMENT '督导人员类别名称',
    `eval_other_type` STRING COMMENT '督导人员其他类别',
    `supervisoname` STRING COMMENT '督导人员姓名',
    `mang_way_code` STRING COMMENT '管理方式代码',
    `mang_way_name` STRING COMMENT '管理方式名称',
    `mang_way_change_mark` STRING COMMENT '管理方式变更标志',
    `change_date` DATE COMMENT '管理方式变更日期',
    `urine_spot_check_code` STRING COMMENT '尿液抽查代码',
    `urine_spot_check_name` STRING COMMENT '尿液抽查名称',
    `check_drug_mark_code` STRING COMMENT '核药代码',
    `check_drug_mark_name` STRING COMMENT '核药名称',
    `fill_card_mark_code` STRING COMMENT '记录卡填写代码',
    `fill_card_mark_name` STRING COMMENT '记录卡填写名称',
    `deal_opinion` STRING COMMENT '处理意见',
    `psn_name` STRING COMMENT '访视人姓名',
    `psn_comm_code` STRING COMMENT '访视社区代码',
    `psn_comm_name` STRING COMMENT '访视社区名称',
    `create_time` TIMESTAMP COMMENT '数据生成时间',
    `tel` STRING COMMENT '联系电话',
    `fu_org_code` STRING COMMENT '随访机构统一社会信用代码',
    `visit_way_code` STRING COMMENT '随访方式代码',
    `visit_way_name` STRING COMMENT '随访方式名称',
    `trt_plan_code` STRING COMMENT '治疗方案代码',
    `trt_plan_name` STRING COMMENT '治疗方案名称',
    `trt_stage_code` STRING COMMENT '治疗阶段代码',
    `trt_stage_name` STRING COMMENT '治疗阶段名称',
    `follow_dor_advice_mark` STRING COMMENT '遵医嘱服药标志',
    `recheck_mark` STRING COMMENT '按期复查标志',
    `drug_use_way_code` STRING COMMENT '药物使用途径代码',
    `drug_use_way_name` STRING COMMENT '药物使用途径名称',
    `drug_form_code` STRING COMMENT '治疗药品剂型代码',
    `drug_form_name` STRING COMMENT '治疗药品剂型名称',
    `omit_times` INT(16) COMMENT '漏服药次数',
    `supervise_way_code` STRING COMMENT '督导方式代码',
    `supervise_way_name` STRING COMMENT '督导方式名称',
    `drug_dys_mark` STRING COMMENT '药物不良反应标志',
    `defs_code` STRING COMMENT '药物不良反应代码',
    `medn_defs_name` STRING COMMENT '药物不良反应名称',
    `trt_change_mark` STRING COMMENT '更改治疗方案标志',
    `trt_change_reason_code` STRING COMMENT '更改治疗方案原因代码',
    `trt_change_reason_name` STRING COMMENT '更改治疗方案原因名称',
    `trt_change_date` DATE COMMENT '更改治疗方案日期',
    `new_trt_code` STRING COMMENT '更改后方案代码',
    `new_trt_name` STRING COMMENT '更改后方案名称',
    `new_use_dscr` STRING COMMENT '调整后的用药情况',
    `omit_times_per_month` INT(16) COMMENT '每月漏服次数',
    `omit_reason` STRING COMMENT '漏服原因',
    `symptom_signs_code` STRING COMMENT '症状及体征代码',
    `symptom_signs_name` STRING COMMENT '症状及体征名称',
    `other_symptom` STRING COMMENT '其他主要症状',
    `cur_smoke_value` INT(16) COMMENT '目前吸烟量',
    `aim_smoke_value` INT(16) COMMENT '目标吸烟量',
    `cur_drink_value` INT(16) COMMENT '目前饮酒量ml',
    `aim_drink_value` INT(16) COMMENT '目标饮酒量ml',
    `complicat_flag` STRING COMMENT '并发症或合并症标志',
    `complicat_dscr` STRING COMMENT '并发症或合并症情况',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `tprt` DECIMAL(20,4) COMMENT '体温℃',
    `breathing` INT(16) COMMENT '呼吸',
    `lung_respiratory_sound_code` STRING COMMENT '两肺呼吸音代码',
    `lung_respiratory_sound_name` STRING COMMENT '两肺呼吸音名称',
    `abn_sound_position_code` STRING COMMENT '异常呼吸音位置代码',
    `abn_sound_position_name` STRING COMMENT '异常呼吸音位置名称',
    `other_positive_names` STRING COMMENT '其他阳性体征',
    `smear_afs_code` STRING COMMENT '痰涂片抗酸染色代码',
    `smear_afs_name` STRING COMMENT '痰涂片抗酸染色名称',
    `sputum_not_exam_reason` STRING COMMENT '痰涂片未查原因',
    `sample_medium_rstl_code` STRING COMMENT '痰液结核菌培养代码',
    `sample_medium_rstl_name` STRING COMMENT '痰液结核菌培养名称',
    `drug_sensitivity_rstl_code` STRING COMMENT '结核菌药敏试验代码',
    `drug_sensitivity_rstl_name` STRING COMMENT '结核菌药敏试验名称',
    `culture_rslt_code` STRING COMMENT '培养结果代码',
    `culture_rslt_name` STRING COMMENT '培养结果名称',
    `culture_uncheck_rslt` STRING COMMENT '痰培养未查原因',
    `drug_fast_dscr` STRING COMMENT '耐药描述',
    `dr_abn_code` STRING COMMENT '胸部X线检查结果代码',
    `dr_abn_name` STRING COMMENT '胸部X线检查结果名称',
    `galt` STRING COMMENT 'ALT',
    `gast` STRING COMMENT 'AST',
    `gtbil` STRING COMMENT 'TBIL',
    `sscr` STRING COMMENT 'Scr',
    `sbun` STRING COMMENT 'BUN',
    `sua` STRING COMMENT 'UA',
    `xhb` STRING COMMENT 'Hb',
    `xrbc` STRING COMMENT 'RBC',
    `xwbc` STRING COMMENT 'WBC',
    `xplt` STRING COMMENT 'PLT',
    `xxc` STRING COMMENT '血沉',
    `other_abn_record` STRING COMMENT '其他异常检查结果记录',
    `conclude_code` STRING COMMENT '本次随访结论代码',
    `conclude_name` STRING COMMENT '本次随访结论名称',
    `referral_flag` STRING COMMENT '转诊标志',
    `accept_org_name` STRING COMMENT '转入医疗机构名称',
    `accept_depart_name` STRING COMMENT '转入科室名称',
    `referral_reason` STRING COMMENT '转诊原因',
    `in_date` DATE COMMENT '到位日期',
    `out_agency_name` STRING COMMENT '转出单位',
    `transferoutdate` DATE COMMENT '转出日期',
    `fu_rslt_2week` STRING COMMENT '转诊两周随访结果',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fill_date` DATE COMMENT '填报日期',
    `filler_no` STRING COMMENT '填表人工号',
    `fill_name` STRING COMMENT '填表人姓名',
    `flu_times` INT(16) COMMENT '第几次随访',
    `state` STRING COMMENT '修改标志',
    `data_rank` STRING COMMENT '密级',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `whtr_refl` STRING COMMENT '是否转诊',
    `stop_trt_time` STRING COMMENT '停止治疗时间',
    `trt_stop_reason_code` STRING COMMENT '停止治疗原因代码',
    `trt_stop_reason_name` STRING COMMENT '停止治疗原因名称',
    `eval_dr_sign` STRING COMMENT '评估医生签名',
    `otp_dr_sign` STRING COMMENT '外院医生签名',
    `on_ins_sub_turn_reg_time` STRING COMMENT '上机构下级转登记时间',
    `drug_name_code` STRING COMMENT '药物代码',
    `drug_name` STRING COMMENT '药品名称',
    `used` STRING COMMENT '用法',
    `dos` STRING COMMENT '用量',
    `medication_time` STRING COMMENT '用药时间',
    `dose_adhe` STRING COMMENT '服药依从性',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='结核病随访服务记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_basic_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `woman_id` STRING COMMENT '妇女标识符ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `ntly_code` STRING COMMENT '国籍代码',
    `ntly_name` STRING COMMENT '国籍名称',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `mrg_stas_code` STRING COMMENT '婚姻状况代码',
    `mrg_stas_name` STRING COMMENT '婚姻状况名称',
    `resd_prov_code` STRING COMMENT '户籍地址省自治区直辖市代码',
    `resd_prov_name` STRING COMMENT '户籍地址省自治区直辖市名称',
    `resd_city_code` STRING COMMENT '户籍地址市地区代码',
    `resd_city_name` STRING COMMENT '户籍地址市地区名称',
    `resd_coty_code` STRING COMMENT '户籍地址县区代码',
    `resd_coty_name` STRING COMMENT '户籍地址县区名称',
    `resd_town_code` STRING COMMENT '户籍地址乡镇街道代码',
    `resd_town_name` STRING COMMENT '户籍地址乡镇街道名称',
    `resd_addr_comm_code` STRING COMMENT '户籍地居委会村代码',
    `resd_addr_comm_name` STRING COMMENT '户籍地居委会村名称',
    `resd_cotry_name` STRING COMMENT '户籍地址村街路弄等',
    `resd_addr_housnum` STRING COMMENT '户籍地门牌号包括“室”',
    `cur_addr_prov_code` STRING COMMENT '现居住地址省自治区直辖市代码',
    `cur_addr_prov_name` STRING COMMENT '现居住地址省自治区直辖市名称',
    `cur_addr_city_code` STRING COMMENT '现居住地市地区代码',
    `cur_addr_city_name` STRING COMMENT '现居住地市地区名称',
    `cur_addr_coty_code` STRING COMMENT '现居住地址县区代码',
    `cur_addr_coty_name` STRING COMMENT '现居住地址县区名称',
    `cur_addr_town_code` STRING COMMENT '现居住地址乡镇街道代码',
    `cur_addr_town_name` STRING COMMENT '现居住地址乡镇街道名称',
    `cur_addr_comm_code` STRING COMMENT '现居住地址居委会村代码',
    `cur_addr_comm_name` STRING COMMENT '现居住地址居委会村名称',
    `cur_addr_street` STRING COMMENT '现居住地址村街路弄等',
    `cur_addr_housnum` STRING COMMENT '现居住地门牌号(包括“室”)',
    `cur_addr_poscode` STRING COMMENT '现住地址邮政编码',
    `addr_districts_code` STRING COMMENT '行政区划代码',
    `addr_districts_name` STRING COMMENT '行政区划名称',
    `empr_name` STRING COMMENT '工作单位名称',
    `tel` STRING COMMENT '联系电话',
    `mobile` STRING COMMENT '手机号码',
    `past_dis_his` STRING COMMENT '既往疾病史',
    `his_breast_cancer_diag_date` DATE COMMENT '疾病史乳腺癌确诊日期',
    `his_breast_cancer_treatment` STRING COMMENT '疾病史乳腺癌治疗方法',
    `his_contact_bleed` STRING COMMENT '疾病史触血',
    `his_cervical_cancer_diag_date` DATE COMMENT '疾病史宫颈癌确诊日期',
    `his_cervical_cancer_treatment` STRING COMMENT '疾病史宫颈癌治疗方法',
    `vulva_disease_his` STRING COMMENT '妇科病史',
    `sex_bleeding_his` STRING COMMENT '性交出血史',
    `oprn_his` STRING COMMENT '手术史',
    `gynaecology_proc_his` STRING COMMENT '妇科手术史',
    `dise_now` STRING COMMENT '现病史',
    `family_hereditary_his` STRING COMMENT '家族遗传性疾病史',
    `pat_relate_code` STRING COMMENT '患者(家族遗传性疾病患者)与本人关系代码',
    `pat_relate_name` STRING COMMENT '患者(家族遗传性疾病患者)与本人关系名称',
    `breast_cancer_family_his` STRING COMMENT '乳腺癌家族史',
    `consanguine_mar_mark` STRING COMMENT '家族近亲婚配标志',
    `consanguine_relate_code` STRING COMMENT '家族近亲婚配者与本人关系代码',
    `consanguine_relate_name` STRING COMMENT '家族近亲婚配者与本人关系名称',
    `birth_control` STRING COMMENT '避孕史',
    `algs_his` STRING COMMENT '过敏史',
    `menarche_age` INT(16) COMMENT '初潮年龄岁',
    `term_birth_times` INT(16) COMMENT '足月产次数',
    `pre_birth_times` INT(16) COMMENT '早产次数次',
    `abortion_sum_cnt` INT(16) COMMENT '流产总次数',
    `prg_cnt` INT(16) COMMENT '孕次',
    `matn_cnt` INT(16) COMMENT '产次',
    `spontaneous_abortion_times` INT(16) COMMENT '自然流产次数',
    `induced_abortion_times` INT(16) COMMENT '人工流产次数',
    `vaginal_midwifery_times` INT(16) COMMENT '阴道助产次数',
    `caesar_times` INT(16) COMMENT '剖宫产次数',
    `dead_fetus_no` INT(16) COMMENT '死胎例数',
    `stillbirth_no` INT(16) COMMENT '死产例数',
    `puerperium_infected` STRING COMMENT '产褥感染',
    `birth_defect_no` INT(16) COMMENT '出生缺陷儿例数',
    `pregnancy_hp` STRING COMMENT '妊娠高血压',
    `pregnancy_dm` STRING COMMENT '妊娠糖尿病',
    `pregnancy_dm_other_com` STRING COMMENT '妊娠糖尿病其他合并症',
    `history_of_macrosomia` STRING COMMENT '巨大儿产史',
    `last_gest_end_date` DATE COMMENT '末次妊娠终止日期',
    `last_gest_end_code` STRING COMMENT '末次妊娠终止方式代码',
    `last_gest_end_name` STRING COMMENT '末次妊娠终止方式名称',
    `last_deliver_date` DATE COMMENT '末次分娩日期',
    `last_deliver_way_code` STRING COMMENT '末次分娩方式代码',
    `last_deliver_way_name` STRING COMMENT '末次分娩方式名称',
    `birth_girl_no` INT(16) COMMENT '生育女数',
    `birth_boy_no` INT(16) COMMENT '生育子数',
    `children_genetic_diseases` STRING COMMENT '子女患遗传性疾病情况',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `manl_no` STRING COMMENT '手册编号',
    `ssc_no` STRING COMMENT '社保卡号',
    `filed_emp` STRING COMMENT '建档单位',
    `manl_stas_code` STRING COMMENT '手册状态代码',
    `manl_stas_name` STRING COMMENT '手册状态名称',
    `matn_stas_code` STRING COMMENT '孕妇状态代码',
    `matn_stas_name` STRING COMMENT '孕妇状态名称',
    `whtr_hrisk` STRING COMMENT '是否高危',
    `inhosp_no` STRING COMMENT '院内编号',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `whtr_brth` STRING COMMENT '是否分娩',
    `whtr_misc` STRING COMMENT '是否流产',
    `appf_sco_10_m` STRING COMMENT 'appf评分10分钟',
    `postp_recu_plc_modi_flag` STRING COMMENT '产后修养地修改标志',
    `mthr_age` INT(16) COMMENT '母亲年龄',
    `fthr_age` INT(16) COMMENT '父亲年龄',
    `whtr_sing_paren` STRING COMMENT '是否单亲',
    `brth_rcd_dld_mark` STRING COMMENT '分娩记录下载标记',
    `fst_flup_dld_mark` STRING COMMENT '首次随访下载标记',
    `two_to_fifth_flup_dld_mark` STRING COMMENT '二到五次随访下载标记',
    `postp_42_days_visu_dld_mark` STRING COMMENT '产后42天访视下载标记',
    `tel_stas` STRING COMMENT '电话状态',
    `next_follo_up_date` DATE COMMENT '下次追访日期',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='妇女基本情况信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_prenatal_extend_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `antenatal_id` STRING COMMENT '产前信息ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `pregnant_card_id` STRING COMMENT '孕卡ID',
    `exam_date` DATE COMMENT '检查日期',
    `otp_no` STRING COMMENT '门急诊号',
    `ipt_no` STRING COMMENT '住院号',
    `manl_sys_no` STRING COMMENT '手册系统编号',
    `adequ_age` INT(16) COMMENT '实足年龄',
    `husb_name` STRING COMMENT '丈夫姓名',
    `husb_age` DECIMAL(20,4) COMMENT '丈夫年龄',
    `husb_job_emp` STRING COMMENT '丈夫工作单位',
    `hus_health_code` STRING COMMENT '丈夫健康情况代码',
    `hus_health_name` STRING COMMENT '丈夫健康情况名称',
    `mrg_his_last_mena` DATE COMMENT '婚姻史：末次月经',
    `mrg_his_age_marr` DECIMAL(20,4) COMMENT '婚姻史：结婚年龄',
    `mrg_his_affi_mrg` STRING COMMENT '婚姻史：亲缘结婚',
    `mrg_his_mena_his` STRING COMMENT '婚姻史：月经史',
    `mrg_his_edc` DATE COMMENT '婚姻史：预产期',
    `mrg_his_preg_time` DECIMAL(20,4) COMMENT '婚姻史：孕次',
    `mrg_his_fetl_birt_vagi_brth` DECIMAL(20,4) COMMENT '婚姻史：产次：阴道分娩',
    `mrg_his_fetl_birt_cesr_sect` DECIMAL(20,4) COMMENT '婚姻史：产次：剖宫产',
    `hist_cur_preg_prey_react` STRING COMMENT '现孕史妊娠反应',
    `curpreg_fsm_code` STRING COMMENT '现孕史初感胎动代码',
    `curpreg_fsm_name` STRING COMMENT '现孕史初感胎动名称',
    `hist_cur_preg_seve_vomi` STRING COMMENT '现孕史：剧吐',
    `hist_cur_preg_vagi_blee` STRING COMMENT '现孕史：阴道出血',
    `hist_cur_preg_feve` STRING COMMENT '现孕史：发热',
    `hist_cur_preg_alg` STRING COMMENT '现孕史：过敏',
    `hist_cur_preg_dose` STRING COMMENT '现孕史：服药',
    `hist_cur_preg_viri_infe` STRING COMMENT '现孕史：病毒感染',
    `hist_cur_preg_cont_harm_subs` STRING COMMENT '现孕史：接触有害物质',
    `curpreg_bcp` STRING COMMENT '现孕史：服避孕药',
    `hist_cur_preg_oth` STRING COMMENT '现孕史：其他：',
    `prey_his_first_chid_date` DATE COMMENT '妊娠史：第一胎日期',
    `preyhis_fcnm_code` STRING COMMENT '妊娠史第一胎情况n足月代码',
    `preyhis_fcnm_name` STRING COMMENT '妊娠史第一胎情况n足月名称',
    `prey_his_seco_birt_date` DATE COMMENT '妊娠史：第二胎日期',
    `prey_his_seco_birt_info` STRING COMMENT '妊娠史：第二胎情况',
    `prey_his_thrd_chid_date` DATE COMMENT '妊娠史：第三胎日期',
    `prey_his_thrd_chid_info` STRING COMMENT '妊娠史：第三胎情况',
    `prey_his_four_trim_date` DATE COMMENT '妊娠史：第四胎日期',
    `prey_his_four_trim_info` STRING COMMENT '妊娠史：第四胎情况',
    `past_hear` STRING COMMENT '既往史：心',
    `past_lung` STRING COMMENT '既往史：肺',
    `past_live` STRING COMMENT '既往史：肝',
    `past_his_kid` STRING COMMENT '既往史：肾',
    `past_his_high_bloo_pres` STRING COMMENT '既往史：高血压',
    `past_his_diab` STRING COMMENT '既往史：糖尿病',
    `past_his_hyper` STRING COMMENT '既往史：甲亢',
    `past_his_algs` STRING COMMENT '既往史：过敏史',
    `past_his_psych` STRING COMMENT '既往史：精神病',
    `past_his_hema` STRING COMMENT '既往史：血液病',
    `past_his_epile` STRING COMMENT '既往史：癫痫',
    `past_his_oprn_his` STRING COMMENT '既往史：手术史',
    `past_his_oth` STRING COMMENT '既往史：其他',
    `fmhis_in_pers` STRING COMMENT '家族史：本人(保存双胎信息)',
    `fmhis_love_ones` STRING COMMENT '家族史：爱人(保存双胎信息)',
    `phys_exam_bas_bloo_pres` STRING COMMENT '体检：基础血压',
    `phys_exam_bloo_pres` STRING COMMENT '体检：血压',
    `phys_exam_wt_ind` STRING COMMENT '体检：体重指数',
    `phys_exam_kid` STRING COMMENT '体检：肾',
    `phys_exam_spin_fors` STRING COMMENT '体检：脊柱四肢',
    `phys_exam_swel` STRING COMMENT '体检：浮肿',
    `phys_exam_tend_refl` STRING COMMENT '体检：腱反射',
    `phys_exam_vari_vein` STRING COMMENT '体检：静脉曲张',
    `phys_exam_oth` STRING COMMENT '体检：其他',
    `gyn_exam_vulv_code` STRING COMMENT '妇检外阴代码',
    `gyn_exam_vulv_name` STRING COMMENT '妇检外阴名称',
    `gyn_exam_vagi_code` STRING COMMENT '妇检阴道代码',
    `gyn_exam_vagi_name` STRING COMMENT '妇检阴道名称',
    `gyn_exam_cerv_code` STRING COMMENT '妇检宫颈代码',
    `gyn_exam_cerv_name` STRING COMMENT '妇检宫颈名称',
    `gyn_exam_uter_code` STRING COMMENT '妇检宫体代码',
    `gyn_exam_uter_name` STRING COMMENT '妇检宫体名称',
    `gyn_exam_att` STRING COMMENT '妇检：附件',
    `pelvic_iip` STRING COMMENT '产检：骨盘测量：骼棘间径',
    `pelvic_icp` STRING COMMENT '产检：骨盘测量：骼嵴间径',
    `pelvic_sfp` STRING COMMENT '产检：骨盘测量：骶耻外径',
    `pelvic_sip` STRING COMMENT '产检：骨盘测量：坐骨结节间径',
    `asst_exam_hemog` STRING COMMENT '辅助检查：血红蛋白',
    `asst_exam_urin_rout_urin_prot` STRING COMMENT '辅助检查：尿常规尿蛋白',
    `asst_exam_leuk_cnvl_vagi_secre` STRING COMMENT '辅助检查：白带常规阴道分泌物',
    `asst_exam_blotype_abo` STRING COMMENT '辅助检查：血型ABO',
    `asst_exam_hiv` STRING COMMENT '辅助检查：HIV',
    `asst_exam_hbsag` STRING COMMENT '辅助检查：HBsAg',
    `asst_exam_labr_old_scre` STRING COMMENT '辅助检查：产前筛查',
    `asst_exam_kidn_fun` STRING COMMENT '辅助检查：肾功',
    `asstexam_lft_gpt` STRING COMMENT '辅助检查：肝功血清谷丙转氨酶(UL)',
    `asst_exam_bmus` STRING COMMENT '辅助检查：B超',
    `asst_exam_electro` STRING COMMENT '辅助检查：心电图',
    `hcare_guid` STRING COMMENT '保健指导',
    `fstdiag_diag_g` STRING COMMENT '初诊诊断G',
    `fstdiag_diag_p` STRING COMMENT '初诊诊断P',
    `fstdiag_diag_week_womb_prey` STRING COMMENT '初诊诊断：周宫内妊娠',
    `fstdiag_diag_diag_cont` STRING COMMENT '初诊诊断：诊断内容',
    `admission_date` DATE COMMENT '入院日期',
    `chfcomp` STRING COMMENT '主诉',
    `adm_diag` STRING COMMENT '入院诊断描述',
    `dspo` STRING COMMENT '处理',
    `refl` STRING COMMENT '转诊',
    `exam_emp` STRING COMMENT '检查单位',
    `obst_otp_no` STRING COMMENT '产科门诊编号',
    `prey_his_fetts_one` STRING COMMENT '妊娠史：胎次一',
    `prey_his_fetts_2` STRING COMMENT '妊娠史：胎次二',
    `prey_his_fetts_thr` STRING COMMENT '妊娠史：胎次三',
    `prey_his_fetts_4` STRING COMMENT '妊娠史：胎次四',
    `illhis_inqer` STRING COMMENT '病史询问者',
    `exam` STRING COMMENT '检查者',
    `resd_status_code` STRING COMMENT '户口状态代码',
    `resd_status_name` STRING COMMENT '户口状态名称',
    `sign` STRING COMMENT '签名',
    `exam_unit_name` STRING COMMENT '检查单位名称',
    `exam_dr` STRING COMMENT '检查医生',
    `exam_dr_name` STRING COMMENT '检查医生名称',
    `asst_exam_blotype_rh` STRING COMMENT '辅助检查：血型Rh',
    `sgot_value` INT(16) COMMENT '血清谷草转氨酶UL',
    `blo_prot` STRING COMMENT '血蛋白(gL)',
    `dbil_value` DECIMAL(20,4) COMMENT '总胆红素(μmolL)',
    `comb_bili` STRING COMMENT '结合胆红素(umolL)',
    `blo_urea_nitr` STRING COMMENT '血尿素氮(mmolL)',
    `fill_form_dr` STRING COMMENT '填表医生',
    `last_edit` STRING COMMENT '最后编辑者',
    `last_edit_time` DATE COMMENT '最后编辑时间',
    `phys_exam_live` STRING COMMENT '体检：肝',
    `fill_form_geso` INT(16) COMMENT '填表孕周',
    `fill_form_geso_days` INT(16) COMMENT '填表孕周天数',
    `hrisk_fac` STRING COMMENT '高危因素',
    `hrisk_fac_no` STRING COMMENT '高危因素编号',
    `hrisk_sco` INT(16) COMMENT '高危评分',
    `asst_exam_urin_rout_urin_gluc` STRING COMMENT '辅助检查：尿常规尿糖',
    `asst_exam_urin_rout_urin_keto` STRING COMMENT '辅助检查：尿常规尿酮体',
    `asstexam_urrout_occultblood` STRING COMMENT '辅助检查：尿常规尿潜血',
    `asst_exam_urin_rout_oth` STRING COMMENT '辅助检查：尿常规其他',
    `asst_exam_leuk_cnvl_vagi_clea` STRING COMMENT '辅助检查：白带常规阴道清洁度',
    `fmhis_high_bloo_pres` STRING COMMENT '家族史高血压',
    `fmhis_diab` STRING COMMENT '家族史糖尿病',
    `fmhis_hered_dise` STRING COMMENT '家族史遗传病',
    `fmhis_psych` STRING COMMENT '家族史精神病',
    `fmhis_demen` STRING COMMENT '家族史痴呆',
    `fmhis_defo` STRING COMMENT '家族史畸形',
    `fmhis_oth` STRING COMMENT '家族史其他',
    `fmhis_high_bloo_pres_love` STRING COMMENT '家族史高血压(爱人)',
    `fmhis_diab_love` STRING COMMENT '家族史糖尿病(爱人)',
    `fmhis_hered_dise_love` STRING COMMENT '家族史遗传病(爱人)',
    `fmhis_psych_love` STRING COMMENT '家族史精神病(爱人)',
    `fmhis_demen_love` STRING COMMENT '家族史痴呆(爱人)',
    `fmhis_defo_love` STRING COMMENT '家族史畸形(爱人)',
    `fmhis_oth_love` STRING COMMENT '家族史其他(爱人)',
    `gyn_exam_uter_heig` STRING COMMENT '妇检：宫高',
    `asst_exam_hepa_c` STRING COMMENT '辅助检查：丙肝',
    `labr_old_scre_18_tris` STRING COMMENT '产前筛查18三体',
    `labr_old_scre_21_tris` STRING COMMENT '产前筛查21三体',
    `labr_old_scre_neur_tube_defe` STRING COMMENT '产前筛查神经管缺陷',
    `prey_his_fetts_five` STRING COMMENT '妊娠史：胎次五',
    `prey_his_fifth_fets_date` DATE COMMENT '妊娠史：第五胎日期',
    `prey_his_fifth_fets_info` STRING COMMENT '妊娠史：第五胎情况',
    `lynn_cocc` DATE COMMENT '琳球菌',
    `crter` STRING COMMENT '创建者',
    `crte_date` DATE COMMENT '创建日期',
    `husb_cert_no` STRING COMMENT '丈夫身份证件号码',
    `husb_cert_type_code` STRING COMMENT '丈夫身份证件类别代码',
    `husb_cert_type_name` STRING COMMENT '丈夫身份证件类别名称',
    `fetal_move` STRING COMMENT '胎动',
    `obst_exam_bott_uter_hgt` INT(16) COMMENT '产科检查：宫底高度cm',
    `obst_exam_abde` INT(16) COMMENT '产科检查：腹围cm',
    `first_exp_hwb` STRING COMMENT '先露头腰臀',
    `obst_exam_fetl_posi` STRING COMMENT '产科检查：胎位',
    `dropsy` STRING COMMENT '浮肿',
    `err_addr_modi_hosp` STRING COMMENT '错误地址修正医院(外院)',
    `err_addr_modi_dr_id` STRING COMMENT '错误地址修正医生ID',
    `err_addr_modi_name` STRING COMMENT '错误地址修正姓名',
    `err_addr_modi_time` DATE COMMENT '错误地址修改时间',
    `tel` STRING COMMENT '联系电话',
    `whtr_notc` STRING COMMENT '是否通知',
    `asst_exam_sug_scre` STRING COMMENT '辅助检查：糖筛查',
    `asst_exam_empt_stom_bloo_gluc` STRING COMMENT '辅助检查：空腹血糖',
    `last_mena_whtr_bmus_proj` STRING COMMENT '末次月经是否B超推算',
    `whtr_intact_mark` STRING COMMENT '是否完整标识',
    `hrisk_fac_2` STRING COMMENT '高危因素2',
    `hrisk_fac_3` STRING COMMENT '高危因素3',
    `hrisk_fac_no_2` STRING COMMENT '高危因素编号2',
    `hrisk_fac_no_3` STRING COMMENT '高危因素编号3',
    `hrisk_sco_2` INT(16) COMMENT '高危评分2',
    `high_crit_preg_week_2` INT(16) COMMENT '高危孕周2',
    `hrisk_sco_3` INT(16) COMMENT '高危评分3',
    `high_crit_preg_week_3` INT(16) COMMENT '高危孕周3',
    `asstexam_br_wbc_count` INT(16) COMMENT '辅助检查：血常规白细胞计数值',
    `asstexam_br_plateletcount` INT(16) COMMENT '辅助检查：血常规血小板计数值',
    `asst_exam_bloo_rout_oth` STRING COMMENT '辅助检查：血常规其他',
    `asstexam_hbsag` STRING COMMENT '辅助检查：乙型肝炎五项乙型肝炎表面抗原',
    `asstexam_hbsab` STRING COMMENT '辅助检查：乙型肝炎五项乙型肝炎表面抗体',
    `asstexam_hbeag` STRING COMMENT '辅助检查：乙型肝炎五项乙型肝炎e抗原',
    `asstexam_hbeab` STRING COMMENT '辅助检查：乙型肝炎五项乙型肝炎e抗体',
    `asstexam_hbcab` STRING COMMENT '辅助检查：乙型肝炎五项乙型肝炎核心抗体',
    `husb_brdy` DATE COMMENT '丈夫出生日期',
    `prey_his_memo` STRING COMMENT '妊娠史备注',
    `past_his_anem` STRING COMMENT '既往史贫血',
    `gyn_oprn_his_flag` STRING COMMENT '妇科手术史标志',
    `gynaecology_proc_his` STRING COMMENT '妇科手术史',
    `psn_his_smok` STRING COMMENT '个人史吸烟',
    `whtr_drnk` STRING COMMENT '是否饮酒',
    `psn_his_taki_medn` STRING COMMENT '个人史服用药物',
    `psn_his_cont_toxi_harm_subs` STRING COMMENT '个人史接触有毒有害物质',
    `psn_his_cont_radi` STRING COMMENT '个人史接触放射线',
    `psn_his_oth` STRING COMMENT '个人史其他',
    `prey_copn_his` STRING COMMENT '妊娠合并症史',
    `prey_cop_his` STRING COMMENT '妊娠并发症史',
    `pre_preg_wt` DECIMAL(20,4) COMMENT '孕前体重(Kg)',
    `gyn_exam_refu_exam_flag` STRING COMMENT '妇检拒检标志',
    `vulv_refu_exam_flag` STRING COMMENT '外阴拒检标志',
    `vagi_refu_exam_flag` STRING COMMENT '阴道拒检标志',
    `cerv_refu_exam_flag` STRING COMMENT '宫颈拒检标志',
    `uter_refu_exam_flag` STRING COMMENT '宫体拒检标志',
    `att_refu_exam_flag` STRING COMMENT '附件拒检标志',
    `syph_sero_test_type_code` STRING COMMENT '梅毒血清学试验类别代码',
    `syph_sero_test_type_name` STRING COMMENT '梅毒血清学试验类别名称',
    `mthr_ntly_code` STRING COMMENT '母亲国籍代码',
    `mthr_ntly_name` STRING COMMENT '母亲国籍名称',
    `fthr_ntly_code` STRING COMMENT '父亲国籍代码',
    `fthr_ntly_name` STRING COMMENT '父亲国籍名称',
    `mother_cert_no` STRING COMMENT '母亲身份证件号码',
    `mother_psncert_type_code` STRING COMMENT '母亲身份证件类别代码',
    `mother_psncert_type_name` STRING COMMENT '母亲身份证件类别名称',
    `father_cert_no` STRING COMMENT '父亲身份证件号码',
    `father_cert_type_code` STRING COMMENT '父亲身份证件类别代码',
    `father_cert_type_name` STRING COMMENT '父亲身份证件类别名称',
    `add_flag` STRING COMMENT '补录标志(0否1是)',
    `examination_date` DATE COMMENT '体检日期',
    `ssc_no` STRING COMMENT '社保卡号',
    `asstexam_urrout_wbc` STRING COMMENT '辅助检查：尿常规白细胞',
    `asstexam_urrout_noabn` STRING COMMENT '辅助检查：尿常规未见异常',
    `chfcomp_menop_week` STRING COMMENT '主诉停经几周',
    `chfcomp_symp` STRING COMMENT '主诉症状',
    `gyn_exam_oth` STRING COMMENT '妇科检查其他',
    `chfcomp_symp_oth` STRING COMMENT '主诉症状其他',
    `asstexam_bus_bpd` STRING COMMENT '辅助检查：B彩超双顶径(mm)',
    `asstexam_bus_headcirc` STRING COMMENT '辅助检查：B彩超头围',
    `asst_exam_b_colo_ultr_abde` STRING COMMENT '辅助检查：B彩超腹围',
    `asstexam_bus_amnioticfluid` STRING COMMENT '辅助检查：B彩超羊水',
    `asstexam_bus_fetalheart` STRING COMMENT '辅助检查：B彩超胎心',
    `asstexam_bus_placentamaturity` STRING COMMENT '辅助检查：B彩超胎盘成熟度',
    `asstexam_bus_placentaattach` STRING COMMENT '辅助检查：B彩超胎盘附于',
    `asstexam_bus_placcervdist` STRING COMMENT '辅助检查：B彩超胎盘下缘距子宫颈内口',
    `asstexam_bus_umbilicalflow` STRING COMMENT '辅助检查：B彩超脐血流',
    `asstexam_bus_uterusthick` STRING COMMENT '辅助检查：B彩超子宫下段厚度',
    `asst_exam_b_colo_ultr_oth` STRING COMMENT '辅助检查：B彩超其他',
    `asst_exam_b_colo_ultr_ccls` STRING COMMENT '辅助检查：B彩超结论',
    `asst_exam_thyr_dise_scre_ft4` STRING COMMENT '辅助检查：甲状腺疾病筛查FT4',
    `asst_exam_thyr_dise_scre_tsh` STRING COMMENT '辅助检查：甲状腺疾病筛查TSH',
    `asst_exam_thyr_dise_scre_tpoab` STRING COMMENT '辅助检查：甲状腺疾病筛查TPOAb',
    `asstexam_biofull_noabn` STRING COMMENT '辅助检查：生化全套未见明显异常',
    `asstexam_biofull_fbg` STRING COMMENT '辅助检查：生化全套空腹血糖',
    `asstexam_biofull_gpt` STRING COMMENT '辅助检查：生化全套谷丙转氨酶',
    `asstexam_biofull_got` STRING COMMENT '辅助检查：生化全套谷草转氨酶',
    `asstexam_biofull_tp` STRING COMMENT '辅助检查：生化全套总蛋白',
    `asst_exam_bioch_full_set_albu` STRING COMMENT '辅助检查：生化全套白蛋白',
    `asstexam_biofull_tba` STRING COMMENT '辅助检查：生化全套总胆汁酸',
    `asstexam_biofull_tbil` STRING COMMENT '辅助检查：生化全套总胆红素',
    `asst_exam_bioch_full_set_myon` STRING COMMENT '辅助检查：生化全套肌尿',
    `asstexam_biofull_bun` STRING COMMENT '辅助检查：生化全套尿素氮',
    `asstexam_biofull_ua` STRING COMMENT '辅助检查：生化全套尿酸',
    `asst_exam_bioch_full_set_ldh` STRING COMMENT '辅助检查：生化全套LDH',
    `asst_exam_bioch_full_set_oth` STRING COMMENT '辅助检查：生化全套其他',
    `asst_exam_rpr` STRING COMMENT '辅助检查：RPR',
    `asstexam_lf_fbg` STRING COMMENT '辅助检查：肝功=空腹血糖',
    `asst_exam_live_fun_oth` STRING COMMENT '辅助检查：肝功其他',
    `asst_exam_kidn_fun_oth` STRING COMMENT '辅助检查：肾功其他',
    `asst_exam_er_bioch` STRING COMMENT '辅助检查：急诊生化',
    `asst_exam_electro_un_abn` STRING COMMENT '辅助检查：心电图未异常',
    `asst_exam_coag_efcc` STRING COMMENT '辅助检查：凝血功能',
    `asstexam_bus_femurneck` STRING COMMENT '辅助检查：B彩超股骨颈',
    `asst_exam_refu_check` STRING COMMENT '辅助检查：拒查',
    `mthr_naty_code` STRING COMMENT '母亲民族代码',
    `mthr_naty_name` STRING COMMENT '母亲民族名称',
    `fthr_naty_code` STRING COMMENT '父亲民族代码',
    `fthr_naty_name` STRING COMMENT '父亲民族名称',
    `asst_exam_tppa` STRING COMMENT '辅助检查：TPPA',
    `whtr_mrg` STRING COMMENT '是否结婚',
    `hcare_guid_oth` STRING COMMENT '保健指导：其他',
    `asst_exam_oth` STRING COMMENT '辅助检查：其他',
    `asstexam_nipt_tris13` STRING COMMENT '辅助检查：无创基因检测13三体',
    `asstexam_nipt_tris21` STRING COMMENT '辅助检查：无创基因检测21三体',
    `asst_exam_refu_check_oth` STRING COMMENT '辅助检查：拒查其他',
    `asstexam_coag_neutpct` STRING COMMENT '辅助检查：凝血功能中性粒百分比',
    `asst_exam_coag_efcc_pt` STRING COMMENT '辅助检查：凝血功能PT',
    `asst_exam_coag_efcc_inr` STRING COMMENT '辅助检查：凝血功能INR',
    `asst_exam_coag_efcc_fg` STRING COMMENT '辅助检查：凝血功能FG',
    `asst_exam_coag_efcc_aptt` STRING COMMENT '辅助检查：凝血功能APTT',
    `asst_exam_coag_efcc_d_dime` STRING COMMENT '辅助检查：凝血功能D二聚体',
    `high_crit_preg_week_1` INT(16) COMMENT '高危孕周1',
    `hrisk_ordr_date_2` DATE COMMENT '高危预约日期2',
    `hrisk_ordr_date_3` DATE COMMENT '高危预约日期3',
    `bloo_rout_bilg_date` DATE COMMENT '血常规开单日期',
    `blotype_bilg_date` DATE COMMENT '血型开单日期',
    `urin_rout_bilg_date` DATE COMMENT '尿常规开单日期',
    `leuk_cnvl_bilg_date` DATE COMMENT '白带常规开单日期',
    `hepa_b_five_bilg_date` DATE COMMENT '乙型肝炎五项开单日期',
    `rpr_bilg_date` DATE COMMENT 'RPR开单日期',
    `tppa_bilg_date` DATE COMMENT 'TPPA开单日期',
    `hiv_bilg_date` DATE COMMENT 'HIV开单日期',
    `hepa_c_bilg_date` DATE COMMENT '丙肝开单日期',
    `hbsag_bilg_date` DATE COMMENT 'HBSAG开单日期',
    `labr_old_scre_bilg_date` DATE COMMENT '产前筛查开单日期',
    `ninve_gene_dect_bilg_date` DATE COMMENT '无创基因检测开单日期',
    `oggt_bilg_date` DATE COMMENT 'OGGT开单日期',
    `kidn_fun_bilg_date` DATE COMMENT '肾功开单日期',
    `live_fun_bilg_date` DATE COMMENT '肝功开单日期',
    `bioch_full_set_bilg_date` DATE COMMENT '生化全套开单日期',
    `b_colo_ultr_bilg_date` DATE COMMENT 'B彩超开单日期',
    `er_bioch_bilg_date` DATE COMMENT '急诊生化开单日期',
    `thyr_dise_scre_bilg_date` DATE COMMENT '甲状腺疾病筛查开单日期',
    `electro_bilg_date` DATE COMMENT '心电图开单日期',
    `coag_efcc_bilg_date` DATE COMMENT '凝血功能开单日期',
    `asst_exam_bioch_full_set_pot` STRING COMMENT '辅助检查：生化全套钾',
    `asst_exam_bioch_full_set_sodi` STRING COMMENT '辅助检查：生化全套钠',
    `asst_exam_bioch_full_set_chlo` STRING COMMENT '辅助检查：生化全套氯',
    `asst_exam_bioch_full_set_calc` STRING COMMENT '辅助检查：生化全套钙',
    `asst_exam_bioch_full_set_magn` STRING COMMENT '辅助检查：生化全套镁',
    `asst_exam_er_bioch_pot` STRING COMMENT '辅助检查：急诊生化钾',
    `asst_exam_er_bioch_sodi` STRING COMMENT '辅助检查：急诊生化钠',
    `asst_exam_er_bioch_chlo` STRING COMMENT '辅助检查：急诊生化氯',
    `asst_exam_er_bioch_calc` STRING COMMENT '辅助检查：急诊生化钙',
    `asst_exam_er_bioch_magn` STRING COMMENT '辅助检查：急诊生化镁',
    `asst_exam_er_bioch_totl_prot` STRING COMMENT '辅助检查：急诊生化总蛋白',
    `asst_exam_er_bioch_albu` STRING COMMENT '辅助检查：急诊生化白蛋白',
    `asst_exam_er_bioch_drt_bili` STRING COMMENT '辅助检查：急诊生化直接胆红素',
    `asst_exam_er_bioch_indi_bili` STRING COMMENT '辅助检查：急诊生化间接胆红素',
    `asstexam_er_gpt` STRING COMMENT '辅助检查：急诊生化谷丙转氨酶',
    `asstexam_er_got` STRING COMMENT '辅助检查：急诊生化谷草转氨酶',
    `asst_exam_er_bioch_bloo_gluc` STRING COMMENT '辅助检查：急诊生化血糖',
    `asst_exam_er_bioch_lacta_deam` STRING COMMENT '辅助检查：急诊生化乳酸脱氨酶',
    `asst_exam_er_bioch_urea` STRING COMMENT '辅助检查：急诊生化尿素',
    `asst_exam_er_bioch_creat` STRING COMMENT '辅助检查：急诊生化肌酐',
    `asst_exam_thyr_ft3` STRING COMMENT '辅助检查：甲状腺FT3',
    `asst_exam_coag_efcc_proth_time` STRING COMMENT '辅助检查：凝血功能凝血酶时间',
    `asstexam_lf_tba` STRING COMMENT '辅助检查：肝功总胆汁酸',
    `asst_exam_live_fun_totl_prot` STRING COMMENT '辅助检查：肝功总蛋白',
    `asst_exam_er_bioch_uric_acid` STRING COMMENT '辅助检查：急诊生化尿酸',
    `asst_exam_bloo_rout_lymph_prct` STRING COMMENT '辅助检查：血常规淋巴细胞百分比',
    `asstexam_br_mcv` STRING COMMENT '辅助检查：血常规平均红细胞体积',
    `asstexam_br_mch` STRING COMMENT '辅助检查：血常规平均RBC血红蛋白含量',
    `uter_oth` STRING COMMENT '宫体其他',
    `asst_exam_urin_rout_urobi` STRING COMMENT '辅助检查：尿常规尿胆原',
    `asst_exam_urin_rout_bili` STRING COMMENT '辅助检查：尿常规胆红素',
    `asst_exam_er_bioch_totl_chol` STRING COMMENT '辅助检查：急诊生化总胆固醇',
    `leuk_cnvl_mold` STRING COMMENT '白带常规：霉菌',
    `leuk_cnvl_tric` STRING COMMENT '白带常规：滴虫',
    `leuk_cnvl_bacte_vagi_dise_bv` STRING COMMENT '白带常规：细菌性阴道病BV',
    `leuk_cnvl_oth` STRING COMMENT '白带常规：其他',
    `leuk_cnvl_whtr_abn` STRING COMMENT '白带常规：是否异常',
    `asstexam_br_neutpct` STRING COMMENT '辅助检查：血常规中性粒百分比',
    `husb_domi` STRING COMMENT '丈夫户籍地',
    `to_be_diag` STRING COMMENT '拟诊',
    `retes_ordr_date` DATE COMMENT '复检预约日期',
    `retes_def_btn` STRING COMMENT '复检默认按钮',
    `phys_exam_def_btn` STRING COMMENT '体检默认按钮',
    `ogtt_oth` STRING COMMENT 'OGTT其他',
    `labr_old_scre_oth` STRING COMMENT '产前筛查其他',
    `aids_syph_b_cslt` STRING COMMENT '艾梅乙咨询',
    `glyc_hemog_bilg_date` DATE COMMENT '糖化血红蛋白开单日期',
    `asst_exam_glyc_hemog` STRING COMMENT '辅助检查：糖化血红蛋白',
    `inpt_the_name` STRING COMMENT '录入者姓名',
    `exam_operator_name` STRING COMMENT '检查者姓名',
    `fetus_umbil_bloo_flow_mnit_pi` STRING COMMENT '胎儿脐血流监测PI',
    `fetus_umbil_bloo_flow_mnit_ri` STRING COMMENT '胎儿脐血流监测RI',
    `fetus_umbflow_orderdate` DATE COMMENT '胎儿脐血流监测开单日期',
    `upld_flag` STRING COMMENT '上传标志',
    `prey_his_fetts_six` STRING COMMENT '妊娠史胎次六',
    `prey_his_fetts_seve` STRING COMMENT '妊娠史胎次七',
    `bloo_rout_exam_emp` STRING COMMENT '血常规检查单位',
    `bloo_gluc_mes` STRING COMMENT '血糖测定',
    `bloo_gluc_mes_bilg_date` DATE COMMENT '血糖测定开单日期',
    `anti_a_pote_bilg_date` DATE COMMENT '抗A效价开单日期',
    `anti_b_pote_bilg_date` DATE COMMENT '抗B效价开单日期',
    `anti_a_pote` STRING COMMENT '抗A效价',
    `anti_b_pote` STRING COMMENT '抗B效价',
    `amnio_flui_diag` STRING COMMENT '羊水诊断',
    `cerv_cyt_exam` STRING COMMENT '宫颈细胞学检查',
    `ultra_uterusthick` STRING COMMENT '超声测量子宫下段厚度',
    `ultra_measu_cerv_leng` STRING COMMENT '超声测量宫颈长度',
    `rapi_crp` STRING COMMENT '快速CRP',
    `medt_anem_gene_dect` STRING COMMENT '地中海贫血基因检测',
    `drt_reta_glon_test` STRING COMMENT '直接抗人球蛋白试验(Coombs′试验)',
    `fetus_mri` STRING COMMENT '胎儿核磁共振',
    `prey_react_mon` STRING COMMENT '妊娠反应_月1~121+~12+',
    `first_sens_fetl_mov_mon` STRING COMMENT '初感胎动_月1~121+~12+',
    `trust` STRING COMMENT 'TRUST',
    `trust_exam_emp` STRING COMMENT 'TRUST检查单位',
    `trust_bilg_date` DATE COMMENT 'TRUST开单日期',
    `elisa` STRING COMMENT 'ELISA',
    `elisa_exam_emp` STRING COMMENT 'ELISA检查单位',
    `elisa_bilg_date` DATE COMMENT 'ELISA开单日期',
    `rt` STRING COMMENT 'RT',
    `rt_exam_emp` STRING COMMENT 'RT检查单位',
    `rt_bilg_date` DATE COMMENT 'RT开单日期',
    `asst_exam_thyr_ft3_emp` STRING COMMENT '辅助检查：甲状腺FT3单位1pgml；2pmolL',
    `asst_exam_thyr_ft4_emp` STRING COMMENT '辅助检查：甲状腺FT4单位1pgml；2pmolL',
    `asst_exam_thyr_tsh_emp` STRING COMMENT '辅助检查：甲状腺tsh单位1uIUmL；2MiuL',
    `asstexam_bus_afi` STRING COMMENT '辅助检查：B彩超羊水指数',
    `asst_exam` STRING COMMENT '辅助检查',
    `syph_whtr_dect` STRING COMMENT '梅毒是否检测',
    `syph_whtr_ifet` STRING COMMENT '梅毒是否感染',
    `syph_whtr_trt` STRING COMMENT '梅毒是否治疗',
    `hepa_b_whtr_dect` STRING COMMENT '乙肝是否检测',
    `hepa_b_dect_rslt` STRING COMMENT '乙肝检测结果',
    `hiv_cnfm_date` DATE COMMENT 'HIV确诊日期',
    `hiv_dect_rslt` STRING COMMENT 'HIV检测结果',
    `hiv_whtr_dect` STRING COMMENT 'HIV是否检测',
    `scre_rslt_info_no_3` STRING COMMENT '筛查结果情况编号3',
    `eval_cont` STRING COMMENT '评估内容',
    `eval_cont_no` STRING COMMENT '评估内容编号',
    `eval_rslt_info_no` STRING COMMENT '评估结果情况编号',
    `eval_cont_2` STRING COMMENT '评估内容2',
    `eval_cont_3` STRING COMMENT '评估内容3',
    `eval_cont_no_2` STRING COMMENT '评估内容编号2',
    `eval_cont_no_3` STRING COMMENT '评估内容编号3',
    `eval_rslt_info_no_2` STRING COMMENT '评估结果情况编号2',
    `eval_rslt_info_no_3` STRING COMMENT '评估结果情况编号3',
    `scre_cont` STRING COMMENT '筛查内容',
    `scre_cont_no` STRING COMMENT '筛查内容编号',
    `scre_rslt_info_no` STRING COMMENT '筛查结果情况编号',
    `scre_cont_2` STRING COMMENT '筛查内容2',
    `scre_cont_3` STRING COMMENT '筛查内容3',
    `scre_cont_no_2` STRING COMMENT '筛查内容编号2',
    `scre_cont_no_3` STRING COMMENT '筛查内容编号3',
    `scre_rslt_info_no_2` STRING COMMENT '筛查结果情况编号2',
    `rchk_flag` STRING COMMENT '复检标识',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='产前信息扩展表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_first_prenatal_visit` (
    `rid` STRING COMMENT '数据唯一记录号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `fu_no` STRING COMMENT '随访编号',
    `pregnant_card_id` STRING COMMENT '孕卡ID',
    `fu_org_code` STRING COMMENT '随访机构统一社会信用代码',
    `full_name` STRING COMMENT '姓名',
    `register_datetime` TIMESTAMP COMMENT '填表日期',
    `input_gest_weeks` INT(16) COMMENT '填表孕周d',
    `pregnancy_age` INT(16) COMMENT '孕妇年龄',
    `husb_name` STRING COMMENT '丈夫姓名',
    `husb_age` DECIMAL(20,4) COMMENT '丈夫年龄',
    `husband_tel_phone` STRING COMMENT '丈夫电话号码',
    `prg_cnt` INT(16) COMMENT '孕次',
    `deliver_matn_cnt` INT(16) COMMENT '阴道分娩产次',
    `pregnancy_matn_cnt` INT(16) COMMENT '剖宫产次',
    `last_menses_date_mark` STRING COMMENT '是否知道末次月经日期',
    `last_mena_date` DATE COMMENT '末次月经日期',
    `expect_deliver_date` DATE COMMENT '预产期',
    `past_history_code` STRING COMMENT '既往史代码',
    `past_history_name` STRING COMMENT '既往史名称',
    `family_his_code` STRING COMMENT '家族史代码',
    `family_his_name` STRING COMMENT '家族史名称',
    `per_his_code` STRING COMMENT '个人史代码',
    `per_his_name` STRING COMMENT '个人史名称',
    `gyn_oprn_his_flag` STRING COMMENT '妇科手术史标志',
    `deliver_his_code` STRING COMMENT '孕产史代码',
    `deliver_his_name` STRING COMMENT '孕产史名称',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `bmi` DECIMAL(20,4) COMMENT '体质指数',
    `sbp` INT(16) COMMENT '收缩压mmHg',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `heart_check_rslt_code` STRING COMMENT '心脏听诊检查结果代码',
    `heart_check_rslt_name` STRING COMMENT '心脏听诊检查结果名称',
    `heart_check_rslt_dscr` STRING COMMENT '心脏听诊检查结果异常描述',
    `lungs_check_rslt_code` STRING COMMENT '肺部听诊检查结果代码',
    `lungs_check_rslt_name` STRING COMMENT '肺部听诊检查结果名称',
    `lungs_check_rslt_dscr` STRING COMMENT '肺部听诊检查结果异常描述',
    `vulva_rslt_code` STRING COMMENT '外阴检查结果代码',
    `vulva_rslt_name` STRING COMMENT '外阴检查结果名称',
    `vulva_rslt_dscr` STRING COMMENT '外阴检查结果异常描述',
    `deliver_rslt_code` STRING COMMENT '阴道检查结果代码',
    `deliver_rslt_name` STRING COMMENT '阴道检查结果名称',
    `deliver_rslt_dscr` STRING COMMENT '阴道检查结果异常描述',
    `cervical_rslt_code` STRING COMMENT '宫颈检查结果代码',
    `cervical_rslt_name` STRING COMMENT '宫颈检查结果名称',
    `cervical_rslt_dscr` STRING COMMENT '宫颈检查结果异常描述',
    `uterine_rslt_code` STRING COMMENT '子宫检查结果代码',
    `uterine_rslt_name` STRING COMMENT '子宫检查结果名称',
    `uterine_rslt_dscr` STRING COMMENT '子宫检查结果异常描述',
    `adnexa_uteri_rslt_code` STRING COMMENT '附件检查结果代码',
    `adnexa_uteri_rslt_name` STRING COMMENT '附件检查结果名称',
    `adnexa_uteri_rslt_dscr` STRING COMMENT '附件检查结果异常描述',
    `hgb_value` INT(16) COMMENT '血红蛋白值gL',
    `wbc_value` DECIMAL(20,4) COMMENT '白细胞计数值',
    `platelet_value` INT(16) COMMENT '血小板计数值(GL)',
    `other_exam_res` STRING COMMENT '血常规其他检查结果',
    `urinary_protein` STRING COMMENT '尿蛋白',
    `uglu_quan_check_value` DECIMAL(20,4) COMMENT '尿糖定量检测(mmolL)',
    `urine_ket_code` STRING COMMENT '尿酮体定性检测结果代码',
    `urine_ket_name` STRING COMMENT '尿酮体定性检测结果名称',
    `urine_occult_blood_code` STRING COMMENT '尿潜血代码',
    `urine_occult_blood_name` STRING COMMENT '尿潜血名称',
    `urine_other_rslt` STRING COMMENT '尿常规其他检查结果',
    `blotype_abo_code` STRING COMMENT 'ABO血型代码',
    `blotype_abo_name` STRING COMMENT 'ABO血型名称',
    `blotype_rh_code` STRING COMMENT 'Rh血型代码',
    `blotype_rh_name` STRING COMMENT 'Rh血型名称',
    `serum_transa_value` DECIMAL(20,4) COMMENT '血糖mmolL',
    `serum_sgpt_value` INT(16) COMMENT '血清谷丙转氨酶值UL',
    `sgot_value` INT(16) COMMENT '血清谷草转氨酶UL',
    `tbi_value` STRING COMMENT '白蛋白',
    `dbil_value` DECIMAL(20,4) COMMENT '总胆红素(μmolL)',
    `scr_bilirubin_value` DECIMAL(20,4) COMMENT '结合胆红素',
    `scr_value` DECIMAL(20,4) COMMENT '血清肌酐检测值µmolL',
    `blood_urea_nitrogen_value` STRING COMMENT '血尿素',
    `vagina_secret_code` STRING COMMENT '阴道分泌物代码',
    `vagina_secret_name` STRING COMMENT '阴道分泌物名称',
    `vagina_clean_code` STRING COMMENT '阴道清洁度代码',
    `vagina_clean_name` STRING COMMENT '阴道清洁度名称',
    `hbsag_b_code` STRING COMMENT '乙型肝炎表面抗原代码',
    `hbsag_b_name` STRING COMMENT '乙型肝炎表面抗原名称',
    `hbsab_code` STRING COMMENT '乙型肝炎表面抗体代码',
    `hbsab_name` STRING COMMENT '乙型肝炎表面抗体名称',
    `hbeag_code` STRING COMMENT '乙型肝炎e抗原代码',
    `hbeag_name` STRING COMMENT '乙型肝炎e抗原名称',
    `hbeab_code` STRING COMMENT '乙型肝炎e抗体代码',
    `hbeab_name` STRING COMMENT '乙型肝炎e抗体名称',
    `hbab_code` STRING COMMENT '乙型肝炎核心抗体代码',
    `hbab_name` STRING COMMENT '乙型肝炎核心抗体名称',
    `vdrl_code` STRING COMMENT '梅毒血清学试验代码',
    `vdrl_name` STRING COMMENT '梅毒血清学试验名称',
    `hiv_anti_code` STRING COMMENT 'HIV抗体检测结果代码',
    `hiv_anti_name` STRING COMMENT 'HIV抗体检测结果名称',
    `bscan_exam_rslt` STRING COMMENT 'B超检查结果',
    `check_other_rslt` STRING COMMENT '辅助检查的其他结果',
    `overall_ability_code` STRING COMMENT '总体评估结果代码',
    `overall_ability_name` STRING COMMENT '总体评估结果名称',
    `overall_ability_dscr` STRING COMMENT '总体评估结果异常描述',
    `guidelines_code` STRING COMMENT '保健指导代码',
    `guidelines_name` STRING COMMENT '保健指导名称',
    `transfer_treatment` STRING COMMENT '有无转诊',
    `referral_reason` STRING COMMENT '转诊原因',
    `transfer_treatment_org_code` STRING COMMENT '转诊机构统一社会信用代码',
    `transfer_treatment_org_name` STRING COMMENT '转诊机构名称',
    `accept_dept_code` STRING COMMENT '转诊科室代码',
    `referral_dept_name` STRING COMMENT '转诊科室名称',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fu_doc_no` STRING COMMENT '随访医生工号',
    `fu_doc_name` STRING COMMENT '随访医生姓名',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `fill_form_geso_days` INT(16) COMMENT '填表孕周天数',
    `b_us_bpd` STRING COMMENT 'B彩超双顶径',
    `b_ultrasonic_conclusion` STRING COMMENT 'B彩超结论',
    `b_ultrasonic_other` STRING COMMENT 'B彩超其他',
    `head_circ` DECIMAL(20,4) COMMENT '头围cm',
    `femur` STRING COMMENT '股骨颈',
    `abde` DECIMAL(20,4) COMMENT '腹围cm',
    `amniotic_fluid` STRING COMMENT '羊水',
    `cardiac` STRING COMMENT '胎心',
    `placenta_maturity` STRING COMMENT '胎盘成熟度',
    `placenta_attached` STRING COMMENT '胎盘附于',
    `placenta_uterus_spacing` STRING COMMENT '胎盘下缘距子宫颈内口',
    `umbilical_blood_flow` STRING COMMENT '脐血流',
    `lower_uterus_thickness` STRING COMMENT '子宫下段厚度',
    `amniotic_fluid_index` STRING COMMENT '羊水指数',
    `induced_abortion_num` STRING COMMENT '人工流产',
    `overall_evaluation_remark` STRING COMMENT '总体评估为其他时描述',
    `health_guide_remark` STRING COMMENT '保健指导描述',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='第1次产前随访信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_prenatal_visits_2_5` (
    `rid` STRING COMMENT '数据唯一记录号',
    `fu_no` STRING COMMENT '随访编号',
    `pregnant_card_id` STRING COMMENT '孕卡ID',
    `fu_org_code` STRING COMMENT '随访机构统一社会信用代码',
    `fu_times` INT(16) COMMENT '随访次数',
    `full_name` STRING COMMENT '姓名',
    `urge_date` DATE COMMENT '随访日期督促日期',
    `geso_val` INT(16) COMMENT '孕周d',
    `chfcomp` STRING COMMENT '主诉',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `cervix_height` DECIMAL(20,4) COMMENT '宫底高度cm',
    `sbp` INT(16) COMMENT '收缩压mmHg',
    `dbp` INT(16) COMMENT '舒张压mmHg',
    `abde` DECIMAL(20,4) COMMENT '腹围cm',
    `fetalpositionstatus` STRING COMMENT '胎位情况',
    `fhr` INT(16) COMMENT '胎心率次min',
    `hgb_value` INT(16) COMMENT '血红蛋白值gL',
    `urinary_protein` STRING COMMENT '尿蛋白',
    `oth_asst_exam` STRING COMMENT '其他辅助检查',
    `fu_rslt_code` STRING COMMENT '随访结果分类代码',
    `fu_rslt_name` STRING COMMENT '随访结果分类名称',
    `fu_rslt_dscr` STRING COMMENT '随访结果分类异常描述',
    `guide_code` STRING COMMENT '指导方式代码',
    `guide_name` STRING COMMENT '指导方式名称',
    `transfer_treatment` STRING COMMENT '有无转诊',
    `referral_reason` STRING COMMENT '转诊原因',
    `transfer_treatment_org_code` STRING COMMENT '转诊机构统一社会信用代码',
    `transfer_treatment_org_name` STRING COMMENT '转诊机构名称',
    `accept_dept_code` STRING COMMENT '转诊科室代码',
    `referral_dept_name` STRING COMMENT '转诊科室名称',
    `next_follow_date` DATE COMMENT '下次随访日期',
    `fu_doc_no` STRING COMMENT '随访医生工号',
    `fu_doc_name` STRING COMMENT '随访医生姓名',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `subdi_mark_code` STRING COMMENT '次别标记代码',
    `subdi_mark_name` STRING COMMENT '次别标记名称',
    `geso` INT(16) COMMENT '孕周天',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `whtr_imp_exte_emp` STRING COMMENT '是否导入外单位',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='第2-5次产前随访信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_delivery_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `deliverid` STRING COMMENT '分娩记录ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `pregnant_card_id` STRING COMMENT '孕卡ID',
    `deliver_date` DATE COMMENT '分娩日期时间',
    `total_reproduct_min` INT(16) COMMENT '总产程时长min',
    `first_labor_min` INT(16) COMMENT '第1产程时长min',
    `second_labor_min` INT(16) COMMENT '第2产程时长min',
    `third_labor_min` INT(16) COMMENT '第3产程时长min',
    `contractions_start_time` DATE COMMENT '阵缩开始时间',
    `induce_way_code` STRING COMMENT '引产方式代码',
    `induce_way_name` STRING COMMENT '引产方式名称',
    `membranes_rupture_way_code` STRING COMMENT '胎膜破裂方式代码',
    `membranes_rupture_way_name` STRING COMMENT '胎膜破裂方式名称',
    `membranes_rupture_time` TIMESTAMP COMMENT '胎膜破裂时间',
    `fetus_delivery_time` DATE COMMENT '胎儿娩出时间',
    `placenta_delivery_time` DATE COMMENT '胎盘娩出日期时间',
    `uterus_full_open_time` TIMESTAMP COMMENT '宫口开全时间',
    `placenta_delivery_way_code` STRING COMMENT '胎盘娩出方式代码',
    `placenta_delivery_way_name` STRING COMMENT '胎盘娩出方式名称',
    `membranes_complete_case_flag` STRING COMMENT '胎膜完整情况标志',
    `deliver_preg_week` INT(16) COMMENT '分娩孕周d',
    `deliver_way_othdcr` STRING COMMENT '分娩方式其他描述',
    `deliver_way_code` STRING COMMENT '分娩方式代码',
    `deliver_way_name` STRING COMMENT '分娩方式名称',
    `total_bleeding_no` INT(16) COMMENT '总出血量ml',
    `birthing_bleeding_no` INT(16) COMMENT '产时出血量ml',
    `blood_reason` STRING COMMENT '出血原因',
    `after_birth_2h_bleeding_nu` INT(16) COMMENT '产后两小时出血量ml',
    `use_drug_dscr` STRING COMMENT '用药情况',
    `after_birth_30m_dscr` STRING COMMENT '产后半小时情况',
    `after_birth_60m_dscr` STRING COMMENT '产后一小时情况',
    `after_birth_90m_dscr` STRING COMMENT '产后一个半小时情况',
    `after_birth_120m_dscr` STRING COMMENT '产后两小时情况',
    `urine_flag` STRING COMMENT '离产房时小便标志',
    `amn_fluid_case` STRING COMMENT '羊水情况',
    `umb_dscr` STRING COMMENT '脐带状况',
    `placenta_dscr` STRING COMMENT '胎盘状况',
    `birth_canal_injury_dscr` STRING COMMENT '产道损伤状况',
    `skin_touch_time` STRING COMMENT '母婴皮肤接触时间',
    `operation_testify` STRING COMMENT '手术指证',
    `oprn_oprt_name` STRING COMMENT '手术及操作名称',
    `proc_special_dscr` STRING COMMENT '手术特殊记录',
    `is_accompany_childbirth` STRING COMMENT '是否陪伴分娩',
    `is_early_contact_sucking` STRING COMMENT '是否早接触早吸吮',
    `nwb_name` STRING COMMENT '新生儿姓名',
    `nwb_gend_code` STRING COMMENT '新生儿性别代码',
    `nwb_gend_name` STRING COMMENT '新生儿性别名称',
    `birth_weight` DECIMAL(20,4) COMMENT '新生儿体重(g)',
    `birth_length` DECIMAL(20,4) COMMENT '新生儿出生身长cm',
    `head_circum` DECIMAL(20,4) COMMENT '新生儿头围',
    `chest_circum` DECIMAL(20,4) COMMENT '新生儿胸围',
    `apgar1_score` INT(16) COMMENT 'Apgar评分值1分钟',
    `apgar5_score` INT(16) COMMENT 'Apgar评分值5分钟',
    `apgar10_score` INT(16) COMMENT 'Apgar评分值10分钟',
    `birth_situat_code` STRING COMMENT '出生情况代码',
    `birth_situat_name` STRING COMMENT '出生情况名称',
    `pregnancy_result_code` STRING COMMENT '妊娠结局代码',
    `pregnancy_result_name` STRING COMMENT '妊娠结局名称',
    `birth_defect_code` STRING COMMENT '出生缺陷情况代码',
    `birth_defect_name` STRING COMMENT '出生缺陷情况名称',
    `birth_defect_dscr` STRING COMMENT '出生缺陷情况描述',
    `nwb_hear_screen_case_code` STRING COMMENT '新生儿听力筛查代码',
    `nwb_hear_screen_case_name` STRING COMMENT '新生儿听力筛查名称',
    `leave_date` DATE COMMENT '出院日期',
    `diag_res` STRING COMMENT '诊断结果',
    `deliver_org_code` STRING COMMENT '接生机构代码',
    `deliver_org_name` STRING COMMENT '接生机构名称',
    `reg_org_name` STRING COMMENT '登记机构名称',
    `case_mark` STRING COMMENT '结案标志',
    `end_case_date` DATE COMMENT '结案日期',
    `close_case_org_code` STRING COMMENT '结案机构统一社会信用代码',
    `close_case_org_name` STRING COMMENT '结案机构名称',
    `deliver_emp_code` STRING COMMENT '接生者工号',
    `deliver_emp_name` STRING COMMENT '接生者姓名',
    `record_emp_name` STRING COMMENT '记录者姓名',
    `record_emp_code` STRING COMMENT '记录者工号',
    `nurse_emp_name` STRING COMMENT '护理者姓名',
    `fill_date` DATE COMMENT '填报日期',
    `amn_fluid_amount` INT(16) COMMENT '羊水量mL',
    `hcg` STRING COMMENT '血βHCG',
    `hiv_anti_code` STRING COMMENT 'HIV抗体检测结果代码',
    `hiv_anti_name` STRING COMMENT 'HIV抗体检测结果名称',
    `pulse` INT(16) COMMENT '脉搏',
    `nwb_rescue_code` STRING COMMENT '新生儿抢救方法代码',
    `nwb_rescue_name` STRING COMMENT '新生儿抢救方法名称',
    `nwb_rescue_flag` STRING COMMENT '新生儿抢救标志',
    `nwb_intensive_dscr_code` STRING COMMENT '新生儿并发症代码',
    `nwb_intensive_dscr_name` STRING COMMENT '新生儿并发症名称',
    `nwb_intensive_mark` STRING COMMENT '新生儿并发症标志',
    `diag_way_code` STRING COMMENT '妊娠诊断方法代码',
    `diag_way_name` STRING COMMENT '妊娠诊断方法名称',
    `nwb_urine_dscr` STRING COMMENT '新生儿小便状况记录',
    `red_hip_flag` STRING COMMENT '臀红标志',
    `jaundice_level_code` STRING COMMENT '新生儿黄疸程度代码',
    `jaundice_level_name` STRING COMMENT '新生儿黄疸程度名称',
    `head_circ` DECIMAL(20,4) COMMENT '头围cm',
    `bas_wt` DECIMAL(20,4) COMMENT '基础体重kg',
    `ht` DECIMAL(20,4) COMMENT '身长cm',
    `nwb_heart_rate` INT(16) COMMENT '新生儿心率次分钟',
    `last_gestate_stop_date` DATE COMMENT '前次妊娠终止日期',
    `deliver_time` DATE COMMENT '前次分娩日期',
    `previous_gest_end_code` STRING COMMENT '前次妊娠终止方式代码',
    `previous_gest_end_name` STRING COMMENT '前次妊娠终止方式名称',
    `previous_deliver_way_code` STRING COMMENT '前次分娩方式代码',
    `previous_deliver_way_name` STRING COMMENT '前次分娩方式名称',
    `preg_name` STRING COMMENT '产妇姓名',
    `brdy` DATE COMMENT '出生日期',
    `ipt_no` STRING COMMENT '住院号',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `perineum_cut_mark` STRING COMMENT '会阴切开标志',
    `perine_suture_no` INT(16) COMMENT '会阴缝合针数',
    `perinaeum_case_code` STRING COMMENT '会阴裂伤程度代码',
    `perinaeum_case_name` STRING COMMENT '会阴裂伤程度名称',
    `is_critical` STRING COMMENT '是否危急',
    `intensive_code` STRING COMMENT '产时并发症代码',
    `intensive_name` STRING COMMENT '产时并发症名称',
    `blood_pressure` STRING COMMENT '产后血压(收缩压舒张压)',
    `breast_milk_duration` INT(16) COMMENT '产后开奶时长min',
    `mother_res` STRING COMMENT '产妇结局',
    `fetals` INT(16) COMMENT '胎数',
    `nwb_body_temperat` DECIMAL(20,4) COMMENT '新生儿体温℃',
    `birth_defect_mark` STRING COMMENT '出生缺陷标志',
    `birth_defect_type_code` STRING COMMENT '出生缺陷类别代码',
    `birth_defect_type_name` STRING COMMENT '出生缺陷类别名称',
    `birth_defect_no` INT(16) COMMENT '出生缺陷儿例数',
    `deliver_res` STRING COMMENT '分娩结局',
    `nwb_death_flag` STRING COMMENT '新生儿死亡',
    `nwb_death_reason` STRING COMMENT '新生儿死亡原因',
    `nwb_death_time` DATE COMMENT '新生儿死亡时间',
    `midwifery_psn_name` STRING COMMENT '助产人员姓名',
    `midwifery_org_name` STRING COMMENT '助产机构名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `brth_loc` STRING COMMENT '分娩地点',
    `fets_part` STRING COMMENT '胎方位',
    `ipt_date` DATE COMMENT '住院日期',
    `last_edit` STRING COMMENT '最后编辑者',
    `last_edit_date` DATE COMMENT '最后编辑日期',
    `whtr_notc_code` STRING COMMENT '是否通知代码',
    `whtr_notc_name` STRING COMMENT '是否通知名称',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `hrisk_fac` STRING COMMENT '高危因素',
    `hrisk_fac_id` STRING COMMENT '高危因素号',
    `hrisk_ordr_date` DATE COMMENT '高危预约日期',
    `hrisk_sco` INT(16) COMMENT '高危评分',
    `midy_the_id2` STRING COMMENT '接生者ID2',
    `midy_the_name_2` STRING COMMENT '接生者名字2',
    `bir_cert_num` STRING COMMENT '出生证编号',
    `whtr_hrisk` STRING COMMENT '是否高危',
    `postp_thirty_m_blee` INT(16) COMMENT '产后30分钟出血',
    `postp_60_m_blee` INT(16) COMMENT '产后60分钟出血',
    `postp_90_m_blee` INT(16) COMMENT '产后90分钟出血',
    `postp_120_m_blee` INT(16) COMMENT '产后120分钟出血',
    `postpartum_30m_sbp` INT(16) COMMENT '产后30分钟血压：收缩压',
    `postpartum_30m_dbp` INT(16) COMMENT '产后30分钟血压：舒张压',
    `postp_60_m_bloo_pres_systpre` INT(16) COMMENT '产后60分钟血压：收缩压',
    `postpartum_60m_dbp` INT(16) COMMENT '产后60分钟血压：舒张压',
    `postp_90_m_bloo_pres_systpre` INT(16) COMMENT '产后90分钟血压：收缩压',
    `postpartum_90m_dbp` INT(16) COMMENT '产后90分钟血压：舒张压',
    `oxyto_inj_amt` STRING COMMENT '催产素注射量',
    `oxyto_inj_part` STRING COMMENT '催产素注射部位',
    `ergot_inj_amt` STRING COMMENT '麦角注射量',
    `ergot_inj_part` STRING COMMENT '麦角注射部位',
    `ergot_inj_oth` STRING COMMENT '麦角注射其他',
    `in_stit_val` STRING COMMENT '内缝针数',
    `in_stit_type` STRING COMMENT '内缝线型',
    `exte_stit_val` STRING COMMENT '外缝针数',
    `exte_stit_type` STRING COMMENT '外缝线型',
    `perin_sutur_new_anal_exam` STRING COMMENT '会阴缝合后肛检',
    `earl_expo_earl_aspi_begn_time` STRING COMMENT '早接触早允吸开始时间',
    `earl_expo_earl_aspi_dura` STRING COMMENT '早接触早允吸持续时间',
    `earl_expo_earl_aspi_info_code` STRING COMMENT '早接触早允吸情况代码',
    `earl_expo_earl_aspi_info_name` STRING COMMENT '早接触早允吸情况名称',
    `noskincontact_reason` STRING COMMENT '未行皮肤早接触早允吸原因',
    `matn_leave_labo_deli_time` DATE COMMENT '产妇离开产房时间',
    `labr_real_esta_new_uri_time` DATE COMMENT '产房产后小便时间',
    `perin_incs_indi` STRING COMMENT '会阴切开指征',
    `cesr_sect_indi` STRING COMMENT '剖宫产指征',
    `cesr_sect_gyn_ind` STRING COMMENT '剖宫产妇指征',
    `sev_copn` STRING COMMENT '严重合并症(心肝肾等)',
    `memo` STRING COMMENT '备注说明',
    `bedno` STRING COMMENT '病床号',
    `matr_syst_admi` STRING COMMENT '是否孕产妇系统管理',
    `inhosp_prete_labo` STRING COMMENT '是否院内先产',
    `criti_matr_flag` STRING COMMENT '是否危重孕产妇标志',
    `diffic_fac` STRING COMMENT '难产因素',
    `umbil_cord_abn` STRING COMMENT '脐带异常',
    `puer_cop` STRING COMMENT '产褥期并发症',
    `parturient_time` TIMESTAMP COMMENT '临产时间',
    `perin_sutur_num_ned` INT(16) COMMENT '会阴缝合针数',
    `perin_fiss_info` STRING COMMENT '会阴裂伤情况',
    `cervical_case` STRING COMMENT '宫颈情况',
    `uter_heig` INT(16) COMMENT '宫高',
    `fetl_posi_abn` STRING COMMENT '胎位异常',
    `time_deli_abnor` STRING COMMENT '产时异常情况',
    `eclam` STRING COMMENT '是否子痫',
    `inhosp_eclam` STRING COMMENT '是否院内子痫',
    `midy_info_ipt_brth_code` STRING COMMENT '接生情况住院分娩代码',
    `midy_info_ipt_brth_name` STRING COMMENT '接生情况住院分娩名称',
    `add_flag` STRING COMMENT '补录标志(0否1是)',
    `whtr_city_exte_brth` STRING COMMENT '是否市外分娩(0否1是)',
    `postpartum_30m_pulse` INT(16) COMMENT '产后脉搏30分单位次分',
    `postp_pul_60_sco_emp_per_min` INT(16) COMMENT '产后脉搏60分单位次分',
    `postp_pul_90_sco_emp_per_min` INT(16) COMMENT '产后脉搏90分单位次分',
    `postp_pul_120_sco_emp_per_min` INT(16) COMMENT '产后脉搏120分单位次分',
    `fulfi_info` STRING COMMENT '落实情况',
    `whtr_assi_labo_ins` STRING COMMENT '是否助产机构1是2否',
    `matn_ipt_medcas_no` STRING COMMENT '产妇住院病案号',
    `emp_resper` STRING COMMENT '单位负责人',
    `tel` STRING COMMENT '联系电话',
    `brth_loc_code` STRING COMMENT '分娩地点代码',
    `brth_loc_name` STRING COMMENT '分娩地点名称',
    `prov_matr_child_upld_flag` STRING COMMENT '省妇幼上传标志',
    `bloo_collec_date` DATE COMMENT '采血日期',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='分娩记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_folic_acid_register` (
    `rid` STRING COMMENT '数据唯一记录号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `sys_no` STRING COMMENT '系统编号',
    `card_no` STRING COMMENT '卡号',
    `manl_sys_no` STRING COMMENT '手册系统编号',
    `woman_id` STRING COMMENT '妇女标识符ID',
    `preg_week_w` INT(16) COMMENT '孕周：周',
    `preg_week_d` INT(16) COMMENT '孕周：天',
    `rece_date` DATE COMMENT '领取日期',
    `rece_val` INT(16) COMMENT '领取数',
    `rece_huma` STRING COMMENT '领取人',
    `whtr_prpa_prg` STRING COMMENT '是否准备怀孕',
    `whtr_high_crit_preg` STRING COMMENT '是否高危待孕',
    `issu_huma` STRING COMMENT '发放人',
    `issu_huma_name` STRING COMMENT '发放人姓名',
    `issu_emp` STRING COMMENT '发放单位',
    `remark` STRING COMMENT '备注',
    `add_the` STRING COMMENT '添加者',
    `add_date` DATE COMMENT '添加日期',
    `last_edit` STRING COMMENT '最后编辑者',
    `last_edit_date` DATE COMMENT '最后编辑日期',
    `hosp_no` STRING COMMENT '医院编号',
    `full_name` STRING COMMENT '姓名',
    `brdy` DATE COMMENT '出生日期',
    `age` INT(16) COMMENT '年龄',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `tel` STRING COMMENT '联系电话',
    `adress_pro` STRING COMMENT '省',
    `adress_city` STRING COMMENT '市',
    `adress_county` STRING COMMENT '区县',
    `adress_rural` STRING COMMENT '街道乡',
    `adress_village` STRING COMMENT '村居',
    `location_detl_addr` STRING COMMENT '详细地址',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='叶酸登记表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_marital_exam_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `woman_id` STRING COMMENT '妇女标识符ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `sort` STRING COMMENT '序号',
    `fillin_date` DATE COMMENT '填写日期',
    `full_name` STRING COMMENT '姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `brdy` DATE COMMENT '出生日期',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `occup_code` STRING COMMENT '职业代码',
    `occup_name` STRING COMMENT '职业名称',
    `edu_background_code` STRING COMMENT '学历代码',
    `edu_background_name` STRING COMMENT '学历名称',
    `nation_code` STRING COMMENT '民族代码',
    `nation_name` STRING COMMENT '民族名称',
    `prenup_prov` STRING COMMENT '填婚前检查表时报的：省',
    `prenup_city` STRING COMMENT '填婚前检查表时报的：市',
    `prenup_county` STRING COMMENT '填婚前检查表时报的：区县',
    `prenup_subdistrict` STRING COMMENT '填婚前检查表时报的：街道乡',
    `filli_prenup_check_time_of_vil` STRING COMMENT '填婚前检查表时报的：村居',
    `prenup_housenum` STRING COMMENT '填婚前检查表时报的：门牌号',
    `resd_addr_prov_code` STRING COMMENT '户籍地省自治区直辖市代码',
    `resd_addr_prov_name` STRING COMMENT '户籍地省自治区直辖市名称',
    `resd_addr_city_code` STRING COMMENT '户籍地市地区代码',
    `resd_addr_city_name` STRING COMMENT '户籍地市地区名称',
    `resd_coty_code` STRING COMMENT '户籍地址县区代码',
    `resd_coty_name` STRING COMMENT '户籍地址县区名称',
    `resd_town_code` STRING COMMENT '户籍地址乡镇街道代码',
    `resd_town_name` STRING COMMENT '户籍地址乡镇街道名称',
    `resd_addr_comm_code` STRING COMMENT '户籍地居委会村代码',
    `resd_addr_comm_name` STRING COMMENT '户籍地居委会村名称',
    `curr_addr_prov` STRING COMMENT '现住址：省',
    `curr_addr_city` STRING COMMENT '现住址：市',
    `cur_addr_coty_code` STRING COMMENT '现居住地址县区代码',
    `cur_addr_coty_name` STRING COMMENT '现居住地址县区名称',
    `curr_addr_subd` STRING COMMENT '现住址：街道乡',
    `curr_addr_vil` STRING COMMENT '居住地村路街弄',
    `curr_addr_hsnum` STRING COMMENT '现住址：门牌号',
    `poscode` STRING COMMENT '邮政编码',
    `workplace` STRING COMMENT '工作单位',
    `tel` STRING COMMENT '联系电话',
    `oth_name` STRING COMMENT '对方姓名',
    `ref_no` STRING COMMENT '编号',
    `oppo_no` STRING COMMENT '对方编号',
    `exam_date` DATE COMMENT '检查日期',
    `bloo_rela` STRING COMMENT '血缘关系',
    `past_illhis` STRING COMMENT '既往病史',
    `oprn_his` STRING COMMENT '手术史',
    `past_illhis_oth` STRING COMMENT '既往病史其他',
    `dise_now` STRING COMMENT '现病史',
    `mena_menar_age` INT(16) COMMENT '月经初潮年龄女',
    `menst_prd_of_divi` STRING COMMENT '经期周期之被除数',
    `menst_prd_divi` STRING COMMENT '经期周期之除数',
    `mens_flow` STRING COMMENT '经量',
    `dysme` STRING COMMENT '痛经无轻中重女',
    `last_mena_date` DATE COMMENT '末次月经日期',
    `past_mrg_his` STRING COMMENT '既往婚育史男女',
    `child_psncnt` INT(16) COMMENT '子女人数男女',
    `adequ_mon_cnt` INT(16) COMMENT '足月次数女',
    `pret_cnt` INT(16) COMMENT '早产次数女',
    `misc_cnt` INT(16) COMMENT '流产次数女',
    `with_hered_rel_of_fmhis` STRING COMMENT '与遗传有关的家族史',
    `relation_patient_code` STRING COMMENT '患者与本人关系代码',
    `relation_patient_name` STRING COMMENT '患者与本人关系名称',
    `fm_clos_rela_marr` STRING COMMENT '家族近亲婚配',
    `prenup_illhis_exam_sign` STRING COMMENT '婚前病史受检者签名',
    `prenup_illhis_exam_dr_id` STRING COMMENT '婚前病史检查医师ID',
    `prenup_illhis_exam_dr_sign` STRING COMMENT '婚前病史检查医师签名',
    `bloo_pres_of_divid` STRING COMMENT '血压之被除数',
    `bloo_pres_of_divis` STRING COMMENT '血压之除数',
    `sp_body_cond` STRING COMMENT '特殊体态',
    `ment_sta` STRING COMMENT '精神状态',
    `sp_face` STRING COMMENT '特殊面容',
    `inte` STRING COMMENT '智力',
    `skin_hai` STRING COMMENT '皮肤毛发',
    `five_sens` STRING COMMENT '五官',
    `thyr` STRING COMMENT '甲状腺',
    `heart_rate` INT(16) COMMENT '心率次min',
    `rhythm_heart_dscr` STRING COMMENT '心律',
    `murm` STRING COMMENT '杂音',
    `lung` STRING COMMENT '肺',
    `live` STRING COMMENT '肝',
    `fors_spin` STRING COMMENT '四肢脊柱',
    `physexm_oth` STRING COMMENT '体格检查其他',
    `prenup_physexm_dr_id` STRING COMMENT '婚前体格检查医师ID',
    `prenup_physexm_dr_sign` STRING COMMENT '婚前体格检查医师签名',
    `laryn` STRING COMMENT '喉结',
    `male_pubi_hair` STRING COMMENT '男性阴毛',
    `peni` STRING COMMENT '阴茎',
    `foresk` STRING COMMENT '包皮',
    `testi_bilat_ask_wth` STRING COMMENT '睾丸双侧扪及',
    `testi_volu_left` STRING COMMENT '睾丸体积左',
    `testi_volu_rght` STRING COMMENT '睾丸体积右',
    `testi_un_ask_wth` STRING COMMENT '睾丸未扪及左右',
    `epidi` STRING COMMENT '附睾',
    `nodu_left` STRING COMMENT '结节左',
    `nodu_rght` STRING COMMENT '结节右',
    `sperm_cord_vari_vein` STRING COMMENT '精索静脉曲张',
    `sperm_cord_vari_vein_deg` STRING COMMENT '精索静脉曲张程度',
    `male_second_sex_char_oth` STRING COMMENT '男性第二性征其他',
    `malessc_examdrid` STRING COMMENT '男性第二性征检查医师ID',
    `malessc_examdrsign` STRING COMMENT '男性第二性征检查医师签名',
    `fml_pubi_hair` STRING COMMENT '女性阴毛',
    `breast_exam_rslt` STRING COMMENT '乳房',
    `anal_chec_vulv` STRING COMMENT '肛查：外阴',
    `anal_chec_secre` STRING COMMENT '肛查：分泌物',
    `anal_chec_uter` STRING COMMENT '肛查：子宫',
    `anal_chec_att` STRING COMMENT '肛查：附件',
    `vagi_exam_vulv` STRING COMMENT '阴道检查：外阴',
    `vagi_exam_vagi` STRING COMMENT '阴道检查：阴道',
    `vagi_exam_cerv` STRING COMMENT '阴道检查：宫颈',
    `vagi_exam_uter` STRING COMMENT '阴道检查：子宫',
    `vagi_exam_att` STRING COMMENT '阴道检查：附件',
    `fml_second_sex_char_oth` STRING COMMENT '女性第二性征其他',
    `fema_vagi_exam_sign` STRING COMMENT '女性阴道检查签名',
    `fml_second_sex_char_exam_dr_id` STRING COMMENT '女性第二性征检查医师ID',
    `femalessc_examdrsign` STRING COMMENT '女性第二性征检查医师签名',
    `chest_xray` STRING COMMENT '胸透',
    `transa` STRING COMMENT '转氨酶',
    `bloo_rout` STRING COMMENT '血常规',
    `hbsag_check_res_name` STRING COMMENT '乙肝表面抗原',
    `urin_rout` STRING COMMENT '尿常规',
    `syph_fitr` STRING COMMENT '梅毒筛选',
    `vagi_secre` STRING COMMENT '阴道分泌物',
    `gonoc` STRING COMMENT '淋球菌',
    `oth_sp_exam` STRING COMMENT '其他特殊检查',
    `exam_rslt` STRING COMMENT '项目检查结果',
    `disediag` STRING COMMENT '疾病诊断',
    `disediag_codg` STRING COMMENT '疾病诊断编码',
    `medi_opnn` STRING COMMENT '医学意见',
    `medi_opnn_exam_mal_id` STRING COMMENT '医学意见受检男方ID',
    `medi_opnn_exam_mal_sign` STRING COMMENT '医学意见受检男方签名',
    `medi_opnn_exam_fml_id` STRING COMMENT '医学意见受检女方ID',
    `medi_opnn_exam_fml_sign` STRING COMMENT '医学意见受检女方签名',
    `prenup_hc_cslt` STRING COMMENT '婚前卫生咨询',
    `cslt_guid_rslt` STRING COMMENT '咨询指导结果',
    `cslt_guid_rslt_exam_mal_id` STRING COMMENT '咨询指导结果受检男方ID',
    `cslt_guid_rslt_exam_mal_sign` STRING COMMENT '咨询指导结果受检男方签名',
    `cslt_guid_rslt_exam_fml_id` STRING COMMENT '咨询指导结果受检女方ID',
    `cslt_guid_rslt_exam_fml_sign` STRING COMMENT '咨询指导结果受检女方签名',
    `referral_flag` STRING COMMENT '转诊标志',
    `refl_hosp_id` STRING COMMENT '转诊医院ID',
    `refl_hosp` STRING COMMENT '转诊医院',
    `transfer_treatment_date` DATE COMMENT '转诊日期',
    `appoint_flup_date` DATE COMMENT '预约复诊日期',
    `issuance_date` DATE COMMENT '出具《婚前医学检查证明》日期',
    `cnvl_main_test_dr_id` STRING COMMENT '常规主检医师ID',
    `cnvl_main_test_dr_sign` STRING COMMENT '常规主检医师签名',
    `hosp_id` STRING COMMENT '医院ID',
    `remark` STRING COMMENT '备注',
    `prenupexam_drid` STRING COMMENT '婚前医学检查登记疾病和咨询指导记录本之医师ID',
    `prenupexam_drsign` STRING COMMENT '婚前医学检查登记疾病和咨询指导记录本之医师签名',
    `prenupexam_certissudate` DATE COMMENT '婚前医学检查证明的发放日期',
    `rcd_of_stas` INT(16) COMMENT '记录的状态',
    `hiv_anti_code` STRING COMMENT 'HIV抗体检测结果代码',
    `hiv_anti_name` STRING COMMENT 'HIV抗体检测结果名称',
    `whtr_fore_rela` STRING COMMENT '是否涉外',
    `cert_code` STRING COMMENT '证件类型代码',
    `cert_name` STRING COMMENT '证件类型名称',
    `age` INT(16) COMMENT '年龄',
    `prt_stas` STRING COMMENT '打印状态',
    `son_psncnt` INT(16) COMMENT '子人数',
    `adequ_age` INT(16) COMMENT '实足年龄',
    `adequ_agem` STRING COMMENT '实足月龄',
    `bloo_rout_val` INT(16) COMMENT '血常规值',
    `transa_val` INT(16) COMMENT '转氨酶值',
    `urinary_protein` STRING COMMENT '尿蛋白',
    `urin_gluc` STRING COMMENT '尿糖',
    `occu_bloo` STRING COMMENT '隐血',
    `abnormal_rslt` STRING COMMENT '异常情况',
    `folat_issu` STRING COMMENT '叶酸发放',
    `euge_dect` STRING COMMENT '优生检测',
    `whit_bloo_cells` STRING COMMENT '白血球',
    `last_modi_date` DATE COMMENT '最后修改日期',
    `bas_info_inpt_psn` STRING COMMENT '基础信息录入人员',
    `illhis_inpt_psn` STRING COMMENT '病史录入人员',
    `physexm_inpt_psn` STRING COMMENT '体格检查录入人员',
    `cnvl_exam_inpt_psn` STRING COMMENT '常规检查录入人员',
    `whtr_flup` STRING COMMENT '是否随访',
    `whtr_earl_preg` STRING COMMENT '是否早孕',
    `upld_flag` STRING COMMENT '上传标志',
    `hemog_a2` STRING COMMENT '血红蛋白A2',
    `hemog_af` STRING COMMENT '血红蛋白A+F',
    `hemog_barts` STRING COMMENT '血红蛋白Barts',
    `hemog_c` STRING COMMENT '血红蛋白C',
    `hemog_f` STRING COMMENT '血红蛋白F',
    `hemog_h` STRING COMMENT '血红蛋白H',
    `hemog_s` STRING COMMENT '血红蛋白S',
    `prom_thyr_horm_mes` STRING COMMENT '促甲状腺激素测定',
    `pre_preg_euge_exam_untes` STRING COMMENT '孕前优生检查未检',
    `gian_cell_viru_igg_anti_mes` STRING COMMENT '巨细胞病毒IgG抗体测定',
    `rube_viru_igg_anti_mes` STRING COMMENT '风疹病毒IgG抗体测定',
    `toxo_igm_anti_mes` STRING COMMENT '弓形虫IgM抗体测定',
    `cnfm_diab_child_matn_his` STRING COMMENT '确诊地贫患儿生育史',
    `diab_fmhis` STRING COMMENT '地贫家族史',
    `diab_face` STRING COMMENT '地贫面容',
    `diab_scre` STRING COMMENT '地贫筛查',
    `confdmchild_stillbirthcnt` STRING COMMENT '确诊地贫患儿生育史_死产次数',
    `confdmchild_natmisccnt` STRING COMMENT '确诊地贫患儿生育史_自然流产次数',
    `hsv-ii_igg_test` STRING COMMENT 'II型单纯疱疹IgG抗体测定',
    `hsv-ii_igm_test` STRING COMMENT 'II型单纯疱疹IgM抗体测定',
    `dyspla` STRING COMMENT '发育不良',
    `jaun` STRING COMMENT '黄疸',
    `resd_natu_code` STRING COMMENT '户籍性质类型代码',
    `resd_natu_name` STRING COMMENT '户籍性质类型名称',
    `gian_cell_viru_igm_anti_mes` STRING COMMENT '巨细胞病毒IgM抗体测定',
    `whol_bod_edem` STRING COMMENT '全身水肿',
    `bld_his` STRING COMMENT '输血史',
    `matn_certno` STRING COMMENT '生育证号',
    `face_atro_pall` STRING COMMENT '脸色萎黄苍白',
    `hepa_b_surf_anti` STRING COMMENT '乙型肝炎表面抗体',
    `hepa_b_e_hbeab` STRING COMMENT '乙型肝炎e抗体',
    `hepa_b_e_hbeag` STRING COMMENT '乙型肝炎e抗原',
    `hepa_b_cor_anti` STRING COMMENT '乙型肝炎核心抗体',
    `toxo_igg_anti_mes` STRING COMMENT '弓形虫IgG抗体测定',
    `rube_viru_igm_anti_mes` STRING COMMENT '风疹病毒IgM抗体测定',
    `are_you_curr_taki_medi` STRING COMMENT '目前是否服药',
    `whtr_inj_excs_vac` STRING COMMENT '是否注射过疫苗',
    `currcontraceptive` STRING COMMENT '现用避孕措施或目前终止避孕者原避孕措施',
    `contr_cont_used_time` STRING COMMENT '避孕措施持续使用时间(月)',
    `years_discontinue` STRING COMMENT '目前终止避孕者原避孕措施停用时间(年)',
    `months_discontinue` STRING COMMENT '目前终止避孕者原避孕措施停用时间(月)',
    `whtr_into_meat_eat_eggs` STRING COMMENT '是否进食肉蛋类',
    `whtr_anor_vege` STRING COMMENT '是否厌食蔬菜',
    `whtr_yes_consu_fles_hob` STRING COMMENT '是否有食用生肉嗜好',
    `whtr_smok` STRING COMMENT '是否吸烟',
    `whtr_exis_passi_smok` STRING COMMENT '是否存在被动吸烟',
    `whtr_drnk` STRING COMMENT '是否饮酒',
    `whtr_used_coca_etc._toxo_drug` STRING COMMENT '是否使用可卡因等毒麻药品',
    `whtr_bad_brea` STRING COMMENT '是否口臭',
    `whtr_gums_blee` STRING COMMENT '是否牙龈出血',
    `envexposure` STRING COMMENT '生活或工作环境中是否接触以下因素',
    `whtr_feel_life_job_pre` STRING COMMENT '是否感到生活工作压力',
    `relationstension` STRING COMMENT '与亲友同事的关系是否紧张',
    `whtr_feel_econ_pre` STRING COMMENT '是否感到经济压力',
    `whtr_do_well_prg_prpa` STRING COMMENT '是否做好怀孕准备',
    `other1` STRING COMMENT '其他',
    `height` DECIMAL(20,4) COMMENT '身高cm',
    `weight` DECIMAL(20,4) COMMENT '体重kg',
    `wt_ind` STRING COMMENT '体重指数',
    `liverkidney_gpt` STRING COMMENT '肝肾功能检测谷丙转氨酶(ALT)UL',
    `creat_umoll` STRING COMMENT '肌酐(Cr)umolL',
    `blotype_abo_code` STRING COMMENT 'ABO血型代码',
    `blotype_abo_name` STRING COMMENT 'ABO血型名称',
    `bloo_gluc` STRING COMMENT '血糖',
    `gynecological_check` STRING COMMENT '妇科B超',
    `cnvl_exam_oth` STRING COMMENT '常规检查其他',
    `exam_type_code` STRING COMMENT '检查类型代码',
    `exam_type_name` STRING COMMENT '检查类型名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='婚检情况记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_newborn_basic_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `nebo_guid` STRING COMMENT '唯一标识',
    `nebo_birthno` STRING COMMENT '新生儿出生证号',
    `nb_cert_no` STRING COMMENT '新生儿身份证件号码',
    `baby_psncert_type_code` STRING COMMENT '新生儿身份证件类别代码',
    `baby_psncert_type_name` STRING COMMENT '新生儿身份证件类别名称',
    `chil_mother` STRING COMMENT '母亲姓名',
    `mother_cert_no` STRING COMMENT '母亲身份证件号码',
    `mother_psncert_type_code` STRING COMMENT '母亲身份证件类别代码',
    `mother_psncert_type_name` STRING COMMENT '母亲身份证件类别名称',
    `chil_father` STRING COMMENT '父亲姓名',
    `father_cert_no` STRING COMMENT '父亲身份证件号码',
    `father_cert_type_code` STRING COMMENT '父亲身份证件类别代码',
    `father_cert_type_name` STRING COMMENT '父亲身份证件类别名称',
    `tel` STRING COMMENT '联系电话',
    `mobile` STRING COMMENT '手机号码',
    `nebo_mhbsag_code` STRING COMMENT '母亲HBsAg代码',
    `nebo_mhbsag_name` STRING COMMENT '母亲HBsAg名称',
    `nebo_fhbsag_code` STRING COMMENT '父亲HBsAg代码',
    `nebo_fhbsag_name` STRING COMMENT '父亲HBsAg名称',
    `cur_addr_coty_code` STRING COMMENT '现居住地址县区代码',
    `cur_addr_coty_name` STRING COMMENT '现居住地址县区名称',
    `nebo_address_depa_id` STRING COMMENT '现住址乡镇',
    `nebo_depa_id` STRING COMMENT '产科单位',
    `nebo_address` STRING COMMENT '家庭地址',
    `nwb_name` STRING COMMENT '新生儿姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `nebo_birthtime` STRING COMMENT '出生时间',
    `chil_wt` DECIMAL(20,4) COMMENT '出生体重kg',
    `nebo_nati_code` STRING COMMENT '新生儿民族代码',
    `nebo_nati_name` STRING COMMENT '新生儿民族名称',
    `resd_coty_code` STRING COMMENT '户籍地址县区代码',
    `resd_coty_name` STRING COMMENT '户籍地址县区名称',
    `resd_town_code` STRING COMMENT '户籍地址乡镇街道代码',
    `resd_town_name` STRING COMMENT '户籍地址乡镇街道名称',
    `resd_addr` STRING COMMENT '户籍地址详细地址',
    `mthr_naty_code` STRING COMMENT '母亲民族代码',
    `mthr_naty_name` STRING COMMENT '母亲民族名称',
    `nebo_childcode` STRING COMMENT '儿童社保卡号',
    `nebo_mothercode` STRING COMMENT '母亲社保卡号',
    `nebo_fathercode` STRING COMMENT '父亲社保卡号',
    `chil_fetus` STRING COMMENT '胎次',
    `birth_gest_weeks` INT(16) COMMENT '出生孕周d',
    `nebo_birthstate` STRING COMMENT '分娩情况',
    `nebo_defects` STRING COMMENT '出生缺陷',
    `nebo_defects_desc` STRING COMMENT '出生缺陷描述',
    `nebo_screen` STRING COMMENT '新生儿疾病筛查结果',
    `nebo_apgar1` STRING COMMENT 'Apgar1',
    `nebo_apgar2` STRING COMMENT 'Apgar5',
    `nebo_apgar3` STRING COMMENT 'Apgar10',
    `nebo_doctor` STRING COMMENT '录入医生',
    `fillin_date` DATE COMMENT '填写日期',
    `sys_no` STRING COMMENT '系统编号',
    `nebo_bcg` STRING COMMENT '是否自愿接种卡介苗',
    `nebi_bcg_reason` STRING COMMENT '卡介苗未种原因',
    `nebo_hepb` STRING COMMENT '是否自愿接种乙肝',
    `nebi_hepb_reason` STRING COMMENT '乙肝未种原因',
    `nebi_hepb_reasonother` STRING COMMENT '其他原因',
    `nebo_hepbig` STRING COMMENT '是否自愿接种乙肝免疫球蛋白',
    `uploadtime` DATE COMMENT '上传时间',
    `wom_hcare_manl_sys_no` STRING COMMENT '妇女保健手册系统编号',
    `crte_date` DATE COMMENT '创建日期',
    `crter` STRING COMMENT '创建者',
    `last_edit_date` DATE COMMENT '最后编辑日期',
    `last_edit` STRING COMMENT '最后编辑者',
    `modify_datetime` TIMESTAMP COMMENT '最后修改时间',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='新生儿基本信息';

CREATE TABLE IF NOT EXISTS `ods_hcs_hwm_newborn_vaccin_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `nebi_nebo_id` STRING COMMENT '新生儿ID',
    `vacc_code` STRING COMMENT '疫苗代码',
    `vacc_name` STRING COMMENT '疫苗名称',
    `vaccinate_inoc_time` INT(16) COMMENT '接种剂次',
    `nebi_free` STRING COMMENT '是否免费',
    `nebi_date` DATE COMMENT '接种时间',
    `nebi_dose` STRING COMMENT '接种剂量',
    `vacc_batch` STRING COMMENT '疫苗批号',
    `nebi_corp_code` STRING COMMENT '疫苗生产厂家',
    `nebi_nurse` STRING COMMENT '接种护士',
    `nebi_inpl_id` INT(16) COMMENT '接种部位',
    `nebi_inoculateway` INT(16) COMMENT '接种途径',
    `nebi_editdate` DATE COMMENT '修改时间',
    `vcnt_info_id` STRING COMMENT '接种信息ID',
    `hosp_id` STRING COMMENT '医院ID',
    `patn_id` STRING COMMENT '病人ID',
    `uploadtime` DATE COMMENT '上传时间',
    `crte_date` DATE COMMENT '创建日期',
    `crter` STRING COMMENT '创建者',
    `last_edit_date` DATE COMMENT '最后编辑日期',
    `last_edit` STRING COMMENT '最后编辑者',
    `modify_datetime` TIMESTAMP COMMENT '最后修改时间',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='新生儿接种记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hch_basic_info` (
    `rid` STRING COMMENT '数据唯一记录号',
    `chilid` STRING COMMENT '儿童ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `psncert_type_code` STRING COMMENT '身份证件类别代码',
    `psncert_type_name` STRING COMMENT '身份证件类别名称',
    `certno` STRING COMMENT '身份证件号码',
    `birth_certificate_no` STRING COMMENT '出生医学证明编号',
    `chil_mother` STRING COMMENT '母亲姓名',
    `nwb_name` STRING COMMENT '新生儿姓名',
    `gender_code` STRING COMMENT '性别代码',
    `gender_name` STRING COMMENT '性别名称',
    `preg_matn_cnt` INT(16) COMMENT '胎产次',
    `geso_val` INT(16) COMMENT '孕周d',
    `chil_wt` DECIMAL(20,4) COMMENT '出生体重kg',
    `brdy` DATE COMMENT '出生日期',
    `birth_prov_code` STRING COMMENT '出生地址省自治区直辖市代码',
    `birth_prov_name` STRING COMMENT '出生地址省自治区直辖市名称',
    `birth_city_code` STRING COMMENT '出生地址市地区代码',
    `birth_city_name` STRING COMMENT '出生地址市地区名称',
    `birth_coty_code` STRING COMMENT '出生地址县区代码',
    `birth_coty_name` STRING COMMENT '出生地址县区名称',
    `birth_town_code` STRING COMMENT '出生地址乡镇街道代码',
    `birth_town_name` STRING COMMENT '出生地址乡镇街道名称',
    `birth_comm_code` STRING COMMENT '出生地址居委会村代码',
    `birth_comm_name` STRING COMMENT '出生地址居委会村名称',
    `birth_cotry_name` STRING COMMENT '出生地址村街路弄等',
    `birth_addr_housnum` STRING COMMENT '出生地门牌号包括“室”',
    `birth_addr` STRING COMMENT '出生详细地址',
    `first_time` DATE COMMENT '初诊日期',
    `otp_no` STRING COMMENT '门急诊号',
    `no_consult` STRING COMMENT '未来诊原因',
    `remark` STRING COMMENT '备注',
    `family_hereditary_his` STRING COMMENT '家族遗传性疾病史',
    `relation_patient_baby_code` STRING COMMENT '患者(家庭遗传性疾病患者)与本人(儿童)关系代码',
    `relation_patient_baby_name` STRING COMMENT '患者(家庭遗传性疾病患者)与本人(儿童)关系名称',
    `consanguine_mar_mark` STRING COMMENT '家族近亲婚配标志',
    `consanguine_relate_code` STRING COMMENT '家族近亲婚配者与本人关系代码',
    `relation_inbreed_name` STRING COMMENT '家族近亲婚配者与本人(儿童)关系名称',
    `pregnancy_card_id` STRING COMMENT '产妇孕卡ID',
    `gravidity` INT(16) COMMENT '产妇孕次',
    `matn_cnt` INT(16) COMMENT '产次',
    `abn_fert_his` STRING COMMENT '产妇异常生育史',
    `matn_edu_background_code` STRING COMMENT '产妇学历代码',
    `matn_edu_background_name` STRING COMMENT '产妇学历名称',
    `per_family_income_code` STRING COMMENT '家庭年人均收入代码',
    `per_family_income_name` STRING COMMENT '家庭年人均收入名称',
    `family_addr` STRING COMMENT '家庭住址',
    `family_cur_prov_code` STRING COMMENT '家庭现住地址省自治区直辖市代码',
    `family_cur_prov_name` STRING COMMENT '家庭现住地址省自治区直辖市名称',
    `family_cur_city_code` STRING COMMENT '家庭现住地址市地区代码',
    `family_cur_city_name` STRING COMMENT '家庭现住地址市地区名称',
    `family_cur_coty_code` STRING COMMENT '家庭现住地址县区代码',
    `family_cur_coty_name` STRING COMMENT '家庭现住地址县区名称',
    `family_cur_town_code` STRING COMMENT '家庭现住地址乡镇街道代码',
    `family_cur_town_name` STRING COMMENT '家庭现住地址乡镇街道名称',
    `family_cur_comm_code` STRING COMMENT '家庭现住地址居委会村代码',
    `family_cur_comm_name` STRING COMMENT '家庭现住地址居委会村名称',
    `family_cur_cotry_name` STRING COMMENT '家庭现住地址村街路弄等',
    `family_cur_addr_housnum` STRING COMMENT '家庭现住地址门牌号码',
    `family_cur_postalcode` STRING COMMENT '家庭邮政编码',
    `hometelephonenumber` STRING COMMENT '家庭电话号码',
    `resd_flag_code` STRING COMMENT '户籍标志代码',
    `resd_flag_name` STRING COMMENT '户籍标志名称',
    `mother_cert_no` STRING COMMENT '母亲身份证件号码',
    `mother_psncert_type_code` STRING COMMENT '母亲身份证件类别代码',
    `mother_psncert_type_name` STRING COMMENT '母亲身份证件类别名称',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `card_type_code` STRING COMMENT '卡类型代码',
    `card_type_name` STRING COMMENT '卡类型名称',
    `card_no` STRING COMMENT '卡号',
    `fthr_no` STRING COMMENT '父亲编号',
    `nwb_asph` STRING COMMENT '新生儿窒息',
    `hear_scre_asoc_chara_sec` STRING COMMENT '听力筛查关联字段',
    `dise_scre_asoc_chara_sec` STRING COMMENT '疾病筛查关联字段',
    `resd_status_code` STRING COMMENT '户口状态代码',
    `resd_status_name` STRING COMMENT '户口状态名称',
    `resd_natu_code` STRING COMMENT '户籍性质类型代码',
    `resd_natu_name` STRING COMMENT '户籍性质类型名称',
    `mob` STRING COMMENT '手机',
    `whtr_hrisk` STRING COMMENT '是否高危',
    `whtr_adm` STRING COMMENT '是否入园',
    `whtr_hepa_b_vac_vcnt` STRING COMMENT '是否乙肝疫苗接种',
    `whtr_bcg_vac_vcnt` STRING COMMENT '是否卡介苗接种',
    `manl_stas_code` STRING COMMENT '手册状态代码',
    `manl_stas_name` STRING COMMENT '手册状态名称',
    `live_stas_code` STRING COMMENT '居住状态代码',
    `live_stas_name` STRING COMMENT '居住状态名称',
    `coun` STRING COMMENT '咨询电话',
    `whtr_die` STRING COMMENT '是否死亡',
    `vill_for_emp_set_file_no` STRING COMMENT '以村为单位设置档案编号',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `appf_sco_1_m` STRING COMMENT 'appf评分1分钟',
    `appf_sco_5_m` STRING COMMENT 'appf评分5分钟',
    `whtr_emig` STRING COMMENT '是否迁出',
    `whtr_stt` STRING COMMENT '是否统计',
    `whtr_sing_paren` STRING COMMENT '是否单亲',
    `appf_sco_10_m` STRING COMMENT 'appf评分10分钟',
    `whtr_pret` STRING COMMENT '是否早产',
    `whtr_twin_preg` STRING COMMENT '是否双胎',
    `newbornscreen_negcode` STRING COMMENT '新生儿疾病筛查检查均阴性代码',
    `newbornscreen_negname` STRING COMMENT '新生儿疾病筛查检查均阴性名称',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='儿童基本情况信息表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hch_newborn_visit_record` (
    `rid` STRING COMMENT '数据唯一记录号',
    `nb_visit_id` STRING COMMENT '新生儿访视记录表编号',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `visit_no` INT(16) COMMENT '新生儿访视次数',
    `children_visit_id` STRING COMMENT '儿童访视基本信息编号',
    `interview_org_code` STRING COMMENT '访视机构统一社会信用代码',
    `together_mark` STRING COMMENT '母婴是否同室',
    `nb_hear_screen_case_code` STRING COMMENT '新生儿听力筛查结果代码',
    `nb_hear_screen_case_name` STRING COMMENT '新生儿听力筛查结果名称',
    `nb_dis_screen_proj_code` STRING COMMENT '新生儿疾病筛查结果代码',
    `nb_dis_screen_proj_name` STRING COMMENT '新生儿疾病筛查结果名称',
    `nebo_screen` STRING COMMENT '新生儿疾病筛查结果',
    `feedway_type_code` STRING COMMENT '喂养方式代码',
    `feedway_type_name` STRING COMMENT '喂养方式名称',
    `daily_suck_no` INT(16) COMMENT '每天吃奶量(ml)',
    `daily_suck_times` INT(16) COMMENT '每天吃奶次数次数日',
    `diet_case_code` STRING COMMENT '婴儿食欲情况代码',
    `diet_case_name` STRING COMMENT '婴儿食欲情况名称',
    `sleep_case_code` STRING COMMENT '婴儿睡眠情况代码',
    `sleep_case_name` STRING COMMENT '婴儿睡眠情况名称',
    `nb_shit_code` STRING COMMENT '婴儿大便性状代码',
    `nb_shit_name` STRING COMMENT '婴儿大便性状名称',
    `nb_shit_dscr` STRING COMMENT '婴儿大便性状情况',
    `baby_shit_times` INT(16) COMMENT '婴儿每日大便次数',
    `vomit_mark` STRING COMMENT '有无呕吐',
    `not_breastfeed_dscr` STRING COMMENT '非纯母乳喂养原因',
    `baby_tprt` DECIMAL(20,4) COMMENT '婴儿体温值',
    `baby_wt` DECIMAL(20,4) COMMENT '婴儿体重值',
    `baby_pulse` INT(16) COMMENT '婴儿脉率(次min)',
    `heart_rate` INT(16) COMMENT '心率次min',
    `br` INT(16) COMMENT '婴儿呼吸频率(次min)',
    `jaundice_part_mark` STRING COMMENT '婴儿黄疸标志',
    `jaundice_part_code` STRING COMMENT '婴儿黄疸部位代码',
    `jaundice_part_name` STRING COMMENT '婴儿黄疸部位名称',
    `other_jaundice_part` STRING COMMENT '婴儿黄疸部位其他描述',
    `bregma_horiz_diam` DECIMAL(20,4) COMMENT '前囟横径(cm)',
    `bregma_vert_diam` DECIMAL(20,4) COMMENT '前囟纵径(cm)',
    `bregma_tension_code` STRING COMMENT '前囟张力代码',
    `bregma_tension_name` STRING COMMENT '前囟张力名称',
    `infant_spirit_type` STRING COMMENT '婴儿精神情况',
    `child_complexion_code` STRING COMMENT '婴儿面色情况代码',
    `child_complexion_name` STRING COMMENT '婴儿面色情况名称',
    `child_head_hematoma_size` STRING COMMENT '婴儿头颅血肿大小',
    `skin_check_res_code` STRING COMMENT '婴儿皮肤情况代码',
    `skin_check_res_name` STRING COMMENT '婴儿皮肤情况名称',
    `eye_appear_check_abnorm_mark` STRING COMMENT '婴儿眼睛有无异常',
    `eye_appear_check_abnorm_dscr` STRING COMMENT '婴儿眼睛异常描述',
    `ear_appear_check_abnorm_mark` STRING COMMENT '婴儿耳有无异常',
    `ear_appear_check_abnorm_dscr` STRING COMMENT '婴儿耳外观异常描述',
    `nose_abnorm_check_mark` STRING COMMENT '婴儿鼻有无异常',
    `nose_abnorm_check_res_dscr` STRING COMMENT '婴儿鼻异常描述',
    `oral_check_abnorm_mark` STRING COMMENT '婴儿口腔有无异常',
    `oral_check_abnorm_res_dscr` STRING COMMENT '婴儿口腔异常描述',
    `head_check_abnorm_mark` STRING COMMENT '婴儿头有无异常',
    `head_check_abnorm_res_dscr` STRING COMMENT '婴儿头异常描述',
    `heart_auscultate_abnorm_mark` STRING COMMENT '婴儿心有无异常',
    `heart_auscultate_abnorm_dscr` STRING COMMENT '婴儿心异常描述',
    `lung_auscultate_abnorm_mark` STRING COMMENT '婴儿肺有无异常',
    `lung_auscultate_abnorm_dscr` STRING COMMENT '婴儿肺异常描述',
    `chest_check_abnorm_mark` STRING COMMENT '婴儿胸部有无异常',
    `chest_check_abnorm_res_dscr` STRING COMMENT '婴儿胸部异常描述',
    `abdominal_check_abnorm_mark` STRING COMMENT '婴儿腹部有无异常',
    `abdominal_check_abnorm_dscr` STRING COMMENT '婴儿腹部异常描述',
    `hips_check_abnorm_mark` STRING COMMENT '婴儿臀部有无异常',
    `hips_check_abnorm_res_dscr` STRING COMMENT '婴儿臀部异常描述',
    `aedea_check_abnorm_mark` STRING COMMENT '婴儿生殖器情况',
    `aedea_check_abnorm_dscr` STRING COMMENT '婴儿生殖器异常描述',
    `cm_check_abnorm_mark` STRING COMMENT '婴儿有无先天畸形',
    `umbilical_check_res_code` STRING COMMENT '婴儿脐带情况代码',
    `umbilical_check_res_name` STRING COMMENT '婴儿脐带情况名称',
    `red_hip_flag` STRING COMMENT '臀红标志',
    `limbs_act_abnorm_mark` STRING COMMENT '婴儿四肢活动度有无异常',
    `limbs_act_check_abnorm_rscr` STRING COMMENT '婴儿四肢活动度异常描述',
    `baby_neck_mass_mark` STRING COMMENT '婴儿颈部包块标志',
    `neck_mass_check_res_dscr` STRING COMMENT '婴儿颈部包块检查结果描述',
    `anus_check_abnorm_mark` STRING COMMENT '婴儿肛门检查有无异常',
    `anus_check_abnorm_res_dscr` STRING COMMENT '婴儿肛门检查异常描述',
    `spine_check_abnorm_mark` STRING COMMENT '婴儿脊柱检查有无异常',
    `spine_check_abnorm_res_dscr` STRING COMMENT '婴儿脊柱检查异常描述',
    `referral_flag` STRING COMMENT '转诊标志',
    `accept_org_name` STRING COMMENT '转入医疗机构名称',
    `accept_depart_name` STRING COMMENT '转入科室名称',
    `referral_reason` STRING COMMENT '转诊原因',
    `transfer_treatment_mark` STRING COMMENT '转诊建议标志',
    `referral_rec_dscr` STRING COMMENT '转诊建议内容',
    `nb_health_guide_type_code` STRING COMMENT '新生儿访视健康指导类别代码',
    `nb_health_guide_type_name` STRING COMMENT '新生儿访视健康指导类别名称',
    `check_sum_dscr` STRING COMMENT '查床小结描述',
    `nb_dscr` STRING COMMENT '新生儿情况描述',
    `chkpsn_name` STRING COMMENT '检查者姓名访视人员姓名',
    `exam_operator_code` STRING COMMENT '检查者工号',
    `reg_org_code` STRING COMMENT '登记机构统一社会信用代码',
    `visits_org_name` STRING COMMENT '登记机构名称访视机构名称',
    `appoint_nwb_sys_date` DATE COMMENT '预约新生儿系统观察日期',
    `exam_emp` STRING COMMENT '检查单位',
    `interview_date` DATE COMMENT '访视日期',
    `next_visit_date` DATE COMMENT '下次访视日期',
    `next_visit_place` STRING COMMENT '下次访视地点',
    `special_situat_record` STRING COMMENT '特殊情况记录',
    `process_guide_ad` STRING COMMENT '处理及指导意见',
    `data_rank` STRING COMMENT '密级',
    `state` STRING COMMENT '修改标志',
    `business_time` TIMESTAMP COMMENT '数据业务生成时间',
    `reserve1` STRING COMMENT '预留一',
    `reserve2` STRING COMMENT '预留二',
    `belo_to_age_gro_code` STRING COMMENT '所属年龄组代码',
    `belo_to_age_gro_name` STRING COMMENT '所属年龄组名称',
    `exam_item_time_peri_code` STRING COMMENT '检查项目时间段代码',
    `exam_item_time_peri_name` STRING COMMENT '检查项目时间段名称',
    `hgt_info_code` STRING COMMENT '身高情况代码',
    `hgt_info_name` STRING COMMENT '身高情况名称',
    `wt_code` STRING COMMENT '体重情况代码',
    `wt_name` STRING COMMENT '体重情况名称',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `one_yea_old_teethi` STRING COMMENT '一岁出牙数',
    `wt_eval_detl_code` STRING COMMENT '体重评价详细代码',
    `wt_eval_detl_name` STRING COMMENT '体重评价详细名称',
    `hgt_eval_detl_code` STRING COMMENT '身高评价详细代码',
    `hgt_eval_detl_name` STRING COMMENT '身高评价详细名称',
    `head_circ_eval` STRING COMMENT '头围评价',
    `diag_rslt_nurt_code` STRING COMMENT '诊断结果营养代码',
    `diag_rslt_nurt_name` STRING COMMENT '诊断结果营养名称',
    `diag_rslt_tonsi_code` STRING COMMENT '诊断结果扁桃体代码',
    `diag_rslt_tonsi_name` STRING COMMENT '诊断结果扁桃体名称',
    `diag_rslt_oth_code` STRING COMMENT '诊断结果其他代码',
    `diag_rslt_oth_name` STRING COMMENT '诊断结果其他名称',
    `diag_rslt_hrisk_code` STRING COMMENT '诊断结果高危代码',
    `diag_rslt_hrisk_name` STRING COMMENT '诊断结果高危名称',
    `com_eval_code` STRING COMMENT '综合评价代码',
    `com_eval_name` STRING COMMENT '综合评价名称',
    `nurt_eval_code` STRING COMMENT '营养评价代码',
    `nurt_eval_name` STRING COMMENT '营养评价名称',
    `nurt_eval_na` STRING COMMENT '营养评价NA',
    `measu_way` STRING COMMENT '测量方式',
    `wt_hgt_stdv_code` STRING COMMENT '体重身高(身长)标准差代码',
    `wt_hgt_stdv_name` STRING COMMENT '体重身高(身长)标准差名称',
    `over_nutr_eval_code` STRING COMMENT '营养过剩评价代码',
    `over_nutr_eval_name` STRING COMMENT '营养过剩评价名称',
    `malnu_hrisk_file_id` STRING COMMENT '营养不良高危档案ID',
    `anem_hrisk_file_id` STRING COMMENT '贫血高危档案ID',
    `ricke_hrisk_file_id` STRING COMMENT '佝偻病高危档案ID',
    `reg_hrisk_file_id` STRING COMMENT '登记高危档案ID',
    `hrisk_eval` STRING COMMENT '高危评估',
    `hrisk_exis_prb` STRING COMMENT '高危存在问题',
    `hrisk_guid` STRING COMMENT '高危指导',
    `trt` STRING COMMENT '治疗',
    `vitd_trt` STRING COMMENT 'VitD治疗',
    `exis_prb_anem` STRING COMMENT '存在问题贫血',
    `hrisk_guid_anem` STRING COMMENT '高危指导贫血',
    `exis_prb_ricke` STRING COMMENT '存在问题佝偻',
    `hrisk_guid_ricke` STRING COMMENT '高危指导佝偻',
    `kinde_id` STRING COMMENT '幼儿园ID',
    `clss_id` STRING COMMENT '班级ID',
    `tcm_diet_condi` STRING COMMENT '中医饮食调养',
    `tcm_rise_fall_regu` STRING COMMENT '中医起居调摄',
    `pass_paren_pt_by_rubb_mtd` STRING COMMENT '传授家长穴位按揉方法',
    `parenttcm_knowledge` STRING COMMENT '家长掌握中医药保健知识和操作方法情况',
    `tcmadvice_satisfaction` STRING COMMENT '对医生的中医药预防保健指导是否满意',
    `hrisk_chld_spec_cas_mgt_rcd_id` STRING COMMENT '高危儿童专案管理记录ID',
    `eval_rslt` STRING COMMENT '评估结果',
    `dspo` STRING COMMENT '处理',
    `highriskchild_evalmethod` STRING COMMENT '高危儿童及心理发育异常儿童登记表评估方法',
    `highriskchild_evalresult` STRING COMMENT '高危儿童及心理发育异常儿童登记表评估结果',
    `highriskchild_guidance` STRING COMMENT '高危儿童及心理发育异常儿童登记表指导',
    `highriskchild_handling` STRING COMMENT '高危儿童及心理发育异常儿童登记表处理',
    `mind_devt_abn_hrisk_file_id` STRING COMMENT '心理发育异常高危档案ID',
    `whtr_out` STRING COMMENT '是否外出(1是2否)',
    `highriskcase_guidance` STRING COMMENT '高危儿童专案管理记录指导',
    `highriskcase_handling` STRING COMMENT '高危儿童专案管理记录处理',
    `guid_data_used` STRING COMMENT '导数据使用',
    `mil_anem` STRING COMMENT '轻度贫血',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='新生儿访视记录表';

CREATE TABLE IF NOT EXISTS `ods_hcs_hch_high_risk_register` (
    `rid` STRING COMMENT '数据唯一记录号',
    `chilid` STRING COMMENT '儿童ID',
    `unified_uscid` STRING COMMENT '统一社会信用代码',
    `manl_sys_inte_code` STRING COMMENT '手册系统内码',
    `casefil_time` DATE COMMENT '立案时间',
    `caseopen_childsymptoms` STRING COMMENT '立案时患儿主要症状体征',
    `norm_labo_diffic` STRING COMMENT '顺产难产',
    `feed_his` STRING COMMENT '喂养史',
    `adequ_age` INT(16) COMMENT '实足年龄',
    `pret_week` STRING COMMENT '早产周数',
    `infi_child_diag` STRING COMMENT '体弱儿诊断',
    `infi_child_past_his` STRING COMMENT '体弱儿既往史',
    `birth_ht` DECIMAL(20,4) COMMENT '出生身长cm',
    `chil_wt` DECIMAL(20,4) COMMENT '出生体重kg',
    `chil_fetus` STRING COMMENT '胎次',
    `sin_twin_preg_code` STRING COMMENT '单双胎代码',
    `sin_twin_preg_name` STRING COMMENT '单双胎名称',
    `end_case_date` DATE COMMENT '结案日期',
    `caseclose_childsymptoms` STRING COMMENT '结案时患儿主要症状体征',
    `case_close_dor_name` STRING COMMENT '结案医师姓名',
    `case_close_org_name` STRING COMMENT '结案单位名称',
    `build_date` DATE COMMENT '建档日期',
    `filed_emp` STRING COMMENT '建档单位',
    `diag_rslt_no` STRING COMMENT '诊断结果编号',
    `whtr_clscase` STRING COMMENT '是否结案',
    `imp_data_of_old_sys_no` STRING COMMENT '导入数据的旧系统编号',
    `crte_date` DATE COMMENT '创建日期',
    `crter` STRING COMMENT '创建者',
    `last_modi_date` DATE COMMENT '最后修改日期',
    `last_modi_the` STRING COMMENT '最后修改者',
    `file_type_code` STRING COMMENT '档案类型代码',
    `file_type_name` STRING COMMENT '档案类型名称',
    `trans_type_code` STRING COMMENT '转归类型代码',
    `trans_type_name` STRING COMMENT '转归类型名称',
    `bir_his_code` STRING COMMENT '出生史代码',
    `bir_his_name` STRING COMMENT '出生史名称',
    `6_indi_mon_in_feed_his_code` STRING COMMENT '6个月内喂养史代码',
    `6_indi_mon_in_feed_his_name` STRING COMMENT '6个月内喂养史名称',
    `moth_prg_anem_weeks` STRING COMMENT '母孕期贫血情况(孕周)',
    `moth_prg_anem_hemoglobin` STRING COMMENT '母孕期贫血情况(血红蛋白)',
    `moth_prg_anem_info` STRING COMMENT '母孕期贫血情况',
    `moth_prg_anem_medication` STRING COMMENT '母孕期贫血情况(药物)',
    `moth_prg_anem_dosage` STRING COMMENT '母孕期贫血情况(剂量)',
    `moth_prg_anem_cure` STRING COMMENT '母孕期贫血情况(疗程周)',
    `child_ironfoodstartage` STRING COMMENT '儿童开始添加含铁食物年龄(月)开始食物转换年龄(月)',
    `moth_prg_and_lacta_code` STRING COMMENT '母孕期和哺乳期代码',
    `moth_prg_and_lacta_name` STRING COMMENT '母孕期和哺乳期名称',
    `chld_taki_vitd` STRING COMMENT '儿童服用VitD',
    `begn_taki_vitd_age_mon` STRING COMMENT '开始服用VitD年龄(月)',
    `begn_taki_vitd_age_day` STRING COMMENT '开始服用VitD年龄(天)',
    `begn_taki_vitd` STRING COMMENT '开始服用VitD',
    `rickets_sign_code` STRING COMMENT '佝偻病体征代码',
    `rickets_sign_name` STRING COMMENT '佝偻病体征名称',
    `calc` STRING COMMENT '血钙',
    `blo_phos` STRING COMMENT '血磷',
    `blo_akp` STRING COMMENT '血AKP',
    `blo_25d` STRING COMMENT '血25(OH)D',
    `x_line_exam` STRING COMMENT 'X线检查',
    `hrisk_eval_code` STRING COMMENT '高危评估代码',
    `hrisk_eval_name` STRING COMMENT '高危评估名称',
    `hrisk_sec` STRING COMMENT '高危分段',
    `reg_date` TIMESTAMP COMMENT '登记日期',
    `hrisk_fac_or_abnor` STRING COMMENT '高危因素或异常情况',
    `follo_up_rslt` STRING COMMENT '追访结果',
    `hrisk_spec_cas_mgt_exis_prb` STRING COMMENT '高危专案管理存在问题',
    `hrisk_spec_cas_mgt_guid` STRING COMMENT '高危专案管理指导',
    `refl_emp_id` STRING COMMENT '转诊单位ID',
    `upload_time` TIMESTAMP COMMENT '数据上传时间',
    `updt_time` TIMESTAMP COMMENT '数据更新时间',
    `subsys_code` STRING COMMENT '子系统代码',
    `subsys_name` STRING COMMENT '子系统名称',
    `admdvs` STRING COMMENT '行政区划编码',
    `crte_time` TIMESTAMP COMMENT '数据创建时间',
    `deleted` STRING COMMENT '数据删除状态',
    `deleted_time` TIMESTAMP COMMENT '数据删除时间'
) COMMENT='儿童高危登记表';

