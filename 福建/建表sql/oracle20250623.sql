-- 生成时间：2025-06-23 14:48:13
-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建


ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS';

-- 删除已存在的对象
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE table_creation_log PURGE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 创建日志表
CREATE TABLE table_creation_log (
    id NUMBER PRIMARY KEY,
    table_name VARCHAR2(255) NOT NULL,
    status VARCHAR2(10) NOT NULL,
    message CLOB,
    error_code VARCHAR2(10),
    create_time TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- 创建序列
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE table_creation_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE table_creation_log_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

-- 创建触发器
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER table_creation_log_trg';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE OR REPLACE TRIGGER table_creation_log_trg
BEFORE INSERT ON table_creation_log
FOR EACH ROW
BEGIN
    IF :new.id IS NULL THEN
        SELECT table_creation_log_seq.nextval INTO :new.id FROM dual;
    END IF;
END;
/

-- 创建存储过程
CREATE OR REPLACE PROCEDURE log_table_result(
    p_table_name IN VARCHAR2,
    p_status IN VARCHAR2,
    p_message IN VARCHAR2,
    p_error_code IN VARCHAR2
) AS
BEGIN
    INSERT INTO table_creation_log (table_name, status, message, error_code)
    VALUES (p_table_name, p_status, p_message, p_error_code);
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error logging result for table ' || p_table_name || ': ' || SQLERRM);
END;
/


-- 创建表 tj_charge
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE tj_charge (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    fee_date DATE,
    fee_no VARCHAR2(50),
    refd_mark VARCHAR2(1),
    comb_type VARCHAR2(255),
    tj_comb_sno VARCHAR2(100),
    emp_code VARCHAR2(50),
    mp_name VARCHAR2(100),
    fee_time DATE,
    medfee_sumamt VARCHAR2(255),
    payment_amt VARCHAR2(255),
    offer_amt VARCHAR2(255),
    card_no VARCHAR2(255),
    card_type_code VARCHAR2(50),
    card_type_name VARCHAR2(100),
    invo_no VARCHAR2(255),
    invono_print_time DATE,
    state VARCHAR2(1),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_1_tj_ PRIMARY KEY (uscid, upload_time, sys_prdr_code, fee_date, fee_no, refd_mark)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE tj_charge IS ''体检收费表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码uscid+数据上传时间upload_time+系统建设厂商代码sys_prdr_code+收/退费日期fee_date+收/退费编号fee_no+退费标志refd_mark''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.fee_date IS ''收/退费日期 复合主键；格式为YYYYMMDD''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.fee_no IS ''收/退费编号 复合主键；见门诊收费表说明（1）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.refd_mark IS ''退费标志 复合主键；1：收费；2：退费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.comb_type IS ''体检类别代码 1个人体检2单位体检''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.tj_comb_sno IS ''体检流水号 体检类别为个人体检时必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.emp_code IS ''体检单位编码 院内表示一个体检单位的唯一编码，体检类别为单位体检时必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.mp_name IS ''体检单位名称 体检类别为单位体检时必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.fee_time IS ''收/退费日期时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.medfee_sumamt IS ''收/退费总额 收退费均以正数表达。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.payment_amt IS ''实收金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.offer_amt IS ''优惠金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.card_no IS ''卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.card_type_code IS ''卡类型代码 见卡类型代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.card_type_name IS ''卡类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.invo_no IS ''发票号 超过一张发票时，以“；”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.invono_print_time IS ''发票打印日期时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.state IS ''修改标志 1 是 0 否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.reserve1 IS ''预留一 为处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.reserve2 IS ''预留二 为处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据改造厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN tj_charge.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_1_tj_ ON tj_charge (rid, upload_time)';

    log_table_result('tj_charge', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('tj_charge', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = '成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = '失败' THEN 1 ELSE 0 END) as failed_count
FROM table_creation_log;

-- 显示失败的表详情
SELECT 
    table_name, 
    message, 
    error_code,
    create_time 
FROM table_creation_log 
WHERE status = '失败' 
ORDER BY create_time;