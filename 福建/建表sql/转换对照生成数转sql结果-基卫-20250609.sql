insert overwrite table ods_hcs_phd_health_basic
select 
    concat(files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t.df_id as patientid,
    t.sscardno as card_no,
    '0' as card_type_code,
    '社保卡' as card_type_name,
    t.adress_pro as addr_city,
    t.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t.sex as gender_code,
    d_gender_name.dic_val_name as gender_name,
    t.name as full_name,
    '待定' as patient_souc_code,
    '待定' as patient_souc_name,
    t.mstatus as mrg_stas_code,
    d_mrg_stas_name.dic_val_name as mrg_stas_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    null as birth_addr,
    t.folk as nation_code,
    d_nation_name.dic_val_name as nation_name,
    null as ntly_code,
    null as ntly_name,
    null as mobile,
    t.telphone as tel,
    null as empr_poscode,
    t.workplace as empr_name,
    null as empr_addr,
    null as residential_code,
    null as residential_name,
    null as curr_addr_prov_code,
    t.adress_pro as curr_addr_prov_name,
    null as curr_addr_city_code,
    t.adress_city as curr_addr_city_name,
    null as curr_addr_coty_code,
    t.adress_county as curr_addr_coty_name,
    null as curr_addr_town_code,
    t.adress_rural as curr_addr_town_name,
    null as curr_addr_comm_code,
    null as curr_addr_comm_name,
    t.adress_village as curr_addr_cotry_name,
    t.adrss_hnumber as curr_addr_housnum,
    null as residential_addr,
    null as resd_addr_code,
    null as resd_addr_name,
    null as resd_addr_prov_code,
    t.hkdshe as resd_addr_prov_name,
    null as resd_addr_coty_code,
    t.hkdxia as resd_addr_coty_name,
    null as resd_addr_subd_code,
    t.hkdzhe as resd_addr_subd_name,
    null as resd_addr_comm_code,
    t.hkdcun as resd_addr_comm_name,
    null as resd_addr_cotry_name,
    t.hkdmph as resd_addr_housnum,
    null as resd_addr,
    null as resd_addr_poscode,
    null as coner_name,
    null as relation_code,
    null as relation_name,
    null as coner_addr,
    null as coner_org_name,
    null as coner_poscode,
    null as coner_tel,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) end as deleted_time
from t_dwellerfile as t
left join data_dic_a as d_gender_name on d_gender_name.dic_type_code='GENDER_CODE' and d_gender_name.dic_val_code=t.sex
left join data_dic_a as d_mrg_stas_name on d_mrg_stas_name.dic_type_code='MRG_STAS_CODE' and d_mrg_stas_name.dic_val_code=t.mstatus
left join data_dic_a as d_nation_name on d_nation_name.dic_type_code='NATION_CODE' and d_nation_name.dic_val_code=t.folk
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_home
select 
    concat(files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as files_uscid,
    t.df_id as patientid,
    t.name as full_name,
    t.sex as gender_code,
    d_gender_name.dic_val_name as gender_name,
    to_date(t.birthday,'%Y%m%d') as brdy,
    t.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t.cdegree as edu_background_code,
    d_edu_background_name.dic_val_name as edu_background_name,
    null as occup_code,
    null as occup_name,
    null as emp_status_code,
    null as emp_status_name,
    t.rprtype as per_addr_code,
    d_per_addr_name.dic_val_name as per_addr_name,
    t.folk as nation_code,
    d_nation_name.dic_val_name as nation_name,
    t.bloodtype as blotype_abo_code,
    d_blotype_abo_name.dic_val_name as blotype_abo_name,
    t.rhxx as blotype_rh_code,
    d_blotype_rh_name.dic_val_name as blotype_rh_name,
    t.mstatus as mrg_stas_code,
    d_mrg_stas_name.dic_val_name as mrg_stas_name,
    null as medfee_paymtd_code,
    null as medfee_paymtd_name,
    null as expose_his_code,
    t.expose as expose_his_name,
    case when t.ycbs00 is null then '0' else '1' end as hereditary_mark,
    null as hereditary_code,
    t.ycbs00 as hereditary_name,
    null as chronic_code,
    null as chronic_name,
    d_disa_info_code.dic_val_code as disa_info_code,
    case t.cjqk0 when '0' then '无残疾' when '7' then '视力残疾' when '8' then '听力残疾' when '9' then '言语残疾' when '10' then '智力残疾' when '11' then '肢体残疾' when '12' then '精神残疾' else t.cjqk0 end as disa_info_name,
    null as disable_certificate_no,
    null as brf_code,
    null as brf_name,
    case when t.cfpfss is null then '0' else '1' end as kitchen_exhaust_mark,
    d_kitchen_exhaust_code.dic_val_code as kitchen_exhaust_code,
    case t.cfpfss when '1' then '无' when '2' then '油烟机' when '3' then '换气扇' when '4' then '烟囱' else t.cfpfss end as kitchen_exhaust_name,
    t.rllx as fuel_type_code,
    d_fuel_type_name.dic_val_name as fuel_type_name,
    t.ys as drink_water_type_code,
    d_drink_water_type_name.dic_val_name as drink_water_type_name,
    t.cs as wc_type_code,
    d_wc_type_name.dic_val_name as wc_type_name,
    t.qcl as avian_corral_type_code,
    d_avian_corral_type_name.dic_val_name as avian_corral_type_name,
    to_date(t.jdrq00,'%Y%m%d') as build_date,
    null as build_org_code,
    null as build_org_name,
    null as build_org_tel,
    null as mang_org_code,
    t.doctor as duty_dor_no,
    sysuserinfo.zwxm00 as duty_dor_name,
    t.bz0000 as dscr,
    sysuserinfo.lxdh00 as duty_dor_tel,
    t.lrybh0 as registerhiscode,
    t.lryxm0 as registerhisname,
    null as reg_dor_no,
    null as enter_dor_name,
    t.cjsj as reg_date,
    null as inquirer_name,
    t.investigators as inquirer_no,
    to_date(t.idate,'%Y%m%d') as inquirer_date,
    case t.sfyxda when '1' then '1' when '0' then '2' else t.sfyxda end as health_rec_status_code,
    '待定' as health_rec_status_name,
    null as data_rank,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    t.workplace as workplace,
    t.feepaytype as feepay_type_code,
    t.feepaytype as feepay_type_name,
    t.pobservationtypecode as pobservation_type_code,
    t.pobservationtypecode as pobservation_type_name,
    t.pobservationcode as pobservation_code,
    t.pobservationname as pobservation_name,
    t.pobservationmethods as pobservationmethods_code,
    t.pobservationmethods as pobservationmethods_name,
    t.pobservationresultcode as pobservationresult_code,
    t.pobservationresult as pobservationresult_name,
    t.observationsdate as observations_date,
    t.observationedate as observatione_date,
    t.czzgbx as medpay_uebmi,
    t.czjmbx as medpay_trpbmi,
    t.xrhzyl as medpay_nrcmc,
    t.pkjz as med_fee_pay_way_poor_assi,
    t.syylbx as med_fee_pay_way_busi_hi,
    t.qgf as medpay_fape,
    t.qzf as med_fee_pay_way_full_ownpay,
    t.qtfs00 as med_fee_pay_way_oth_way,
    t.jzsfq as fmhis_fthr,
    t.jzsmq as fmhis_mthr,
    t.jzsxm as fmhis_brot_sist,
    t.jzszn as fmhis_child,
    t.default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) end as deleted_time
from t_dwellerfile as t
left join data_dic_a as d_gender_name on d_gender_name.dic_type_code='GENDER_CODE' and d_gender_name.dic_val_code=t.sex
left join data_dic_a as d_edu_background_name on d_edu_background_name.dic_type_code='EDU_BACKGROUND_CODE' and d_edu_background_name.dic_val_code=t.cdegree
left join data_dic_a as d_per_addr_name on d_per_addr_name.dic_type_code='PER_ADDR_CODE' and d_per_addr_name.dic_val_code=t.rprtype
left join data_dic_a as d_nation_name on d_nation_name.dic_type_code='NATION_CODE' and d_nation_name.dic_val_code=t.folk
left join data_dic_a as d_blotype_abo_name on d_blotype_abo_name.dic_type_code='BLOTYPE_ABO_CODE' and d_blotype_abo_name.dic_val_code=t.bloodtype
left join data_dic_a as d_blotype_rh_name on d_blotype_rh_name.dic_type_code='BLOTYPE_RH_CODE' and d_blotype_rh_name.dic_val_code=t.rhxx
left join data_dic_a as d_mrg_stas_name on d_mrg_stas_name.dic_type_code='MRG_STAS_CODE' and d_mrg_stas_name.dic_val_code=t.mstatus
left join data_dic_a as d_disa_info_code on d_disa_info_code.dic_type_code='DISA_INFO_CODE' and d_disa_info_code.dic_val_name=t.cjqk0
left join data_dic_a as d_kitchen_exhaust_code on d_kitchen_exhaust_code.dic_type_code='KITCHEN_EXHAUST_CODE' and d_kitchen_exhaust_code.dic_val_name=t.cfpfss
left join data_dic_a as d_fuel_type_name on d_fuel_type_name.dic_type_code='FUEL_TYPE_CODE' and d_fuel_type_name.dic_val_code=t.rllx
left join data_dic_a as d_drink_water_type_name on d_drink_water_type_name.dic_type_code='DRINK_WATER_TYPE_CODE' and d_drink_water_type_name.dic_val_code=t.ys
left join data_dic_a as d_wc_type_name on d_wc_type_name.dic_type_code='WC_TYPE_CODE' and d_wc_type_name.dic_val_code=t.cs
left join data_dic_a as d_avian_corral_type_name on d_avian_corral_type_name.dic_type_code='AVIAN_CORRAL_TYPE_CODE' and d_avian_corral_type_name.dic_val_code=t.qcl
left join sysuserinfo on sysuserinfo.ygbh00=t.doctor
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_lifestyle
select 
    concat(subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.df_id as patientid,
    '待定' as files_uscid,
    t.jktj_sfhs_id as ref_no,
    t.shfs_tydl_jcdlsj as phys_exer_adhe_excs_time,
    t.shsf_ysxg as habits_diet_code,
    d_habits_diet_name.dic_val_name as habits_diet_name,
    t.shsf_xyqk_xynl as start_smoke_way_age,
    t.shsf_xyqk_jynl as stop_smoke_way_age,
    t.shfs_yjqk_jjnl as stop_drink_way_age,
    t.shfs_yjqk_ksyjnl as start_drink_way_age,
    t.shfs_yjqk_sfcjj as last_year_drunkenness_mark,
    d_drink_type_code.dic_val_code as drink_type_code,
    case t.shfs_yjzl_ when '1' then '白酒' when '2' then '啤酒' when '3' then '葡萄酒' when '4' then '黄酒' when '5' then '其他' else t.shfs_yjzl_ end as drink_type_name,
    t.shfs_yjzl_qt0000 as drink_type_others,
    t.shfs_zybl_qk as prfs_expo_info,
    t.yyid00 as ins_id,
    t.jktjcs as hl_phys_exam_cnt,
    t.sfhs_zybl_hxpcsnr as prfs_expo_chem_prot_mes_cont,
    t.sfhs_zybl_dwcsnr as prfs_expo_toxi_prot_mes_cont,
    t.sfhs_zybl_sxcsnr as prfs_expo_rdat_prot_mes_cont,
    t.shfs_tydl_dlpl as excs_frqu_code,
    d_excs_frqu_name.dic_val_name as excs_frqu_name,
    t.shfs_tydl_mcdlsj as each_excs_time,
    t.shfs_tydl_dlfs as excs_way,
    t.shsf_xyqk_xyzk as smoke_mark_code,
    d_smoke_mark_name.dic_val_name as smoke_mark_name,
    t.shsf_xyqk_rxyl as smok_day,
    t.shfs_yjqk_yjpl as drnk_frqu_code,
    d_drnk_frqu_name.dic_val_name as drnk_frqu_name,
    t.shfs_yjqk_ryjl as drnk_day,
    t.shfs_yjqk_sfjj as stop_drink_code,
    d_stop_drink_name.dic_val_name as stop_drink_name,
    t.sfhs_zybl_fc as prfs_expo_dust,
    t.sfhs_zybl_fccs as prfs_expo_dust_prot_mes,
    t.sfhs_zybl_qt as prfs_expo_oth,
    t.sfhs_zybl_qtcs as prfs_expo_oth_prot_mes,
    t.sfhs_zybl_fccsnr as prfs_expo_dust_prot_mes_cont,
    null as upload_time,
    null as updt_time,
    null as subsys_code,
    null as subsys_name,
    null as admdvs,
    null as crte_time,
    null as deleted,
    null as deleted_time
from jktj_shfs as t
left join data_dic_a as d_habits_diet_name on d_habits_diet_name.dic_type_code='HABITS_DIET_CODE' and d_habits_diet_name.dic_val_code=t.shsf_ysxg
left join data_dic_a as d_drink_type_code on d_drink_type_code.dic_type_code='DRINK_TYPE_CODE' and d_drink_type_code.dic_val_name=t.shfs_yjzl_
left join data_dic_a as d_excs_frqu_name on d_excs_frqu_name.dic_type_code='EXCS_FRQU_CODE' and d_excs_frqu_name.dic_val_code=t.shfs_tydl_dlpl
left join data_dic_a as d_smoke_mark_name on d_smoke_mark_name.dic_type_code='SMOKE_MARK_CODE' and d_smoke_mark_name.dic_val_code=t.shsf_xyqk_xyzk
left join data_dic_a as d_drnk_frqu_name on d_drnk_frqu_name.dic_type_code='DRNK_FRQU_CODE' and d_drnk_frqu_name.dic_val_code=t.shfs_yjqk_yjpl
left join data_dic_a as d_stop_drink_name on d_stop_drink_name.dic_type_code='STOP_DRINK_CODE' and d_stop_drink_name.dic_val_code=t.shfs_yjqk_sfjj
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_illness
select 
    concat(ref_no,files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.disease_history_id as ref_no,
    '待定' as files_uscid,
    t.health_record_no as patientid,
    t.ot_jbgw_person_pdh_001 as dise_his_type_code,
    d_dise_his_type_name.dic_val_name as dise_his_type_name,
    t.ot_jbgw_person_pdh_002 as cnfm_date,
    null as remark,
    '0' as state,
    t.update_date as business_time,
    null as reserve1,
    null as reserve2,
    t.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    t.default_update_time as upload_time,
    t.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t.provice_district_code as admdvs,
    t.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from ot_jbgw_person_pdh as t
left join data_dic_a as d_dise_his_type_name on d_dise_his_type_name.dic_type_code='DISE_HIS_TYPE_CODE' and d_dise_his_type_name.dic_val_code=t.ot_jbgw_person_pdh_001
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_surgery
select 
    concat(ref_no,files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.surgery_history_id as ref_no,
    '待定' as files_uscid,
    t.health_record_no as patientid,
    t.ot_jbgw_person_poh_001 as proc_name,
    t.ot_jbgw_person_poh_002 as proc_date,
    null as remark,
    '0' as state,
    t.update_date as business_time,
    null as reserve1,
    null as reserve2,
    t.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    t.default_update_time as upload_time,
    t.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t.provice_district_code as admdvs,
    t.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from ot_jbgw_person_poh as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_injury
select 
    concat(ref_no,files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.trauma_history_id as ref_no,
    '待定' as files_uscid,
    t.health_record_no as patientid,
    t.ot_jbgw_person_trauma001 as trauma_name,
    t.ot_jbgw_person_trauma02 as trauma_date,
    null as remark,
    '0' as state,
    t.update_date as business_time,
    null as reserve1,
    null as reserve2,
    t.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    t.default_update_time as upload_time,
    t.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t.provice_district_code as admdvs,
    t.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from ot_jbgw_person_trauma as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_transfusion
select 
    concat(ref_no,files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.blood_transfusion_history_id as ref_no,
    '待定' as files_uscid,
    t.health_record_no as patientid,
    t.ot_jbgw_person_blood_002 as transfuse_reason,
    t.ot_jbgw_person_blood_001 as transfuse_datetime,
    null as remark,
    '0' as state,
    t.update_date as business_time,
    null as reserve1,
    null as reserve2,
    t.provice_district_code as provice_district_code,
    '待定' as provice_district_name,
    t.default_update_time as upload_time,
    t.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t.provice_district_code as admdvs,
    t.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from ot_jbgw_person_blood as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phd_health_family
select 
    concat(ref_no,files_uscid,patientid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as ref_no,
    '待定' as files_uscid,
    t_dwellerfile.df_id as patientid,
    '待定' as relation_patient_code,
    '待定' as relation_patient_name,
    '待定' as disease_code,
    t_dwellerfile.jzsfq as disease_name,
    null as remark,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t_dwellerfile.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) end as deleted_time
from  as t
where 1=1 and jzsfq is not null
union all
where jzsmq is not null
union all
where jzsxm is not null
union all
where jzszn is not null
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_sign
select 
    concat(sign_up_uscid,signid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t.ot_jtys_sign_id as signid,
    t.family_doctor_id as sign_dor_no,
    '待定' as sign_dor_name,
    t.jtys_team_id as sign_team_code,
    oe_jtys_team.team_name as sign_team_name,
    t.id_card_type as psncert_type_code,
    d_psncert_type_name.dic_val_name as psncert_type_name,
    t.id_card_no as certno,
    t.name as resident_name,
    t.health_record_id as health_rec_id,
    t.service_date as sign_datetime,
    t.ot_jtys_sign_004 as unsign_date,
    null as unsign_reason,
    '待定' as sign_status_code,
    '待定' as sign_status_name,
    null as reg_dor_code,
    null as reg_dor_name,
    null as reg_time,
    null as remark,
    '0' as state,
    t.create_time as create_time,
    null as reserve1,
    null as reserve2,
    oe_jtys_team.presideid_card_type as presideid_card_type_code,
    d_presideid_card_type_name.dic_val_name as presideid_card_type_name,
    oe_jtys_team.presideid_card_no as presideid_card_no,
    t.default_update_time as upload_time,
    t.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    t.provice_district_code as admdvs,
    t.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from ot_jtys_sign as t
left join oe_jtys_team on t.FAMILY_DOCTOR_ ID = oe_jtys_team.FAMILY_DOCTOR_ ID
left join data_dic_a as d_psncert_type_name on d_psncert_type_name.dic_type_code='PSNCERT_TYPE_CODE' and d_psncert_type_name.dic_val_code=t.id_card_type
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_record
select 
    concat(sign_up_uscid,health_rec_id,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t.zzdah0 as health_rec_id,
    '待定' as householder_psncert_type_code,
    '待定' as householder_psncert_type_name,
    '待定' as householder_cert_no,
    t.hmname as resident_name,
    '待定' as patientid,
    t.tel as family_tel,
    null as family_addr_code,
    null as family_addr_name,
    null as family_addr_prov_code,
    t.adress_pro as family_addr_prov_name,
    null as family_addr_city_code,
    t.adress_city as family_addr_city_name,
    null as family_addr_coty_code,
    t.adress_county as family_addr_coty_name,
    null as family_addr_town_code,
    t.adress_rural as family_addr_town_name,
    null as family_addr_comm_code,
    null as family_addr_comm_name,
    t.adress_village as family_addr_cotry_name,
    t.adrss_hnumber as family_addr_housnum,
    concat(adress_pro,adress_city,adress_county,adress_rural,adress_village,adrss_hnumber) as family_addr,
    null as poscode,
    null as family_rec_status_code,
    null as family_rec_status_name,
    null as inquirer_staff_no,
    null as inquirer_staff_name,
    null as inquirer_date,
    t.creator as registerhiscode,
    '待定' as registerhisname,
    to_date(t.cdate,'%Y%m%d') as reg_date,
    null as data_rank,
    t.default_create_time as create_time,
    t.isdel as state,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    t.default_update_time as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    to_date(t.cdate,'%Y%m%d') as crte_time,
    t.isdel as deleted,
    null as deleted_time
from t_familyfile as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_member
select 
    concat(sign_up_uscid,health_rec_id,psncert_type_code,psncert_type_name,certno,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as sign_up_uscid,
    t_familyfile.f_id as health_rec_id,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    t.idcardno as certno,
    t_familyfile.hmname as resident_name,
    case t.r_id when '0' then '1' else '0' end as householder_mark,
    t.r_id as householder_relation_code,
    d_householder_relation_name.dic_val_name as householder_relation_name,
    null as data_rank,
    t.cjsj as create_time,
    case t.sfyxda when '0' then '1' when '1' then '0' else t.sfyxda end as state,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    t.cjsj as crte_time,
    case t.isdel0 when '0' then '1' when '1' then '0' else t.isdel0 end as deleted,
    case t.isdel0 when '0' then to_timestamp(concat(t.zhgxrq,' ',t.zhgxsj)) end as deleted_time
from t_dwellerfile as t
left join t_familyfile on t.f_id = t_familyfile.f_id
left join data_dic_a as d_householder_relation_name on d_householder_relation_name.dic_type_code='HOUSEHOLDER_RELATION_CODE' and d_householder_relation_name.dic_val_code=t.r_id
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_staff
select 
    concat(job_no,uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    sysuserinfo.userid as job_no,
    '待定' as uscid,
    '待定' as rgst_name,
    sysuserinfo.zwxm00 as full_name,
    sysuserinfo.sfzh00 as certno,
    sysuserinfo.groupid as dept_code,
    null as team,
    null as pro_tech_duty_code,
    null as pro_tech_duty_name,
    null as job_title_code,
    null as job_title_name,
    to_date(t.csrq00,'%Y%m%d') as brdy,
    null as psn_type_code,
    null as psn_type_name,
    sysuserinfo.ygxz00 as staff_nature_code,
    d_staff_nature_name.dic_val_name as staff_nature_name,
    null as practice_type_code,
    null as practice_type_name,
    null as gp_flag,
    null as edu_background_code,
    null as edu_background_name,
    null as professional_code,
    null as professional_name,
    '0' as state,
    to_timestamp(t.xgrqsj) as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    to_timestamp(t.xgrqsj) as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    to_timestamp(t.cjrqsj) as crte_time,
    '0' as deleted,
    null as deleted_time
from  as t
where 1=1 -- and t.ygxz00 in ('0','1','2') -- 0实习医生,1处方医生,2护士
) as tab

-- ================================================
insert overwrite table ods_hcs_phf_health_team
select 
    concat(unified_uscid,team_code,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as unified_uscid,
    oe_jtys_team.jtys_team_id as team_code,
    oe_jtys_team.team_name as team_name,
    '1' as state,
    oe_jtys_team.upload_date as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    oe_jtys_team.update_date as updt_time,
    '待定' as subsys_code,
    '待定' as subsys_name,
    '待定' as admdvs,
    oe_jtys_team.create_time as crte_time,
    '0' as deleted,
    null as deleted_time
from  as t

) as tab

-- ================================================
insert overwrite table ods_hcs_phe_record_info
select 
    concat(test_uscid,examination_no,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    '待定' as test_uscid,
    t.jktj_ybzkid as examination_no,
    t.df_id as patientid,
    t_dwellerfile.idcardno as certno,
    '01' as psncert_type_code,
    '居民身份证' as psncert_type_name,
    null as appoint_id,
    null as plan_code,
    t_dwellerfile.name as full_name,
    to_date(t.edate,'%Y%m%d') as examination_date,
    null as main_dor_no,
    t.doctor as main_dor_name,
    d_sympt_code.dic_val_code as sympt_code,
    t.tjzzqk as sympt_name,
    t.ybzk_tiwen as tprt,
    t.ybzk_ml as pule,
    t.ybzk_hxpl as vent_frqu,
    t.ybzk_zszy as left_dbp,
    t.ybzk_zssy as left_sbp,
    t.ybzk_yszy as right_dbp,
    t.ybzk_yssy as right_sbp,
    t.ybzk_sg as height,
    t.ybzk_tz as weight,
    t.ybzk_tzzs as bmi,
    t.ybzk_yw as waist_cm,
    t.ybzk_tunwei as hipline,
    t.ybzk_ytwbz as whr,
    t.ybzk_lnrzgn as elder_cognition_res_code,
    d_elder_cognition_res_name.dic_val_name as elder_cognition_res_name,
    t.ybzk_lnzljc as elder_wit_score,
    t.lnrjkpj as elder_health_status_code,
    d_elder_health_status_name.dic_val_name as elder_health_status_name,
    t.lnrshpj as elder_self_eval_code,
    d_elder_self_eval_name.dic_val_name as elder_self_eval_name,
    t.ybzk_lnqgzt as elder_emotional_status_code,
    d_elder_emotional_status_name.dic_val_name as elder_emotional_status_name,
    t.ybzk_lnyypf as elder_depression_score,
    null as excs_frqu_code,
    null as excs_frqu_name,
    null as exercise_each,
    null as insist_exercise_month,
    null as exercise_code,
    null as habits_diet_code,
    null as habits_diet_name,
    null as smok_info_code,
    null as smok_info_name,
    null as smok_day,
    null as start_smoke_age,
    null as stop_smoke_age,
    null as drnk_frqu_code,
    null as drnk_frqu_name,
    null as drnk_day,
    null as stop_drink_code,
    null as stop_drink_name,
    null as stop_drink_age,
    null as start_drink_age,
    null as last_year_drunkenness_mark,
    null as drink_type_code,
    null as drink_type_name,
    jktj_zqgn.jktj_zqgn_kqkc as oral_exterior_code,
    d_oral_exterior_name.dic_val_name as oral_exterior_name,
    jktj_zqgn.jktj_zqgn_kqcl as dentition_type_code,
    d_dentition_type_name.dic_val_name as dentition_type_name,
    concat(jktj_zqgn.jktj_zqgn_quechi,'',jktj_zqgn.jktj_zqgn_quchi0,'',jktj_zqgn.jktj_zqgn_yichi0) as dentition_explain,
    jktj_zqgn.jktj_zqgn_kqyb as phary_exam_res_code,
    d_phary_exam_res_name.dic_val_name as phary_exam_res_name,
    jktj_zqgn.jktj_zqgn_slzy as left_original_value,
    jktj_zqgn.jktj_zqgn_slyy as right_original_hyperopia_value,
    jktj_zqgn.jktj_zqgn_jzslzy as left_redress_value,
    jktj_zqgn.jktj_zqgn_jzslyy as right_redress_hyperopia_value,
    null as left_vision_code,
    null as left_vision_name,
    null as right_vision_code,
    null as right_vision_name,
    case jktj_ct.ct_yd when '1' then '0' when '2' then '1' else jktj_ct.ct_yd end as eyeground_abnorm_mark,
    jktj_ct.ct_ydqt as fundoscopy_abnorm_descr,
    jktj_zqgn.jktj_zqgn_tl as hear_check_res_code,
    d_hear_check_res_name.dic_val_name as hear_check_res_name,
    jktj_zqgn.jktj_zqgn_ydgn as sport_function_status_code,
    d_sport_function_status_name.dic_val_name as sport_function_status_name,
    d_skin_check_abnorm_code.dic_val_code as skin_check_abnorm_code,
    case jktj_ct.ct_pf when '1' then '正常' when '2' then '潮红' when '3' then '苍白' when '4' then '发绀' when '5' then '黄染' when '6' then '色素沉着' when '7' then '其他' else jktj_ct.ct_pf end as skin_check_abnorm_name,
    jktj_ct.ct_gm as scleral_check_res_code,
    d_scleral_check_res_name.dic_val_name as scleral_check_res_name,
    d_lymph_check_res_code.dic_val_code as lymph_check_res_code,
    case jktj_ct.ct_lbj when '1' then '未触及' when '2' then '锁骨上' when '3' then '腋窝' when '4' then '其他' else jktj_ct.ct_lbj end as lymph_check_res_name,
    jktj_ct.ct_ftzx as barrel_chest_mark,
    case jktj_ct.ct_fhxy when '1' then '0' when '2' then '1' else jktj_ct.ct_fhxy end as lung_abnorm_breath_mark,
    jktj_ct.ct_fhxyyc as lung_abnorm_breath_descr,
    jktj_ct.ct_fly as lung_rale_code,
    d_lung_rale_name.dic_val_name as lung_rale_name,
    jktj_ct.ct_flyqt as lung_rale_describe,
    jktj_ct.ct_xzxl as heart_rate,
    jktj_ct.ct_xzxinl as heart_rate_type_code,
    d_heart_rate_type_name.dic_val_name as heart_rate_type_name,
    jktj_ct.ct_xzzy as heart_murmur_mark,
    jktj_ct.ct_xzzyqt as heart_murmur_describe,
    jktj_ct.ct_fbyt as abdominal_tend_mark,
    jktj_ct.ct_fbytqt as abdominal_tend_descr,
    jktj_ct.ct_fbbk as abdominal_mass_mark,
    jktj_ct.ct_fbbkqt as abdominal_mass_descr,
    jktj_ct.ct_fbgd as abdominal_hepatauxe_mark,
    jktj_ct.ct_fbgdqt as abdominal_hepatauxe_descr,
    jktj_ct.ct_fbpd as splenauxe_mark,
    jktj_ct.ct_fbpdqt as splenauxe_descr,
    jktj_ct.ct_fbydzy as abdominal_dullness_mark,
    jktj_ct.ct_fbydzyqt as abdominal_dullness_descr,
    jktj_ct.ct_xzsz as legs_edema_check_res_code,
    d_legs_edema_check_res_name.dic_val_name as legs_edema_check_res_name,
    jktj_ct.ct_zbdmbd as foot_dorsal_artery_code,
    d_foot_dorsal_artery_name.dic_val_name as foot_dorsal_artery_name,
    jktj_ct.ct_gmzz as anus_check_res_type_code,
    d_anus_check_res_type_name.dic_val_name as anus_check_res_type_name,
    jktj_ct.ct_rxqk as breast_check_res_code,
    d_breast_check_res_name.dic_val_name as breast_check_res_name,
    case jktj_ct.ct_fkwy when '1' then '0' when '2' then '1' else jktj_ct.ct_fkwy end as vulva_abnorm_mark,
    jktj_ct.ct_fkwyqt as vulva_abnorm_descr,
    case jktj_ct.ct_fkyd when '1' then '0' when '2' then '1' else jktj_ct.ct_fkyd end as vagina_abnorm_mark,
    jktj_ct.ct_fkydqt as vagina_abnorm_descr,
    case jktj_ct.ct_fkgj when '1' then '0' when '2' then '1' else jktj_ct.ct_fkgj end as cervix_abnorm_mark,
    jktj_ct.ct_fkgjqt as cervix_abnorm_descr,
    case jktj_ct.ct_fkgt when '1' then '0' when '2' then '1' else jktj_ct.ct_fkgt end as corpusuteri_abnorm_mark,
    jktj_ct.ct_fkgtqt as corpusuteri_abnorm_descr,
    case jktj_ct.ct_fkfj when '1' then '0' when '2' then '1' else jktj_ct.ct_fkfj end as adnex_abnorm_mark,
    jktj_ct.ct_fkfjqt as gyn_adnex_abnorm_descr,
    jktj_ct.ct_qt as other_health_check_res,
    case jktj_fzjc.fzjc_xdt when '1' then '0' when '2' then '1' else jktj_fzjc.fzjc_xdt end as ecg_abnorm_mark,
    jktj_fzjc.fzjc_xdtqt as ecg_abnorm_descr,
    case jktj_fzjc.fzjc_xbxxp when '1' then '0' when '2' then '1' else jktj_fzjc.fzjc_xbxxp end as xray_abnorm_mark,
    jktj_fzjc.fzjc_xbxxqt as xray_abnorm_descr,
    jktj_fzjc.fzjc_bc as bscan_abnorm_mark,
    jktj_fzjc.fzjc_bcqt as bscan_abnorm_descr,
    case jktj_fzjc.fzjc_gjtp when '1' then '0' when '2' then '1' else jktj_fzjc.fzjc_gjtp end as cps_abnorm_mark,
    jktj_fzjc.fzjc_gjtpqt as cps_abnorm_descr,
    jktj_fzjc.fzjc_qt0000 as other_assist_check,
    jktj_zyjkwt.zyjkwt_nxg as cardiovascular_code,
    d_cardiovascular_name.dic_val_name as cardiovascular_name,
    jktj_zyjkwt.zyjkwt_sz as chronic_kidney_code,
    d_chronic_kidney_name.dic_val_name as chronic_kidney_name,
    jktj_zyjkwt.zyjkwt_xzwfx as cardiopathy_code,
    d_cardiopathy_name.dic_val_name as cardiopathy_name,
    jktj_zyjkwt.zyjkwt_xgwfx as vas_code,
    d_vas_name.dic_val_name as vas_name,
    jktj_zyjkwt.zyjkwt_ybwfx as oculopathy_code,
    d_oculopathy_name.dic_val_name as oculopathy_name,
    jktj_zyjkwt.zyjkwt_sjxtjb as neuro_exam_abnormal_mark,
    jktj_zyjkwt.zyjkwt_sjxtqt as neuro_exam_abnormal_descr,
    jktj_zyjkwt.zyjkwt_qtxtjb as systemic_disease_mark,
    jktj_zyjkwt.zyjkwt_qtxtqt as systemic_disease_descr,
    jktj_jkpj.jkpj_tjsfyc as hl_eval_abnorm_flag,
    concat(jktj_jkpj.jkpj_tjyc1,'',jktj_jkpj.jkpj_tjyc2,'',jktj_jkpj.jkpj_tjyc3,'',jktj_jkpj.jkpj_tjyc4) as abnormal_descr,
    case jktj_jkpj.jkzd when '1' then '0' when '2' then '1' when '3' then '2' when '4' then '3' else jktj_jkpj.jkzd end as health_guide_code,
    d_health_guide_name.dic_val_name as health_guide_name,
    case jktj_jkpj.wxyskz when '7' then '9' else jktj_jkpj.wxyskz end as risk_control_ad_code,
    d_risk_control_ad_name.dic_val_name as risk_control_ad_name,
    jktj_jkpj.wxyskz_jtzmb as aim_weight,
    d_offer_vacc_code.dic_val_code as offer_vacc_code,
    jktj_jkpj.wxyskz_ymjz as offer_vacc_name,
    null as data_rank,
    '0' as state,
    t.ctime as business_time,
    null as reserve1,
    null as reserve2,
    t.gxygbh as updt_emplo_no,
    t.gxygxm as updt_emplo_name,
    t.default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_ybzk as t
left join t_dwellerfile on t.df_id= t_dwellerfile.df_id
left join data_dic_a as d_sympt_code on d_sympt_code.dic_type_code='SYMPT_CODE' and d_sympt_code.dic_val_name=t.tjzzqk
left join data_dic_a as d_elder_cognition_res_name on d_elder_cognition_res_name.dic_type_code='ELDER_COGNITION_RES_CODE' and d_elder_cognition_res_name.dic_val_code=t.ybzk_lnrzgn
left join data_dic_a as d_elder_health_status_name on d_elder_health_status_name.dic_type_code='ELDER_HEALTH_STATUS_CODE' and d_elder_health_status_name.dic_val_code=t.lnrjkpj
left join data_dic_a as d_elder_self_eval_name on d_elder_self_eval_name.dic_type_code='ELDER_SELF_EVAL_CODE' and d_elder_self_eval_name.dic_val_code=t.lnrshpj
left join data_dic_a as d_elder_emotional_status_name on d_elder_emotional_status_name.dic_type_code='ELDER_EMOTIONAL_STATUS_CODE' and d_elder_emotional_status_name.dic_val_code=t.ybzk_lnqgzt
left join jktj_zqgn on t.df_id= jktj_zqgn.df_id
left join data_dic_a as d_oral_exterior_name on d_oral_exterior_name.dic_type_code='ORAL_EXTERIOR_CODE' and d_oral_exterior_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_dentition_type_name on d_dentition_type_name.dic_type_code='DENTITION_TYPE_CODE' and d_dentition_type_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_phary_exam_res_name on d_phary_exam_res_name.dic_type_code='PHARY_EXAM_RES_CODE' and d_phary_exam_res_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hear_check_res_name on d_hear_check_res_name.dic_type_code='HEAR_CHECK_RES_CODE' and d_hear_check_res_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_sport_function_status_name on d_sport_function_status_name.dic_type_code='SPORT_FUNCTION_STATUS_CODE' and d_sport_function_status_name.dic_val_code=t.jktj_zqgn_ydgn
left join data_dic_a as d_skin_check_abnorm_code on d_skin_check_abnorm_code.dic_type_code='SKIN_CHECK_ABNORM_CODE' and d_skin_check_abnorm_code.dic_val_name=t.ct_pf
left join jktj_ct on t.df_id= jktj_ct.df_id
left join data_dic_a as d_scleral_check_res_name on d_scleral_check_res_name.dic_type_code='SCLERAL_CHECK_RES_CODE' and d_scleral_check_res_name.dic_val_code=t.ct_gm
left join data_dic_a as d_lymph_check_res_code on d_lymph_check_res_code.dic_type_code='LYMPH_CHECK_RES_CODE' and d_lymph_check_res_code.dic_val_name=t.ct_lbj
left join data_dic_a as d_lung_rale_name on d_lung_rale_name.dic_type_code='LUNG_RALE_CODE' and d_lung_rale_name.dic_val_code=t.ct_fly
left join data_dic_a as d_heart_rate_type_name on d_heart_rate_type_name.dic_type_code='HEART_RATE_TYPE_CODE' and d_heart_rate_type_name.dic_val_code=t.ct_xzxinl
left join data_dic_a as d_legs_edema_check_res_name on d_legs_edema_check_res_name.dic_type_code='LEGS_EDEMA_CHECK_RES_CODE' and d_legs_edema_check_res_name.dic_val_code=t.ct_xzsz
left join data_dic_a as d_foot_dorsal_artery_name on d_foot_dorsal_artery_name.dic_type_code='FOOT_DORSAL_ARTERY_CODE' and d_foot_dorsal_artery_name.dic_val_code=t.ct_zbdmbd
left join data_dic_a as d_anus_check_res_type_name on d_anus_check_res_type_name.dic_type_code='ANUS_CHECK_RES_TYPE_CODE' and d_anus_check_res_type_name.dic_val_code=t.ct_gmzz
left join data_dic_a as d_breast_check_res_name on d_breast_check_res_name.dic_type_code='BREAST_CHECK_RES_CODE' and d_breast_check_res_name.dic_val_code=t.ct_rxqk
left join jktj_fzjc on t.df_id= jktj_fzjc.df_id
left join jktj_zyjkwt on t.df_id= jktj_zyjkwt.df_id
left join data_dic_a as d_cardiovascular_name on d_cardiovascular_name.dic_type_code='CARDIOVASCULAR_CODE' and d_cardiovascular_name.dic_val_code=t.zyjkwt_nxg
left join data_dic_a as d_chronic_kidney_name on d_chronic_kidney_name.dic_type_code='CHRONIC_KIDNEY_CODE' and d_chronic_kidney_name.dic_val_code=t.zyjkwt_sz
left join data_dic_a as d_cardiopathy_name on d_cardiopathy_name.dic_type_code='CARDIOPATHY_CODE' and d_cardiopathy_name.dic_val_code=t.zyjkwt_xzwfx
left join data_dic_a as d_vas_name on d_vas_name.dic_type_code='VAS_CODE' and d_vas_name.dic_val_code=t.zyjkwt_xgwfx
left join data_dic_a as d_oculopathy_name on d_oculopathy_name.dic_type_code='OCULOPATHY_CODE' and d_oculopathy_name.dic_val_code=t.zyjkwt_ybwfx
left join jktj_jkpj on t.df_id= jktj_jkpj.df_id
left join data_dic_a as d_health_guide_name on d_health_guide_name.dic_type_code='HEALTH_GUIDE_CODE' and d_health_guide_name.dic_val_code=t.jkzd
left join data_dic_a as d_risk_control_ad_name on d_risk_control_ad_name.dic_type_code='RISK_CONTROL_AD_CODE' and d_risk_control_ad_name.dic_val_code=t.wxyskz
left join data_dic_a as d_offer_vacc_code on d_offer_vacc_code.dic_type_code='OFFER_VACC_CODE' and d_offer_vacc_code.dic_val_name=t.wxyskz_ymjz
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_med_info
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.ywbh_id as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    t.ywmc as drug_name,
    null as drugusage,
    null as drug_used_dosunt,
    null as drug_used_sdose,
    null as drug_used_idose,
    d_drug_used_way_code.dic_val_code as drug_used_way_code,
    t.ywyf as drug_used_way_name,
    t.yysj as medication_time,
    null as tcmdrug_type_code,
    null as tcmdrug_type_name,
    t.fyycx as medication_compliance_code,
    d_medication_compliance_name.dic_val_name as medication_compliance_name,
    null as data_rank,
    '0' as state,
    t.default_update_time as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    t.default_update_time as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.default_update_time as crte_time,
    '0' as deleted,
    null as deleted_time
from t_mxjb_sf_yyqk as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_drug_used_way_code on d_drug_used_way_code.dic_type_code='DRUG_USED_WAY_CODE' and d_drug_used_way_code.dic_val_name=t.ywyf
left join data_dic_a as d_medication_compliance_name on d_medication_compliance_name.dic_type_code='MEDICATION_COMPLIANCE_CODE' and d_medication_compliance_name.dic_val_code=t.fyycx
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_home_bed_history
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.jtbcsid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    to_date(t.jcrq,'%Y%m%d') as build_bed_date,
    to_date(t.ccrq,'%Y%m%d') as remove_bed_date,
    t.bjyy as bed_reason,
    t.yljgmc as bed_org_name,
    t.bcbah as medcasno,
    null as data_rank,
    '0' as state,
    t.cdate as business_time,
    null as reserve1,
    null as reserve2,
    t.cdate as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_jtbcs as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_nonimm_vacc_history
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.fmyghid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    d_vacc_code.dic_val_code as vacc_code,
    t.ymmc as vacc_name,
    to_date(t.jzrq,'%Y%m%d') as vacc_time,
    '待定' as vaccinate_uscid,
    t.jzyy as vaccinate_org_name,
    null as data_rank,
    '0' as state,
    t.cdate as business_time,
    null as reserve1,
    null as reserve2,
    t.cdate as upload_time,
    to_timestamp(t.edate) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_fmygh as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_vacc_code on d_vacc_code.dic_type_code='VACC_CODE' and d_vacc_code.dic_val_name=t.ymmc
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_elderly_care_assess
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    jktj_ybzk.jktj_ybzkid as ref_no,
    '待定' as test_uscid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    null as dinner_score,
    null as freshen_score,
    null as dress_score,
    null as toilet_score,
    null as acty_score,
    jktj_ybzk.lnrshpj as total_score,
    null as data_rank,
    '0' as state,
    jktj_ybzk.ctime as business_time,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    to_timestamp(t.zhbjsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    jktj_ybzk.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from  as t
where 1=1 and t.lnrshpj is not null
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_lab_record
select 
    concat(test_uscid,hl_phys_exam_asst_exam_id,asst_exam_kdn_fun_na_conc,asst_exam_pl_trigl,asst_exam_pl_sero_high_den_lipopr_chol) as rid
    ,tab.*
from 
(
select  
    '待定' as test_uscid,
    t.jktj_fzjcid as hl_phys_exam_asst_exam_id,
    t.yyid00 as ins_id,
    t.df_id as hl_file_id,
    t.jktjcs as hl_phys_exam_cnt,
    t.fzjc_kfxt as asst_exam_empt_stom_bloo_gluc,
    t.fzjc_kfxthk as asst_exam_empt_stom_bloo_gluc_mg,
    t.fzjc_xcghdb as asst_exam_bloo_rout_hemog,
    t.fzjc_xcgbxb as asst_exam_bloo_rout_whit_bloo_cells,
    t.fzjc_xcgxxb as asst_exam_bloo_rout_plate,
    t.fzjc_xcgqt as asst_exam_bloo_rout_oth,
    t.fzjc_ncgndb as asst_exam_urin_rout_urin_prot,
    t.fzjc_ncgnt as asst_exam_urin_rout_urin_gluc,
    t.fzjc_ncgntt as asst_exam_urin_rout_urin_keto,
    t.fzjc_ncgnqx as asst_exam_urin_rout_urin_occu_bloo,
    t.fzjc_ncgqt as asst_exam_urin_rout_oth,
    t.fzjc_nwlbdb as asst_exam_urin_micro_albu,
    t.fzjc_dbqx as asst_exam_sto_occu_bloo,
    t.fzjc_ggnxqb as asst_exam_live_fun_sero_gluta_transa,
    t.fzjc_ggnxqc as asst_exam_live_fun_sero_grain_grass_transa,
    t.fzjc_ggnbdb as asst_exam_live_fun_albu,
    t.fzjc_ggnzdh as asst_exam_live_fun_totl_bili,
    t.fzjc_ggnjhd as asst_exam_live_fun_comb_bili,
    t.fzjc_sgnqjg as asst_exam_kdn_fun_sero_creat,
    t.fzjc_sgnnsd as asst_exam_kdn_fun_blo_urea_nitr,
    t.fzjc_sgnjnd as asst_exam_kdn_fun_k_conc,
    t.fzjc_sgnnnd as asst_exam_kdn_fun_na_conc,
    t.fzjc_xzzdgc as asst_exam_pl_totl_chol,
    t.fzjc_xzgysz as asst_exam_pl_trigl,
    t.fzjc_xzdmdz as asst_exam_pl_sero_low_den_lipopr_chol,
    t.fzjc_xzgmdz as asst_exam_pl_sero_high_den_lipopr_chol,
    t.fzjc_thxhdb as asst_exam_glyc_hemog,
    t.fzjc_yxgyky as asst_exam_hepa_b_surf_anti,
    t.fzjc_yd as asst_exam_fund,
    t.fzjc_ydqt as asst_exam_fund_abn,
    t.fzjc_xdt as asst_exam_electro,
    t.fzjc_xdtqt as asst_exam_electro_abn,
    t.fzjc_xbxxp as asst_exam_chst_x_line_piec,
    t.fzjc_xbxxqt as asst_exam_chst_x_line_piec_abn,
    t.fzjc_bc as asst_exam_b_over,
    t.fzjc_bcqt as asst_exam_b_over_oth,
    t.fzjc_gjtp as asst_exam_cerv_smea,
    t.fzjc_gjtpqt as asst_exam_cerv_smea_abn,
    t.fzjc_qt0000 as asst_exam_oth,
    t.fzjc_ncgbxb as urin_rout_urin_whit_bloo_cells,
    t.fzjc_ncgdhs as urin_rout_urin_bili,
    t.fzjc_ncgyxsy as urin_rout_urin_nit,
    t.fzjc_ggnag as live_fun_albu_glon_rat,
    t.fzjc_ggnudbil as live_fun_indi_bili,
    t.fzjc_ncgalp as live_fun_alp,
    t.fzjc_ggnggt as live_fun_r_ggt,
    t.fzjc_sgnns as kdn_fun_uric_acid,
    t.fzjc_xdtfrist as asst_exam_electro1,
    t.fzjc_xdtsecond as asst_exam_electro2,
    t.fzjc_xdtthree as asst_exam_electro3,
    t.fzjc_bc1 as abd_b_over_lgp_sple,
    t.fzjc_bcqt1 as abd_b_over_lgp_sple_oth,
    t.ctime as asst_exam_rcd_sav_time,
    t.fzjc_bc2 as asst_exam_abd_b_over1,
    t.fzjc_bc3 as asst_exam_abd_b_over2,
    t.fzjc_bc4 as asst_exam_abd_b_over3,
    t.fzjc_bc5 as asst_exam_abd_b_over4,
    null as reserve1,
    null as reserve2,
    t.default_update_time as upload_time,
    t.zhxgsj as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_fzjc as t
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_physical_exam
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.jktj_ctid as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t.df_id as patientid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    t.jktj_ctid as check_id,
    t.ct_pf as skin_codition_code,
    d_skin_codition_name.dic_val_name as skin_codition_name,
    t.ct_pfqt as skin_condition_remark,
    t.ct_gm as sclera_code,
    d_sclera_name.dic_val_name as sclera_name,
    t.ct_gmqt as sclera_remark,
    t.ct_lbj as lymph_node_code,
    d_lymph_node_name.dic_val_name as lymph_node_name,
    t.ct_lbjqt as lymph_node_remark,
    t.ct_ftzx as barrel_chest_flag,
    t.ct_fhxy as breath_sound_code,
    d_breath_sound_name.dic_val_name as breath_sound_name,
    t.ct_fhxyyc as breath_sound_abnormal_remark,
    t.ct_fly as rale_code,
    d_rale_name.dic_val_name as rale_name,
    t.ct_flyqt as rale_remark,
    t.ct_xzxl as heart_rate,
    t.ct_xzxinl as heart_rhythm_code,
    d_heart_rhythm_name.dic_val_name as heart_rhythm_name,
    t.ct_xzzy as heart_noise_flag,
    t.ct_xzzyqt as heart_noise_remark,
    t.ct_fbyt as tenderness_flag,
    t.ct_fbytqt as tenderness_remark,
    t.ct_fbbk as bag_piece_flag,
    t.ct_fbbkqt as bag_piece_remark,
    t.ct_fbgd as hepatomegaly_flag,
    t.ct_fbgdqt as hepatomegaly_remark,
    t.ct_fbpd as splenomegaly_flag,
    t.ct_fbpdqt as splenomegaly_remark,
    t.ct_fbydzy as move_dullness_flag,
    t.ct_fbydzyqt as move_dullness_remark,
    t.ct_xzsz as legs_edema_check_res_code,
    d_legs_edema_check_res_name.dic_val_name as legs_edema_check_res_name,
    t.ct_zbdmbd as foot_dorsal_artery_code,
    d_foot_dorsal_artery_name.dic_val_name as foot_dorsal_artery_name,
    t.ct_gmzz as anus_check_res_type_code,
    d_anus_check_res_type_name.dic_val_name as anus_check_res_type_name,
    t.ct_gmzzqt as anus_dre_remark,
    t.ct_rxqk as mammary_gland_code,
    d_mammary_gland_name.dic_val_name as mammary_gland_name,
    t.ct_rxqt as mammary_gland_remark,
    t.ct_fkwy as vulva_code,
    d_vulva_name.dic_val_name as vulva_name,
    t.ct_fkwyqt as vulva_remark,
    t.ct_fkyd as vagina_code,
    d_vagina_name.dic_val_name as vagina_name,
    t.ct_fkydqt as vagina_remark,
    t.ct_fkgj as cervical_code,
    d_cervical_name.dic_val_name as cervical_name,
    t.ct_fkgjqt as cervical_remark,
    t.ct_fkgt as uterine_body_code,
    d_uterine_body_name.dic_val_name as uterine_body_name,
    t.ct_fkgtqt as uterine_body_remark,
    t.ct_fkfj as attachment_code,
    d_attachment_name.dic_val_name as attachment_name,
    t.ct_fkfjqt as attachment_remark,
    t.ct_yd as fundus_code,
    d_fundus_name.dic_val_name as fundus_name,
    t.ct_ydqt as fundus_remark,
    t.default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_ct as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_skin_codition_name on d_skin_codition_name.dic_type_code='SKIN_CODITION_CODE' and d_skin_codition_name.dic_val_code=t.ct_pf
left join data_dic_a as d_sclera_name on d_sclera_name.dic_type_code='SCLERA_CODE' and d_sclera_name.dic_val_code=t.ct_gm
left join data_dic_a as d_lymph_node_name on d_lymph_node_name.dic_type_code='LYMPH_NODE_CODE' and d_lymph_node_name.dic_val_code=t.ct_lbj
left join data_dic_a as d_breath_sound_name on d_breath_sound_name.dic_type_code='BREATH_SOUND_CODE' and d_breath_sound_name.dic_val_code=t.ct_fhxy
left join data_dic_a as d_rale_name on d_rale_name.dic_type_code='RALE_CODE' and d_rale_name.dic_val_code=t.ct_fly
left join data_dic_a as d_heart_rhythm_name on d_heart_rhythm_name.dic_type_code='HEART_RHYTHM_CODE' and d_heart_rhythm_name.dic_val_code=t.ct_xzxinl
left join data_dic_a as d_legs_edema_check_res_name on d_legs_edema_check_res_name.dic_type_code='LEGS_EDEMA_CHECK_RES_CODE' and d_legs_edema_check_res_name.dic_val_code=t.ct_xzsz
left join data_dic_a as d_foot_dorsal_artery_name on d_foot_dorsal_artery_name.dic_type_code='FOOT_DORSAL_ARTERY_CODE' and d_foot_dorsal_artery_name.dic_val_code=t.ct_zbdmbd
left join data_dic_a as d_anus_check_res_type_name on d_anus_check_res_type_name.dic_type_code='ANUS_CHECK_RES_TYPE_CODE' and d_anus_check_res_type_name.dic_val_code=t.ct_gmzz
left join data_dic_a as d_mammary_gland_name on d_mammary_gland_name.dic_type_code='MAMMARY_GLAND_CODE' and d_mammary_gland_name.dic_val_code=t.ct_rxqk
left join data_dic_a as d_vulva_name on d_vulva_name.dic_type_code='VULVA_CODE' and d_vulva_name.dic_val_code=t.ct_fkwy
left join data_dic_a as d_vagina_name on d_vagina_name.dic_type_code='VAGINA_CODE' and d_vagina_name.dic_val_code=t.ct_fkyd
left join data_dic_a as d_cervical_name on d_cervical_name.dic_type_code='CERVICAL_CODE' and d_cervical_name.dic_val_code=t.ct_fkgj
left join data_dic_a as d_uterine_body_name on d_uterine_body_name.dic_type_code='UTERINE_BODY_CODE' and d_uterine_body_name.dic_val_code=t.ct_fkgt
left join data_dic_a as d_attachment_name on d_attachment_name.dic_type_code='ATTACHMENT_CODE' and d_attachment_name.dic_val_code=t.ct_fkfj
left join data_dic_a as d_fundus_name on d_fundus_name.dic_type_code='FUNDUS_CODE' and d_fundus_name.dic_val_code=t.ct_yd
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_physical_exam_extend
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.jktj_ctid as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t.df_id as patientid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    t.jktj_ctid as check_id,
    jktj_zqgn.jktj_zqgn_kqkc as oral_lips_code,
    d_oral_lips_name.dic_val_name as oral_lips_name,
    jktj_zqgn.jktj_zqgn_kqcl as oral_dentition_code,
    d_oral_dentition_name.dic_val_name as oral_dentition_name,
    jktj_zqgn.jktj_zqgn_kqyb as oral_pharyngeal_code,
    d_oral_pharyngeal_name.dic_val_name as oral_pharyngeal_name,
    jktj_zqgn.jktj_zqgn_quechi as oral_dentition_missing_tooth,
    jktj_zqgn.jktj_zqgn_quchi0 as oral_dentition_decayed_tooth,
    jktj_zqgn.jktj_zqgn_yichi0 as oral_dentition_false_tooth,
    jktj_zqgn.jktj_zqgn_slzy as eyesight_left,
    jktj_zqgn.jktj_zqgn_slyy as eyesight_right,
    jktj_zqgn.jktj_zqgn_jzslzy as left_redress_value,
    jktj_zqgn.jktj_zqgn_jzslyy as right_redress_hyperopia_value,
    jktj_zqgn.jktj_zqgn_tl as hearing_code,
    d_hearing_name.dic_val_name as hearing_name,
    jktj_zqgn.jktj_zqgn_ydgn as motor_function_code,
    d_motor_function_name.dic_val_name as motor_function_name,
    t.ct_qt as other_remark,
    to_timestamp(t.zhxgsj) as modify_datetime,
    t.default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.ctime as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_ct as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
left join jktj_zqgn on t.df_id= jktj_zqgn.df_id
left join data_dic_a as d_oral_lips_name on d_oral_lips_name.dic_type_code='ORAL_LIPS_CODE' and d_oral_lips_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_oral_dentition_name on d_oral_dentition_name.dic_type_code='ORAL_DENTITION_CODE' and d_oral_dentition_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_oral_pharyngeal_name on d_oral_pharyngeal_name.dic_type_code='ORAL_PHARYNGEAL_CODE' and d_oral_pharyngeal_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hearing_name on d_hearing_name.dic_type_code='HEARING_CODE' and d_hearing_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_motor_function_name on d_motor_function_name.dic_type_code='MOTOR_FUNCTION_CODE' and d_motor_function_name.dic_val_code=t.jktj_zqgn_ydgn
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_main_issues
select 
    concat(ref_no,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.jktj_zyjkwtid as ref_no,
    to_date(t.edate,'%Y%m%d') as exam_datetime,
    '待定' as test_uscid,
    t.df_id as patientid,
    jktj_ybzk.jktj_ybzkid as examination_no,
    t.jktjcs as hl_phys_exam_cnt,
    t.zyjkwt_nxg as cardiovascular_code,
    d_cardiovascular_name.dic_val_name as cardiovascular_name,
    t.zyjkwt_nxgqt as cerebrovascular_disease_remark,
    t.zyjkwt_sz as chronic_kidney_code,
    d_chronic_kidney_name.dic_val_name as chronic_kidney_name,
    t.zyjkwt_szqt as kidney_disease_remark,
    t.zyjkwt_xzwfx as cardiopathy_code,
    d_cardiopathy_name.dic_val_name as cardiopathy_name,
    t.zyjkwt_xzqt as heart_disease_remark,
    t.zyjkwt_xgwfx as vas_code,
    d_vas_name.dic_val_name as vas_name,
    t.zyjkwt_xgqt as vascular_disease_remark,
    t.zyjkwt_ybwfx as oculopathy_code,
    d_oculopathy_name.dic_val_name as oculopathy_name,
    t.zyjkwt_ybqt as eyes_disease_remark,
    t.zyjkwt_sjxtjb as neuro_exam_abnormal_mark,
    t.zyjkwt_sjxtqt as neuro_exam_abnormal_descr,
    t.zyjkwt_qtxtjb as systemic_disease_mark,
    t.zyjkwt_qtxtqt as systemic_disease_descr,
    t.zytzbs_phz as gentle_constitution,
    t.zytzbs_qxz as tcm_vdc,
    t.zytzbs_yangxz as yang_deficiency_constitution,
    t.zytzbs_yinxz as dryness_constitution,
    t.zytzbs_tsz as phlegm_damp_constitution,
    t.zytzbs_srz as damp_heat_constitution,
    t.zytzbs_xyz as blood_stasis_constitution,
    t.zytzbs_qyz as look_depressed_constitution,
    t.zytzbs_tbz as sensitive_constitution,
    jktj_jkpj.jkpj_tjsfyc as medical_abnormal_flag,
    jktj_jkpj.jkpj_tjyc1 as medical_abnormal_remark1,
    jktj_jkpj.jkpj_tjyc2 as medical_abnormal_remark2,
    jktj_jkpj.jkpj_tjyc3 as medical_abnormal_remark3,
    jktj_jkpj.jkpj_tjyc4 as medical_abnormal_remark4,
    case jktj_jkpj.jkzd when '1' then '0' when '2' then '1' when '3' then '2' when '4' then '3' else jktj_jkpj.jkzd end as health_guide_code,
    d_health_guide_name.dic_val_name as health_guide_name,
    case jktj_jkpj.wxyskz when '7' then '9' else jktj_jkpj.wxyskz end as risk_factor_control_code,
    d_risk_factor_control_name.dic_val_name as risk_factor_control_name,
    jktj_jkpj.wxyskz_jtzmb as weight_target,
    jktj_jkpj.wxyskz_ymjz as vaccination,
    jktj_jkpj.wxyskz_qt as risk_factor_control_remark,
    jktj_jkpj.jkzd00 as health_guide_content,
    jktj_jkpj.jjcf00 as healthy_teaching,
    t.cdate as register_datetime,
    t.default_update_time as upload_time,
    to_timestamp(t.zhxgsj) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_zyjkwt as t
left join jktj_ybzk on t.df_id= jktj_ybzk.df_id
left join data_dic_a as d_cardiovascular_name on d_cardiovascular_name.dic_type_code='CARDIOVASCULAR_CODE' and d_cardiovascular_name.dic_val_code=t.zyjkwt_nxg
left join data_dic_a as d_chronic_kidney_name on d_chronic_kidney_name.dic_type_code='CHRONIC_KIDNEY_CODE' and d_chronic_kidney_name.dic_val_code=t.zyjkwt_sz
left join data_dic_a as d_cardiopathy_name on d_cardiopathy_name.dic_type_code='CARDIOPATHY_CODE' and d_cardiopathy_name.dic_val_code=t.zyjkwt_xzwfx
left join data_dic_a as d_vas_name on d_vas_name.dic_type_code='VAS_CODE' and d_vas_name.dic_val_code=t.zyjkwt_xgwfx
left join data_dic_a as d_oculopathy_name on d_oculopathy_name.dic_type_code='OCULOPATHY_CODE' and d_oculopathy_name.dic_val_code=t.zyjkwt_ybwfx
left join jktj_jkpj on t.df_id= jktj_jkpj.df_id
left join data_dic_a as d_health_guide_name on d_health_guide_name.dic_type_code='HEALTH_GUIDE_CODE' and d_health_guide_name.dic_val_code=t.jkzd
left join data_dic_a as d_risk_factor_control_name on d_risk_factor_control_name.dic_type_code='RISK_FACTOR_CONTROL_CODE' and d_risk_factor_control_name.dic_val_code=t.wxyskz
where 1=1
) as tab

-- ================================================
insert overwrite table ods_hcs_phe_organ_function
select 
    concat(hl_phys_exam_orga_efcc_id,test_uscid,subsys_code,admdvs) as rid
    ,tab.*
from 
(
select  
    t.jktj_zqgnid as hl_phys_exam_orga_efcc_id,
    '待定' as test_uscid,
    t.df_id as patientid,
    t.jktjcs as hl_phys_exam_cnt,
    t.jktj_zqgn_kqkc as oral_lips_code,
    d_oral_lips_name.dic_val_name as oral_lips_name,
    t.jktj_zqgn_kqcl as oral_dentition_code,
    d_oral_dentition_name.dic_val_name as oral_dentition_name,
    t.jktj_zqgn_kqyb as oral_pharyngeal_code,
    d_oral_pharyngeal_name.dic_val_name as oral_pharyngeal_name,
    t.jktj_zqgn_slzy as eyesight_left,
    t.jktj_zqgn_slyy as eyesight_right,
    t.jktj_zqgn_jzslzy as left_redress_value,
    t.jktj_zqgn_jzslyy as right_redress_hyperopia_value,
    t.jktj_zqgn_tl as hearing_code,
    d_hearing_name.dic_val_name as hearing_name,
    t.jktj_zqgn_ydgn as motor_function_code,
    d_motor_function_name.dic_val_name as motor_function_name,
    t.jktj_zqgn_quechi as ora_tooth_row_mis,
    t.jktj_zqgn_quchi0 as ora_tooth_row_cari,
    t.jktj_zqgn_yichi0 as ora_tooth_row_denti,
    t.default_update_time as upload_time,
    to_timestamp(t.edate) as updt_time,
    'phe' as subsys_code,
    '健康体检记录信息分册' as subsys_name,
    '待定' as admdvs,
    t.cdate as crte_time,
    '0' as deleted,
    null as deleted_time
from jktj_zqgn as t
left join data_dic_a as d_oral_lips_name on d_oral_lips_name.dic_type_code='ORAL_LIPS_CODE' and d_oral_lips_name.dic_val_code=t.jktj_zqgn_kqkc
left join data_dic_a as d_oral_dentition_name on d_oral_dentition_name.dic_type_code='ORAL_DENTITION_CODE' and d_oral_dentition_name.dic_val_code=t.jktj_zqgn_kqcl
left join data_dic_a as d_oral_pharyngeal_name on d_oral_pharyngeal_name.dic_type_code='ORAL_PHARYNGEAL_CODE' and d_oral_pharyngeal_name.dic_val_code=t.jktj_zqgn_kqyb
left join data_dic_a as d_hearing_name on d_hearing_name.dic_type_code='HEARING_CODE' and d_hearing_name.dic_val_code=t.jktj_zqgn_tl
left join data_dic_a as d_motor_function_name on d_motor_function_name.dic_type_code='MOTOR_FUNCTION_CODE' and d_motor_function_name.dic_val_code=t.jktj_zqgn_ydgn
where 1=1
) as tab

-- ================================================
