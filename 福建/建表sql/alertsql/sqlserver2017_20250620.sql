-- 生成时间：2025-06-20 09:19:42
-- sqlserver2017数据库ALTER语句

-- 10、福建省三医一张网_数据采集标准规范 转诊（院）服务分册(征求意见稿).docx - 转诊（院）记录
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_referral') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_referral') AND name = 'referral_doc_name'))
  EXEC sp_updateextendedproperty 'MS_Description', N'转诊医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_referral', 'COLUMN', 'referral_doc_name';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'转诊医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_referral', 'COLUMN', 'referral_doc_name';
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_referral') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_referral') AND name = 'referral_doc_no'))
  EXEC sp_updateextendedproperty 'MS_Description', N'转诊医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_referral', 'COLUMN', 'referral_doc_no';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'转诊医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_referral', 'COLUMN', 'referral_doc_no';

-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx - 24h内入出院记录
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_inout_rec_in24h') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_inout_rec_in24h') AND name = 'dscg_drord_isu_code'))
  EXEC sp_updateextendedproperty 'MS_Description', N'出院医嘱开立人工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_inout_rec_in24h', 'COLUMN', 'dscg_drord_isu_code';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'出院医嘱开立人工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_inout_rec_in24h', 'COLUMN', 'dscg_drord_isu_code';

-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx - 产程经过记录
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_labor') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_labor') AND name = 'labor_examer_name'))
  EXEC sp_updateextendedproperty 'MS_Description', N'产程检查者姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_labor', 'COLUMN', 'labor_examer_name';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'产程检查者姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_labor', 'COLUMN', 'labor_examer_name';
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_labor') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_labor') AND name = 'labor_examer_code'))
  EXEC sp_updateextendedproperty 'MS_Description', N'产程检查者工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_labor', 'COLUMN', 'labor_examer_code';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'产程检查者工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_labor', 'COLUMN', 'labor_examer_code';

-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx - 抢救记录
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_rescue') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_rescue') AND name = 'rec_doc_code'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_rescue', 'COLUMN', 'rec_doc_code';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_rescue', 'COLUMN', 'rec_doc_code';
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_rescue') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_rescue') AND name = 'rec_doc_name'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_rescue', 'COLUMN', 'rec_doc_name';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_rescue', 'COLUMN', 'rec_doc_name';

-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx - 术前小结
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_preop_sum') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_preop_sum') AND name = 'rec_doc_name'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_sum', 'COLUMN', 'rec_doc_name';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_sum', 'COLUMN', 'rec_doc_name';
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_preop_sum') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_preop_sum') AND name = 'rec_doc_code'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_sum', 'COLUMN', 'rec_doc_code';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_sum', 'COLUMN', 'rec_doc_code';

-- 5、福建省三医一张网_数据采集标准规范 病历管理分册(征求意见稿).docx - 术前讨论
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_preop_discu') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_preop_discu') AND name = 'rec_doc_name'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_discu', 'COLUMN', 'rec_doc_name';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师姓名', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_discu', 'COLUMN', 'rec_doc_name';
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('emr_preop_discu') AND name = 'MS_Description' AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('emr_preop_discu') AND name = 'rec_doc_code'))
  EXEC sp_updateextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_discu', 'COLUMN', 'rec_doc_code';
ELSE
  EXEC sp_addextendedproperty 'MS_Description', N'记录医师工号', 'SCHEMA', 'dbo', 'TABLE', 'emr_preop_discu', 'COLUMN', 'rec_doc_code';

