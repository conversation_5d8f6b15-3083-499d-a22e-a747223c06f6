ALTER TABLE patient_basic_info MODIFY patient_name VARCHAR2(255);
ALTER TABLE patient_clinic_activity MODIFY patient_name VARCHAR2(255);
ALTER TABLE outp_register_record MODIFY patient_name VARCHAR2(255);
ALTER TABLE outp_visit_record MODIFY patient_name VARCHAR2(255);
ALTER TABLE inp_admission_record MODIFY patient_name VARCHAR2(255);
ALTER TABLE inp_discharge_record MODIFY patient_name VARCHAR2(255);
ALTER TABLE lis_request_form MODIFY patient_name VARCHAR2(255);
ALTER TABLE lis_report_master MODIFY patient_name VARCHAR2(255);
ALTER TABLE exam_request_form MODIFY patient_name VARCHAR2(255);
ALTER TABLE exam_report_master MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_otpmedi MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_observmedi MODIFY patient_name VARCHAR2(255);
ALTER TABLE cis_lh_summary MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_adm_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_inhosp_assess MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_first_dis_course MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_daily_dis_course MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_outhosp MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_death_record MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_consult_info MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_sympt_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_opr_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_opr_informed MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_anst_informed MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_oth_informed MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_doc_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE pacu_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE case_base_info MODIFY patient_name VARCHAR2(255);
ALTER TABLE emr_referral MODIFY patient_name VARCHAR2(255);
ALTER TABLE infect_rpt MODIFY patient_name VARCHAR2(255);
ALTER TABLE infec_rec MODIFY patient_name VARCHAR2(255);
ALTER TABLE medmanage_ae_saes MODIFY patient_name VARCHAR2(255);
ALTER TABLE medmanage_ae_maes MODIFY patient_name VARCHAR2(255);
ALTER TABLE medmanage_ae_daes MODIFY patient_name VARCHAR2(255);
