-- 生成时间：2025-06-13 15:45:01
-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建


ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS';

-- 删除已存在的对象
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE table_creation_log PURGE';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- 创建日志表
CREATE TABLE table_creation_log (
    id NUMBER PRIMARY KEY,
    table_name VARCHAR2(255) NOT NULL,
    status VARCHAR2(10) NOT NULL,
    message CLOB,
    error_code VARCHAR2(10),
    create_time TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- 创建序列
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE table_creation_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE SEQUENCE table_creation_log_seq START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

-- 创建触发器
BEGIN
    EXECUTE IMMEDIATE 'DROP TRIGGER table_creation_log_trg';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

CREATE OR REPLACE TRIGGER table_creation_log_trg
BEFORE INSERT ON table_creation_log
FOR EACH ROW
BEGIN
    IF :new.id IS NULL THEN
        SELECT table_creation_log_seq.nextval INTO :new.id FROM dual;
    END IF;
END;
/

-- 创建存储过程
CREATE OR REPLACE PROCEDURE log_table_result(
    p_table_name IN VARCHAR2,
    p_status IN VARCHAR2,
    p_message IN VARCHAR2,
    p_error_code IN VARCHAR2
) AS
BEGIN
    INSERT INTO table_creation_log (table_name, status, message, error_code)
    VALUES (p_table_name, p_status, p_message, p_error_code);
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error logging result for table ' || p_table_name || ': ' || SQLERRM);
END;
/


-- 创建表 patient_basic_info
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE patient_basic_info (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    enroll_time DATE,
    modify_time DATE,
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    enroll_type_code VARCHAR2(100),
    enroll_type_name VARCHAR2(100),
    patient_souc_code VARCHAR2(50),
    patient_souc_name VARCHAR2(100),
    nultitude_type_code VARCHAR2(50),
    nultitude_type_name VARCHAR2(100),
    hos_files_rec_no VARCHAR2(255),
    health_rec_no VARCHAR2(255),
    card_no VARCHAR2(255),
    card_type_code VARCHAR2(50),
    card_type_name VARCHAR2(100),
    opt_medcasno VARCHAR2(255),
    ipt_medcasno VARCHAR2(255),
    medical_no VARCHAR2(255),
    community_resi_flag VARCHAR2(1),
    ic_card_no VARCHAR2(255),
    resi_hlthcard_no VARCHAR2(255),
    ele_hlthcard_no VARCHAR2(255),
    insutype VARCHAR2(50),
    insutype_name VARCHAR2(50),
    med_insu_no VARCHAR2(255),
    hi_card_no VARCHAR2(255),
    gender_code VARCHAR2(50),
    gender_name VARCHAR2(100),
    brdy DATE,
    empr_name VARCHAR2(100),
    empr_tel VARCHAR2(50),
    empr_poscode VARCHAR2(50),
    empr_addr VARCHAR2(4000),
    occup_code VARCHAR2(50),
    occup_name VARCHAR2(100),
    psn_tel VARCHAR2(50),
    curr_addr VARCHAR2(4000),
    current_addr_districts_code VARCHAR2(50),
    current_addr_districts_name VARCHAR2(100),
    curr_addr_prov_name VARCHAR2(100),
    curr_addr_city_name VARCHAR2(100),
    curr_addr_coty_name VARCHAR2(100),
    curr_addr_town_name VARCHAR2(100),
    curr_addr_vil VARCHAR2(100),
    curr_addr_housnum VARCHAR2(100),
    curr_addr_poscode VARCHAR2(100),
    birth_addr VARCHAR2(4000),
    resd_addr_districts_code VARCHAR2(255),
    resd_addr_districts_name VARCHAR2(255),
    birth_addr_prov_name VARCHAR2(100),
    birth_addr_city_name VARCHAR2(100),
    birth_addr_coty_name VARCHAR2(100),
    birth_addr_subd_name VARCHAR2(100),
    birth_addr_vil VARCHAR2(100),
    birth_addr_housnum VARCHAR2(100),
    birth_addr_poscode VARCHAR2(100),
    coner_name VARCHAR2(50),
    coner_psncert_type_code VARCHAR2(255),
    coner_psncert_type_name VARCHAR2(255),
    coner_cert_no VARCHAR2(255),
    patient_rlts_code VARCHAR2(50),
    patient_rlts_name VARCHAR2(100),
    coner_tel VARCHAR2(100),
    coner_addr VARCHAR2(4000),
    coner_addr_prov_name VARCHAR2(255),
    coner_addr_city_name VARCHAR2(255),
    coner_addr_coty_name VARCHAR2(255),
    coner_addr_subd_name VARCHAR2(255),
    coner_addr_vil VARCHAR2(255),
    coner_addr_housnum VARCHAR2(255),
    coner_poscode VARCHAR2(100),
    napl_code VARCHAR2(50),
    napl_name VARCHAR2(100),
    psncert_type_code VARCHAR2(50),
    psncert_type_name VARCHAR2(100),
    certno VARCHAR2(255),
    edu_background_code VARCHAR2(255),
    edu_background_name VARCHAR2(255),
    mrg_stas_code VARCHAR2(50),
    mrg_stas_name VARCHAR2(100),
    nation_code VARCHAR2(50),
    nation_name VARCHAR2(100),
    ntly_code VARCHAR2(50),
    ntly_name VARCHAR2(100),
    blood_type_code VARCHAR2(50),
    blood_type_name VARCHAR2(100),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_1_pat PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, enroll_type_code)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE patient_basic_info IS ''患者基本信息表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+注册类型代码enroll_type_code''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.enroll_time IS ''注册时间 业务时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.modify_time IS ''最后更新时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_name IS ''患者姓名 就诊者本人在公安户籍管理部门正式登记注册的姓氏和名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.enroll_type_code IS ''注册类型代码 复合主键，例如：门诊、急诊、住院、体检等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.enroll_type_name IS ''注册类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_souc_code IS ''患者来源代码 例如：本县、本市其他县、本省其他市、外省等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_souc_name IS ''患者来源名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.nultitude_type_code IS ''人群分类代码 例如：幼托儿童、散居儿童、学生、教师、保育员及保姆等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.nultitude_type_name IS ''人群分类名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.hos_files_rec_no IS ''医院内部档案号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.health_rec_no IS ''城乡居民健康档案编号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.card_no IS ''卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.card_type_code IS ''卡类型代码 社保卡、医保卡、医院自费卡、居民健康卡、电子健康卡、其他卡、医保电子凭证、省管干部/市管干部/在编军人''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.card_type_name IS ''卡类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.opt_medcasno IS ''门诊病案号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.ipt_medcasno IS ''住院病案号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.medical_no IS ''病历卡（本）号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.community_resi_flag IS ''社区居民标志 0 否 1 是''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.ic_card_no IS ''IC卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.resi_hlthcard_no IS ''居民健康卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.ele_hlthcard_no IS ''电子健康卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.insutype IS ''医疗保险类型代码 例如：城镇职工基本医疗保险、公务员补助医疗、企业补充医疗保险、大额补充医疗保险等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.insutype_name IS ''医疗保险类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.med_insu_no IS ''医疗保险号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.hi_card_no IS ''医保卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.gender_code IS ''性别代码 例如：男性、女性、未说明的性别等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.gender_name IS ''性别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.brdy IS ''出生日期''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.empr_name IS ''工作单位名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.empr_tel IS ''工作单位电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.empr_poscode IS ''工作单位邮编''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.empr_addr IS ''工作单位地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.occup_code IS ''职业代码 例如：幼托儿童,学生(大中小学),工人,农民等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.occup_name IS ''职业名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.psn_tel IS ''本人电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr IS ''居住地址详细地址 社保卡患者必填写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.current_addr_districts_code IS ''居住地址行政区划代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.current_addr_districts_name IS ''居住地址行政区划名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_prov_name IS ''居住地-省（自治区、直辖市）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_city_name IS ''居住地-市（地区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_coty_name IS ''居住地-县（区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_town_name IS ''居住地-乡（镇、街道）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_vil IS ''居住地-村（路、街、弄）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_housnum IS ''居住地-门牌号（包括“室”）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.curr_addr_poscode IS ''居住地址邮编''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr IS ''出生详细地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.resd_addr_districts_code IS ''户籍地址行政区划代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.resd_addr_districts_name IS ''户籍地址行政区划名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_prov_name IS ''出生地-省（自治区、直辖市）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_city_name IS ''出生地-市（地区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_coty_name IS ''出生地-县（区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_subd_name IS ''出生地-乡（镇、街道）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_vil IS ''出生地-村（路、街、弄）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_housnum IS ''出生地-门牌号（包括“室”）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.birth_addr_poscode IS ''出生地址邮编''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_name IS ''联系人/监护人姓名 联系人/监护人在公安户籍管理 部门正式登记注册的姓氏和名称 患者为新生儿时为必填，可填写父母家人相关信息;''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_psncert_type_code IS ''联系人/监护人身份证件类别代码 例如：居民身份证,居民户口簿,临时居民身份证,外国人护照,残疾人证等 患者为新生儿时为必填，可填写父母家人相关信息;''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_psncert_type_name IS ''联系人/监护人身份证件类别名称 患者为新生儿时为必填，可填写父母家人相关信息;''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_cert_no IS ''联系人/监护人身份证件号码 患者为新生儿时为必填，可填写父母家人相关信息;''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_rlts_code IS ''与患者关系代码 例如：本人或户主、配偶、子、女等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.patient_rlts_name IS ''与患者关系名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_tel IS ''联系人/监护人电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr IS ''联系人/监护人地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_prov_name IS ''联系人/监护人地址-省（自治区、直辖市）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_city_name IS ''联系人/监护人地址-市（地区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_coty_name IS ''联系人/监护人地址-县（区）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_subd_name IS ''联系人/监护人地址-乡（镇、街道）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_vil IS ''联系人/监护人地址-村（路、街、弄）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_addr_housnum IS ''联系人/监护人地址-门牌号（包括“室”）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.coner_poscode IS ''联系人/监护人邮编''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.napl_code IS ''籍贯代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.napl_name IS ''籍贯名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.psncert_type_code IS ''身份证件类别代码 例如：居民身份证,居民户口簿,临时居民身份证,外国人护照,残疾人证等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.psncert_type_name IS ''身份证件类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.certno IS ''身份证件号码 患者的身份证件上的唯一法定标识符 。新生儿无身份证件号码时，身份证件类别应为其他法定有效证件，身份证件号码为出生日期 8 位数字''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.edu_background_code IS ''学历代码 例如：小学毕业、小学肄业、大学本科/专科教育、大学本科毕业、大学本科结业、大学本科肄业、大学普通班毕业、大学专科毕业等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.edu_background_name IS ''学历名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.mrg_stas_code IS ''婚姻状况代码 未婚、已婚、初婚、再婚、复婚、丧偶、离婚、未说明的婚姻状况''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.mrg_stas_name IS ''婚姻状况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.nation_code IS ''民族代码 例如：汉族、蒙古族、回族、藏族等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.nation_name IS ''民族名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.ntly_code IS ''国籍代码 例如：阿富汗、阿尔巴尼亚、南极洲、阿尔及利亚、美属萨摩亚、安道尔等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.ntly_name IS ''国籍名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.blood_type_code IS ''患者血型代码 例如：A型、B型、O型、AB型等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.blood_type_name IS ''患者血型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据改造厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_basic_info.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_1_pat ON patient_basic_info (rid, upload_time)';

    log_table_result('patient_basic_info', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('patient_basic_info', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 patient_allergy_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE patient_allergy_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    algs_sn VARCHAR2(100),
    patient_no VARCHAR2(100),
    otp_ipt_flag VARCHAR2(1),
    otp_ipt_name VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    ipt_no VARCHAR2(255),
    otp_no VARCHAR2(255),
    algs_sympt_code VARCHAR2(255),
    algs_sympt_name VARCHAR2(255),
    aise_code VARCHAR2(4000),
    aise_name VARCHAR2(4000),
    algs_sympt_strtime DATE,
    algs_sympt_endtime DATE,
    fmhis_flag VARCHAR2(1),
    algs_type_code VARCHAR2(255),
    algs_type_name VARCHAR2(255),
    sevdeg_code VARCHAR2(255),
    sevdeg_name VARCHAR2(255),
    skintst_medstffcode VARCHAR2(255),
    skintst_medstffname VARCHAR2(255),
    skintst_time DATE,
    skintst_detl VARCHAR2(255),
    skintst_resu_code VARCHAR2(255),
    skintst_resu_name VARCHAR2(255),
    rec_time DATE,
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_2_pat PRIMARY KEY (uscid, upload_time, sys_prdr_code, algs_sn, patient_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE patient_allergy_record IS ''患者过敏记录''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+过敏记录流水号algs_sn+患者编号patient_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_sn IS ''过敏记录流水号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.otp_ipt_flag IS ''门诊/住院标志 例如：0门诊，1住院，2体检，3家床，9其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.otp_ipt_name IS ''门诊/住院标志名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.mdtrt_sn IS ''就诊流水号 用于与住院就诊记录表或门诊就诊记录表关联的外键（可选关联关系）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.ipt_no IS ''住院号 为住院时，填写住院号，门诊号字段填“-”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.otp_no IS ''门（急）诊号 按照某一特定编码规则赋予门（急）诊就诊对象的顺序号；为门诊就诊时，填写门诊号，住院号填“-”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_sympt_code IS ''过敏症状代码 例如：头晕,胸闷,关节肿痛,视力模糊等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_sympt_name IS ''过敏症状名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.aise_code IS ''过敏源代码 例如：青霉素类抗生素,牛奶,环境,植物花粉,混合性过敏源等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.aise_name IS ''过敏源名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_sympt_strtime IS ''过敏症状开始时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_sympt_endtime IS ''过敏症状停止时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.fmhis_flag IS ''是否家族史 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_type_code IS ''过敏类别代码 例如：药物过敏,食物过敏,其他过敏,环境过敏,混合型过敏''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.algs_type_name IS ''过敏类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.sevdeg_code IS ''严重程度代码 一般、中等、严重''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.sevdeg_name IS ''严重程度名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_medstffcode IS ''皮试人员工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_medstffname IS ''皮试人员姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_time IS ''皮试时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_detl IS ''皮试详细内容''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_resu_code IS ''皮试结果代码 例如：阴性（-）,皮试中,过敏等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.skintst_resu_name IS ''皮试结果名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.rec_time IS ''记录时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据改造厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_allergy_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_2_pat ON patient_allergy_record (rid, upload_time)';

    log_table_result('patient_allergy_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('patient_allergy_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 patient_health_summary
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE patient_health_summary (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    otp_ipt_flag VARCHAR2(1),
    otp_ipt_name VARCHAR2(255),
    ipt_no VARCHAR2(255),
    otp_no VARCHAR2(255),
    patient_no VARCHAR2(100),
    dept_code VARCHAR2(100),
    dept_name VARCHAR2(255),
    patient_type_code VARCHAR2(255),
    patient_type_name VARCHAR2(255),
    mdtrt_time DATE,
    adm_time DATE,
    dscg_time DATE,
    attk_time DATE,
    diag_reason VARCHAR2(4000),
    wm_diag_code VARCHAR2(4000),
    wm_diag_name VARCHAR2(4000),
    tcm_dise_code VARCHAR2(4000),
    tcm_dise_name VARCHAR2(4000),
    tcmsymp_code VARCHAR2(4000),
    tcmsymp_name VARCHAR2(4000),
    oth_wm_diag_code VARCHAR2(4000),
    oth_wm_diag_name VARCHAR2(4000),
    oprn_oprt_code VARCHAR2(4000),
    oprn_oprt_name VARCHAR2(4000),
    key_drug_name VARCHAR2(4000),
    key_drug_use_way VARCHAR2(4000),
    drug_dys_case VARCHAR2(4000),
    medi_tcm_type_code VARCHAR2(255),
    medi_tcm_type_name VARCHAR2(255),
    oth_med_dspo VARCHAR2(4000),
    death_reason_code VARCHAR2(255),
    death_reason_name VARCHAR2(4000),
    dis_pro_code VARCHAR2(255),
    dis_pro_name VARCHAR2(255),
    duty_doc_no VARCHAR2(255),
    duty_doc_name VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_3_pat PRIMARY KEY (uscid, upload_time, sys_prdr_code, mdtrt_sn, patient_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE patient_health_summary IS ''卫生事件摘要''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+就诊流水号mdtrt_sn+患者编号patient_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.mdtrt_sn IS ''就诊流水号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.otp_ipt_flag IS ''门诊/住院标志 例如：0门诊，1住院，2体检，3家床，9其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.otp_ipt_name IS ''门诊/住院标志名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.ipt_no IS ''住院号 为住院时，填写住院号，门诊号字段填“-”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.otp_no IS ''门（急）诊号 按照某一特定编码规则赋予门（急）诊就诊对象的顺序号；为门诊就诊时，填写门诊号，住院号填“-”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.dept_code IS ''科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.dept_name IS ''科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.patient_type_code IS ''患者类型代码 例如：门诊、急诊、住院、体检、外院等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.patient_type_name IS ''患者类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.mdtrt_time IS ''就诊时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.adm_time IS ''入院时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.dscg_time IS ''出院时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.attk_time IS ''发病时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.diag_reason IS ''就诊原因''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.wm_diag_code IS ''西医诊断代码 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.wm_diag_name IS ''西医诊断名称 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.tcm_dise_code IS ''中医病名代码 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.tcm_dise_name IS ''中医病名名称 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.tcmsymp_code IS ''中医证候代码 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.tcmsymp_name IS ''中医证候名称 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.oth_wm_diag_code IS ''其他西医诊断代码 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.oth_wm_diag_name IS ''其他西医诊断名称 多个以“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.oprn_oprt_code IS ''手术及操作代码 有多项时用“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.oprn_oprt_name IS ''手术及操作名称 有多项时用“;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.key_drug_name IS ''关键药物名称 有多项时用”;”隔开''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.key_drug_use_way IS ''关键药物用法 有多项时用”;”隔开''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.drug_dys_case IS ''药物不良反应情况''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.medi_tcm_type_code IS ''中药使用类别代码 未使用、中成药、中草药、其他中药''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.medi_tcm_type_name IS ''中药使用类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.oth_med_dspo IS ''其他医学处置''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.death_reason_code IS ''根本死因代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.death_reason_name IS ''根本死因名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.dis_pro_code IS ''疾病转归代码 例如：治愈,好转,稳定,恶化,死亡,其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.dis_pro_name IS ''疾病转归名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.duty_doc_no IS ''责任医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.duty_doc_name IS ''责任医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据改造厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_health_summary.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_3_pat ON patient_health_summary (rid, upload_time)';

    log_table_result('patient_health_summary', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('patient_health_summary', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 patient_clinic_activity
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE patient_clinic_activity (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    activity_id VARCHAR2(100),
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    activity_type_code VARCHAR2(100),
    activity_type_name VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    psncert_type_code VARCHAR2(50),
    psncert_type_name VARCHAR2(100),
    certno VARCHAR2(255),
    chfcomp VARCHAR2(4000),
    dise_now VARCHAR2(4000),
    phys_exam VARCHAR2(4000),
    asst_exam VARCHAR2(4000),
    diag_time DATE,
    infect_diag_code VARCHAR2(4000),
    infect_diag_name VARCHAR2(4000),
    wm_diag_code VARCHAR2(4000),
    wm_diag_name VARCHAR2(4000),
    tcm_dise_code VARCHAR2(4000),
    tcm_dise_name VARCHAR2(4000),
    tcm_syndrome_code VARCHAR2(255),
    tcmsymp_name VARCHAR2(4000),
    diag_doc_code VARCHAR2(255),
    diag_doc_name VARCHAR2(255),
    dept_code VARCHAR2(100),
    dept_name VARCHAR2(255),
    operator_code VARCHAR2(255),
    operator_name VARCHAR2(255),
    oper_time DATE,
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_4_pat PRIMARY KEY (uscid, upload_time, sys_prdr_code, activity_id, patient_no, activity_type_code, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE patient_clinic_activity IS ''诊疗活动信息表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+诊疗活动IDactivity_id+患者编号patient_no+诊疗活动类别代码activity_type_code+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.activity_id IS ''诊疗活动ID 复合主键，诊疗活动记录在院内的唯一标识''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.patient_name IS ''患者姓名 就诊者本人在公安户籍管理部门正式登记注册的姓氏和名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.activity_type_code IS ''诊疗活动类别代码 复合主键，医生做出疾病诊断的业务活动环节名称代码,代码和名称需关联''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.activity_type_name IS ''诊疗活动类别名称 代码和名称来源国家传染病上报: 1门诊2急诊3留观入观4留观出观5入院5住院6首次病程7日常病程8出院等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.mdtrt_sn IS ''就诊流水号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.psncert_type_code IS ''身份证件类别代码 例如：居民身份证,居民户口簿,临时居民身份证,外国人护照,残疾人证等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.psncert_type_name IS ''身份证件类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.certno IS ''身份证件号码 患者的身份证件上的唯一法定标识符 。新生儿无身份证件号码时，身份证件类别应为其他法定有效证件，身份证件号码为出生日期 8 位数字''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.chfcomp IS ''主诉 患者向医师描述的对自身本次疾病相关的感受的主要记录''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.dise_now IS ''现病史 对患者当前所患疾病情况的详细描述''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.phys_exam IS ''体格检查 对患者进行的体格检查项目及主要检查结果的描述，包括主要的阳性体征和必要的阴性体征''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.asst_exam IS ''辅助检查 患者辅助检查结果的详细描述''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.diag_time IS ''诊断时间 医师做出疾病诊断的具体时间，精确到小时''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.infect_diag_code IS ''传染病诊断代码 传染病诊断代码 疾病诊断为传染病时必填，多个诊断使用||分隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.infect_diag_name IS ''传染病诊断名称 传染病诊断名称 疾病诊断为传染病时必填，多个诊断使用||分隔。名称须与代码所对应的值域代码名称一致。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.wm_diag_code IS ''西医诊断代码 疾病诊断在西医诊断在ICD-10编码体系中的编码。必填，多个诊断使用||分隔。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.wm_diag_name IS ''西医诊断名称 疾病诊断在西医诊断在ICD-10编码体系中的名称。必填，多个诊断使用||分隔。名称须与代码所对应的值域代码名称一致。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.tcm_dise_code IS ''中医病名代码 疾病诊断在中医病名特定分类体系中的代码 非必填，多个病名使用||分隔。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.tcm_dise_name IS ''中医病名名称 疾病诊断的中医病名。 非必填，多个病名使用||分隔。名称须与代码所对应的值域代码名称一致。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.tcm_syndrome_code IS ''中医证候编码 中医诊断在中医证候特定分类体系中的代码。 非必填，多个证候使用||分隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.tcmsymp_name IS ''中医证候名称 由医师根据患者就诊时的情况，综合分析所作出的中医证候名称。 非必填，多个证候使用||分隔。名称 须与代码所对应的值域代码名称一致。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.diag_doc_code IS ''诊断医生工号 诊断医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.diag_doc_name IS ''诊断医生姓名 诊断医生姓名。 患者姓名只有中文时不可含有空格。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.dept_code IS ''科室代码 做出疾病诊断的医疗机构院内科室代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.dept_name IS ''科室名称 做出疾病诊断的医疗机构院内科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.operator_code IS ''操作员工号 操作人是指医院信息系统中实际操作数据的用户。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.operator_name IS ''操作人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.oper_time IS ''操作时间 数据操作的具体时间，精确到秒。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据改造厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN patient_clinic_activity.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_4_pat ON patient_clinic_activity (rid, upload_time)';

    log_table_result('patient_clinic_activity', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('patient_clinic_activity', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_register_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_register_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    register_time DATE,
    register_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    register_type_code VARCHAR2(255),
    register_type_name VARCHAR2(255),
    rgst_dept_code VARCHAR2(255),
    rgst_dept_name VARCHAR2(255),
    register_doc_code VARCHAR2(255),
    register_doc_name VARCHAR2(255),
    rgst_serv_fee NUMBER(20,4),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    ic_card_no VARCHAR2(255),
    aftred_rgst_serv_fee NUMBER(20,4),
    med_insu_no VARCHAR2(255),
    hi_card_no VARCHAR2(255),
    register_status_code VARCHAR2(255),
    register_status_name VARCHAR2(255),
    return_time DATE,
    mdtrt_sn VARCHAR2(100),
    register_medical_type_code VARCHAR2(255),
    register_medical_type_name VARCHAR2(255),
    er_flag VARCHAR2(255),
    first_visit_flag VARCHAR2(1),
    order_flag VARCHAR2(255),
    special_need_flag VARCHAR2(1),
    ordr_way_code VARCHAR2(255),
    ordr_way_name VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_5_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, register_no, patient_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_register_record IS ''门急诊挂号记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+挂号号register_no+患者编号patient_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_time IS ''挂号时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_no IS ''挂号号 复合主键，如果就诊号和挂号号不是分开的，挂号号就是就诊号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.patient_name IS ''患者姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_type_code IS ''挂号类型代码 例如：普通门诊、便民门诊、医技号等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_type_name IS ''挂号类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rgst_dept_code IS ''挂号科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rgst_dept_name IS ''挂号科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_doc_code IS ''挂号医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_doc_name IS ''挂号医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rgst_serv_fee IS ''挂号费 挂退号费用都以正数表示。对于自助挂号，或者挂号时先不收费的情况，可填写为0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.ic_card_no IS ''IC卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.aftred_rgst_serv_fee IS ''减免后挂号费 指符合相应减免政策，对自费诊疗费减免后，实际收取的挂号费。挂退号费用都以正数表示。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.med_insu_no IS ''医疗保险号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.hi_card_no IS ''医保卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_status_code IS ''挂号状态代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_status_name IS ''挂号状态名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.return_time IS ''退号时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.mdtrt_sn IS ''就诊流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_medical_type_code IS ''挂号医学类型代码 例如：西医、中医''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.register_medical_type_name IS ''挂号医学类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.er_flag IS ''急诊标识 例如：普通、急诊''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.first_visit_flag IS ''初诊标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.order_flag IS ''是否预约挂号 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.special_need_flag IS ''特需标志 1 是，1否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.ordr_way_code IS ''预约挂号方式代码 例如：现场预约,电话预约,短信预约,网络预约,手机APP预约,其他类型预约''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.ordr_way_name IS ''预约挂号方式名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_register_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_5_out ON outp_register_record (rid, upload_time)';

    log_table_result('outp_register_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_register_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_visit_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_visit_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    otp_mdtrt_time DATE,
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    age_year NUMBER(16),
    age_month NUMBER(16),
    age_day NUMBER(16),
    otp_wm_diag_code VARCHAR2(4000),
    otp_wm_diag_name VARCHAR2(255),
    first_doc_code VARCHAR2(255),
    first_doc_name VARCHAR2(255),s
    mdtrt_dept_code VARCHAR2(50),
    mdtrt_dept_name VARCHAR2(100),
    cure_group_code VARCHAR2(255),
    cure_group_name VARCHAR2(255),
    oth_doc_code VARCHAR2(4000),
    oth_doc_name VARCHAR2(4000),
    first_visit_flag VARCHAR2(1),
    order_flag VARCHAR2(255),
    er_flag VARCHAR2(255),
    referral_flag VARCHAR2(1),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    register_no VARCHAR2(100),
    register_time DATE,
    register_type_code VARCHAR2(255),
    register_type_name VARCHAR2(255),
    register_doc_code VARCHAR2(255),
    register_doc_name VARCHAR2(255),
    rgst_serv_fee NUMBER(20,4),
    ic_card_no VARCHAR2(255),
    aftred_rgst_serv_fee NUMBER(20,4),
    med_insu_no VARCHAR2(255),
    hi_card_no VARCHAR2(255),
    insur_visit_no VARCHAR2(255),
    insur_visit_time DATE,
    register_status_code VARCHAR2(255),
    register_status_name VARCHAR2(255),
    return_time DATE,
    gender_code VARCHAR2(50),
    gender_name VARCHAR2(100),
    brdy DATE,
    psn_tel VARCHAR2(50),
    curr_addr VARCHAR2(4000),
    certno VARCHAR2(255),
    mrg_stas_code VARCHAR2(50),
    mrg_stas_name VARCHAR2(100),
    height NUMBER(20,4),
    weight NUMBER(20,4),
    birctrl_matn_date DATE,
    birctrl_type VARCHAR2(255),
    birctrl_type_name VARCHAR2(255),
    matn_type VARCHAR2(255),
    matn_type_name VARCHAR2(255),
    geso_val NUMBER(16),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_6_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_visit_record IS ''门急诊就诊记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.otp_mdtrt_time IS ''门诊就诊时间 业务时间&采集时间戳 如果就诊时间和挂号时间不是分开的，就用挂号时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.patient_name IS ''患者姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.age_year IS ''年龄（年） 患者年龄满1周岁的实足年龄，为患者出生后按照日历计算的历法年龄，以实足年龄的相应整数填写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.age_month IS ''年龄（月） 年龄不满实足1周岁的月龄''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.age_day IS ''年龄（天） 年龄不满实足1个月的天数''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.otp_wm_diag_code IS ''门诊诊断代码 若有多条，填写主要诊断代码。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.otp_wm_diag_name IS ''门诊诊断名称 若有多条，填写主要诊断名称。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.first_doc_code IS ''首诊医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.first_doc_name IS ''首诊医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.mdtrt_dept_code IS ''就诊科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.mdtrt_dept_name IS ''就诊科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.cure_group_code IS ''接诊医组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.cure_group_name IS ''接诊医组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.oth_doc_code IS ''其他接诊医生工号列表 多个时”;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.oth_doc_name IS ''其他接诊医生姓名列表 多个时”;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.first_visit_flag IS ''初诊标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.order_flag IS ''是否预约挂号 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.er_flag IS ''急诊标识 例如：普通、急诊''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.referral_flag IS ''转诊标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_no IS ''挂号号 如果就诊号和挂号号不是分开的，挂号号就是就诊号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_time IS ''挂号时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_type_code IS ''挂号类型代码 例如：普通门诊、便民门诊、医技号等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_type_name IS ''挂号类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_doc_code IS ''挂号医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_doc_name IS ''挂号医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.rgst_serv_fee IS ''挂号费 挂退号费用都以正数表示。对于自助挂号，或者挂号时先不收费的情况，可填写为0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.ic_card_no IS ''IC卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.aftred_rgst_serv_fee IS ''减免后挂号费 指符合相应减免政策，对自费诊疗费减免后，实际收取的挂号费。挂退号费用都以正数表示。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.med_insu_no IS ''医疗保险号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.hi_card_no IS ''医保卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.insur_visit_no IS ''医保挂号号 医保患者必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.insur_visit_time IS ''医保挂号时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_status_code IS ''挂号状态代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.register_status_name IS ''挂号状态名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.return_time IS ''退号时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.gender_code IS ''性别代码 例如：男性、女性、未说明的性别等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.gender_name IS ''性别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.brdy IS ''出生日期''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.psn_tel IS ''本人电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.curr_addr IS ''居住地址详细地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.certno IS ''身份证件号码 社保卡患者必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.mrg_stas_code IS ''婚姻状况代码 未婚、已婚、初婚、再婚、复婚、丧偶、离婚、未说明的婚姻状况''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.mrg_stas_name IS ''婚姻状况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.height IS ''身高（cm） 记录患者就诊时的身高，单位cm''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.weight IS ''体重（kg） 记录患者就诊时的体重，单位kg''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.birctrl_matn_date IS ''计划生育手术或生育日期 生育门诊按需录入''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.birctrl_type IS ''计划生育手术类别代码 生育门诊按需录入 例如：放置宫内节育器,绝育手术(输卵管),绝育复通手术(输卵管)等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.birctrl_type_name IS ''计划生育手术类别名称 生育门诊按需录入''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.matn_type IS ''生育类别代码 例如：顺产、难产、剖宫产等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.matn_type_name IS ''生育类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.geso_val IS ''孕周（d）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_visit_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_6_out ON outp_visit_record (rid, upload_time)';

    log_table_result('outp_visit_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_visit_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_diagnosis_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_diagnosis_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    diag_time DATE,
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    diag_srt_no VARCHAR2(100),
    diag_code VARCHAR2(100),
    diag_name VARCHAR2(255),
    maindiag_flag VARCHAR2(1),
    diagnosis_sort_no VARCHAR2(255),
    diag_dept_code VARCHAR2(255),
    diag_dept_name VARCHAR2(255),
    diagnosis_method_code VARCHAR2(255),
    diagnosis_method_name VARCHAR2(255),
    symp_dscr VARCHAR2(4000),
    memo VARCHAR2(4000),
    diag_doc_code VARCHAR2(255),
    diag_doc_name VARCHAR2(255),
    vali_flag VARCHAR2(1),
    infet_flag VARCHAR2(1),
    infect_report_flag VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_7_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn, diag_srt_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_diagnosis_record IS ''门急诊断记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+就诊流水号mdtrt_sn+诊断流水号diag_srt_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_time IS ''诊断时间 业务时间&采集时间戳 若没有用就诊时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_srt_no IS ''诊断流水号 复合主键，业务表的唯一诊断记录''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_code IS ''疾病诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_name IS ''疾病诊断名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.maindiag_flag IS ''主要诊断标志 1：主诊断；0或空 次要诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diagnosis_sort_no IS ''诊断排序号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_dept_code IS ''诊断科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_dept_name IS ''诊断科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diagnosis_method_code IS ''诊断方法代码 例如：西医诊断,中医诊断,中医证型诊断,中医病名诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diagnosis_method_name IS ''诊断方法名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.symp_dscr IS ''症状描述 接诊医生对患者症状的简要描述''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.memo IS ''备注说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_doc_code IS ''诊断医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.diag_doc_name IS ''诊断医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.vali_flag IS ''有效标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.infet_flag IS ''传染病标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.infect_report_flag IS ''传染病上传标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_diagnosis_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_7_out ON outp_diagnosis_record (rid, upload_time)';

    log_table_result('outp_diagnosis_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_diagnosis_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_order_master
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_order_master (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    apply_datetime DATE,
    order_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    drord_type_code VARCHAR2(255),
    drord_type_name VARCHAR2(255),
    rx_type_code VARCHAR2(255),
    rx_type_name VARCHAR2(255),
    drord_stas VARCHAR2(255),
    drord_stas_name VARCHAR2(255),
    bilg_doc_code VARCHAR2(255),
    bilg_doc_name VARCHAR2(255),
    bilg_dept_codg VARCHAR2(255),
    bilg_dept_name VARCHAR2(255),
    order_input_operator VARCHAR2(255),
    execute_time DATE,
    execute_operator_code VARCHAR2(255),
    execute_operator_name VARCHAR2(255),
    check_time DATE,
    check_operator_code VARCHAR2(255),
    check_operator_name VARCHAR2(255),
    exe_dept_code VARCHAR2(255),
    exe_dept_name VARCHAR2(255),
    medfeesumamt NUMBER(20,4),
    charge NUMBER(20,4),
    tcm_medi_method_code VARCHAR2(255),
    tcm_medi_method_name VARCHAR2(255),
    drug_use_tcm_way_code VARCHAR2(255),
    drug_use_tcm_way_name VARCHAR2(4000),
    expiredate DATE,
    memo VARCHAR2(4000),
    order_sign VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_8_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, order_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_order_master IS ''门诊医嘱主表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+医嘱号order_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.apply_datetime IS ''开单时间 业务时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.order_no IS ''医嘱号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drord_type_code IS ''医嘱类别代码 例如：西药、护理、膳食、输血、输氧、转科、术后、出院等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drord_type_name IS ''医嘱类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.rx_type_code IS ''处方类型代码 普通处方、急诊处方、儿科处方、麻醉、精一处方、精二药品处方、检查检验申请、治疗处置、体检处方、其他、不明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.rx_type_name IS ''处方类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drord_stas IS ''医嘱状态代码 例如：新开、待审、已审核、已发送、已缴费、已执行、已取消(DC，)、已停止、作废、其他、未知''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drord_stas_name IS ''医嘱状态名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.bilg_doc_code IS ''开单医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.bilg_doc_name IS ''开单医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.bilg_dept_codg IS ''开单科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.bilg_dept_name IS ''开单科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.order_input_operator IS ''医嘱录入人 建议门诊时医嘱录入人信息''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.execute_time IS ''执行时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.execute_operator_code IS ''执行人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.execute_operator_name IS ''执行人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.check_time IS ''校对时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.check_operator_code IS ''校对人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.check_operator_name IS ''校对人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.exe_dept_code IS ''执行科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.exe_dept_name IS ''执行科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.medfeesumamt IS ''总费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.charge IS ''自付金额 患者个人负担的金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.tcm_medi_method_code IS ''中药煎煮法代码 中药处方必填。 例如：包煎、冲服、后煎、后下等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.tcm_medi_method_name IS ''中药煎煮法名称 中药处方必填。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drug_use_tcm_way_code IS ''中药用药方法代码 例如：内服、按医嘱、泡脚、香疗、雾化吸入等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.drug_use_tcm_way_name IS ''中药用药方法名称 中药饮片处方使用。必须填写。如“分早晚两次空腹温服”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.expiredate IS ''有效期截止日期''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.memo IS ''备注说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.order_sign IS ''签名确认''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_master.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_8_out ON outp_order_master (rid, upload_time)';

    log_table_result('outp_order_master', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_order_master', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_order_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_order_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    apply_datetime DATE,
    order_no VARCHAR2(100),
    order_serial_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    execute_time DATE,
    drord_item_code VARCHAR2(255),
    drord_item_name VARCHAR2(255),
    group_no VARCHAR2(255),
    spec VARCHAR2(255),
    dosage VARCHAR2(255),
    dosunt VARCHAR2(255),
    drug_dosform_code VARCHAR2(255),
    drug_dosform_name VARCHAR2(255),
    drug_used_frqu_code VARCHAR2(255),
    drug_used_frqu_name VARCHAR2(255),
    medc_days NUMBER(16),
    drug_method_code VARCHAR2(255),
    drug_method_name VARCHAR2(255),
    drug_batch_no VARCHAR2(100),
    price NUMBER(20,4),
    quantity NUMBER(16),
    medfeesumamt NUMBER(20,4),
    charge NUMBER(20,4),
    drug_used_idose NUMBER(20,4),
    drug_used_idose_unit VARCHAR2(255),
    exam_part_code VARCHAR2(255),
    exam_part_name VARCHAR2(4000),
    skin_test_code VARCHAR2(255),
    skin_test_name VARCHAR2(255),
    skintst_resu_code VARCHAR2(255),
    skintst_resu_name VARCHAR2(255),
    insur_flag VARCHAR2(1),
    pool_prop_selfpay NUMBER(20,4),
    selfpro_flag VARCHAR2(1),
    drord_dscr VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_9_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, order_no, order_serial_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_order_detail IS ''门诊医嘱明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+医嘱号order_no+医嘱子序号order_serial_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.apply_datetime IS ''开单时间 业务时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.order_no IS ''医嘱号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.order_serial_no IS ''医嘱子序号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.execute_time IS ''执行时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drord_item_code IS ''医嘱项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drord_item_name IS ''医嘱项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.group_no IS ''组号 中草药忽略''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.spec IS ''项目规格 制剂规格，以每片、每包或每支为单位的药物制剂内所含有效成分的量。如（1）：0.3g；（2）：25mg；（3）：50mg；（4）：0.5mg:2ml''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.dosage IS ''每次剂量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.dosunt IS ''剂量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_dosform_code IS ''药物剂型代码 例如：丸剂,散剂,颗粒剂,片剂等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_dosform_name IS ''药物剂型名称 遵循《CS03.01.026剂型代码表》标准''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_used_frqu_code IS ''用药频次代码 例如：bid、biw、Hs、q12h''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_used_frqu_name IS ''用药频次名称 （q2h每2小时一次） 若无“频次名称”，存“频次代码” 一时段内用药次数。如：一日三次。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.medc_days IS ''用药天数''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_method_code IS ''用药方法代码 例如：口服、直肠用药、舌下用药、注射用药等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_method_name IS ''用药方法名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_batch_no IS ''药品批次 药品发药时具体的药品批次号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.price IS ''单价''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.quantity IS ''数量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.medfeesumamt IS ''总费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.charge IS ''自付金额 患者个人负担的金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_used_idose IS ''药物使用总剂量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drug_used_idose_unit IS ''药物使用总剂量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.exam_part_code IS ''检查部位代码 例如：双侧鼻孔、臀部、左臂、左前胸等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.exam_part_name IS ''检查部位名称 影像检查医嘱，说明被检查的部位。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.skin_test_code IS ''皮试项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.skin_test_name IS ''皮试项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.skintst_resu_code IS ''皮试结果代码 例如：阴性（-）,皮试中,过敏等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.skintst_resu_name IS ''皮试结果名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.insur_flag IS ''医保标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.pool_prop_selfpay IS ''基本医疗保险统筹基金支付比例''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.selfpro_flag IS ''自备药标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.drord_dscr IS ''医嘱说明 草药医嘱时可以录入先煎、后入等草药药物用药方法''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_order_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_9_out ON outp_order_detail (rid, upload_time)';

    log_table_result('outp_order_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_order_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_charge_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_charge_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    charge_time DATE,
    fee_detail_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    charge_operator_code VARCHAR2(255),
    charge_operator_name VARCHAR2(255),
    charge_mode_code VARCHAR2(255),
    charge_mode_name VARCHAR2(255),
    med_chrgitm_type_code VARCHAR2(255),
    med_chrgitm_type_name VARCHAR2(255),
    item_code VARCHAR2(100),
    item_name VARCHAR2(4000),
    apply_no VARCHAR2(100),
    order_no VARCHAR2(100),
    spec VARCHAR2(255),
    unit VARCHAR2(255),
    price NUMBER(20,4),
    quantity NUMBER(16),
    drug_batch_no VARCHAR2(100),
    drug_expyend DATE,
    cost NUMBER(20,4),
    charge NUMBER(20,4),
    insur_charge NUMBER(20,4),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    receipt_class_code VARCHAR2(255),
    receipt_class_name VARCHAR2(255),
    case_receipt_class_code VARCHAR2(255),
    account_class_code VARCHAR2(255),
    account_class_name VARCHAR2(255),
    case_receipt_class_name VARCHAR2(255),
    insur_item_code VARCHAR2(255),
    insur_item_name VARCHAR2(255),
    insur_receipt_class_code VARCHAR2(255),
    insur_receipt_class_name VARCHAR2(255),
    charge_item_level_code VARCHAR2(255),
    charge_item_level_name VARCHAR2(255),
    selfpay_rate VARCHAR2(255),
    apply_doc_code VARCHAR2(255),
    apply_doc_name VARCHAR2(255),
    apply_dept_code VARCHAR2(255),
    apply_dept_name VARCHAR2(255),
    apply_group_code VARCHAR2(255),
    apply_group_name VARCHAR2(255),
    execute_operator_code VARCHAR2(255),
    execute_operator_name VARCHAR2(255),
    exe_dept_code VARCHAR2(255),
    exe_dept_name VARCHAR2(255),
    execute_group_code VARCHAR2(255),
    execute_group_name VARCHAR2(255),
    settle_no VARCHAR2(100),
    settle_time DATE,
    return_charge_detail_no VARCHAR2(255),
    return_quantity NUMBER(20,4),
    return_cost NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_10_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, fee_detail_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_charge_detail IS ''门诊费用明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+费用明细号fee_detail_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_time IS ''扣费时间 业务时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.fee_detail_no IS ''费用明细号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_operator_code IS ''扣费操作人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_operator_name IS ''扣费操作人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_mode_code IS ''扣费方式代码 例如：扣单据、退卡费/病历本工本费、医保返还、配药/发药、挂号扣费等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_mode_name IS ''扣费方式名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.med_chrgitm_type_code IS ''医疗收费项目类别代码 例如：床位费、诊察费、检查费、化验费、放射费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.med_chrgitm_type_name IS ''医疗收费项目类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.item_code IS ''项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.item_name IS ''项目名称 填写院内项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_no IS ''单据号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.order_no IS ''医嘱号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.spec IS ''项目规格''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.unit IS ''计量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.price IS ''单价''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.quantity IS ''数量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.drug_batch_no IS ''药品批次 药品需要填写；多个批次则用英文的“;”间隔。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.drug_expyend IS ''（药品的）有效期截止日期 药品需要填写；''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.cost IS ''费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge IS ''自付金额 患者个人负担的金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.insur_charge IS ''医保支付金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.receipt_class_code IS ''发票类别代码 例如：西药费、治疗费、手术费、救护车费、MRI费、CT费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.receipt_class_name IS ''发票类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.case_receipt_class_code IS ''病案首页费用类别代码 例如：一般医疗服务费,护理费,麻醉费,手术费等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.account_class_code IS ''会计科目类别代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.account_class_name IS ''会计科目类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.case_receipt_class_name IS ''病案首页费用类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.insur_item_code IS ''医保项目代码 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.insur_item_name IS ''医保项目名称 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.insur_receipt_class_code IS ''医保发票类别代码 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.insur_receipt_class_name IS ''医保发票类别名称 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_item_level_code IS ''项目等级代码 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.charge_item_level_name IS ''项目等级名称 甲类、乙类、丙类 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.selfpay_rate IS ''自付比例 医保病人需上传''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_doc_code IS ''申请医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_doc_name IS ''申请医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_dept_code IS ''申请科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_dept_name IS ''申请科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_group_code IS ''申请医生所在医组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.apply_group_name IS ''申请医生所在医组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.execute_operator_code IS ''执行人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.execute_operator_name IS ''执行人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.exe_dept_code IS ''执行科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.exe_dept_name IS ''执行科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.execute_group_code IS ''执行医疗组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.execute_group_name IS ''执行医疗组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.settle_no IS ''结算号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.settle_time IS ''结算时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.return_charge_detail_no IS ''退费号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.return_quantity IS ''退费数量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.return_cost IS ''退费金额 收退费均以正数表达。收/退费总额=实收金额+优惠金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_charge_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_10_out ON outp_charge_detail (rid, upload_time)';

    log_table_result('outp_charge_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_charge_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_settle_master
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_settle_master (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    settle_time DATE,
    settle_no VARCHAR2(100),
    invono VARCHAR2(255),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    settle_type_code VARCHAR2(255),
    settle_type_name VARCHAR2(255),
    operation_type_code VARCHAR2(255),
    operation_type_name VARCHAR2(255),
    rgst_dept_code VARCHAR2(255),
    rgst_dept_name VARCHAR2(255),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    contract_unit_code VARCHAR2(255),
    contract_unit_name VARCHAR2(255),
    settle_operator_code VARCHAR2(255),
    settle_operator_name VARCHAR2(255),
    cancel_operator_code VARCHAR2(255),
    cancel_operator_name VARCHAR2(255),
    cancel_opetation_time DATE,
    med_type VARCHAR2(255),
    med_type_name VARCHAR2(255),
    cancel_settle_no VARCHAR2(255),
    account_no VARCHAR2(255),
    diag_code VARCHAR2(100),
    diag_name VARCHAR2(255),
    medfeesumamt NUMBER(20,4),
    charge NUMBER(20,4),
    derate_cost NUMBER(20,4),
    fundpay_sumamt NUMBER(20,4),
    insur_account_pay NUMBER(20,4),
    hifp_pay NUMBER(20,4),
    self_charge NUMBER(20,4),
    special_need_fee NUMBER(20,4),
    special_need_drugfee NUMBER(20,4),
    rgst_serv_fee NUMBER(20,4),
    digtrt_fee NUMBER(20,4),
    trt_fee NUMBER(20,4),
    exam_charge NUMBER(20,4),
    rgtrt_fee NUMBER(20,4),
    matl_fee NUMBER(20,4),
    bed_fee NUMBER(20,4),
    nurs_fee NUMBER(20,4),
    pharm_fee NUMBER(20,4),
    ord_digtrt_fee NUMBER(20,4),
    laboratory_fee NUMBER(20,4),
    wm_fee NUMBER(20,4),
    tcmpat_fee NUMBER(20,4),
    tcmherb_fee NUMBER(20,4),
    oth_fee NUMBER(20,4),
    insur_settle_no VARCHAR2(255),
    insur_visit_no VARCHAR2(255),
    insur_settle_flag VARCHAR2(1),
    insur_settle_rea VARCHAR2(4000),
    insur_over_limit_flag VARCHAR2(1),
    insur_pay_line NUMBER(20,4),
    business_account_pay NUMBER(20,4),
    business_fund_pay NUMBER(20,4),
    business_insur_pay NUMBER(20,4),
    insur_classb_pay NUMBER(20,4),
    insur_over_top_pay NUMBER(20,4),
    civil_account_pay NUMBER(20,4),
    cvlserv_pay NUMBER(20,4),
    other_fund_pay NUMBER(20,4),
    hosp_part_amt NUMBER(20,4),
    hifmi_pay NUMBER(20,4),
    hifob_pay NUMBER(20,4),
    home_help_pay NUMBER(20,4),
    health_account_pay NUMBER(20,4),
    health_fund_pay NUMBER(20,4),
    transfer_prior_payment NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_11_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, settle_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_settle_master IS ''门诊结算主表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+结算号settle_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_time IS ''结算时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_no IS ''结算号 复合主键，门诊结算流水号，门诊多次结算需保证唯一。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.invono IS ''发票号码 超过一张发票时，以”;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_type_code IS ''结算类型代码 例如：门诊结算,出院结算,结算冲销,中途结算等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_type_name IS ''结算类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.operation_type_code IS ''结算操作类型代码 例如：正常结算,结算冲销,挂号结算,退号结算等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.operation_type_name IS ''结算操作类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rgst_dept_code IS ''挂号科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rgst_dept_name IS ''挂号科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.contract_unit_code IS ''结算合同单位代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.contract_unit_name IS ''结算合同单位名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_operator_code IS ''结算员工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.settle_operator_name IS ''结算员姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.cancel_operator_code IS ''冲销人/取消人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.cancel_operator_name IS ''冲销人/取消人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.cancel_opetation_time IS ''冲销/取消时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.med_type IS ''医疗类别代码 例如：普通门诊、门诊两病、普通住院、精神病住院等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.med_type_name IS ''医疗类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.cancel_settle_no IS ''取消结算号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.account_no IS ''结账号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.diag_code IS ''疾病诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.diag_name IS ''疾病诊断名称 若有多条，填写主要诊断。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.medfeesumamt IS ''总费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.charge IS ''自付金额 医保报销后，患者个人负担的金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.derate_cost IS ''减免金额 医院减免''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.fundpay_sumamt IS ''基金支付总金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_account_pay IS ''医保个人账户支付''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.hifp_pay IS ''基本医疗保险统筹基金支出''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.self_charge IS ''不传医保自费金额 纯自费，医保目录无此药品''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.special_need_fee IS ''特需费用 收/退费总额中可归入特需的数额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.special_need_drugfee IS ''特需药费 特需费用中药费的部分''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rgst_serv_fee IS ''挂号费 挂退号费用都以正数表示。对于自助挂号，或者挂号时先不收费的情况，可填写为0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.digtrt_fee IS ''诊疗费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.trt_fee IS ''治疗费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.exam_charge IS ''检查费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.rgtrt_fee IS ''手术费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.matl_fee IS ''卫生材料费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.bed_fee IS ''床位费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.nurs_fee IS ''护理费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.pharm_fee IS ''药事服务费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.ord_digtrt_fee IS ''一般诊疗费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.laboratory_fee IS ''化验费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.wm_fee IS ''西药费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.tcmpat_fee IS ''中成药费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.tcmherb_fee IS ''中草药费''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.oth_fee IS ''其他费用 挂退号费用都以正数表示。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_settle_no IS ''医保结算号 医保患者必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_visit_no IS ''医保挂号号 医保患者必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_settle_flag IS ''医保结算标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_settle_rea IS ''医保未结算原因 当医保结算标志为否时，此字段必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_over_limit_flag IS ''医保超限标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_pay_line IS ''医保起付线''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.business_account_pay IS ''商保个人账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.business_fund_pay IS ''商保统筹基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.business_insur_pay IS ''商业保险支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_classb_pay IS ''医保乙类支付（医保自理支付） 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.insur_over_top_pay IS ''超封顶线自付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.civil_account_pay IS ''公务员账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.cvlserv_pay IS ''公务员医疗补助资金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.other_fund_pay IS ''其他基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.hosp_part_amt IS ''医院负担金额 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.hifmi_pay IS ''居民大病保险资金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.hifob_pay IS ''职工大额医疗费用补助基金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.home_help_pay IS ''民政求助支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.health_account_pay IS ''健康账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.health_fund_pay IS ''保健基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.transfer_prior_payment IS ''转诊先自付''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_master.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_11_out ON outp_settle_master (rid, upload_time)';

    log_table_result('outp_settle_master', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_settle_master', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 outp_settle_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE outp_settle_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    settle_time DATE,
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    settle_no VARCHAR2(100),
    settle_detail_no VARCHAR2(100),
    receipt_class_code VARCHAR2(255),
    receipt_class_name VARCHAR2(255),
    medfeesumamt NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_12_out PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn, settle_no, settle_detail_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE outp_settle_detail IS ''门诊结算明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+就诊流水号mdtrt_sn+结算号settle_no+结算明细号settle_detail_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.settle_time IS ''结算时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.mdtrt_sn IS ''就诊流水号 复合主键，门诊就诊流水号，可以为挂号记录的挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.settle_no IS ''结算号 复合主键，门诊结算流水号，门诊多次结算需保证唯一。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.settle_detail_no IS ''结算明细号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.receipt_class_code IS ''发票类别代码 例如：西药费、治疗费、手术费、救护车费、MRI费、CT费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.receipt_class_name IS ''发票类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.medfeesumamt IS ''总费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN outp_settle_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_12_out ON outp_settle_detail (rid, upload_time)';

    log_table_result('outp_settle_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('outp_settle_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_admission_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_admission_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    adm_time DATE,
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    ipt_no VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    ipt_number VARCHAR2(255),
    age_year NUMBER(16),
    age_month NUMBER(16),
    age_day NUMBER(16),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    otp_doc_code VARCHAR2(100),
    otp_doc_name VARCHAR2(4000),
    adm_wardarea_code VARCHAR2(50),
    adm_wardarea_name VARCHAR2(100),
    adm_dept_codg VARCHAR2(50),
    adm_dept_name VARCHAR2(4000),
    ipt_doc_code VARCHAR2(100),
    ipt_doc_name VARCHAR2(100),
    accept_nurse_code VARCHAR2(255),
    accept_nurse_name VARCHAR2(255),
    adm_reg_time DATE,
    adm_operator_code VARCHAR2(255),
    adm_operator_name VARCHAR2(255),
    adm_way_code VARCHAR2(50),
    adm_way_name VARCHAR2(100),
    adm_dise_cond_code VARCHAR2(255),
    adm_dise_cond_name VARCHAR2(255),
    admission_state_code VARCHAR2(255),
    admission_state_name VARCHAR2(255),
    adm_diag_code VARCHAR2(4000),
    adm_diag_name VARCHAR2(255),
    ipt_purpose_code VARCHAR2(255),
    ipt_purpose_name VARCHAR2(255),
    wardno VARCHAR2(255),
    sickbed_no VARCHAR2(255),
    ipt_status_code VARCHAR2(255),
    ipt_status_name VARCHAR2(255),
    nurscare_lv_code VARCHAR2(255),
    nurscare_lv_name VARCHAR2(255),
    nurse_flag VARCHAR2(1),
    super_doc_code VARCHAR2(255),
    super_doc_name VARCHAR2(255),
    group_code VARCHAR2(255),
    group_name VARCHAR2(255),
    patient_souc_code VARCHAR2(50),
    patient_souc_name VARCHAR2(100),
    isolation_flag VARCHAR2(1),
    first_visit_flag VARCHAR2(1),
    med_insu_no VARCHAR2(255),
    hi_card_no VARCHAR2(255),
    insur_ipt_no VARCHAR2(255),
    gender_code VARCHAR2(50),
    gender_name VARCHAR2(100),
    brdy DATE,
    psn_tel VARCHAR2(50),
    curr_addr VARCHAR2(4000),
    mrg_stas_code VARCHAR2(50),
    mrg_stas_name VARCHAR2(100),
    certno VARCHAR2(255),
    height NUMBER(20,4),
    weight NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_13_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_admission_record IS ''住院患者入院记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_time IS ''入院时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.patient_name IS ''患者姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_no IS ''住院号 若无则可填住院就诊流水号 某患者多次住院同一住院号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_number IS ''住院序号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.age_year IS ''年龄（年） 患者年龄满1周岁的实足年龄，为患者出生后按照日历计算的历法年龄，以实足年龄的相应整数填写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.age_month IS ''年龄（月） 年龄不满实足1周岁的月龄''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.age_day IS ''年龄（天） 年龄不满实足1个月的天数''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.otp_doc_code IS ''门诊医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.otp_doc_name IS ''门诊医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_wardarea_code IS ''入院病区（房）代码 填写院内病区代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_wardarea_name IS ''入院病区（房）名称 填写院内病区名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_dept_codg IS ''入院科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_dept_name IS ''入院科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_doc_code IS ''住院医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_doc_name IS ''住院医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.accept_nurse_code IS ''接诊护士工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.accept_nurse_name IS ''接诊护士姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_reg_time IS ''入院登记时间 入院登记操作时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_operator_code IS ''入院登记操作人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_operator_name IS ''入院登记操作人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_way_code IS ''入院类型（途径）代码 门诊、急诊、其他医疗机构转入、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_way_name IS ''入院类型（途径）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_dise_cond_code IS ''入院疾病病情代码 有、临床未确定、情况不明、无''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_dise_cond_name IS ''入院疾病病情名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.admission_state_code IS ''入院情况代码 普通/一般、急、病重、病危、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.admission_state_name IS ''入院情况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_diag_code IS ''入院诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.adm_diag_name IS ''入院诊断名称 若有多条，填写主要诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_purpose_code IS ''住院目的代码 治疗、分娩、身体检查、计划生育、其他、未知''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_purpose_name IS ''住院目的名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.wardno IS ''病房号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.sickbed_no IS ''床位号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_status_code IS ''住院状态代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.ipt_status_name IS ''住院状态名称 在院；在科；转科；出院等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.nurscare_lv_code IS ''护理等级代码 例如：特级护理、一级护理、二级护理、三级护理、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.nurscare_lv_name IS ''护理等级名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.nurse_flag IS ''陪伴标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.super_doc_code IS ''上级医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.super_doc_name IS ''上级医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.group_code IS ''主管医疗小组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.group_name IS ''主管医疗小组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.patient_souc_code IS ''患者来源代码 例如：本县、本市其他县、本省其他市、外省等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.patient_souc_name IS ''患者来源名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.isolation_flag IS ''隔离标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.first_visit_flag IS ''初诊标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.med_insu_no IS ''医疗保险号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.hi_card_no IS ''医保卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.insur_ipt_no IS ''医保住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.gender_code IS ''性别代码 例如：男性、女性、未说明的性别等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.gender_name IS ''性别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.brdy IS ''出生日期''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.psn_tel IS ''本人电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.curr_addr IS ''居住地址详细地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.mrg_stas_code IS ''婚姻状况代码 未婚、已婚、初婚、再婚、复婚、丧偶、离婚、未说明的婚姻状况''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.mrg_stas_name IS ''婚姻状况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.certno IS ''身份证件号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.height IS ''身高（cm） 记录患者就诊时的身高，单位cm''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.weight IS ''体重（kg） 记录患者就诊时的体重，单位kg''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_admission_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_13_inp ON inp_admission_record (rid, upload_time)';

    log_table_result('inp_admission_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_admission_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_discharge_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_discharge_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    dscg_time DATE,
    patient_no VARCHAR2(100),
    patient_name VARCHAR2(255),
    ipt_no VARCHAR2(255),
    mdtrt_sn VARCHAR2(100),
    ipt_number VARCHAR2(255),
    age_year NUMBER(16),
    age_month NUMBER(16),
    age_day NUMBER(16),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    otp_doc_code VARCHAR2(100),
    otp_doc_name VARCHAR2(4000),
    adm_time DATE,
    adm_wardarea_code VARCHAR2(50),
    adm_wardarea_name VARCHAR2(100),
    adm_dept_codg VARCHAR2(50),
    adm_dept_name VARCHAR2(4000),
    ipt_doc_code VARCHAR2(100),
    ipt_doc_name VARCHAR2(100),
    accept_nurse_code VARCHAR2(255),
    accept_nurse_name VARCHAR2(255),
    adm_reg_time DATE,
    adm_operator_code VARCHAR2(255),
    adm_operator_name VARCHAR2(255),
    adm_way_code VARCHAR2(50),
    adm_way_name VARCHAR2(100),
    adm_dise_cond_code VARCHAR2(255),
    adm_dise_cond_name VARCHAR2(255),
    admission_state_code VARCHAR2(255),
    admission_state_name VARCHAR2(255),
    adm_diag_code VARCHAR2(4000),
    adm_diag_name VARCHAR2(255),
    ipt_purpose_code VARCHAR2(255),
    ipt_purpose_name VARCHAR2(255),
    dscg_wardarea_code VARCHAR2(255),
    dscg_wardarea_name VARCHAR2(255),
    dscg_dept_code VARCHAR2(50),
    dscg_dept_name VARCHAR2(4000),
    wardno VARCHAR2(255),
    sickbed_no VARCHAR2(255),
    ipt_status_code VARCHAR2(255),
    ipt_status_name VARCHAR2(255),
    nurscare_lv_code VARCHAR2(255),
    nurscare_lv_name VARCHAR2(255),
    nurse_flag VARCHAR2(1),
    discharge_doc_code VARCHAR2(255),
    discharge_doc_name VARCHAR2(255),
    super_doc_code VARCHAR2(255),
    super_doc_name VARCHAR2(255),
    group_code VARCHAR2(255),
    group_name VARCHAR2(255),
    discharge_nurse_code VARCHAR2(255),
    discharge_nurse_name VARCHAR2(255),
    dscg_diag_code VARCHAR2(4000),
    dscg_diag_name VARCHAR2(255),
    dscg_dise_cond_code VARCHAR2(255),
    dscg_dise_cond_name VARCHAR2(255),
    dis_pro_code VARCHAR2(255),
    dis_pro_name VARCHAR2(255),
    patient_souc_code VARCHAR2(50),
    patient_souc_name VARCHAR2(100),
    isolation_flag VARCHAR2(1),
    first_visit_flag VARCHAR2(1),
    settle_flag VARCHAR2(1),
    med_insu_no VARCHAR2(255),
    hi_card_no VARCHAR2(255),
    insur_ipt_no VARCHAR2(255),
    gender_code VARCHAR2(50),
    gender_name VARCHAR2(100),
    brdy DATE,
    psn_tel VARCHAR2(50),
    curr_addr VARCHAR2(4000),
    mrg_stas_code VARCHAR2(50),
    mrg_stas_name VARCHAR2(100),
    certno VARCHAR2(255),
    height NUMBER(20,4),
    weight NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_14_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_discharge_record IS ''住院患者出院记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_time IS ''出院时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.patient_name IS ''患者姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_no IS ''住院号 若无则可填住院就诊流水号 某患者多次住院同一住院号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_number IS ''住院序号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.age_year IS ''年龄（年） 患者年龄满1周岁的实足年龄，为患者出生后按照日历计算的历法年龄，以实足年龄的相应整数填写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.age_month IS ''年龄（月） 年龄不满实足1周岁的月龄''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.age_day IS ''年龄（天） 年龄不满实足1个月的天数''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.otp_doc_code IS ''门诊医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.otp_doc_name IS ''门诊医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_time IS ''入院时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_wardarea_code IS ''入院病区（房）代码 填写院内病区代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_wardarea_name IS ''入院病区（房）名称 填写院内病区名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_dept_codg IS ''入院科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_dept_name IS ''入院科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_doc_code IS ''住院医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_doc_name IS ''住院医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.accept_nurse_code IS ''接诊护士工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.accept_nurse_name IS ''接诊护士姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_reg_time IS ''入院登记时间 入院登记操作时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_operator_code IS ''入院登记操作人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_operator_name IS ''入院登记操作人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_way_code IS ''入院类型（途径）代码 门诊、急诊、其他医疗机构转入、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_way_name IS ''入院类型（途径）名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_dise_cond_code IS ''入院疾病病情代码 有、临床未确定、情况不明、无''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_dise_cond_name IS ''入院疾病病情名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.admission_state_code IS ''入院情况代码 普通/一般、急、病重、病危、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.admission_state_name IS ''入院情况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_diag_code IS ''入院诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.adm_diag_name IS ''入院诊断名称 若有多条，填写主要诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_purpose_code IS ''住院目的代码 治疗、分娩、身体检查、计划生育、其他、未知''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_purpose_name IS ''住院目的名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_wardarea_code IS ''出院病区（房）代码 填写院内病区代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_wardarea_name IS ''出院病区（房）名称 填写院内病区名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_dept_code IS ''出院科室代码 科室编码，未出院时填写默认值“/”''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_dept_name IS ''出院科室名称 科室名称。未出院时填写默认值“/” 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.wardno IS ''病房号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.sickbed_no IS ''床位号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_status_code IS ''住院状态代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.ipt_status_name IS ''住院状态名称 在院；在科；转科；出院等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.nurscare_lv_code IS ''护理等级代码 例如：特级护理、一级护理、二级护理、三级护理、其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.nurscare_lv_name IS ''护理等级名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.nurse_flag IS ''陪伴标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.discharge_doc_code IS ''出院主管医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.discharge_doc_name IS ''出院主管医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.super_doc_code IS ''上级医师工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.super_doc_name IS ''上级医师姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.group_code IS ''主管医疗小组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.group_name IS ''主管医疗小组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.discharge_nurse_code IS ''出院主管护士工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.discharge_nurse_name IS ''出院主管护士姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_diag_code IS ''出院诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_diag_name IS ''出院诊断名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_dise_cond_code IS ''出院疾病病情代码 有、临床未确定、情况不明、无''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dscg_dise_cond_name IS ''出院疾病病情名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dis_pro_code IS ''疾病转归代码 例如：治愈,好转,稳定,恶化,死亡,其他''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.dis_pro_name IS ''疾病转归名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.patient_souc_code IS ''患者来源代码 例如：本县、本市其他县、本省其他市、外省等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.patient_souc_name IS ''患者来源名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.isolation_flag IS ''隔离标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.first_visit_flag IS ''初诊标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.settle_flag IS ''出院结算标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.med_insu_no IS ''医疗保险号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.hi_card_no IS ''医保卡号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.insur_ipt_no IS ''医保住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.gender_code IS ''性别代码 例如：男性、女性、未说明的性别等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.gender_name IS ''性别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.brdy IS ''出生日期''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.psn_tel IS ''本人电话号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.curr_addr IS ''居住地址详细地址''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.mrg_stas_code IS ''婚姻状况代码 未婚、已婚、初婚、再婚、复婚、丧偶、离婚、未说明的婚姻状况''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.mrg_stas_name IS ''婚姻状况名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.certno IS ''身份证件号码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.height IS ''身高（cm） 记录患者就诊时的身高，单位cm''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.weight IS ''体重（kg） 记录患者就诊时的体重，单位kg''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_discharge_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_14_inp ON inp_discharge_record (rid, upload_time)';

    log_table_result('inp_discharge_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_discharge_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_diagnosis_record
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_diagnosis_record (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    diag_time DATE,
    mdtrt_sn VARCHAR2(100),
    patient_no VARCHAR2(100),
    diag_srt_no VARCHAR2(100),
    diag_code VARCHAR2(100),
    diag_name VARCHAR2(255),
    maindiag_flag VARCHAR2(1),
    diagnosis_sort_no VARCHAR2(255),
    diag_type_code VARCHAR2(100),
    diag_type_name VARCHAR2(255),
    diag_dept_code VARCHAR2(255),
    diag_dept_name VARCHAR2(255),
    diagnosis_method_code VARCHAR2(255),
    diagnosis_method_name VARCHAR2(255),
    symp_dscr VARCHAR2(4000),
    memo VARCHAR2(4000),
    diag_doc_code VARCHAR2(255),
    diag_doc_name VARCHAR2(255),
    vali_flag VARCHAR2(1),
    infet_flag VARCHAR2(1),
    infect_report_flag VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_15_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, mdtrt_sn, patient_no, diag_srt_no, diag_type_code)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_diagnosis_record IS ''住院诊断记录表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+就诊流水号mdtrt_sn+患者编号patient_no+诊断流水号diag_srt_no+诊断类型代码diag_type_code''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_time IS ''诊断时间 业务时间&采集时间戳 若没有诊断时间，用出院时间采集''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_srt_no IS ''诊断流水号 复合主键，业务表的唯一诊断记录''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_code IS ''疾病诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_name IS ''疾病诊断名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.maindiag_flag IS ''主要诊断标志 1：主诊断；0或空 次要诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diagnosis_sort_no IS ''诊断排序号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_type_code IS ''诊断类型代码 复合主键，例如：出院诊断,门诊诊断,术后诊断,超声诊断等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_type_name IS ''诊断类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_dept_code IS ''诊断科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_dept_name IS ''诊断科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diagnosis_method_code IS ''诊断方法代码 例如：西医诊断,中医诊断,中医证型诊断,中医病名诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diagnosis_method_name IS ''诊断方法名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.symp_dscr IS ''症状描述 接诊医生对患者症状的简要描述''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.memo IS ''备注说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_doc_code IS ''诊断医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.diag_doc_name IS ''诊断医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.vali_flag IS ''有效标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.infet_flag IS ''传染病标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.infect_report_flag IS ''传染病上传标识 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_diagnosis_record.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_15_inp ON inp_diagnosis_record (rid, upload_time)';

    log_table_result('inp_diagnosis_record', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_diagnosis_record', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_order_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_order_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    order_time DATE,
    order_no VARCHAR2(100),
    order_serial_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    drord_prop_type VARCHAR2(255),
    drord_prop_name VARCHAR2(255),
    drord_stas VARCHAR2(255),
    drord_stas_name VARCHAR2(255),
    order_dept_code VARCHAR2(255),
    order_dept_name VARCHAR2(255),
    order_doc_code VARCHAR2(255),
    order_doc_name VARCHAR2(255),
    drord_item_code VARCHAR2(255),
    drord_item_name VARCHAR2(255),
    drug_spec VARCHAR2(255),
    dosage VARCHAR2(255),
    dosunt VARCHAR2(255),
    drug_method_code VARCHAR2(255),
    drug_method_name VARCHAR2(255),
    drug_used_frqu_code VARCHAR2(255),
    drug_used_frqu_name VARCHAR2(255),
    medc_days NUMBER(16),
    drug_used_idose NUMBER(20,4),
    drug_used_idose_unit VARCHAR2(255),
    price NUMBER(20,4),
    drord_memo VARCHAR2(4000),
    insur_flag VARCHAR2(1),
    insur_percent NUMBER(20,4),
    drug_batch_no VARCHAR2(100),
    emergency_flag VARCHAR2(1),
    exam_part_code VARCHAR2(255),
    exam_part_name VARCHAR2(4000),
    skin_test_code VARCHAR2(255),
    skin_test_name VARCHAR2(255),
    skintst_resu_code VARCHAR2(255),
    skintst_resu_name VARCHAR2(255),
    tcm_medi_method_code VARCHAR2(255),
    tcm_medi_method_name VARCHAR2(255),
    abtl_pup_code VARCHAR2(255),
    abtl_pup_name VARCHAR2(4000),
    selfpro_flag VARCHAR2(1),
    baby_flag VARCHAR2(1),
    begin_execute_time DATE,
    prepay_stop_time DATE,
    input_time DATE,
    inputer_code VARCHAR2(255),
    inputer_name VARCHAR2(255),
    check_nurse_code VARCHAR2(255),
    check_nurse_name VARCHAR2(255),
    check_time DATE,
    drord_stop_time DATE,
    drord_stop_doc_code VARCHAR2(255),
    drord_stop_doc_name VARCHAR2(255),
    confirm_drord_stop_time DATE,
    confirm_stop_nurse_code VARCHAR2(255),
    confirm_stop_nurse_name VARCHAR2(255),
    exe_dept_code VARCHAR2(255),
    exe_dept_name VARCHAR2(255),
    execute_operator_code VARCHAR2(255),
    execute_operator_name VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_16_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, order_no, order_serial_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_order_detail IS ''住院医嘱明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+医嘱号order_no+医嘱子序号order_serial_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_time IS ''下嘱时间 业务时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_no IS ''医嘱号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_serial_no IS ''医嘱子序号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_prop_type IS ''医嘱性质代码 长期医嘱、临时医嘱、住院处方、手术用药、麻醉用药、出院带药、出院补药、其他、未知''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_prop_name IS ''医嘱性质名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_stas IS ''医嘱状态代码 新开、待审、已审核、已发送、已缴费、已执行、已取消(DC，)、已停止、作废、其他、未知''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_stas_name IS ''医嘱状态名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_dept_code IS ''下嘱科室代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_dept_name IS ''下嘱科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_doc_code IS ''下嘱医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.order_doc_name IS ''下嘱医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_item_code IS ''医嘱项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_item_name IS ''医嘱项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_spec IS ''药品规格''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.dosage IS ''每次剂量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.dosunt IS ''剂量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_method_code IS ''用药方法代码 例如：口服、直肠用药、舌下用药、注射用药等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_method_name IS ''用药方法名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_used_frqu_code IS ''用药频次代码 例如：bid、biw、Hs、q12h''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_used_frqu_name IS ''用药频次名称 一时段内用药次数。如：一日三次。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.medc_days IS ''用药天数 出院带药用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_used_idose IS ''药物使用总剂量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_used_idose_unit IS ''药物使用总剂量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.price IS ''单价''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_memo IS ''医嘱备注信息 特殊的说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.insur_flag IS ''医保标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.insur_percent IS ''医保比率''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drug_batch_no IS ''药品批次''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.emergency_flag IS ''紧急医嘱标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.exam_part_code IS ''检查部位代码 例如：双侧鼻孔、臀部、左臂、左前胸等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.exam_part_name IS ''检查部位名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.skin_test_code IS ''皮试项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.skin_test_name IS ''皮试项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.skintst_resu_code IS ''皮试结果代码 例如：阴性（-）,皮试中,过敏等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.skintst_resu_name IS ''皮试结果名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.tcm_medi_method_code IS ''中药煎煮法代码 中药处方必填。 例如：包煎、冲服、后煎、后下等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.tcm_medi_method_name IS ''中药煎煮法名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.abtl_pup_code IS ''抗菌药使用目的代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.abtl_pup_name IS ''抗菌药使用目的名称 治疗、预防、皮试等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.selfpro_flag IS ''自备药标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.baby_flag IS ''婴儿标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.begin_execute_time IS ''开始执行时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.prepay_stop_time IS ''预计停止时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.input_time IS ''录入时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.inputer_code IS ''录入人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.inputer_name IS ''录入人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.check_nurse_code IS ''校对护士工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.check_nurse_name IS ''校对护士姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.check_time IS ''校对时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_stop_time IS ''停嘱时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_stop_doc_code IS ''停嘱医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.drord_stop_doc_name IS ''停嘱医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.confirm_drord_stop_time IS ''确认停嘱时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.confirm_stop_nurse_code IS ''确认停嘱护士工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.confirm_stop_nurse_name IS ''确认停嘱护士姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.exe_dept_code IS ''执行科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.exe_dept_name IS ''执行科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.execute_operator_code IS ''执行人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.execute_operator_name IS ''执行人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_order_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_16_inp ON inp_order_detail (rid, upload_time)';

    log_table_result('inp_order_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_order_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_charge_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_charge_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    fee_detail_no VARCHAR2(100),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    charge_time DATE,
    charge_operator_code VARCHAR2(255),
    charge_operator_name VARCHAR2(255),
    charge_mode_code VARCHAR2(255),
    charge_mode_name VARCHAR2(255),
    med_chrgitm_type_code VARCHAR2(255),
    med_chrgitm_type_name VARCHAR2(255),
    item_code VARCHAR2(100),
    item_name VARCHAR2(4000),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    apply_no VARCHAR2(100),
    drug_batch_no VARCHAR2(100),
    item_expyend DATE,
    order_no VARCHAR2(100),
    spec VARCHAR2(255),
    unit VARCHAR2(255),
    price NUMBER(20,4),
    quantity NUMBER(16),
    cost NUMBER(20,4),
    receipt_class_code VARCHAR2(255),
    receipt_class_name VARCHAR2(255),
    case_receipt_class_code VARCHAR2(255),
    case_receipt_class_name VARCHAR2(255),
    account_class_code VARCHAR2(255),
    account_class_name VARCHAR2(255),
    settle_no VARCHAR2(100),
    settle_time DATE,
    apply_doc_code VARCHAR2(255),
    apply_doc_name VARCHAR2(255),
    apply_dept_code VARCHAR2(255),
    apply_dept_name VARCHAR2(255),
    apply_group_code VARCHAR2(255),
    apply_group_name VARCHAR2(255),
    execute_operator_code VARCHAR2(255),
    execute_operator_name VARCHAR2(255),
    exe_dept_code VARCHAR2(255),
    exe_dept_name VARCHAR2(255),
    execute_group_code VARCHAR2(255),
    execute_group_name VARCHAR2(255),
    direct_doc_code VARCHAR2(255),
    direct_doc_name VARCHAR2(255),
    direct_group_code VARCHAR2(255),
    direct_group_name VARCHAR2(255),
    ipt_dept_code VARCHAR2(255),
    ipt_dept_name VARCHAR2(255),
    return_charge_detail_no VARCHAR2(255),
    return_quantity NUMBER(20,4),
    return_cost NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_17_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, fee_detail_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_charge_detail IS ''住院费用明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+费用明细号fee_detail_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.fee_detail_no IS ''费用明细号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.charge_time IS ''扣费时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.charge_operator_code IS ''扣费操作人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.charge_operator_name IS ''扣费操作人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.charge_mode_code IS ''扣费方式代码 例如：扣单据、退卡费/病历本工本费、医保返还、配药/发药、挂号扣费等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.charge_mode_name IS ''扣费方式名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.med_chrgitm_type_code IS ''医疗收费项目类别代码 例如：床位费、诊察费、检查费、化验费、放射费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.med_chrgitm_type_name IS ''医疗收费项目类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.item_code IS ''项目代码 填写院内明细项目代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.item_name IS ''项目名称 填写院内明细项目名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_no IS ''单据号 申请单单据号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.drug_batch_no IS ''药品批次 药品需要填写；多个批次则用英文的“;”间隔。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.item_expyend IS ''（项目的）有效期截止日期 药品、耗材等需要填写；''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.order_no IS ''医嘱号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.spec IS ''项目规格''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.unit IS ''计量单位''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.price IS ''单价''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.quantity IS ''数量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.cost IS ''费用''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.receipt_class_code IS ''发票类别代码 例如：西药费、治疗费、手术费、救护车费、MRI费、CT费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.receipt_class_name IS ''发票类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.case_receipt_class_code IS ''病案首页费用类别代码 例如：一般医疗服务费,护理费,麻醉费,手术费等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.case_receipt_class_name IS ''病案首页费用类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.account_class_code IS ''会计科目类别代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.account_class_name IS ''会计科目类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.settle_no IS ''结算号 住院结算流水号，住院多次结算需保证唯一。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.settle_time IS ''结算时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_doc_code IS ''申请医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_doc_name IS ''申请医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_dept_code IS ''申请科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_dept_name IS ''申请科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_group_code IS ''申请医生所在医组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.apply_group_name IS ''申请医生所在医组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.execute_operator_code IS ''执行人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.execute_operator_name IS ''执行人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.exe_dept_code IS ''执行科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.exe_dept_name IS ''执行科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.execute_group_code IS ''执行医疗组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.execute_group_name IS ''执行医疗组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.direct_doc_code IS ''主管医生工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.direct_doc_name IS ''主管医生姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.direct_group_code IS ''主管医生医疗组代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.direct_group_name IS ''主管医生医疗组名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.ipt_dept_code IS ''住院科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.ipt_dept_name IS ''住院科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.return_charge_detail_no IS ''退费号 标注退费冲销的是哪一个正数的收费号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.return_quantity IS ''退费数量''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.return_cost IS ''退费金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_charge_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_17_inp ON inp_charge_detail (rid, upload_time)';

    log_table_result('inp_charge_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_charge_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_settle_master
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_settle_master (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    settle_time DATE,
    settle_no VARCHAR2(100),
    invono VARCHAR2(255),
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    settle_type_code VARCHAR2(255),
    settle_type_name VARCHAR2(255),
    operation_type_code VARCHAR2(255),
    operation_type_name VARCHAR2(255),
    mdtrt_dept_code VARCHAR2(50),
    mdtrt_dept_name VARCHAR2(100),
    rate_type_code VARCHAR2(50),
    rate_type_name VARCHAR2(100),
    med_type VARCHAR2(255),
    med_type_name VARCHAR2(255),
    contract_unit_code VARCHAR2(255),
    contract_unit_name VARCHAR2(255),
    settle_operator_code VARCHAR2(255),
    settle_operator_name VARCHAR2(255),
    cancel_operator_code VARCHAR2(255),
    cancel_operator_name VARCHAR2(255),
    cancel_opetation_time DATE,
    cancel_settle_no VARCHAR2(255),
    account_no VARCHAR2(255),
    diag_code VARCHAR2(100),
    diag_name VARCHAR2(255),
    medfeesumamt NUMBER(20,4),
    charge NUMBER(20,4),
    derate_cost NUMBER(20,4),
    fundpay_sumamt NUMBER(20,4),
    insur_account_pay NUMBER(20,4),
    hifp_pay NUMBER(20,4),
    hifob_pay NUMBER(20,4),
    self_charge NUMBER(20,4),
    special_need_fee NUMBER(20,4),
    special_need_drugfee NUMBER(20,4),
    digtrt_fee NUMBER(20,4),
    trt_fee NUMBER(20,4),
    nurs_fee NUMBER(20,4),
    rgtrt_fee NUMBER(20,4),
    matl_fee NUMBER(20,4),
    bed_fee NUMBER(20,4),
    pharm_fee NUMBER(20,4),
    ord_digtrt_fee NUMBER(20,4),
    exam_charge NUMBER(20,4),
    laboratory_fee NUMBER(20,4),
    bld_fee NUMBER(20,4),
    oxygen_fee NUMBER(20,4),
    wm_fee NUMBER(20,4),
    tcmpat_fee NUMBER(20,4),
    tcmherb_fee NUMBER(20,4),
    oth_fee NUMBER(20,4),
    insur_settle_no VARCHAR2(255),
    insur_visit_no VARCHAR2(255),
    insur_settle_flag VARCHAR2(1),
    insur_settle_rea VARCHAR2(4000),
    insur_over_limit_flag VARCHAR2(1),
    insur_pay_line NUMBER(20,4),
    business_account_pay NUMBER(20,4),
    business_fund_pay NUMBER(20,4),
    business_insur_pay NUMBER(20,4),
    insur_classb_pay NUMBER(20,4),
    insur_over_top_pay NUMBER(20,4),
    civil_account_pay NUMBER(20,4),
    cvlserv_pay NUMBER(20,4),
    other_fund_pay NUMBER(20,4),
    hosp_part_amt NUMBER(20,4),
    hifmi_pay NUMBER(20,4),
    home_help_pay NUMBER(20,4),
    health_account_pay NUMBER(20,4),
    health_fund_pay NUMBER(20,4),
    transfer_prior_payment NUMBER(20,4),
    clr_optins VARCHAR2(255),
    clr_way VARCHAR2(255),
    clr_type VARCHAR2(255),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_18_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, settle_no, patient_no, mdtrt_sn)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_settle_master IS ''住院结算主表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+结算号settle_no+患者编号patient_no+就诊流水号mdtrt_sn''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_time IS ''结算时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_no IS ''结算号 复合主键，住院结算流水号，住院多次结算需保证唯一。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.invono IS ''发票号码 超过一张发票时，以”;”间隔''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.mdtrt_sn IS ''就诊流水号 复合主键，入院登记时产生的代表该次住院的信息系统唯一识别编号；某患者多次住院不同住院流水号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_type_code IS ''结算类型代码 例如：门诊结算,出院结算,结算冲销,中途结算等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_type_name IS ''结算类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.operation_type_code IS ''结算操作类型代码 例如：正常结算,结算冲销,挂号结算,退号结算等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.operation_type_name IS ''结算操作类型名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.mdtrt_dept_code IS ''就诊科室代码 例如：预防保健科、全科医疗科、内科等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.mdtrt_dept_name IS ''就诊科室名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.rate_type_code IS ''费别代码 例如：城镇职工基本医疗保险,城乡居民基本医疗保险,贫困救助,商业医疗保险等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.rate_type_name IS ''费别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.med_type IS ''医疗类别代码 例如：普通门诊、门诊两病、普通住院、精神病住院等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.med_type_name IS ''医疗类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.contract_unit_code IS ''结算合同单位代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.contract_unit_name IS ''结算合同单位名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_operator_code IS ''结算员工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.settle_operator_name IS ''结算员姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.cancel_operator_code IS ''冲销人/取消人工号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.cancel_operator_name IS ''冲销人/取消人姓名''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.cancel_opetation_time IS ''冲销/取消时间''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.cancel_settle_no IS ''取消结算号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.account_no IS ''结账号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.diag_code IS ''疾病诊断代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.diag_name IS ''疾病诊断名称 若有多条，填写主要诊断''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.medfeesumamt IS ''总费用 收退费均以正数表达。总费用=实收金额+优惠金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.charge IS ''自付金额 医保报销后，患者个人负担的金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.derate_cost IS ''减免金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.fundpay_sumamt IS ''基金支付总金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_account_pay IS ''医保个人账户支付''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.hifp_pay IS ''基本医疗保险统筹基金支出''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.hifob_pay IS ''职工大额医疗费用补助基金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.self_charge IS ''不传医保自费金额 纯自费，医保目录无此药品''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.special_need_fee IS ''特需费用 费用中可归入特需中的数额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.special_need_drugfee IS ''特需药费 特需费用中药费的部分''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.digtrt_fee IS ''诊疗费 收退费均以正数表达，口径按医保''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.trt_fee IS ''治疗费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.nurs_fee IS ''护理费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.rgtrt_fee IS ''手术费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.matl_fee IS ''卫生材料费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.bed_fee IS ''床位费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.pharm_fee IS ''药事服务费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.ord_digtrt_fee IS ''一般诊疗费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.exam_charge IS ''检查费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.laboratory_fee IS ''化验费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.bld_fee IS ''输血费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.oxygen_fee IS ''输氧费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.wm_fee IS ''西药费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.tcmpat_fee IS ''中成药费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.tcmherb_fee IS ''中草药费 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.oth_fee IS ''其他费用 同上''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_settle_no IS ''医保结算号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_visit_no IS ''医保挂号号''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_settle_flag IS ''医保结算标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_settle_rea IS ''医保未结算原因 当医保结算标志为否时，此字段必填''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_over_limit_flag IS ''医保超限标志 1 是，0否''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_pay_line IS ''医保起付线''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.business_account_pay IS ''商保个人账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.business_fund_pay IS ''商保统筹基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.business_insur_pay IS ''商业保险支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_classb_pay IS ''医保乙类支付（医保自理支付） 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.insur_over_top_pay IS ''超封顶线自付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.civil_account_pay IS ''公务员账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.cvlserv_pay IS ''公务员医疗补助资金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.other_fund_pay IS ''其他基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.hosp_part_amt IS ''医院负担金额 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.hifmi_pay IS ''居民大病保险资金支出 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.home_help_pay IS ''民政求助支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.health_account_pay IS ''健康账户支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.health_fund_pay IS ''保健基金支付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.transfer_prior_payment IS ''转诊先自付 没有填0''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.clr_optins IS ''清算经办机构''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.clr_way IS ''清算方式代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.clr_type IS ''清算类别代码''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_master.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_18_inp ON inp_settle_master (rid, upload_time)';

    log_table_result('inp_settle_master', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_settle_master', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/


-- 创建表 inp_settle_detail
DECLARE
    v_sql CLOB;
BEGIN
    -- 创建表
    v_sql := q'[CREATE TABLE inp_settle_detail (
    rid VARCHAR2(255),
    org_name VARCHAR2(100),
    uscid VARCHAR2(100),
    upload_time DATE,
    sys_prdr_code VARCHAR2(50),
    sys_prdr_name VARCHAR2(255),
    settle_time DATE,
    patient_no VARCHAR2(100),
    mdtrt_sn VARCHAR2(100),
    settle_no VARCHAR2(100),
    settle_detail_no VARCHAR2(100),
    receipt_class_code VARCHAR2(255),
    receipt_class_name VARCHAR2(255),
    medfeesumamt NUMBER(20,4),
    reserve1 VARCHAR2(255),
    reserve2 VARCHAR2(255),
    data_clct_prdr_name VARCHAR2(255),
    crte_time DATE,
    updt_time DATE,
    deleted VARCHAR2(1),
    deleted_time DATE,
    CONSTRAINT PK_19_inp PRIMARY KEY (uscid, upload_time, sys_prdr_code, patient_no, mdtrt_sn, settle_no, settle_detail_no)
)]';
    EXECUTE IMMEDIATE v_sql;
    
    -- 添加注释（只有表创建成功才会执行到这里）
    EXECUTE IMMEDIATE 'COMMENT ON TABLE inp_settle_detail IS ''住院结算明细表''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.rid IS ''数据唯一记录号 唯一索引，生成规则：医疗机构统一社会信用代码（18位）uscid+系统建设厂商代码sys_prdr_code+患者编号patient_no+门诊就诊号otp_visit_no+结算号settle_no+结算明细号settle_detail_no''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.org_name IS ''医疗机构名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.uscid IS ''医疗机构统一社会信用代码 见公共字段【医疗机构统一社会信用代码】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.upload_time IS ''数据上传时间 复合主键，唯一索引，见公共字段【数据上传时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.sys_prdr_code IS ''系统建设厂商代码 复合主键，系统建设厂商名称首字母大写''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.sys_prdr_name IS ''系统建设厂商名称 见公共字段【系统建设厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.settle_time IS ''结算时间 业务时间&采集时间戳''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.patient_no IS ''患者编号 复合主键，患者在该医院某次注册的唯一编号，如果同一患者在同一医院注册多次，有多个患者编号。不同医院的患者编号可以相同，患者主索引构建将由“医疗机构统一社会信用代码、患者编码、患者姓名、身份证号"等信息做联合识别。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.mdtrt_sn IS ''就诊流水号 复合主键，用于与住院就诊记录表或门诊就诊记录表关联的外键（可选关联关系）''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.settle_no IS ''结算号 复合主键，住院结算流水号，住院多次结算需保证唯一。''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.settle_detail_no IS ''结算明细号 复合主键''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.receipt_class_code IS ''发票类别代码 例如：西药费、治疗费、手术费、救护车费、MRI费、CT费等等''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.receipt_class_name IS ''发票类别名称''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.medfeesumamt IS ''总费用 收退费均以正数表达。总费用=实收金额+优惠金额''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.reserve1 IS ''预留一 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.reserve2 IS ''预留二 为系统处理该数据而预留''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.data_clct_prdr_name IS ''数据改造厂商名称 见公共字段【数据采集厂商名称】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.crte_time IS ''数据创建时间 见公共字段【数据创建时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.updt_time IS ''数据更新时间 见公共字段【数据更新时间】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.deleted IS ''数据删除状态 见公共字段【数据删除状态】说明''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN inp_settle_detail.deleted_time IS ''数据删除时间 见公共字段【数据删除时间】说明''';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_U_19_inp ON inp_settle_detail (rid, upload_time)';

    log_table_result('inp_settle_detail', '成功', '表创建成功', NULL);
EXCEPTION
    WHEN OTHERS THEN
        log_table_result('inp_settle_detail', '失败', SUBSTR(SQLERRM, 1, 4000), SQLCODE);
END;
/

