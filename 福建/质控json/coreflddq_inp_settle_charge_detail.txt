{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "费用明细号(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "结算号(为空的数量/准确性）", "结算时间(为空的数量/准确性）", "扣费时间(为空的数量/准确性）", "扣费方式代码(为空的数量/准确性）", "扣费方式名称(为空的数量/准确性）", "医疗收费项目类别代码(为空的数量/准确性）", "医疗收费项目类别名称(为空的数量/准确性）", "项目代码(为空的数量/准确性）", "项目名称(为空的数量/准确性）", "费别代码(为空的数量/准确性）", "费别名称(为空的数量/准确性）", "药品批次(为空的数量/准确性）", "医嘱号(为空的数量/准确性）", "项目规格(为空的数量/准确性）", "计量单位(为空的数量/准确性）", "单价(为空的数量/准确性）", "数量(为空的数量/准确性）", "费用(为空的数量/准确性）", "自付金额(为空的数量/准确性）", "医保支付金额(为空的数量/准确性）", "发票类别代码(为空的数量/准确性）", "发票类别名称(为空的数量/准确性）", "病案首页费用类别代码(为空的数量/准确性）", "病案首页费用类别名称(为空的数量/准确性）", "医保项目代码(为空的数量/准确性）", "医保项目名称(为空的数量/准确性）", "医保发票类别代码(为空的数量/准确性）", "医保发票类别名称(为空的数量/准确性）", "项目等级代码(为空的数量/准确性）", "项目等级名称(为空的数量/准确性）", "自付比例(为空的数量/准确性）", "申请科室代码(为空的数量/准确性）", "申请科室名称(为空的数量/准确性）", "执行科室代码(为空的数量/准确性）", "执行科室名称(为空的数量/准确性）", "住院科室代码(为空的数量/准确性）", "住院科室名称(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与住院结算主表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "0", "医保项目代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "业务最大时间": "", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "", "病案首页费用类别名称(为空的数量/准确性）": "0", "业务最小时间": "", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:50.232799", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "医保项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "0", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "0", "hosno": "12352200490349810T", "cnt": "0", "项目等级代码(为空的数量/准确性）": "0", "项目等级名称(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "159", "医保项目代码(为空的数量/准确性）": "159", "与住院结算主表（关联不上的记录数）": "159", "业务最大时间": "2025-05-22 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "2", "住院科室名称(为空的数量/准确性）": "159", "hosname": "龙岩市第二医院", "病案首页费用类别名称(为空的数量/准确性）": "0", "业务最小时间": "2025-03-01 00:00:00.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:38.038367", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "159", "医保项目名称(为空的数量/准确性）": "159", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "0", "住院科室代码(为空的数量/准确性）": "159", "医保发票类别名称(为空的数量/准确性）": "159", "hosno": "h486", "cnt": "159", "项目等级代码(为空的数量/准确性）": "159", "项目等级名称(为空的数量/准确性）": "159", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "159", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "100", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "100", "医保项目代码(为空的数量/准确性）": "100", "与住院结算主表（关联不上的记录数）": "0", "业务最大时间": "2025-01-23 16:36:00.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "100", "数据连续性(数据量/月)": "1", "住院科室名称(为空的数量/准确性）": "0", "hosname": "龙岩市中医院", "病案首页费用类别名称(为空的数量/准确性）": "100", "业务最小时间": "2025-01-23 16:36:00.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:44:58.331723", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "100", "医保项目名称(为空的数量/准确性）": "100", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "100", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "100", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "100", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "100", "hosno": "h489", "cnt": "100", "项目等级代码(为空的数量/准确性）": "100", "项目等级名称(为空的数量/准确性）": "100", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "3060850", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "0", "医保项目代码(为空的数量/准确性）": "2077920", "与住院结算主表（关联不上的记录数）": "33846", "业务最大时间": "2025-06-29 13:58:46.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "6", "住院科室名称(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "病案首页费用类别名称(为空的数量/准确性）": "0", "业务最小时间": "2024-07-29 10:38:03.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:59.160645", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "医保项目名称(为空的数量/准确性）": "2161987", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "3060850", "计量单位(为空的数量/准确性）": "337", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "800296", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "0", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "0", "hosno": "h487", "cnt": "3060850", "项目等级代码(为空的数量/准确性）": "3060850", "项目等级名称(为空的数量/准确性）": "3060850", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4572904", "数据改造厂商名称(为空的数量/准确性）": "4572904", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "4572904", "医保项目代码(为空的数量/准确性）": "4572904", "与住院结算主表（关联不上的记录数）": "2128556", "业务最大时间": "2025-06-24 23:49:35.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "4572904", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "8294", "hosname": "宁德市中医院", "病案首页费用类别名称(为空的数量/准确性）": "4572904", "业务最小时间": "2025-01-01 00:04:05.000000", "发票类别名称(为空的数量/准确性）": "8188", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "4018232", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "1375", "项目代码(为空的数量/准确性）": "8188", "dqtime": "2025-07-03 19:47:41.074994", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "3374182", "医保项目名称(为空的数量/准确性）": "4572904", "数量(为空的数量/准确性）": "8188", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "3022134", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "3022108", "单价(为空的数量/准确性）": "8188", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "4572904", "住院科室代码(为空的数量/准确性）": "8597", "医保发票类别名称(为空的数量/准确性）": "4572904", "hosno": "h357", "cnt": "4572904", "项目等级代码(为空的数量/准确性）": "4572904", "项目等级名称(为空的数量/准确性）": "4572904", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "4572904", "医疗收费项目类别代码(为空的数量/准确性）": "4572904", "执行科室名称(为空的数量/准确性）": "2070725", "结算时间(为空的数量/准确性）": "2095967", "结算号(为空的数量/准确性）": "192768", "医嘱号(为空的数量/准确性）": "3022134"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1333387", "数据改造厂商名称(为空的数量/准确性）": "1207391", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "1333387", "医保项目代码(为空的数量/准确性）": "1333387", "与住院结算主表（关联不上的记录数）": "1148415", "业务最大时间": "2025-06-26 14:29:30.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "1207814", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "3869", "hosname": "宁德市闽东医院", "病案首页费用类别名称(为空的数量/准确性）": "1207814", "业务最小时间": "2025-06-09 00:03:58.000000", "发票类别名称(为空的数量/准确性）": "3869", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "1124361", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "882", "项目代码(为空的数量/准确性）": "3869", "dqtime": "2025-07-03 19:48:29.809699", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "946179", "医保项目名称(为空的数量/准确性）": "1333387", "数量(为空的数量/准确性）": "3869", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "941322", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "941322", "单价(为空的数量/准确性）": "3869", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "1333387", "住院科室代码(为空的数量/准确性）": "3869", "医保发票类别名称(为空的数量/准确性）": "1333387", "hosno": "h378", "cnt": "1333387", "项目等级代码(为空的数量/准确性）": "1333387", "项目等级名称(为空的数量/准确性）": "1333387", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1333387", "医疗收费项目类别代码(为空的数量/准确性）": "1333387", "执行科室名称(为空的数量/准确性）": "638624", "结算时间(为空的数量/准确性）": "1222647", "结算号(为空的数量/准确性）": "955297", "医嘱号(为空的数量/准确性）": "941322"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "32601", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "32601", "数据改造厂商名称(为空的数量/准确性）": "32601", "自付金额(为空的数量/准确性）": "32601", "医保发票类别代码(为空的数量/准确性）": "32601", "医保项目代码(为空的数量/准确性）": "32601", "与住院结算主表（关联不上的记录数）": "752", "业务最大时间": "2025-06-13 18:30:15.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "32601", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市皮肤病防治院", "病案首页费用类别名称(为空的数量/准确性）": "32601", "业务最小时间": "2025-01-02 09:26:56.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "32601", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "753", "费别名称(为空的数量/准确性）": "753", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:47.601305", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "32601", "医保项目名称(为空的数量/准确性）": "32601", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "32601", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "32601", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "32601", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "32601", "hosno": "12350100488099816X", "cnt": "32601", "项目等级代码(为空的数量/准确性）": "32601", "项目等级名称(为空的数量/准确性）": "32601", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "753", "结算号(为空的数量/准确性）": "751", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "4845691", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4845691", "数据改造厂商名称(为空的数量/准确性）": "4845691", "自付金额(为空的数量/准确性）": "4845691", "医保发票类别代码(为空的数量/准确性）": "4845691", "医保项目代码(为空的数量/准确性）": "4845691", "与住院结算主表（关联不上的记录数）": "538925", "业务最大时间": "2025-06-15 22:50:01.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "4845691", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市中医院", "病案首页费用类别名称(为空的数量/准确性）": "4845691", "业务最小时间": "2024-07-23 11:57:07.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "4845691", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "561927", "费别名称(为空的数量/准确性）": "561927", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:55.308877", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "4845691", "医保项目名称(为空的数量/准确性）": "4845691", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "4845691", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "4845691", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "4845691", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "4845691", "hosno": "123501004880997520", "cnt": "4845691", "项目等级代码(为空的数量/准确性）": "4845691", "项目等级名称(为空的数量/准确性）": "4845691", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "561927", "结算号(为空的数量/准确性）": "561927", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "2276183", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2276183", "数据改造厂商名称(为空的数量/准确性）": "2276183", "自付金额(为空的数量/准确性）": "2276183", "医保发票类别代码(为空的数量/准确性）": "2276183", "医保项目代码(为空的数量/准确性）": "2276183", "与住院结算主表（关联不上的记录数）": "0", "业务最大时间": "2025-06-15 14:55:44.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "2276183", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福建省福州儿童医院", "病案首页费用类别名称(为空的数量/准确性）": "2276183", "业务最小时间": "2024-11-20 18:56:56.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "2276183", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:48.732701", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2276183", "医保项目名称(为空的数量/准确性）": "2276183", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "2276183", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "2276183", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "2276183", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "2276183", "hosno": "12350100488099779P", "cnt": "2276183", "项目等级代码(为空的数量/准确性）": "2276183", "项目等级名称(为空的数量/准确性）": "2276183", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "6373295", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "6373295", "数据改造厂商名称(为空的数量/准确性）": "6373295", "自付金额(为空的数量/准确性）": "6373295", "医保发票类别代码(为空的数量/准确性）": "6373295", "医保项目代码(为空的数量/准确性）": "6373295", "与住院结算主表（关联不上的记录数）": "698676", "业务最大时间": "2025-06-15 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "6373295", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "病案首页费用类别名称(为空的数量/准确性）": "6373295", "业务最小时间": "2024-01-11 00:00:00.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "6235896", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "6373295", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "698676", "费别名称(为空的数量/准确性）": "698676", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:06:35.476700", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "6373295", "医保项目名称(为空的数量/准确性）": "6373295", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "6373295", "计量单位(为空的数量/准确性）": "5", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "6373295", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "6373295", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "6373295", "hosno": "12350100488099701P", "cnt": "6373295", "项目等级代码(为空的数量/准确性）": "6373295", "项目等级名称(为空的数量/准确性）": "6373295", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "5607240", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "698676", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "8655938", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "8655938", "数据改造厂商名称(为空的数量/准确性）": "8655938", "自付金额(为空的数量/准确性）": "8655938", "医保发票类别代码(为空的数量/准确性）": "8655938", "医保项目代码(为空的数量/准确性）": "8655938", "与住院结算主表（关联不上的记录数）": "423083", "业务最大时间": "2025-06-15 16:03:19.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "8655938", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "病案首页费用类别名称(为空的数量/准确性）": "8655938", "业务最小时间": "2024-10-14 10:46:16.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "8655938", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "480298", "费别名称(为空的数量/准确性）": "480298", "申请科室名称(为空的数量/准确性）": "14312", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:39.821242", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "8655938", "医保项目名称(为空的数量/准确性）": "8655938", "数量(为空的数量/准确性）": "14312", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "8655938", "计量单位(为空的数量/准确性）": "29634", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "8655938", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "8655938", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "8655938", "hosno": "123501004880997445", "cnt": "8655938", "项目等级代码(为空的数量/准确性）": "8655938", "项目等级名称(为空的数量/准确性）": "8655938", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "480298", "结算号(为空的数量/准确性）": "480298", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "8459630", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "8459630", "数据改造厂商名称(为空的数量/准确性）": "8459630", "自付金额(为空的数量/准确性）": "8459630", "医保发票类别代码(为空的数量/准确性）": "8459630", "医保项目代码(为空的数量/准确性）": "8459630", "与住院结算主表（关联不上的记录数）": "1455058", "业务最大时间": "2025-06-08 14:51:08.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "8459630", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "病案首页费用类别名称(为空的数量/准确性）": "8459630", "业务最小时间": "2024-05-23 16:59:26.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "8459630", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1503780", "费别名称(为空的数量/准确性）": "1503780", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:40.421097", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "8459630", "医保项目名称(为空的数量/准确性）": "8459630", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "8459630", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "8459630", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "8459630", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "8459630", "hosno": "1235010048809971XF", "cnt": "8459630", "项目等级代码(为空的数量/准确性）": "8459630", "项目等级名称(为空的数量/准确性）": "8459630", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "3", "结算时间(为空的数量/准确性）": "1503780", "结算号(为空的数量/准确性）": "732985", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "2869806", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2869806", "数据改造厂商名称(为空的数量/准确性）": "2869806", "自付金额(为空的数量/准确性）": "2869806", "医保发票类别代码(为空的数量/准确性）": "2869806", "医保项目代码(为空的数量/准确性）": "2869806", "与住院结算主表（关联不上的记录数）": "99696", "业务最大时间": "2025-06-08 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "2869806", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州结核病防治院", "病案首页费用类别名称(为空的数量/准确性）": "2869806", "业务最小时间": "2024-10-25 00:00:00.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "2803878", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "2869806", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "99696", "费别名称(为空的数量/准确性）": "99696", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:26.494996", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2869806", "医保项目名称(为空的数量/准确性）": "2869806", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "2869806", "计量单位(为空的数量/准确性）": "1674", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "2869806", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "2869806", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "2869806", "hosno": "12350100488099728F", "cnt": "2869806", "项目等级代码(为空的数量/准确性）": "2869806", "项目等级名称(为空的数量/准确性）": "2869806", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "2255203", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "99696", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "2324691", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2324691", "数据改造厂商名称(为空的数量/准确性）": "2324691", "自付金额(为空的数量/准确性）": "2324691", "医保发票类别代码(为空的数量/准确性）": "2324691", "医保项目代码(为空的数量/准确性）": "2324691", "与住院结算主表（关联不上的记录数）": "1188952", "业务最大时间": "2025-06-15 09:30:31.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "2324691", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市神经精神病防治院", "病案首页费用类别名称(为空的数量/准确性）": "2324691", "业务最小时间": "2024-07-01 11:57:56.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "2324691", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1189278", "费别名称(为空的数量/准确性）": "1189278", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:31.484738", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2324691", "医保项目名称(为空的数量/准确性）": "2324691", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "2324691", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "2324691", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "2324691", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "2324691", "hosno": "12350100488099736A", "cnt": "2324691", "项目等级代码(为空的数量/准确性）": "2324691", "项目等级名称(为空的数量/准确性）": "2324691", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "1189278", "结算号(为空的数量/准确性）": "7786", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "673705", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "673705", "数据改造厂商名称(为空的数量/准确性）": "673705", "自付金额(为空的数量/准确性）": "673705", "医保发票类别代码(为空的数量/准确性）": "673705", "医保项目代码(为空的数量/准确性）": "673705", "与住院结算主表（关联不上的记录数）": "200922", "业务最大时间": "2025-06-14 14:31:10.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "673705", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "病案首页费用类别名称(为空的数量/准确性）": "673705", "业务最小时间": "2024-12-19 16:19:30.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "673705", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "202313", "费别名称(为空的数量/准确性）": "202313", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:46.747675", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "673705", "医保项目名称(为空的数量/准确性）": "673705", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "673705", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "673705", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "673705", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "673705", "hosno": "12350100488099840E", "cnt": "673705", "项目等级代码(为空的数量/准确性）": "673705", "项目等级名称(为空的数量/准确性）": "673705", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "202313", "结算号(为空的数量/准确性）": "199145", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "2655209", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2655209", "数据改造厂商名称(为空的数量/准确性）": "2655209", "自付金额(为空的数量/准确性）": "2655209", "医保发票类别代码(为空的数量/准确性）": "2655209", "医保项目代码(为空的数量/准确性）": "2655209", "与住院结算主表（关联不上的记录数）": "319417", "业务最大时间": "2025-06-15 20:39:16.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "2655209", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "长乐区人民医院", "病案首页费用类别名称(为空的数量/准确性）": "2655209", "业务最小时间": "2024-01-03 10:01:36.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "2655209", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "319417", "费别名称(为空的数量/准确性）": "319417", "申请科室名称(为空的数量/准确性）": "591312", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:21.123095", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2655209", "医保项目名称(为空的数量/准确性）": "2655209", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "2655209", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "2655209", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "2655209", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "2655209", "hosno": "123501824884815728", "cnt": "2655209", "项目等级代码(为空的数量/准确性）": "2655209", "项目等级名称(为空的数量/准确性）": "2655209", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "811446", "结算时间(为空的数量/准确性）": "319417", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "5604679", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "5604679", "数据改造厂商名称(为空的数量/准确性）": "5604679", "自付金额(为空的数量/准确性）": "5604679", "医保发票类别代码(为空的数量/准确性）": "5604679", "医保项目代码(为空的数量/准确性）": "5604679", "与住院结算主表（关联不上的记录数）": "658151", "业务最大时间": "2025-06-09 15:15:25.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "5604679", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "病案首页费用类别名称(为空的数量/准确性）": "5604679", "业务最小时间": "2025-01-01 00:00:23.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "5604679", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "658151", "费别名称(为空的数量/准确性）": "658151", "申请科室名称(为空的数量/准确性）": "271068", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:48.801210", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "5604679", "医保项目名称(为空的数量/准确性）": "5604679", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "5604679", "计量单位(为空的数量/准确性）": "2", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "5604679", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "5604679", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "5604679", "hosno": "1235018148854625X2", "cnt": "5604679", "项目等级代码(为空的数量/准确性）": "5604679", "项目等级名称(为空的数量/准确性）": "5604679", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "519018", "结算时间(为空的数量/准确性）": "658151", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "685277", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "685277", "数据改造厂商名称(为空的数量/准确性）": "685277", "自付金额(为空的数量/准确性）": "685277", "医保发票类别代码(为空的数量/准确性）": "685277", "医保项目代码(为空的数量/准确性）": "685277", "与住院结算主表（关联不上的记录数）": "185557", "业务最大时间": "2025-06-15 23:35:59.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "685277", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "福清市妇幼保健院", "病案首页费用类别名称(为空的数量/准确性）": "685277", "业务最小时间": "2025-01-01 00:57:56.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "685277", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "175246", "费别名称(为空的数量/准确性）": "175246", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:53.648783", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "685277", "医保项目名称(为空的数量/准确性）": "685277", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "685277", "计量单位(为空的数量/准确性）": "5542", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "685277", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "685277", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "685277", "hosno": "12350181488546276W", "cnt": "685277", "项目等级代码(为空的数量/准确性）": "685277", "项目等级名称(为空的数量/准确性）": "685277", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "41", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "41", "结算时间(为空的数量/准确性）": "175246", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "0", "医保项目代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "业务最大时间": "", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "0", "hosname": "", "病案首页费用类别名称(为空的数量/准确性）": "0", "业务最小时间": "", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:19:55.399593", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "医保项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "0", "住院科室代码(为空的数量/准确性）": "0", "医保发票类别名称(为空的数量/准确性）": "0", "hosno": "12350200F36916562L", "cnt": "0", "项目等级代码(为空的数量/准确性）": "0", "项目等级名称(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "11609", "医保项目代码(为空的数量/准确性）": "11609", "与住院结算主表（关联不上的记录数）": "14", "业务最大时间": "2025-06-27 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "住院科室名称(为空的数量/准确性）": "11609", "hosname": "厦门大学附属翔安医院", "病案首页费用类别名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-02 00:00:00.000000", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:21.840238", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "医保项目名称(为空的数量/准确性）": "11609", "数量(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "0", "住院科室代码(为空的数量/准确性）": "11609", "医保发票类别名称(为空的数量/准确性）": "11609", "hosno": "12350200MB010327XM", "cnt": "11609", "项目等级代码(为空的数量/准确性）": "11609", "项目等级名称(为空的数量/准确性）": "11609", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "92866431", "医保项目代码(为空的数量/准确性）": "92866431", "与住院结算主表（关联不上的记录数）": "75999732", "业务最大时间": "2025-07-02 19:08:58.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "107712", "数据连续性(数据量/月)": "24", "住院科室名称(为空的数量/准确性）": "107712", "hosname": "福鼎市医院", "病案首页费用类别名称(为空的数量/准确性）": "107712", "业务最小时间": "2023-06-03 02:56:26.000000", "发票类别名称(为空的数量/准确性）": "107712", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "92866431", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "107712", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:23:55.434414", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "69917624", "医保项目名称(为空的数量/准确性）": "92866431", "数量(为空的数量/准确性）": "107712", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "92866431", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "48689499", "单价(为空的数量/准确性）": "107712", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "92866431", "住院科室代码(为空的数量/准确性）": "107712", "医保发票类别名称(为空的数量/准确性）": "92866431", "hosno": "12352203490467403B", "cnt": "92866431", "项目等级代码(为空的数量/准确性）": "92866431", "项目等级名称(为空的数量/准确性）": "92866431", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "107712", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "59174266", "结算号(为空的数量/准确性）": "946229", "医嘱号(为空的数量/准确性）": "16869816"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "463732", "数据改造厂商名称(为空的数量/准确性）": "463732", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "463732", "医保项目代码(为空的数量/准确性）": "26334", "与住院结算主表（关联不上的记录数）": "446913", "业务最大时间": "2025-06-24 23:56:39.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "463732", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "1273", "hosname": "宁德师范学院附属宁德市医院", "病案首页费用类别名称(为空的数量/准确性）": "463732", "业务最小时间": "2025-06-17 00:07:12.000000", "发票类别名称(为空的数量/准确性）": "1273", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "389358", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "项目代码(为空的数量/准确性）": "1273", "dqtime": "2025-07-03 20:25:48.680969", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "370566", "医保项目名称(为空的数量/准确性）": "26334", "数量(为空的数量/准确性）": "1273", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "337393", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "337393", "单价(为空的数量/准确性）": "1273", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "463732", "住院科室代码(为空的数量/准确性）": "1273", "医保发票类别名称(为空的数量/准确性）": "463732", "hosno": "12352200490348279K", "cnt": "463732", "项目等级代码(为空的数量/准确性）": "463732", "项目等级名称(为空的数量/准确性）": "463732", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "463732", "医疗收费项目类别代码(为空的数量/准确性）": "463732", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "451295", "结算号(为空的数量/准确性）": "327359", "医嘱号(为空的数量/准确性）": "143652"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "47129000", "数据改造厂商名称(为空的数量/准确性）": "47129000", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "47129000", "医保项目代码(为空的数量/准确性）": "45340936", "与住院结算主表（关联不上的记录数）": "6498017", "业务最大时间": "2024-06-14 08:53:09.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "47129000", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "58119", "hosname": "厦门市仙岳医院", "病案首页费用类别名称(为空的数量/准确性）": "47129000", "业务最小时间": "2020-01-01 00:18:22.000000", "发票类别名称(为空的数量/准确性）": "57650", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "45290033", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "724", "项目代码(为空的数量/准确性）": "57650", "dqtime": "2025-07-03 20:20:45.942877", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "34579234", "医保项目名称(为空的数量/准确性）": "47129000", "数量(为空的数量/准确性）": "57650", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "34405701", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "34405703", "单价(为空的数量/准确性）": "57650", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "47129000", "住院科室代码(为空的数量/准确性）": "79871", "医保发票类别名称(为空的数量/准确性）": "47129000", "hosno": "123502004266007248", "cnt": "47129000", "项目等级代码(为空的数量/准确性）": "47129000", "项目等级名称(为空的数量/准确性）": "47129000", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "47129000", "医疗收费项目类别代码(为空的数量/准确性）": "47129000", "执行科室名称(为空的数量/准确性）": "2432869", "结算时间(为空的数量/准确性）": "6499975", "结算号(为空的数量/准确性）": "4545", "医嘱号(为空的数量/准确性）": "34405701"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "1121978", "医保项目代码(为空的数量/准确性）": "1121978", "与住院结算主表（关联不上的记录数）": "1110984", "业务最大时间": "2025-07-02 23:41:08.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "1510", "数据连续性(数据量/月)": "3", "住院科室名称(为空的数量/准确性）": "1510", "hosname": "福鼎市中医院", "病案首页费用类别名称(为空的数量/准确性）": "1510", "业务最小时间": "2025-03-01 08:20:08.000000", "发票类别名称(为空的数量/准确性）": "1510", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "1121978", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "1510", "项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:48.156568", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "849792", "医保项目名称(为空的数量/准确性）": "1121978", "数量(为空的数量/准确性）": "1510", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "1121978", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "583755", "单价(为空的数量/准确性）": "1510", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "1121978", "住院科室代码(为空的数量/准确性）": "1510", "医保发票类别名称(为空的数量/准确性）": "1121978", "hosno": "123522034904674116", "cnt": "1121978", "项目等级代码(为空的数量/准确性）": "1121978", "项目等级名称(为空的数量/准确性）": "1121978", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1510", "医疗收费项目类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "666921", "结算号(为空的数量/准确性）": "53380", "医嘱号(为空的数量/准确性）": "173354"}, {"系统建设厂商代码(为空的数量/准确性）": "0", "扣费方式名称(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "115631", "数据改造厂商名称(为空的数量/准确性）": "115631", "自付金额(为空的数量/准确性）": "0", "医保发票类别代码(为空的数量/准确性）": "339628", "医保项目代码(为空的数量/准确性）": "168871", "与住院结算主表（关联不上的记录数）": "325824", "业务最大时间": "2025-06-28 23:54:10.000000", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "病案首页费用类别代码(为空的数量/准确性）": "339628", "数据连续性(数据量/月)": "0", "住院科室名称(为空的数量/准确性）": "836", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "病案首页费用类别名称(为空的数量/准确性）": "339628", "业务最小时间": "2025-06-16 00:01:10.000000", "发票类别名称(为空的数量/准确性）": "652", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医保支付金额(为空的数量/准确性）": "319154", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "2147", "项目代码(为空的数量/准确性）": "652", "dqtime": "2025-07-03 20:21:20.047097", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "251982", "医保项目名称(为空的数量/准确性）": "168871", "数量(为空的数量/准确性）": "652", "系统建设厂商名称(为空的数量/准确性）": "0", "扣费方式代码(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "251900", "医疗机构名称(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "251850", "单价(为空的数量/准确性）": "652", "费用(为空的数量/准确性）": "0", "自付比例(为空的数量/准确性）": "339628", "住院科室代码(为空的数量/准确性）": "836", "医保发票类别名称(为空的数量/准确性）": "339628", "hosno": "12350200302999454G", "cnt": "339628", "项目等级代码(为空的数量/准确性）": "339628", "项目等级名称(为空的数量/准确性）": "339628", "数据唯一记录号(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "339628", "医疗收费项目类别代码(为空的数量/准确性）": "339628", "执行科室名称(为空的数量/准确性）": "36110", "结算时间(为空的数量/准确性）": "329344", "结算号(为空的数量/准确性）": "146915", "医嘱号(为空的数量/准确性）": "251900"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:41:18", "endTime": "2025-07-03 20:41:18", "duration": 322}, "resultCode": 200, "message": null, "logStack": null}