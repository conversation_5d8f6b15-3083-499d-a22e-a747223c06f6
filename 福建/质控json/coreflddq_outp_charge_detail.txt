{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "扣费时间(为空的数量/准确性）", "费用明细号(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "医疗收费项目类别代码(为空的数量/准确性）", "医疗收费项目类别名称(为空的数量/准确性）", "项目代码(为空的数量/准确性）", "项目名称(为空的数量/准确性）", "医嘱号(为空的数量/准确性）", "项目规格(为空的数量/准确性）", "计量单位(为空的数量/准确性）", "单价(为空的数量/准确性）", "数量(为空的数量/准确性）", "药品批次(为空的数量/准确性）", "费用(为空的数量/准确性）", "自付金额(为空的数量/准确性）", "费别代码(为空的数量/准确性）", "费别名称(为空的数量/准确性）", "发票类别代码(为空的数量/准确性）", "发票类别名称(为空的数量/准确性）", "申请科室代码(为空的数量/准确性）", "申请科室名称(为空的数量/准确性）", "结算号(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与门急诊就诊记录表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:49.609413", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12352200490349810T", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "1496192", "dqtime": "2025-07-03 20:20:47.991892", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "13883899", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "1496192", "数据删除状态(为空的数量/准确性）": "13883899", "数据改造厂商名称(为空的数量/准确性）": "13883899", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "13883899", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "9259382", "单价(为空的数量/准确性）": "1496192", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-07-03 13:09:17.000000", "hosno": "123502004266007248", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "13883899", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "2233226", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:46:14.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1496192", "发票类别名称(为空的数量/准确性）": "1496192", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "13883899", "结算号(为空的数量/准确性）": "420434", "申请科室名称(为空的数量/准确性）": "178"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:23:58.126919", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "23268488", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "2208642", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "26491348", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "18420193", "单价(为空的数量/准确性）": "2208642", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 19:08:37.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "26491348", "数据连续性(数据量/月)": "24", "与门急诊就诊记录表（关联不上的记录数）": "4918011", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:22:33.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "2208676", "发票类别名称(为空的数量/准确性）": "2208676", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "34", "发票类别代码(为空的数量/准确性）": "34", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "9034107", "结算号(为空的数量/准确性）": "4906426", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "1473902", "dqtime": "2025-07-03 20:21:08.218224", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "13207858", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "1473902", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "4339964", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "13207858", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "10018128", "单价(为空的数量/准确性）": "1473902", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:58:39.000000", "hosno": "12350200302999454G", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "13207858", "数据连续性(数据量/月)": "14", "与门急诊就诊记录表（关联不上的记录数）": "2005789", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:00:47.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1473902", "发票类别名称(为空的数量/准确性）": "1473902", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "13207858", "结算号(为空的数量/准确性）": "501844", "申请科室名称(为空的数量/准确性）": "474"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:48.370404", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1090742", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "187108", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "1276979", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "649408", "单价(为空的数量/准确性）": "187108", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 21:08:56.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1276979", "数据连续性(数据量/月)": "3", "与门急诊就诊记录表（关联不上的记录数）": "1069005", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-03-01 08:26:28.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "187108", "发票类别名称(为空的数量/准确性）": "187108", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "425543", "结算号(为空的数量/准确性）": "119061", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:21.772435", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1771187", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "1149401", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:59:58.000000", "hosno": "12350200MB010327XM", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1771187", "数据连续性(数据量/月)": "5", "与门急诊就诊记录表（关联不上的记录数）": "47902", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:33:22.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "24644", "dqtime": "2025-07-03 20:25:48.696902", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "445773", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "24644", "数据删除状态(为空的数量/准确性）": "445773", "数据改造厂商名称(为空的数量/准确性）": "445773", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "445773", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "324287", "单价(为空的数量/准确性）": "24644", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 23:59:56.000000", "hosno": "12352200490348279K", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "445773", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "155545", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-17 00:00:02.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "24644", "发票类别名称(为空的数量/准确性）": "24644", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "445773", "结算号(为空的数量/准确性）": "110905", "申请科室名称(为空的数量/准确性）": "2"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:39.482750", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "18431", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "11921", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-05-27 17:43:45.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "18431", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "240", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-05-20 00:06:42.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "18431", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:44:56.903485", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "100", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "100", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "100", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-01-01 00:00:00.000000", "hosno": "h489", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "100", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "100", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:45:56.403061", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "15874", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "954", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "15874", "项目规格(为空的数量/准确性）": "7800", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-19 08:21:20.000000", "hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "15874", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-14 00:01:41.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:55.463376", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "508923", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "48", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "508923", "项目规格(为空的数量/准确性）": "145787", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2024-12-31 23:55:27.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "508923", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "508923", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-01 00:00:29.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "142043", "dqtime": "2025-07-03 19:47:40.263026", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "4779729", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "142043", "数据删除状态(为空的数量/准确性）": "4779729", "数据改造厂商名称(为空的数量/准确性）": "4779729", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "4779729", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "1670850", "单价(为空的数量/准确性）": "142043", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 23:54:51.000000", "hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "4779729", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1282310", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:31.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "142043", "发票类别名称(为空的数量/准确性）": "142043", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "4779729", "结算号(为空的数量/准确性）": "857769", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "45430", "dqtime": "2025-07-03 19:48:26.032225", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "769904", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "45430", "数据删除状态(为空的数量/准确性）": "769904", "数据改造厂商名称(为空的数量/准确性）": "689585", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "769904", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "586610", "单价(为空的数量/准确性）": "45430", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:29:02.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "769904", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "290605", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:00:08.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "45430", "发票类别名称(为空的数量/准确性）": "45430", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "769904", "结算号(为空的数量/准确性）": "120849", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:48.740358", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "849124", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "849124", "数据改造厂商名称(为空的数量/准确性）": "849124", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "849124", "项目规格(为空的数量/准确性）": "849124", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 16:14:24.000000", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "849124", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:13:33.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "849124", "发票类别名称(为空的数量/准确性）": "849124", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "849124", "费别名称(为空的数量/准确性）": "849124", "医嘱号(为空的数量/准确性）": "849124", "结算号(为空的数量/准确性）": "54107", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:56.823349", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "4384032", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4384032", "数据改造厂商名称(为空的数量/准确性）": "4384032", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "4384032", "项目规格(为空的数量/准确性）": "4384032", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 00:11:14.000000", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "4384032", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:27:02.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "4384032", "发票类别名称(为空的数量/准确性）": "4384032", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "4384032", "费别名称(为空的数量/准确性）": "4384032", "医嘱号(为空的数量/准确性）": "4384032", "结算号(为空的数量/准确性）": "56627", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:46.113461", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2108156", "项目名称(为空的数量/准确性）": "4", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2108156", "数据改造厂商名称(为空的数量/准确性）": "2108156", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "607958", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "2108156", "项目规格(为空的数量/准确性）": "2108156", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-09 02:04:35.000000", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "2108156", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:07:04.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "2108156", "发票类别名称(为空的数量/准确性）": "2108156", "申请科室代码(为空的数量/准确性）": "1", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "2108156", "费别名称(为空的数量/准确性）": "2108156", "医嘱号(为空的数量/准确性）": "2108156", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "1"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:06:41.162093", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "2415225", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2415225", "数据改造厂商名称(为空的数量/准确性）": "2415225", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "2415225", "项目规格(为空的数量/准确性）": "2415225", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 00:00:00.000000", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "2415225", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "245", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-02-18 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "2415225", "发票类别名称(为空的数量/准确性）": "2415225", "申请科室代码(为空的数量/准确性）": "2415110", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "2415225", "费别名称(为空的数量/准确性）": "2415225", "医嘱号(为空的数量/准确性）": "2415225", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:41.587770", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1619469", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "198328", "数据删除状态(为空的数量/准确性）": "1619469", "数据改造厂商名称(为空的数量/准确性）": "1619469", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "198471", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "1619469", "项目规格(为空的数量/准确性）": "1619469", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 23:13:34.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1619469", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 03:37:10.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1619469", "发票类别名称(为空的数量/准确性）": "1619469", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1619469", "费别名称(为空的数量/准确性）": "1619469", "医嘱号(为空的数量/准确性）": "1619469", "结算号(为空的数量/准确性）": "68279", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:35.438667", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "3572862", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3572862", "数据改造厂商名称(为空的数量/准确性）": "3572862", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "3572862", "项目规格(为空的数量/准确性）": "3572862", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 23:00:34.000000", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "3572862", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-31 00:03:08.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "3572862", "发票类别名称(为空的数量/准确性）": "3572862", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "3572862", "费别名称(为空的数量/准确性）": "3572862", "医嘱号(为空的数量/准确性）": "3572862", "结算号(为空的数量/准确性）": "304242", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:18.297300", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "940319", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "940319", "数据改造厂商名称(为空的数量/准确性）": "940319", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "1579", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "940319", "项目规格(为空的数量/准确性）": "940319", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 00:00:00.000000", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "940319", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "247", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2019-05-22 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "940319", "发票类别名称(为空的数量/准确性）": "940319", "申请科室代码(为空的数量/准确性）": "286828", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "940319", "费别名称(为空的数量/准确性）": "940319", "医嘱号(为空的数量/准确性）": "940319", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:26.288886", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "694344", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "694344", "数据改造厂商名称(为空的数量/准确性）": "694344", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "694344", "项目规格(为空的数量/准确性）": "694344", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 17:01:11.000000", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "694344", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 04:27:50.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "694344", "发票类别名称(为空的数量/准确性）": "694344", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "694344", "费别名称(为空的数量/准确性）": "694344", "医嘱号(为空的数量/准确性）": "694344", "结算号(为空的数量/准确性）": "27684", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:50.043778", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1115383", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1115383", "数据改造厂商名称(为空的数量/准确性）": "1115383", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "1115383", "项目规格(为空的数量/准确性）": "1115383", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 20:10:18.000000", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1115383", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-31 00:00:37.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1115383", "发票类别名称(为空的数量/准确性）": "1115383", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1115383", "费别名称(为空的数量/准确性）": "1115383", "医嘱号(为空的数量/准确性）": "1115383", "结算号(为空的数量/准确性）": "114673", "申请科室名称(为空的数量/准确性）": "0"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:16.001701", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1822607", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1822607", "数据改造厂商名称(为空的数量/准确性）": "1822607", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "1822607", "项目规格(为空的数量/准确性）": "1822607", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 00:18:44.000000", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1822607", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:02:47.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1822607", "发票类别名称(为空的数量/准确性）": "1822607", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1822607", "费别名称(为空的数量/准确性）": "1822607", "医嘱号(为空的数量/准确性）": "1822607", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "158121"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:48.216238", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "1649259", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1649259", "数据改造厂商名称(为空的数量/准确性）": "1649259", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "1", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "1649259", "项目规格(为空的数量/准确性）": "1649259", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 00:12:19.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "1649259", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:01:43.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "1649259", "发票类别名称(为空的数量/准确性）": "1649259", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "1649259", "费别名称(为空的数量/准确性）": "1649259", "医嘱号(为空的数量/准确性）": "1649259", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "32411"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:51.916905", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "693279", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "693279", "数据改造厂商名称(为空的数量/准确性）": "693279", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "561", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "693279", "项目规格(为空的数量/准确性）": "693279", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 23:59:35.000000", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "693279", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:02:30.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "693279", "发票类别名称(为空的数量/准确性）": "693279", "申请科室代码(为空的数量/准确性）": "36670", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "693279", "费别名称(为空的数量/准确性）": "693279", "医嘱号(为空的数量/准确性）": "693279", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "36670"}, {"项目代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:01.052176", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "药品批次(为空的数量/准确性）": "0", "项目名称(为空的数量/准确性）": "0", "数量(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "计量单位(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "自付金额(为空的数量/准确性）": "0", "项目规格(为空的数量/准确性）": "0", "单价(为空的数量/准确性）": "0", "费用(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350200F36916562L", "数据上传时间(为空的数量/准确性）": "0", "费用明细号(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医疗收费项目类别名称(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "扣费时间(为空的数量/准确性）": "0", "医疗收费项目类别代码(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "结算号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:35:27", "endTime": "2025-07-03 20:35:27", "duration": 260}, "resultCode": 200, "message": null, "logStack": null}