{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "报告时间(为空的数量/准确性）", "lis患者编号(为空的数量/准确性）", "lis就诊号(为空的数量/准确性）", "报告单号(为空的数量/准确性）", "细菌结果流水号(为空的数量/准确性）", "细菌代码(为空的数量/准确性）", "细菌名称(为空的数量/准确性）", "菌落计数(为空的数量/准确性）", "细菌结果(为空的数量/准确性）", "细菌结果说明(为空的数量/准确性）", "超级细菌标志(为空的数量/准确性）", "培养基(为空的数量/准确性）", "培养时间(为空的数量/准确性）", "培养条件(为空的数量/准确性）", "发现方式(为空的数量/准确性）", "设备类别代码(为空的数量/准确性）", "设备类别名称(为空的数量/准确性）", "有效标志(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "业务最大时间", "业务最小时间", "与检验报告主表（关联不上的记录数）", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:49.252279", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12352200490349810T", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:37.657917", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "62", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-05-20 15:56:17.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "62", "数据连续性(数据量/月)": "2", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-03-01 09:28:50.000000", "细菌代码(为空的数量/准确性）": "1", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "3", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "4566", "dqtime": "2025-07-03 19:45:54.827374", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "4566", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "4566", "细菌结果说明(为空的数量/准确性）": "4566", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "4186", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "4186", "业务最大时间": "2025-06-12 11:47:36.000000", "hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "4566", "数据连续性(数据量/月)": "5", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-03 09:25:42.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "4328", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "605", "培养时间(为空的数量/准确性）": "4566", "培养条件(为空的数量/准确性）": "4566", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:55.387588", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "159", "细菌结果说明(为空的数量/准确性）": "161", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-28 10:18:00.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "120649", "数据连续性(数据量/月)": "65", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-03 10:47:15.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "165", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "117992", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:47:39.549682", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "103", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "66", "细菌结果说明(为空的数量/准确性）": "66", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-25 16:10:51.000000", "hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "103", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 10:21:56.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "93", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "27", "培养时间(为空的数量/准确性）": "103", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:26.898924", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 11:43:37.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "9493", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 08:00:51.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "9014", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "2"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:47.313418", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:46.840363", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "175", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-29 11:06:56.000000", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "756", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-23 07:59:06.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "756", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:45.647296", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:06:35.032244", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-28 21:01:15.000000", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "370876", "数据连续性(数据量/月)": "53", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-01-04 08:43:56.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "185547", "细菌名称(为空的数量/准确性）": "2", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "370876", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:28.596721", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "1285", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-29 17:44:08.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "76253", "数据连续性(数据量/月)": "27", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-03-17 09:01:56.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "30", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "60504", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:29.292250", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:19.020003", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:25.540999", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:45.553500", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:10.639024", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "817189", "dqtime": "2025-07-03 20:15:42.050131", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "817189", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "816588", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "817189", "设备类别代码(为空的数量/准确性）": "817189", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "817189", "业务最大时间": "2025-06-28 09:27:02.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "817189", "数据连续性(数据量/月)": "65", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-03 09:00:24.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "816141", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "817189", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "817189", "报告时间(为空的数量/准确性）": "1212"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:40.171097", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:46.373119", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "0", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 10:10:13.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "12", "数据连续性(数据量/月)": "0", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 11:29:21.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:24:00.545221", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "细菌结果(为空的数量/准确性）": "570", "细菌结果说明(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 20:55:29.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "45645", "数据连续性(数据量/月)": "7", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-04 14:20:25.000000", "细菌代码(为空的数量/准确性）": "0", "菌落计数(为空的数量/准确性）": "0", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "0", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"发现方式(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:25:45.794227", "系统建设厂商代码(为空的数量/准确性）": "0", "培养基(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "23", "细菌结果(为空的数量/准确性）": "286946", "细菌结果说明(为空的数量/准确性）": "286946", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "超级细菌标志(为空的数量/准确性）": "0", "设备类别代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "设备类别名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 09:29:46.000000", "hosno": "12352200490348279K", "数据上传时间(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "954990", "数据连续性(数据量/月)": "65", "报告单号(为空的数量/准确性）": "0", "细菌结果流水号(为空的数量/准确性）": "0", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2014-10-02 08:53:00.000000", "细菌代码(为空的数量/准确性）": "15950", "菌落计数(为空的数量/准确性）": "652050", "细菌名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "与检验报告主表（关联不上的记录数）": "340487", "培养时间(为空的数量/准确性）": "0", "培养条件(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "768"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:41:48", "endTime": "2025-07-03 20:41:49", "duration": 208}, "resultCode": 200, "message": null, "logStack": null}