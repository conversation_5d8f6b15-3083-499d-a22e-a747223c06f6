{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "诊断时间(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "诊断流水号(为空的数量/准确性）", "疾病诊断代码(为空的数量/准确性）", "疾病诊断名称(为空的数量/准确性）", "主要诊断标志(为空的数量/准确性）", "诊断排序号(为空的数量/准确性）", "诊断类型代码(为空的数量/准确性）", "诊断类型名称(为空的数量/准确性）", "诊断科室代码(为空的数量/准确性）", "诊断科室名称(为空的数量/准确性）", "诊断方法代码(为空的数量/准确性）", "诊断方法名称(为空的数量/准确性）", "有效标志(为空的数量/准确性）", "传染病标志(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与住院患者出院记录表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:35.593536", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "734", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2022-05-20 21:52:45.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "734", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2022-05-20 07:39:33.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "653", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "734"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:44:56.764330", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "89", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-10 13:04:53.000000", "hosno": "h489", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "100", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "3", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 12:21:29.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:45:54.798341", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "2884", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "1", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 14:34:11.000000", "hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "208538", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "67", "诊断类型名称(为空的数量/准确性）": "1", "hosname": "漳平市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2019-05-06 15:28:33.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:53.512427", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "3535", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-27 10:10:24.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "1425180", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "54", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-12-20 10:29:43.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:47:37.455247", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "18938", "与住院患者出院记录表（关联不上的记录数）": "779", "数据改造厂商名称(为空的数量/准确性）": "18938", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "4", "疾病诊断名称(为空的数量/准确性）": "10", "诊断方法名称(为空的数量/准确性）": "9395", "业务最大时间": "2025-06-24 21:58:55.000000", "hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "9395", "有效标志(为空的数量/准确性）": "0", "cnt": "18938", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 08:47:56.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "18938"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:26.443792", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "13621", "与住院患者出院记录表（关联不上的记录数）": "3219", "数据改造厂商名称(为空的数量/准确性）": "13621", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "9140", "业务最大时间": "2025-06-26 14:27:17.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "9140", "有效标志(为空的数量/准确性）": "0", "cnt": "13621", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:05:46.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "13621"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:48.659024", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "269", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "269", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "269", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "269", "业务最大时间": "2025-06-13 18:33:29.000000", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "269", "有效标志(为空的数量/准确性）": "269", "cnt": "269", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-06 18:15:50.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "269", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "269"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:54.475599", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "82139", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "82139", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "82139", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "82139", "业务最大时间": "2025-06-15 22:44:42.000000", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "82139", "有效标志(为空的数量/准确性）": "82139", "cnt": "82139", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "1998-01-01 00:14:42.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "82139", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "82105"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:53.719035", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "11289", "与住院患者出院记录表（关联不上的记录数）": "162", "数据改造厂商名称(为空的数量/准确性）": "11289", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "11289", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "11289", "业务最大时间": "2025-06-15 15:34:23.000000", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "11289", "有效标志(为空的数量/准确性）": "11289", "cnt": "11289", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 02:22:15.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "11289", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "11289"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:06:49.421195", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "102946", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "102946", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "102946", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "102946", "业务最大时间": "2025-06-20 00:00:00.000000", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "102946", "有效标志(为空的数量/准确性）": "102946", "cnt": "102946", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2018-06-04 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "102946", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "3125"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:28.312834", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "114519", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "114519", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "114519", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "114519", "业务最大时间": "2025-06-14 10:31:05.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "114519", "有效标志(为空的数量/准确性）": "114519", "cnt": "114519", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-10-14 10:43:50.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "114519", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "114229"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:31.688826", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "45243", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "45243", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "45243", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "45243", "业务最大时间": "2025-06-14 22:22:28.000000", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "45243", "有效标志(为空的数量/准确性）": "45243", "cnt": "45243", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-31 08:11:12.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "45243", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "45243"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:20.953476", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "68008", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "68008", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "68008", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "68008", "业务最大时间": "2025-06-15 00:00:00.000000", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "68008", "有效标志(为空的数量/准确性）": "68008", "cnt": "68008", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-10-25 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "68008", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "16330"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:24.541061", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4271", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "4271", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "4271", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "4271", "业务最大时间": "2025-06-15 09:45:49.000000", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "4271", "有效标志(为空的数量/准确性）": "4271", "cnt": "4271", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 10:22:04.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "4271", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "4271"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:54.712953", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "5292", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "5292", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "5292", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "5292", "业务最大时间": "2025-06-14 14:31:41.000000", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "5292", "有效标志(为空的数量/准确性）": "5292", "cnt": "5292", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-31 08:52:17.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "5292", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "5292"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:25.118057", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "114946", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "114946", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "114946", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "114946", "业务最大时间": "2025-06-18 10:58:06.000000", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "114946", "有效标志(为空的数量/准确性）": "114946", "cnt": "114946", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-10-24 14:52:56.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "114946", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "207"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:40.232877", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "201623", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "201623", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "201623", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "201623", "业务最大时间": "2025-06-13 17:39:07.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "201623", "有效标志(为空的数量/准确性）": "201623", "cnt": "201623", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2017-02-16 12:22:41.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "201623", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "1123"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:46.588983", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "10779", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "10779", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "10779", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "10779", "业务最大时间": "2025-06-17 10:50:00.000000", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "10779", "有效标志(为空的数量/准确性）": "10779", "cnt": "10779", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-18 15:08:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "10779", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "620"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:19:57.167034", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350200F36916562L", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:23.392644", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "336", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "464", "业务最大时间": "2025-07-01 23:56:02.000000", "hosno": "12350200302999454G", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "464", "有效标志(为空的数量/准确性）": "0", "cnt": "464", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-25 23:11:08.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "464"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:42.356985", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "1", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 20:37:20.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "789", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "2", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-04-17 10:16:54.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "300"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:23:57.099989", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "8312", "疾病诊断名称(为空的数量/准确性）": "1738", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:14:43.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "856365", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "24", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:56:52.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "838789"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:25:43.759997", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "5430", "与住院患者出院记录表（关联不上的记录数）": "1808", "数据改造厂商名称(为空的数量/准确性）": "5430", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "3897", "业务最大时间": "2025-06-24 23:49:48.000000", "hosno": "12352200490348279K", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "3897", "有效标志(为空的数量/准确性）": "0", "cnt": "5430", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-17 00:09:13.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "5430"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:48.411766", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12352200490349810T", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "0", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:23.391102", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "3861", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:34:54.000000", "hosno": "12350200MB010327XM", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "63424", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 09:42:08.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}, {"诊断科室代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:44.868249", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "诊断科室名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "497233", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "诊断流水号(为空的数量/准确性）": "0", "诊断排序号(为空的数量/准确性）": "0", "疾病诊断代码(为空的数量/准确性）": "0", "疾病诊断名称(为空的数量/准确性）": "0", "诊断方法名称(为空的数量/准确性）": "0", "业务最大时间": "2025-07-03 10:47:22.000000", "hosno": "123502004266007248", "数据上传时间(为空的数量/准确性）": "0", "诊断方法代码(为空的数量/准确性）": "0", "有效标志(为空的数量/准确性）": "0", "cnt": "497233", "诊断类型代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "48", "诊断类型名称(为空的数量/准确性）": "0", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-06-29 11:25:20.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "主要诊断标志(为空的数量/准确性）": "0", "诊断时间(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:33:50", "endTime": "2025-07-03 20:33:50", "duration": 268}, "resultCode": 200, "message": null, "logStack": null}