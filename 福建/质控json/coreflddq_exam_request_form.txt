{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "申请时间(为空的数量/准确性）", "门诊/住院标志(为空的数量/准确性）", "门诊/住院标志名称(为空的数量/准确性）", "exam系统患者编号(为空的数量/准确性）", "exam系统就诊号(为空的数量/准确性）", "检查申请单号(为空的数量/准确性）", "his系统患者编号(为空的数量/准确性）", "his系统就诊号(为空的数量/准确性）", "患者姓名(为空的数量/准确性）", "性别代码(为空的数量/准确性）", "性别名称(为空的数量/准确性）", "出生日期(为空的数量/准确性）", "申请科室代码(为空的数量/准确性）", "申请科室名称(为空的数量/准确性）", "申请项目代码(为空的数量/准确性）", "申请项目名称(为空的数量/准确性）", "检查类型代码(为空的数量/准确性）", "检查类型名称(为空的数量/准确性）", "检查名称或目的(为空的数量/准确性）", "检查科室代码(为空的数量/准确性）", "检查科室名称(为空的数量/准确性）", "检查时间(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "业务最小时间", "业务最大时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:44.808002", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:15.277572", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "7276", "dqtime": "2025-07-03 19:39:37.820213", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "7276", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "1900-01-01 00:00:00.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "7276", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "3864", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "1585", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "5", "业务最小时间": "0001-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "7276", "检查名称或目的(为空的数量/准确性）": "7276", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "1"}, {"检查类型代码(为空的数量/准确性）": "271", "dqtime": "2025-07-03 19:46:58.147239", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "271", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2025-01-31 23:58:04.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "17404", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:21:07.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "245", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:27.366528", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:30:31.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "41239", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:02:07.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "621", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:48.542808", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:49.428481", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:45.516776", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:06:35.098590", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:36.036945", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-20 23:03:16.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "739434", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "27", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "4", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2023-03-12 09:27:40.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "125401", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "416632", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:29.296961", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:19.148033", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:46.896762", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:44.066214", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 17:10:17.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "566", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 07:12:15.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "195", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "566", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:24:06.788049", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "3", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:29:14.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "3", "检查申请单号(为空的数量/准确性）": "0", "cnt": "370155", "出生日期(为空的数量/准确性）": "3", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "7", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "18", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:37:29.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "455", "检查时间(为空的数量/准确性）": "101802", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "369130", "检查科室代码(为空的数量/准确性）": "1", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:23.789479", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"检查类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:50.043929", "系统建设厂商代码(为空的数量/准确性）": "0", "检查类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "exam系统就诊号(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "exam系统患者编号(为空的数量/准确性）": "4", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "业务最大时间": "2024-01-10 23:36:06.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "检查申请单号(为空的数量/准确性）": "0", "cnt": "920941", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "37", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2015-01-06 08:47:48.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检查科室名称(为空的数量/准确性）": "0", "检查时间(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "检查名称或目的(为空的数量/准确性）": "0", "检查科室代码(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:36:21", "endTime": "2025-07-03 20:36:22", "duration": 279}, "resultCode": 200, "message": null, "logStack": null}