{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "申请时间(为空的数量/准确性）", "门诊/住院标志(为空的数量/准确性）", "门诊/住院标志名称(为空的数量/准确性）", "lis患者编号(为空的数量/准确性）", "lis就诊号(为空的数量/准确性）", "检验申请单号(为空的数量/准确性）", "his系统患者编号(为空的数量/准确性）", "his系统就诊号(为空的数量/准确性）", "患者姓名(为空的数量/准确性）", "性别代码(为空的数量/准确性）", "性别名称(为空的数量/准确性）", "出生日期(为空的数量/准确性）", "申请科室代码(为空的数量/准确性）", "申请科室名称(为空的数量/准确性）", "申请项目代码(为空的数量/准确性）", "申请项目名称(为空的数量/准确性）", "申请状态代码(为空的数量/准确性）", "申请状态名称(为空的数量/准确性）", "标本类别代码(为空的数量/准确性）", "标本类别名称(为空的数量/准确性）", "紧急标志(为空的数量/准确性）", "传染病标志(为空的数量/准确性）", "标本代码(为空的数量/准确性）", "标本名称(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "业务最小时间", "业务最大时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "13", "dqtime": "2025-07-03 19:39:37.059761", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "13", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "13", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "13", "业务最大时间": "2025-05-20 23:21:29.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "3828", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "3", "数据连续性(数据量/月)": "5", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "3", "业务最小时间": "2025-01-21 15:16:36.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "140", "dqtime": "2025-07-03 19:45:55.094013", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "140", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "1", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "140", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "140", "业务最大时间": "2025-06-12 11:51:34.000000", "hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "166490", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2024-03-16 17:08:01.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "4", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "10108", "dqtime": "2025-07-03 19:46:56.448156", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "10098", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "10108", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "10098", "业务最大时间": "2025-01-31 23:58:04.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "62646", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:42.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "62646", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:47:37.609729", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-25 17:06:32.000000", "hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "12337", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "3", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2024-05-22 14:56:41.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "35935", "dqtime": "2025-07-03 19:48:29.487403", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "35935", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:30:31.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "63811", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-12 00:05:46.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:03:46.131137", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:04:53.990338", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 19:49:27.000000", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "7113", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-23 00:44:26.000000", "门诊/住院标志(为空的数量/准确性）": "7113", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:05:41.973489", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "262935", "dqtime": "2025-07-03 20:06:43.901398", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "210850", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "262935", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "16", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "210850", "业务最大时间": "2025-06-27 00:04:12.000000", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "5648026", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "53", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2021-01-01 00:14:39.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:07:31.113991", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 23:58:48.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "4985111", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "27", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2023-03-12 10:57:07.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:09:32.350323", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 21:56:05.000000", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "6147", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2024-12-25 17:01:27.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:10:16.175934", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:32.693038", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 20:25:57.000000", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "505", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福州市第四医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-05-27 14:34:37.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:14:45.458995", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-27 09:30:02.000000", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "2442", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-05-15 10:37:49.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:15:25.362772", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "39828", "dqtime": "2025-07-03 20:15:40.471677", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "11910043", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "39828", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "11910043", "业务最大时间": "2025-06-26 05:51:05.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "24707484", "出生日期(为空的数量/准确性）": "5062", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "65", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2015-01-08 17:41:14.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "24707484", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:18:44.140253", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:19:58.829426", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-05-15 17:24:31.000000", "hosno": "12350200F36916562L", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "1705", "cnt": "2130", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "厦门医学院附属口腔医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2020-09-01 12:34:14.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "4", "dqtime": "2025-07-03 20:20:24.179991", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "4", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "4", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "4", "业务最大时间": "2025-07-01 23:27:50.000000", "hosno": "12350200MB010327XM", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "163437", "出生日期(为空的数量/准确性）": "10", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 08:12:12.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:23:55.583768", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "25", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:50:10.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "25", "cnt": "983195", "出生日期(为空的数量/准确性）": "25", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "7", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "25", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:24:37.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:25.167162", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "92", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "92", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:36:40.000000", "hosno": "12350200302999454G", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "14419014", "出生日期(为空的数量/准确性）": "14419014", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "65", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:13:27.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "1", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "53995", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "53995"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:49.020574", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 17:57:44.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "1642", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 07:09:41.000000", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}, {"申请状态名称(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:42.333443", "系统建设厂商代码(为空的数量/准确性）": "0", "lis患者编号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "标本名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "申请状态代码(为空的数量/准确性）": "0", "标本代码(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123502004266007248", "数据上传时间(为空的数量/准确性）": "0", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "紧急标志(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "传染病标志(为空的数量/准确性）": "0", "检验申请单号(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:47:39", "endTime": "2025-07-03 20:47:39", "duration": 186}, "resultCode": 200, "message": null, "logStack": null}