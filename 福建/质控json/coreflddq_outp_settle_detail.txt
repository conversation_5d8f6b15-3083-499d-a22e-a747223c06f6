{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "结算时间(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "结算号(为空的数量/准确性）", "结算明细号(为空的数量/准确性）", "发票类别代码(为空的数量/准确性）", "发票类别名称(为空的数量/准确性）", "总费用(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与门诊结算主表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"hosno": "123502004266007248", "与门诊结算主表（关联不上的记录数）": "164", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1034970", "dqtime": "2025-07-03 20:20:42.948988", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1034970", "业务最小时间": "2020-01-01 08:14:24.000000", "数据改造厂商名称(为空的数量/准确性）": "1034970", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "8364", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "8407", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-03 10:50:42.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h486", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "18409", "dqtime": "2025-07-03 19:39:36.523761", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-05-20 00:06:42.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-05-27 17:43:45.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h489", "与门诊结算主表（关联不上的记录数）": "100", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "100", "dqtime": "2025-07-03 19:44:58.338861", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-01-01 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h505", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "6547", "dqtime": "2025-07-03 19:45:56.449166", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-06-14 00:18:36.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-19 15:28:03.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h487", "与门诊结算主表（关联不上的记录数）": "792962", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "793200", "cnt": "793200", "dqtime": "2025-07-03 19:46:55.321136", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "65", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:14:53.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 16:25:17.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h357", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "152161", "dqtime": "2025-07-03 19:47:38.421390", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "152161", "业务最小时间": "2025-01-01 00:34:46.000000", "数据改造厂商名称(为空的数量/准确性）": "152161", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "377", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "377", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:51:12.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h378", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "78562", "dqtime": "2025-07-03 19:48:25.795282", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "78562", "业务最小时间": "2025-06-09 00:05:19.000000", "数据改造厂商名称(为空的数量/准确性）": "60608", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "562", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "562", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:29:01.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099816X", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "794944", "dqtime": "2025-07-03 20:03:51.486122", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "794944", "业务最小时间": "2025-01-01 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "794944", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501004880997520", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "4327397", "dqtime": "2025-07-03 20:04:45.873884", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4327397", "业务最小时间": "2025-01-01 02:36:07.000000", "数据改造厂商名称(为空的数量/准确性）": "4327397", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 23:54:24.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099779P", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2108156", "dqtime": "2025-07-03 20:05:41.808874", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2108156", "业务最小时间": "2025-01-01 00:07:04.000000", "数据改造厂商名称(为空的数量/准确性）": "2108156", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "4", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-09 02:04:35.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099701P", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2235617", "dqtime": "2025-07-03 20:06:41.708768", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2235617", "业务最小时间": "2021-10-11 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "2235617", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501004880997445", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1551173", "dqtime": "2025-07-03 20:07:29.215356", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1551173", "业务最小时间": "2025-01-01 03:39:03.000000", "数据改造厂商名称(为空的数量/准确性）": "1551173", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 23:13:34.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "1235010048809971XF", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "3066257", "dqtime": "2025-07-03 20:09:30.261030", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3066257", "业务最小时间": "2024-12-31 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "3066257", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099728F", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "884643", "dqtime": "2025-07-03 20:10:20.697974", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "884643", "业务最小时间": "2021-06-26 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "884643", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099736A", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "666550", "dqtime": "2025-07-03 20:14:25.164084", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "666550", "业务最小时间": "2025-01-01 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "666550", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099840E", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "999918", "dqtime": "2025-07-03 20:14:44.959135", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "999918", "业务最小时间": "2024-12-31 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "999918", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501824884815728", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1154828", "dqtime": "2025-07-03 20:15:26.403323", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1154828", "业务最小时间": "2025-01-01 08:01:43.000000", "数据改造厂商名称(为空的数量/准确性）": "1154828", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 23:45:51.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "1235018148854625X2", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "993278", "dqtime": "2025-07-03 20:15:38.324672", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "993278", "业务最小时间": "2025-01-02 00:07:04.000000", "数据改造厂商名称(为空的数量/准确性）": "993278", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-14 23:39:58.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350181488546276W", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "290699", "dqtime": "2025-07-03 20:18:44.778077", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "290699", "业务最小时间": "2025-01-01 00:17:56.000000", "数据改造厂商名称(为空的数量/准确性）": "290699", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 23:56:22.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350200F36916562L", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "0", "dqtime": "2025-07-03 20:19:54.870161", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350200302999454G", "与门诊结算主表（关联不上的记录数）": "0", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "5505750", "dqtime": "2025-07-03 20:21:04.734607", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "65", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1966", "业务最小时间": "2020-01-01 00:01:10.000000", "数据改造厂商名称(为空的数量/准确性）": "603266", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "39810", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "39804", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:58:39.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350200MB010327XM", "与门诊结算主表（关联不上的记录数）": "91", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1788017", "dqtime": "2025-07-03 20:20:21.684561", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:33:22.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:59:58.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12352203490467403B", "与门诊结算主表（关联不上的记录数）": "23", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2307460", "dqtime": "2025-07-03 20:24:00.728053", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "14", "患者编号(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 02:07:18.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:53:47.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123522034904674116", "与门诊结算主表（关联不上的记录数）": "5", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "45080", "dqtime": "2025-07-03 20:21:40.709048", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "患者编号(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-05-01 08:56:17.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 22:32:35.000000", "结算号(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:54:58", "endTime": "2025-07-03 20:54:58", "duration": 384}, "resultCode": 200, "message": null, "logStack": null}