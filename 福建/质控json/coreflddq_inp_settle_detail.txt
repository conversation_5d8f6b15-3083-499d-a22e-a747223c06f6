{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "结算时间(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "结算号(为空的数量/准确性）", "结算明细号(为空的数量/准确性）", "发票类别代码(为空的数量/准确性）", "发票类别名称(为空的数量/准确性）", "总费用(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与住院结算主表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"hosno": "12350200302999454G", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "865", "dqtime": "2025-07-03 20:21:18.467101", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "408", "业务最小时间": "2025-06-16 08:36:49.000000", "数据改造厂商名称(为空的数量/准确性）": "408", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "15", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "15", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-01 19:21:41.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "467", "dqtime": "2025-07-03 20:21:47.202449", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "患者编号(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-05-01 08:55:48.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 15:20:20.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123502004266007248", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "160129", "dqtime": "2025-07-03 20:20:45.855113", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "160129", "业务最小时间": "2020-01-02 09:22:41.000000", "数据改造厂商名称(为空的数量/准确性）": "160129", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "76925", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "1606", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-03 10:34:43.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "176", "dqtime": "2025-07-03 19:39:35.523953", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "2", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-03-18 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "1", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-05-29 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h489", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "100", "dqtime": "2025-07-03 19:44:56.665753", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "1", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-01-23 16:36:00.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-01-23 16:36:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1738", "dqtime": "2025-07-03 19:45:54.562035", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-06-14 08:33:28.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-19 09:20:59.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1465615", "dqtime": "2025-07-03 19:46:53.441230", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "54", "患者编号(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2020-12-30 11:08:52.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "1992", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 09:50:32.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "8881", "dqtime": "2025-07-03 19:47:38.557871", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "8881", "业务最小时间": "2025-01-01 10:00:32.000000", "数据改造厂商名称(为空的数量/准确性）": "8881", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "63", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "63", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 18:54:12.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "8387", "dqtime": "2025-07-03 19:48:25.852095", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "8387", "业务最小时间": "2025-06-09 08:36:26.000000", "数据改造厂商名称(为空的数量/准确性）": "3976", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "45", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "45", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:12:52.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "31848", "dqtime": "2025-07-03 20:03:46.068142", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "31848", "业务最小时间": "2025-01-06 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "31848", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-13 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "4283764", "dqtime": "2025-07-03 20:04:46.989590", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "4283764", "业务最小时间": "2025-01-01 08:21:18.000000", "数据改造厂商名称(为空的数量/准确性）": "4283764", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 16:44:08.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2276183", "dqtime": "2025-07-03 20:05:53.204583", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2276183", "业务最小时间": "2025-01-01 08:47:34.000000", "数据改造厂商名称(为空的数量/准确性）": "2276183", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 15:43:20.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "5674619", "dqtime": "2025-07-03 20:06:40.722420", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "5674619", "业务最小时间": "2025-01-01 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "5674619", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "8175640", "dqtime": "2025-07-03 20:07:36.139030", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "8175640", "业务最小时间": "2025-01-01 08:48:19.000000", "数据改造厂商名称(为空的数量/准确性）": "8175640", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 16:03:19.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "6955850", "dqtime": "2025-07-03 20:09:30.437837", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "6955850", "业务最小时间": "2024-12-28 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "6955850", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2770110", "dqtime": "2025-07-03 20:10:15.726896", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2770110", "业务最小时间": "2025-01-01 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "2770110", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-08 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "1142708", "dqtime": "2025-07-03 20:14:23.589542", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1142708", "业务最小时间": "2024-12-10 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "1142708", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "471392", "dqtime": "2025-07-03 20:14:48.378759", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "471392", "业务最小时间": "2024-12-31 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "471392", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-14 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "2335792", "dqtime": "2025-07-03 20:15:12.650446", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2335792", "业务最小时间": "2025-01-01 08:18:26.000000", "数据改造厂商名称(为空的数量/准确性）": "2335792", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 20:49:56.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "5179347", "dqtime": "2025-07-03 20:15:37.079871", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "5179347", "业务最小时间": "2025-01-01 08:29:14.000000", "数据改造厂商名称(为空的数量/准确性）": "5179347", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-09 16:54:41.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "509152", "dqtime": "2025-07-03 20:18:50.515722", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "509152", "业务最小时间": "2025-01-01 09:54:23.000000", "数据改造厂商名称(为空的数量/准确性）": "509152", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-15 15:46:51.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350200F36916562L", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "0", "dqtime": "2025-07-03 20:19:56.009574", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "患者编号(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "607259", "dqtime": "2025-07-03 20:24:02.344563", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "14", "患者编号(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2023-06-04 10:28:33.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "7", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 16:58:57.000000", "结算号(为空的数量/准确性）": "0"}, {"hosno": "12350200MB010327XM", "数据上传时间(为空的数量/准确性）": "0", "总费用(为空的数量/准确性）": "0", "cnt": "6585", "dqtime": "2025-07-03 20:20:21.593364", "系统建设厂商代码(为空的数量/准确性）": "0", "结算明细号(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "患者编号(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "业务最小时间": "2025-01-02 00:00:00.000000", "数据改造厂商名称(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "发票类别名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "发票类别代码(为空的数量/准确性）": "0", "与住院结算主表（关联不上的记录数）": "0", "结算时间(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 00:00:00.000000", "结算号(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:54:01", "endTime": "2025-07-03 20:54:02", "duration": 235}, "resultCode": 200, "message": null, "logStack": null}