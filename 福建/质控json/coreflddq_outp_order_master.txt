{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "开单时间(为空的数量/准确性）", "医嘱号(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "医嘱类别代码(为空的数量/准确性）", "医嘱类别名称(为空的数量/准确性）", "处方类型代码(为空的数量/准确性）", "处方类型名称(为空的数量/准确性）", "医嘱状态代码(为空的数量/准确性）", "医嘱状态名称(为空的数量/准确性）", "开单科室代码(为空的数量/准确性）", "开单科室名称(为空的数量/准确性）", "执行科室代码(为空的数量/准确性）", "执行科室名称(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与门急诊就诊记录表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "10058", "dqtime": "2025-07-03 19:39:35.534692", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "10058", "数据删除状态(为空的数量/准确性）": "17073", "数据改造厂商名称(为空的数量/准确性）": "17073", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-06 08:07:01.000000", "hosno": "h486", "数据上传时间(为空的数量/准确性）": "0", "cnt": "17073", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "73", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-05-20 00:03:28.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "100", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "100", "处方类型代码(为空的数量/准确性）": "100", "dqtime": "2025-07-03 19:44:56.759936", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "100", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-01-01 00:00:00.000000", "hosno": "h489", "数据上传时间(为空的数量/准确性）": "0", "cnt": "100", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "100", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "4", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "4", "处方类型代码(为空的数量/准确性）": "8293", "dqtime": "2025-07-03 19:45:55.536998", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "8293", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-18 10:20:06.000000", "hosno": "h505", "数据上传时间(为空的数量/准确性）": "0", "cnt": "8713", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "漳平市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-14 00:01:40.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "191", "dqtime": "2025-07-03 19:46:53.469205", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2024-12-31 23:57:49.000000", "hosno": "h487", "数据上传时间(为空的数量/准确性）": "0", "cnt": "150469", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "60741", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2024-12-01 00:04:31.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "497097", "dqtime": "2025-07-03 19:47:37.383101", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "497097", "数据删除状态(为空的数量/准确性）": "712064", "数据改造厂商名称(为空的数量/准确性）": "712064", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 23:45:13.000000", "hosno": "h357", "数据上传时间(为空的数量/准确性）": "0", "cnt": "712064", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "3", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:28:16.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "63328", "dqtime": "2025-07-03 19:48:28.909487", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "63328", "数据删除状态(为空的数量/准确性）": "120294", "数据改造厂商名称(为空的数量/准确性）": "108122", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:29:01.000000", "hosno": "h378", "数据上传时间(为空的数量/准确性）": "0", "cnt": "120294", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "18", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:01:38.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "862017", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "862017", "处方类型代码(为空的数量/准确性）": "862017", "dqtime": "2025-07-03 20:03:46.107813", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "862017", "数据删除状态(为空的数量/准确性）": "862017", "数据改造厂商名称(为空的数量/准确性）": "862017", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 00:00:00.000000", "hosno": "12350100488099816X", "数据上传时间(为空的数量/准确性）": "0", "cnt": "862017", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1416", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "1592892", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "1592892", "处方类型代码(为空的数量/准确性）": "1592892", "dqtime": "2025-07-03 20:04:45.688823", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "1592892", "数据删除状态(为空的数量/准确性）": "1592892", "数据改造厂商名称(为空的数量/准确性）": "1592892", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:58:59.000000", "hosno": "123501004880997520", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1592892", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "2906", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2022-12-31 00:02:09.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "1511290", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "1511290", "处方类型代码(为空的数量/准确性）": "1511290", "dqtime": "2025-07-03 20:05:45.571009", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "1511290", "数据删除状态(为空的数量/准确性）": "1511290", "数据改造厂商名称(为空的数量/准确性）": "1511290", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-17 02:08:50.000000", "hosno": "12350100488099779P", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1511290", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1402", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:13:45.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "2248014", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "2248014", "处方类型代码(为空的数量/准确性）": "2248014", "dqtime": "2025-07-03 20:06:52.019538", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "2248014", "数据删除状态(为空的数量/准确性）": "2248014", "数据改造厂商名称(为空的数量/准确性）": "2248014", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 00:00:00.000000", "hosno": "12350100488099701P", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2248014", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "3597", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-01-05 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "908012", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "908012", "处方类型代码(为空的数量/准确性）": "908012", "dqtime": "2025-07-03 20:07:28.632236", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "908012", "数据删除状态(为空的数量/准确性）": "908012", "数据改造厂商名称(为空的数量/准确性）": "908012", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 20:29:00.000000", "hosno": "123501004880997445", "数据上传时间(为空的数量/准确性）": "0", "cnt": "908012", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1590", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:25.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "2665916", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "2665916", "处方类型代码(为空的数量/准确性）": "2665916", "dqtime": "2025-07-03 20:09:31.702913", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "2665916", "数据删除状态(为空的数量/准确性）": "2665916", "数据改造厂商名称(为空的数量/准确性）": "2665916", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:56:27.000000", "hosno": "1235010048809971XF", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2665916", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "6164", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2022-12-31 00:00:45.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "725218", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "725218", "处方类型代码(为空的数量/准确性）": "725218", "dqtime": "2025-07-03 20:10:15.743938", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "725218", "数据删除状态(为空的数量/准确性）": "725218", "数据改造厂商名称(为空的数量/准确性）": "725218", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 00:00:00.000000", "hosno": "12350100488099728F", "数据上传时间(为空的数量/准确性）": "0", "cnt": "725218", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1153", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2017-10-27 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "874368", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "874368", "处方类型代码(为空的数量/准确性）": "874368", "dqtime": "2025-07-03 20:14:24.002335", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "874368", "数据删除状态(为空的数量/准确性）": "874368", "数据改造厂商名称(为空的数量/准确性）": "874368", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 22:00:11.000000", "hosno": "12350100488099736A", "数据上传时间(为空的数量/准确性）": "0", "cnt": "874368", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1395", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "760790", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "760790", "处方类型代码(为空的数量/准确性）": "760790", "dqtime": "2025-07-03 20:14:51.293435", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "760790", "数据删除状态(为空的数量/准确性）": "760790", "数据改造厂商名称(为空的数量/准确性）": "760790", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:52:03.000000", "hosno": "12350100488099840E", "数据上传时间(为空的数量/准确性）": "0", "cnt": "760790", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "1803", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "3205002", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "3205002", "处方类型代码(为空的数量/准确性）": "3205002", "dqtime": "2025-07-03 20:15:18.238169", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "3205002", "数据删除状态(为空的数量/准确性）": "3205002", "数据改造厂商名称(为空的数量/准确性）": "3205002", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:47:23.000000", "hosno": "123501824884815728", "数据上传时间(为空的数量/准确性）": "0", "cnt": "3205002", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "7687", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:01:25.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "3555064", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "3555064", "处方类型代码(为空的数量/准确性）": "3555064", "dqtime": "2025-07-03 20:15:36.679539", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "3555064", "数据删除状态(为空的数量/准确性）": "3555064", "数据改造厂商名称(为空的数量/准确性）": "3555064", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:53:43.000000", "hosno": "1235018148854625X2", "数据上传时间(为空的数量/准确性）": "0", "cnt": "3555064", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "6451", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:01:27.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "889612", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "889612", "处方类型代码(为空的数量/准确性）": "889612", "dqtime": "2025-07-03 20:18:49.689399", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "889612", "数据删除状态(为空的数量/准确性）": "889612", "数据改造厂商名称(为空的数量/准确性）": "889612", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 23:49:35.000000", "hosno": "12350181488546276W", "数据上传时间(为空的数量/准确性）": "0", "cnt": "889612", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "943", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:08.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "13970874", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:19:54.920303", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "8", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-07-01 16:53:58.000000", "hosno": "12350200F36916562L", "数据上传时间(为空的数量/准确性）": "0", "cnt": "13970874", "数据连续性(数据量/月)": "65", "与门急诊就诊记录表（关联不上的记录数）": "3288181", "hosname": "厦门医学院附属口腔医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2002-12-09 10:49:12.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "1", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "1", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:42.070769", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123502004266007248", "数据上传时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "41748", "dqtime": "2025-07-03 20:25:43.771706", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "41748", "数据删除状态(为空的数量/准确性）": "65438", "数据改造厂商名称(为空的数量/准确性）": "65438", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-22 23:54:52.000000", "hosno": "12352200490348279K", "数据上传时间(为空的数量/准确性）": "0", "cnt": "65438", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "14", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-17 00:00:23.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "11041516", "dqtime": "2025-07-03 20:21:03.931063", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "11041516", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-29 23:57:38.000000", "hosno": "12350200302999454G", "数据上传时间(为空的数量/准确性）": "0", "cnt": "14974517", "数据连续性(数据量/月)": "53", "与门急诊就诊记录表（关联不上的记录数）": "39", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:00:12.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "1", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:24.473399", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-07-23 10:03:09.000000", "hosno": "12350200MB010327XM", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1509619", "数据连续性(数据量/月)": "5", "与门急诊就诊记录表（关联不上的记录数）": "392", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:17:43.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:44.974862", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 18:39:29.000000", "hosno": "123522034904674116", "数据上传时间(为空的数量/准确性）": "0", "cnt": "35404", "数据连续性(数据量/月)": "1", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-05-01 08:19:10.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:24:05.624339", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 00:01:09.000000", "hosno": "12352203490467403B", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2874788", "数据连续性(数据量/月)": "14", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:24:36.000000", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "1", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "1", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}, {"医嘱状态名称(为空的数量/准确性）": "0", "开单科室代码(为空的数量/准确性）": "0", "医嘱状态代码(为空的数量/准确性）": "0", "处方类型代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:49.941414", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "开单科室名称(为空的数量/准确性）": "0", "处方类型名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12352200490349810T", "数据上传时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "开单时间(为空的数量/准确性）": "0", "就诊流水号(为空的数量/准确性）": "0", "执行科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "医嘱类别代码(为空的数量/准确性）": "0", "执行科室名称(为空的数量/准确性）": "0", "医嘱号(为空的数量/准确性）": "0", "医嘱类别名称(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:34:22", "endTime": "2025-07-03 20:34:23", "duration": 482}, "resultCode": 200, "message": null, "logStack": null}