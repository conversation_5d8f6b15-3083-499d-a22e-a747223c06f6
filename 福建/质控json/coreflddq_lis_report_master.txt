{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "报告时间(为空的数量/准确性）", "门诊/住院标志(为空的数量/准确性）", "门诊/住院标志名称(为空的数量/准确性）", "lis患者编号(为空的数量/准确性）", "lis就诊号(为空的数量/准确性）", "报告单号(为空的数量/准确性）", "his系统患者编号(为空的数量/准确性）", "his系统就诊号(为空的数量/准确性）", "患者姓名(为空的数量/准确性）", "性别代码(为空的数量/准确性）", "性别名称(为空的数量/准确性）", "出生日期(为空的数量/准确性）", "申请科室代码(为空的数量/准确性）", "申请科室名称(为空的数量/准确性）", "申请单号(为空的数量/准确性）", "lis申请单号(为空的数量/准确性）", "申请项目代码(为空的数量/准确性）", "申请项目名称(为空的数量/准确性）", "检验类型代码(为空的数量/准确性）", "检验类型名称(为空的数量/准确性）", "检验目的(为空的数量/准确性）", "申请时间(为空的数量/准确性）", "结果状态代码(为空的数量/准确性）", "结果状态名称(为空的数量/准确性）", "标本采集时间(为空的数量/准确性）", "接收时间(为空的数量/准确性）", "检验时间(为空的数量/准确性）", "报告审核时间(为空的数量/准确性）", "标本类别代码(为空的数量/准确性）", "标本类别名称(为空的数量/准确性）", "标本条形码(为空的数量/准确性）", "样本号(为空的数量/准确性）", "结果正常标志(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "业务最小时间", "业务最大时间", "数据连续性(数据量/月)", "与检验申请表（关联不上的记录数）", "与门急诊就诊记录表（关联不上的记录数）", "与住院患者出院记录表（关联不上的记录数）"], "columnDisplayNames": null, "rows": [{"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "13", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "2425", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-05-20 23:53:35.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "4", "数据连续性(数据量/月)": "2", "与门急诊就诊记录表（关联不上的记录数）": "237", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "4", "业务最小时间": "2025-03-02 10:17:03.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "4", "标本采集时间(为空的数量/准确性）": "28", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "3838", "标本类别名称(为空的数量/准确性）": "13", "dqtime": "2025-07-03 19:39:38.988387", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "4", "与检验申请表（关联不上的记录数）": "1", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "h486", "性别代码(为空的数量/准确性）": "0", "cnt": "3838", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "131", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "49161", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-09 10:19:18.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "漳平市医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:32:32.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "110733", "申请科室代码(为空的数量/准确性）": "4", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "6", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "127", "dqtime": "2025-07-03 19:45:58.076392", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "6", "与检验申请表（关联不上的记录数）": "143213", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "h505", "性别代码(为空的数量/准确性）": "0", "cnt": "143216", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "66094", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2020-12-31 18:07:35.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "12", "与门急诊就诊记录表（关联不上的记录数）": "47050", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:24:15.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "113144", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:57.813465", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "113144", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "h487", "性别代码(为空的数量/准确性）": "0", "cnt": "113144", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "8", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "4784", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-25 17:35:20.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "226", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "宁德市中医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 09:52:14.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "8", "dqtime": "2025-07-03 19:47:40.894532", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "460", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "h357", "性别代码(为空的数量/准确性）": "0", "cnt": "12352", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "3396", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "1381", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:30:13.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "宁德市闽东医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-14 23:34:44.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "3396", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "158", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:29.422209", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "343", "与检验申请表（关联不上的记录数）": "93", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "h378", "性别代码(为空的数量/准确性）": "0", "cnt": "3396", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "27330", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "27330", "标本类别代码(为空的数量/准确性）": "27330", "数据删除状态(为空的数量/准确性）": "27330", "与住院患者出院记录表（关联不上的记录数）": "25601", "数据改造厂商名称(为空的数量/准确性）": "27330", "结果状态代码(为空的数量/准确性）": "27330", "业务最大时间": "2025-06-15 16:28:32.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市皮肤病防治院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 08:10:23.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "27330", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "27330", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "27330", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "27330", "标本类别名称(为空的数量/准确性）": "27330", "dqtime": "2025-07-03 20:03:49.897158", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "27330", "与检验申请表（关联不上的记录数）": "27330", "接收时间(为空的数量/准确性）": "27330", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "27330", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099816X", "性别代码(为空的数量/准确性）": "0", "cnt": "27330", "出生日期(为空的数量/准确性）": "27330", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "170244", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "170244", "标本类别代码(为空的数量/准确性）": "170244", "数据删除状态(为空的数量/准确性）": "170244", "与住院患者出院记录表（关联不上的记录数）": "55389", "数据改造厂商名称(为空的数量/准确性）": "170244", "结果状态代码(为空的数量/准确性）": "170244", "业务最大时间": "2025-06-15 23:07:34.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "170244", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市中医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2024-12-28 15:45:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "170244", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "170244", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "170244", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "13598", "标本类别名称(为空的数量/准确性）": "170244", "dqtime": "2025-07-03 20:04:46.478492", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "170244", "与检验申请表（关联不上的记录数）": "167626", "接收时间(为空的数量/准确性）": "170244", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "170244", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "123501004880997520", "性别代码(为空的数量/准确性）": "0", "cnt": "170244", "出生日期(为空的数量/准确性）": "170244", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "204363", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "204363", "标本类别代码(为空的数量/准确性）": "204363", "数据删除状态(为空的数量/准确性）": "204363", "与住院患者出院记录表（关联不上的记录数）": "122551", "数据改造厂商名称(为空的数量/准确性）": "204363", "结果状态代码(为空的数量/准确性）": "204363", "业务最大时间": "2025-06-15 23:48:50.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "204363", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福建省福州儿童医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 01:47:31.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "204363", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "204363", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "204363", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "204363", "标本类别名称(为空的数量/准确性）": "204363", "dqtime": "2025-07-03 20:05:56.538698", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "204363", "与检验申请表（关联不上的记录数）": "204363", "接收时间(为空的数量/准确性）": "204363", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "204363", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099779P", "性别代码(为空的数量/准确性）": "0", "cnt": "204363", "出生日期(为空的数量/准确性）": "204363", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "186977", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "186977", "标本类别代码(为空的数量/准确性）": "186977", "数据删除状态(为空的数量/准确性）": "186977", "与住院患者出院记录表（关联不上的记录数）": "87653", "数据改造厂商名称(为空的数量/准确性）": "186977", "结果状态代码(为空的数量/准确性）": "186977", "业务最大时间": "2025-06-15 23:54:45.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市第一医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:45:41.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "186977", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "186977", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "186977", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "186977", "标本类别名称(为空的数量/准确性）": "186977", "dqtime": "2025-07-03 20:06:35.934886", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "186977", "与检验申请表（关联不上的记录数）": "1870", "接收时间(为空的数量/准确性）": "186977", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "186977", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099701P", "性别代码(为空的数量/准确性）": "0", "cnt": "186977", "出生日期(为空的数量/准确性）": "186977", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "529836", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "529836", "标本类别代码(为空的数量/准确性）": "529836", "数据删除状态(为空的数量/准确性）": "529836", "与住院患者出院记录表（关联不上的记录数）": "209174", "数据改造厂商名称(为空的数量/准确性）": "529836", "结果状态代码(为空的数量/准确性）": "529836", "业务最大时间": "2025-06-15 23:08:44.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "529836", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福建医科大学孟超肝胆医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2023-03-19 11:03:09.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "529836", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "529836", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "529836", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "529836", "dqtime": "2025-07-03 20:07:30.836449", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "529836", "与检验申请表（关联不上的记录数）": "1", "接收时间(为空的数量/准确性）": "529836", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "529836", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "123501004880997445", "性别代码(为空的数量/准确性）": "0", "cnt": "529836", "出生日期(为空的数量/准确性）": "529836", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "505152", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "505152", "标本类别代码(为空的数量/准确性）": "505152", "数据删除状态(为空的数量/准确性）": "505152", "与住院患者出院记录表（关联不上的记录数）": "164056", "数据改造厂商名称(为空的数量/准确性）": "505152", "结果状态代码(为空的数量/准确性）": "505152", "业务最大时间": "2025-06-16 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "505152", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市第二医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "505152", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "505152", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "505152", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "505152", "dqtime": "2025-07-03 20:09:31.675776", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "505152", "与检验申请表（关联不上的记录数）": "499563", "接收时间(为空的数量/准确性）": "505152", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "505152", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "1235010048809971XF", "性别代码(为空的数量/准确性）": "0", "cnt": "505152", "出生日期(为空的数量/准确性）": "505152", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "315881", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "315881", "标本类别代码(为空的数量/准确性）": "315881", "数据删除状态(为空的数量/准确性）": "315881", "与住院患者出院记录表（关联不上的记录数）": "92155", "数据改造厂商名称(为空的数量/准确性）": "315881", "结果状态代码(为空的数量/准确性）": "315881", "业务最大时间": "2025-06-15 23:15:41.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州结核病防治院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2024-04-23 08:07:32.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "315881", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "315881", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "315881", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "315881", "标本类别名称(为空的数量/准确性）": "315881", "dqtime": "2025-07-03 20:10:21.235566", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "315881", "与检验申请表（关联不上的记录数）": "315881", "接收时间(为空的数量/准确性）": "315881", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "315881", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099728F", "性别代码(为空的数量/准确性）": "0", "cnt": "315881", "出生日期(为空的数量/准确性）": "315881", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "66898", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "66898", "标本类别代码(为空的数量/准确性）": "66898", "数据删除状态(为空的数量/准确性）": "66898", "与住院患者出院记录表（关联不上的记录数）": "22611", "数据改造厂商名称(为空的数量/准确性）": "66898", "结果状态代码(为空的数量/准确性）": "66898", "业务最大时间": "2025-06-16 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "66898", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市神经精神病防治院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "66898", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "66898", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "66898", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "66898", "dqtime": "2025-07-03 20:14:26.852788", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "66898", "与检验申请表（关联不上的记录数）": "66143", "接收时间(为空的数量/准确性）": "66898", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "66898", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099736A", "性别代码(为空的数量/准确性）": "0", "cnt": "66898", "出生日期(为空的数量/准确性）": "66898", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "174572", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "174572", "标本类别代码(为空的数量/准确性）": "174572", "数据删除状态(为空的数量/准确性）": "174572", "与住院患者出院记录表（关联不上的记录数）": "138022", "数据改造厂商名称(为空的数量/准确性）": "174572", "结果状态代码(为空的数量/准确性）": "174572", "业务最大时间": "2025-06-16 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "174572", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福州市妇幼保健院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "174572", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "174572", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "174572", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "174572", "dqtime": "2025-07-03 20:14:54.227493", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "174572", "与检验申请表（关联不上的记录数）": "174544", "接收时间(为空的数量/准确性）": "174572", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "174572", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350100488099840E", "性别代码(为空的数量/准确性）": "0", "cnt": "174572", "出生日期(为空的数量/准确性）": "174572", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "231799", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "231799", "标本类别代码(为空的数量/准确性）": "231799", "数据删除状态(为空的数量/准确性）": "231799", "与住院患者出院记录表（关联不上的记录数）": "115663", "数据改造厂商名称(为空的数量/准确性）": "231799", "结果状态代码(为空的数量/准确性）": "231799", "业务最大时间": "2025-06-15 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "231799", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "长乐区人民医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "231799", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "231799", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "231799", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "231799", "标本类别名称(为空的数量/准确性）": "231799", "dqtime": "2025-07-03 20:15:25.209249", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "231799", "与检验申请表（关联不上的记录数）": "231799", "接收时间(为空的数量/准确性）": "231799", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "231799", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "123501824884815728", "性别代码(为空的数量/准确性）": "0", "cnt": "231799", "出生日期(为空的数量/准确性）": "231799", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "145091", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "145091", "标本类别代码(为空的数量/准确性）": "145091", "数据删除状态(为空的数量/准确性）": "145091", "与住院患者出院记录表（关联不上的记录数）": "66289", "数据改造厂商名称(为空的数量/准确性）": "145091", "结果状态代码(为空的数量/准确性）": "145091", "业务最大时间": "2025-06-15 00:00:00.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "145091", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福建省福清市医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:00:00.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "145091", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "145091", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "145091", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "145091", "标本类别名称(为空的数量/准确性）": "145091", "dqtime": "2025-07-03 20:15:43.404401", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "145091", "与检验申请表（关联不上的记录数）": "79065", "接收时间(为空的数量/准确性）": "145091", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "145091", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "1235018148854625X2", "性别代码(为空的数量/准确性）": "0", "cnt": "145091", "出生日期(为空的数量/准确性）": "145091", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "186647", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "186647", "标本类别代码(为空的数量/准确性）": "186647", "数据删除状态(为空的数量/准确性）": "186647", "与住院患者出院记录表（关联不上的记录数）": "122956", "数据改造厂商名称(为空的数量/准确性）": "186647", "结果状态代码(为空的数量/准确性）": "186647", "业务最大时间": "2025-06-15 23:51:53.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "186647", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福清市妇幼保健院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:20:34.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "186647", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "186647", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "186647", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "186647", "标本类别名称(为空的数量/准确性）": "186647", "dqtime": "2025-07-03 20:18:40.217817", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "186647", "与检验申请表（关联不上的记录数）": "186647", "接收时间(为空的数量/准确性）": "186647", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "186647", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350181488546276W", "性别代码(为空的数量/准确性）": "0", "cnt": "186647", "出生日期(为空的数量/准确性）": "186647", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "3607", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-30 18:26:08.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "184", "检验时间(为空的数量/准确性）": "0", "hosname": "厦门医学院附属口腔医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2020-08-26 15:54:51.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:03.092624", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "7526", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350200F36916562L", "性别代码(为空的数量/准确性）": "7540", "cnt": "9914", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "7540", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "3", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "7041", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-07-01 23:49:37.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "5", "与门急诊就诊记录表（关联不上的记录数）": "1752", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 09:03:55.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "163874", "标本类别名称(为空的数量/准确性）": "3", "dqtime": "2025-07-03 20:20:23.454242", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "5", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350200MB010327XM", "性别代码(为空的数量/准确性）": "0", "cnt": "163874", "出生日期(为空的数量/准确性）": "10", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "0", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "0", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:43.324759", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "0", "接收时间(为空的数量/准确性）": "0", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "123502004266007248", "性别代码(为空的数量/准确性）": "0", "cnt": "0", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "912", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 17:23:40.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "0", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福鼎市中医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2025-06-19 05:33:27.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "0", "结果正常标志(为空的数量/准确性）": "1243", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "28", "申请科室名称(为空的数量/准确性）": "0", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:47.490541", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "198", "接收时间(为空的数量/准确性）": "28", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "123522034904674116", "性别代码(为空的数量/准确性）": "0", "cnt": "1243", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "541", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "339036", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:10:57.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "89", "数据连续性(数据量/月)": "7", "与门急诊就诊记录表（关联不上的记录数）": "0", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "报告审核时间(为空的数量/准确性）": "0", "申请项目名称(为空的数量/准确性）": "89", "业务最小时间": "2023-06-02 23:18:06.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "89", "结果正常标志(为空的数量/准确性）": "683202", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "17637", "申请科室名称(为空的数量/准确性）": "89", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:24:08.297280", "lis患者编号(为空的数量/准确性）": "0", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "1745", "接收时间(为空的数量/准确性）": "247", "his系统患者编号(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12352203490467403B", "性别代码(为空的数量/准确性）": "0", "cnt": "683202", "出生日期(为空的数量/准确性）": "0", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "0"}, {"标本条形码(为空的数量/准确性）": "0", "系统建设厂商代码(为空的数量/准确性）": "0", "样本号(为空的数量/准确性）": "0", "标本类别代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "24088", "数据改造厂商名称(为空的数量/准确性）": "0", "结果状态代码(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 23:50:48.000000", "数据上传时间(为空的数量/准确性）": "0", "申请项目代码(为空的数量/准确性）": "0", "数据连续性(数据量/月)": "65", "与门急诊就诊记录表（关联不上的记录数）": "3041", "门诊/住院标志名称(为空的数量/准确性）": "0", "检验时间(为空的数量/准确性）": "3876", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "报告审核时间(为空的数量/准确性）": "3876", "申请项目名称(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:22:38.000000", "门诊/住院标志(为空的数量/准确性）": "0", "检验目的(为空的数量/准确性）": "0", "申请科室代码(为空的数量/准确性）": "30474", "结果正常标志(为空的数量/准确性）": "182348", "检验类型名称(为空的数量/准确性）": "0", "标本采集时间(为空的数量/准确性）": "75", "申请科室名称(为空的数量/准确性）": "30474", "申请单号(为空的数量/准确性）": "0", "标本类别名称(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:15.954949", "lis患者编号(为空的数量/准确性）": "57", "检验类型代码(为空的数量/准确性）": "0", "与检验申请表（关联不上的记录数）": "5419", "接收时间(为空的数量/准确性）": "242", "his系统患者编号(为空的数量/准确性）": "57", "系统建设厂商名称(为空的数量/准确性）": "0", "lis就诊号(为空的数量/准确性）": "0", "结果状态名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "lis申请单号(为空的数量/准确性）": "0", "申请时间(为空的数量/准确性）": "0", "hosno": "12350200302999454G", "性别代码(为空的数量/准确性）": "0", "cnt": "8900418", "出生日期(为空的数量/准确性）": "8900418", "报告单号(为空的数量/准确性）": "0", "his系统就诊号(为空的数量/准确性）": "0", "数据唯一记录号(为空的数量/准确性）": "0", "性别名称(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "报告时间(为空的数量/准确性）": "3876"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:48:37", "endTime": "2025-07-03 20:48:38", "duration": 256}, "resultCode": 200, "message": null, "logStack": null}