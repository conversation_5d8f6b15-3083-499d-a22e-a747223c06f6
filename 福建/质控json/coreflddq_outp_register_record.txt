{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "挂号时间(为空的数量/准确性）", "挂号号(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "患者姓名(为空的数量/准确性）", "挂号类型代码(为空的数量/准确性）", "挂号类型名称(为空的数量/准确性）", "挂号科室代码(为空的数量/准确性）", "挂号科室名称(为空的数量/准确性）", "挂号费(为空的数量/准确性）", "费别代码(为空的数量/准确性）", "费别名称(为空的数量/准确性）", "挂号状态名称(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:36.695478", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-05-20 23:57:37.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h486", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2949", "数据连续性(数据量/月)": "1", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-05-20 00:02:47.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:44:58.664393", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 11:14:24.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h489", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "100", "数据连续性(数据量/月)": "5", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-03 09:37:52.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "100", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "394", "dqtime": "2025-07-03 19:45:57.701978", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:53:06.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h505", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "77229", "数据连续性(数据量/月)": "9", "hosname": "漳平市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:11:19.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:55.690151", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-20 00:06:29.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h487", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "7087024", "数据连续性(数据量/月)": "65", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:00:45.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:47:37.383777", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 23:41:07.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h357", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "241692", "数据连续性(数据量/月)": "5", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:03:56.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:26.913022", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-26 14:28:52.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "h378", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "72211", "数据连续性(数据量/月)": "0", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:01:29.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "862017", "dqtime": "2025-07-03 20:03:46.138268", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "862017", "数据改造厂商名称(为空的数量/准确性）": "862017", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "4882", "业务最大时间": "2025-06-16 00:00:00.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099816X", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "862017", "数据连续性(数据量/月)": "0", "hosname": "福州市皮肤病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "862017", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "1592892", "dqtime": "2025-07-03 20:04:51.330032", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1592892", "数据改造厂商名称(为空的数量/准确性）": "1592892", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "95061", "业务最大时间": "2025-06-16 23:58:59.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "123501004880997520", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1592892", "数据连续性(数据量/月)": "0", "hosname": "福州市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2022-12-31 00:02:09.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "1592892", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "1511290", "dqtime": "2025-07-03 20:05:43.096697", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "1511290", "数据改造厂商名称(为空的数量/准确性）": "1511290", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "1022", "业务最大时间": "2025-06-17 02:08:50.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099779P", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1511290", "数据连续性(数据量/月)": "0", "hosname": "福建省福州儿童医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:13:45.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "1511290", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "2248014", "dqtime": "2025-07-03 20:06:36.367303", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2248014", "数据改造厂商名称(为空的数量/准确性）": "2248014", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "1700", "业务最大时间": "2025-06-16 00:00:00.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099701P", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2248014", "数据连续性(数据量/月)": "0", "hosname": "福州市第一医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2021-01-05 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "2248014", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "908012", "dqtime": "2025-07-03 20:07:39.839878", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "908012", "数据改造厂商名称(为空的数量/准确性）": "908012", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "1498", "业务最大时间": "2025-06-16 20:29:00.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "123501004880997445", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "908012", "数据连续性(数据量/月)": "0", "hosname": "福建医科大学孟超肝胆医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:25.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "908012", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "2665916", "dqtime": "2025-07-03 20:09:29.390926", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "2665916", "数据改造厂商名称(为空的数量/准确性）": "2665916", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "2145", "业务最大时间": "2025-06-16 23:56:27.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "1235010048809971XF", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2665916", "数据连续性(数据量/月)": "0", "hosname": "福州市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2022-12-31 00:00:45.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "2665916", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "725218", "dqtime": "2025-07-03 20:10:20.327043", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "725218", "数据改造厂商名称(为空的数量/准确性）": "725218", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "6037", "业务最大时间": "2025-06-16 00:00:00.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099728F", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "725218", "数据连续性(数据量/月)": "0", "hosname": "福州结核病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2017-10-27 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "725218", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "874368", "dqtime": "2025-07-03 20:14:26.873367", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "874368", "数据改造厂商名称(为空的数量/准确性）": "874368", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "2378", "业务最大时间": "2025-06-16 22:00:11.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099736A", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "874368", "数据连续性(数据量/月)": "0", "hosname": "福州市神经精神病防治院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "874368", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "760790", "dqtime": "2025-07-03 20:14:45.071382", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "760790", "数据改造厂商名称(为空的数量/准确性）": "760790", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "12803", "业务最大时间": "2025-06-16 23:52:03.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350100488099840E", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "760790", "数据连续性(数据量/月)": "0", "hosname": "福州市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:00.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "760790", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "3205002", "dqtime": "2025-07-03 20:15:21.239804", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3205002", "数据改造厂商名称(为空的数量/准确性）": "3205002", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "135175", "业务最大时间": "2025-06-16 23:47:23.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "123501824884815728", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "3205002", "数据连续性(数据量/月)": "0", "hosname": "长乐区人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:01:25.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "3205002", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "3555064", "dqtime": "2025-07-03 20:15:37.162504", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3555064", "数据改造厂商名称(为空的数量/准确性）": "3555064", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "91511", "业务最大时间": "2025-06-16 23:53:43.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "1235018148854625X2", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "3555064", "数据连续性(数据量/月)": "0", "hosname": "福建省福清市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:01:27.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "3555064", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "889612", "dqtime": "2025-07-03 20:18:40.043681", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "889612", "数据改造厂商名称(为空的数量/准确性）": "889612", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "27078", "业务最大时间": "2025-06-16 23:49:35.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350181488546276W", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "889612", "数据连续性(数据量/月)": "0", "hosname": "福清市妇幼保健院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-01-01 00:00:08.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "889612", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "3", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:19:55.928988", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 12:38:25.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350200F36916562L", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "2495275", "数据连续性(数据量/月)": "63", "hosname": "厦门医学院附属口腔医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 10:04:32.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "1201", "dqtime": "2025-07-03 20:20:28.331870", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-07-01 23:58:35.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350200MB010327XM", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "303883", "数据连续性(数据量/月)": "5", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 00:17:43.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "4", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "4"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:25:46.339759", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-24 23:56:07.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12352200490348279K", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "42096", "数据连续性(数据量/月)": "0", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-17 00:02:10.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "1310612", "dqtime": "2025-07-03 20:23:55.232082", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 23:57:27.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12352203490467403B", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "1310612", "数据连续性(数据量/月)": "14", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 01:22:32.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "1", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "2", "挂号费(为空的数量/准确性）": "544809", "dqtime": "2025-07-03 20:21:09.914881", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-07-01 23:57:14.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "12350200302999454G", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "7258469", "数据连续性(数据量/月)": "65", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 00:00:47.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:48.485475", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "", "挂号时间(为空的数量/准确性）": "0", "hosno": "12352200490349810T", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "20549", "dqtime": "2025-07-03 20:21:46.312985", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 18:36:35.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "123522034904674116", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "20549", "数据连续性(数据量/月)": "1", "hosname": "福鼎市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-05-01 08:18:31.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}, {"挂号科室名称(为空的数量/准确性）": "0", "挂号费(为空的数量/准确性）": "132", "dqtime": "2025-07-03 20:20:43.113524", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "挂号科室代码(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "患者姓名(为空的数量/准确性）": "0", "业务最大时间": "2025-07-02 11:59:01.000000", "挂号时间(为空的数量/准确性）": "0", "hosno": "123502004266007248", "挂号号(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "cnt": "122367", "数据连续性(数据量/月)": "65", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 08:20:54.000000", "就诊流水号(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "挂号类型代码(为空的数量/准确性）": "0", "挂号类型名称(为空的数量/准确性）": "0", "费别代码(为空的数量/准确性）": "0", "挂号状态名称(为空的数量/准确性）": "0", "费别名称(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:45:17", "endTime": "2025-07-03 20:45:17", "duration": 261}, "resultCode": 200, "message": null, "logStack": null}