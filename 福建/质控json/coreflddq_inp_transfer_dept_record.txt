{"details": {"pageParam": null, "resultCode": 200, "resultDesc": "OK", "exception": "", "affectedRows": 0, "columnNames": ["hosname", "hosno", "dqtime", "cnt", "数据唯一记录号(为空的数量/准确性）", "医疗机构名称(为空的数量/准确性）", "医疗机构统一社会信用代码(为空的数量/准确性）", "数据上传时间(为空的数量/准确性）", "系统建设厂商代码(为空的数量/准确性）", "系统建设厂商名称(为空的数量/准确性）", "转入时间(为空的数量/准确性）", "患者编号(为空的数量/准确性）", "就诊流水号(为空的数量/准确性）", "转科转床流水号(为空的数量/准确性）", "转出科室代码(为空的数量/准确性）", "转出科室名称(为空的数量/准确性）", "转出病区代码(为空的数量/准确性）", "转出病区名称(为空的数量/准确性）", "转出床位号(为空的数量/准确性）", "转出时间(为空的数量/准确性）", "转入科室代码(为空的数量/准确性）", "转入科室名称(为空的数量/准确性）", "转入病区代码(为空的数量/准确性）", "转入病区名称(为空的数量/准确性）", "数据改造厂商名称(为空的数量/准确性）", "数据删除状态(为空的数量/准确性）", "与住院患者出院记录表（关联不上的记录数）", "业务最大时间", "业务最小时间", "数据连续性(数据量/月)"], "columnDisplayNames": null, "rows": [{"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:39:38.579261", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3", "与住院患者出院记录表（关联不上的记录数）": "2", "数据改造厂商名称(为空的数量/准确性）": "3", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "1", "业务最大时间": "2025-05-13 09:30:19.000000", "hosno": "h486", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "3", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "0", "hosname": "龙岩市第二医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-03-14 07:35:57.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:44:57.886256", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "100", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "98", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "2025-06-16 11:24:21.000000", "hosno": "h489", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "100", "数据连续性(数据量/月)": "5", "转入科室名称(为空的数量/准确性）": "0", "hosname": "龙岩市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-02 09:31:00.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "100", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "22931", "dqtime": "2025-07-03 19:45:56.571655", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "22931", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "1437", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "21720", "转出科室代码(为空的数量/准确性）": "21720", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "27920", "业务最大时间": "2025-06-24 13:58:10.000000", "hosno": "h505", "转入病区代码(为空的数量/准确性）": "36801", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "12705", "转入时间(为空的数量/准确性）": "37580", "cnt": "50288", "数据连续性(数据量/月)": "51", "转入科室名称(为空的数量/准确性）": "36800", "hosname": "漳平市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2019-05-06 15:07:06.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "36800", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "36801", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:46:55.454209", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "28805", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "2099-12-31 11:22:23.000000", "hosno": "h487", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "113050", "数据连续性(数据量/月)": "56", "转入科室名称(为空的数量/准确性）": "0", "hosname": "龙岩人民医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-10-14 15:10:34.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:47:40.170385", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "17514", "与住院患者出院记录表（关联不上的记录数）": "17514", "数据改造厂商名称(为空的数量/准确性）": "17514", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "8700", "业务最大时间": "2025-06-24 21:58:55.000000", "hosno": "h357", "转入病区代码(为空的数量/准确性）": "17405", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "17514", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "17405", "hosname": "宁德市中医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-01-01 08:14:56.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "17405", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "17405", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 19:48:27.666938", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "7937", "与住院患者出院记录表（关联不上的记录数）": "7937", "数据改造厂商名称(为空的数量/准确性）": "7918", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "4008", "业务最大时间": "2025-06-26 12:04:57.000000", "hosno": "h378", "转入病区代码(为空的数量/准确性）": "7794", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "7937", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "7794", "hosname": "宁德市闽东医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-09 00:16:18.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "7794", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "7794", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:01.075905", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12350200F36916562L", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:20:21.968128", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "2", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "2025-06-28 12:06:59.000000", "hosno": "12350200MB010327XM", "转入病区代码(为空的数量/准确性）": "12", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "132", "数据连续性(数据量/月)": "2", "转入科室名称(为空的数量/准确性）": "0", "hosname": "厦门大学附属翔安医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-04-08 15:19:46.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "12", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "2", "dqtime": "2025-07-03 20:20:40.504174", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "2", "数据删除状态(为空的数量/准确性）": "153921", "与住院患者出院记录表（关联不上的记录数）": "153921", "数据改造厂商名称(为空的数量/准确性）": "153921", "转出科室名称(为空的数量/准确性）": "2", "转出科室代码(为空的数量/准确性）": "2", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "74412", "业务最大时间": "2025-07-03 13:16:29.000000", "hosno": "123502004266007248", "转入病区代码(为空的数量/准确性）": "148166", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "153921", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "148166", "hosname": "厦门市仙岳医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2020-01-01 06:48:20.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "148166", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "148166", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:17.431185", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "827", "与住院患者出院记录表（关联不上的记录数）": "1978", "数据改造厂商名称(为空的数量/准确性）": "827", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "1022", "业务最大时间": "2025-07-02 23:01:10.000000", "hosno": "12350200302999454G", "转入病区代码(为空的数量/准确性）": "1895", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "1978", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "1895", "hosname": "厦门市儿童医院（复旦大学附属儿科医院厦门医院）", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-16 08:11:59.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "1895", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "1895", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:21:47.021786", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "123522034904674116", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:24:09.240967", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "115", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "2025-06-23 13:02:05.000000", "hosno": "12352203490467403B", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "5148", "数据连续性(数据量/月)": "14", "转入科室名称(为空的数量/准确性）": "0", "hosname": "福鼎市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2023-06-03 11:19:45.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:25:45.594650", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "3185", "与住院患者出院记录表（关联不上的记录数）": "3185", "数据改造厂商名称(为空的数量/准确性）": "3185", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "1558", "业务最大时间": "2025-06-24 23:36:27.000000", "hosno": "12352200490348279K", "转入病区代码(为空的数量/准确性）": "3088", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "3185", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "3088", "hosname": "宁德师范学院附属宁德市医院", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "2025-06-17 00:19:04.000000", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "3088", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "3088", "转科转床流水号(为空的数量/准确性）": "0"}, {"转出病区代码(为空的数量/准确性）": "0", "dqtime": "2025-07-03 20:26:48.619821", "系统建设厂商代码(为空的数量/准确性）": "0", "患者编号(为空的数量/准确性）": "0", "转出病区名称(为空的数量/准确性）": "0", "数据删除状态(为空的数量/准确性）": "0", "与住院患者出院记录表（关联不上的记录数）": "0", "数据改造厂商名称(为空的数量/准确性）": "0", "转出科室名称(为空的数量/准确性）": "0", "转出科室代码(为空的数量/准确性）": "0", "系统建设厂商名称(为空的数量/准确性）": "0", "医疗机构名称(为空的数量/准确性）": "0", "转出床位号(为空的数量/准确性）": "0", "业务最大时间": "", "hosno": "12352200490349810T", "转入病区代码(为空的数量/准确性）": "0", "数据上传时间(为空的数量/准确性）": "0", "转出时间(为空的数量/准确性）": "0", "转入时间(为空的数量/准确性）": "0", "cnt": "0", "数据连续性(数据量/月)": "0", "转入科室名称(为空的数量/准确性）": "0", "hosname": "", "数据唯一记录号(为空的数量/准确性）": "0", "业务最小时间": "", "就诊流水号(为空的数量/准确性）": "0", "转入科室代码(为空的数量/准确性）": "0", "医疗机构统一社会信用代码(为空的数量/准确性）": "0", "转入病区名称(为空的数量/准确性）": "0", "转科转床流水号(为空的数量/准确性）": "0"}], "outParams": {}, "callbackParams": {}, "dataLabel": [], "dataRecord": {}, "startTime": "2025-07-03 20:49:13", "endTime": "2025-07-03 20:49:13", "duration": 268}, "resultCode": 200, "message": null, "logStack": null}