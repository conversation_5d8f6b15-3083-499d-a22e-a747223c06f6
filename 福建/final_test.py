from py.质控mysql转其他库 import translate_sql

# 用户提供的原始SQL
sql = """SELECT count(*) FROM outp_charge_detail A where 1=1 and A.APPLY_DOC_NAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' OR A.APPLY_DOC_NAME LIKE '·%' OR A.APPLY_DOC_NAME LIKE '%·' OR (A.APPLY_DOC_NAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.APPLY_DOC_NAME LIKE '% %');"""

# 转换为Oracle
result = translate_sql(sql, 'oracle')

print("原始SQL:")
print(sql)
print("\nOracle转换结果:")
print(result)

# 期望的正确结果
expected = """SELECT COUNT(*) 
FROM outp_charge_detail A 
WHERE 
    (
        REGEXP_LIKE(A.APPLY_DOC_NAME, '[^a-zA-Z\\u4e00-\\u9fa5·]') 
        OR A.APPLY_DOC_NAME LIKE '·%' 
        OR A.APPLY_DOC_NAME LIKE '%·' 
        OR (
            REGEXP_LIKE(A.APPLY_DOC_NAME, '^[\\u4e00-\\u9fa5]+$') 
            AND A.APPLY_DOC_NAME LIKE '% %'
        )
    );"""

print("\n期望的正确结果(格式化后):")
print(expected)

# 检查转换是否成功
if "REGEXP_LIKE((A.APPLY_DOC_NAME" in result:
    print("\n错误: 括号位置不正确")
elif "REGEXP_LIKE(A.APPLY_DOC_NAME" in result and "(REGEXP_LIKE(A.APPLY_DOC_NAME" in result:
    print("\n成功: 正则表达式转换正确，括号位置正确") 