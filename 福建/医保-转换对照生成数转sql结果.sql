insert overwrite table insu_emp_info_b partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    insu_emp_info_b.emp_no as emp_no,
    insu_emp_info_b.emp_mgt_type as emp_mgt_type,
    insu_emp_info_b.prnt_emp_no as prnt_emp_no,
    insu_emp_info_b.asoc_legent_flag as asoc_legent_flag,
    insu_emp_info_b.emp_type as emp_type,
    insu_emp_info_b.emp_name as emp_name,
    insu_emp_info_b.reg_name as reg_name,
    insu_emp_info_b.loc_admdvs as loc_admdvs,
    insu_emp_info_b.coner_name as coner_name,
    insu_emp_info_b.coner_email as coner_email,
    insu_emp_info_b.tel as tel,
    insu_emp_info_b.fax_no as fax_no,
    insu_emp_info_b.tax_reg_no as tax_reg_no,
    insu_emp_info_b.orgcode as orgcode,
    insu_emp_info_b.regno as regno,
    insu_emp_info_b.regno_cert_type as regno_cert_type,
    insu_emp_info_b.emp_addr as emp_addr,
    insu_emp_info_b.poscode as poscode,
    insu_emp_info_b.aprv_esta_dept as aprv_esta_dept,
    insu_emp_info_b.aprv_esta_date as aprv_esta_date,
    insu_emp_info_b.aprv_esta_docno as aprv_esta_docno,
    insu_emp_info_b.prnt_admdvs as prnt_admdvs,
    insu_emp_info_b.insu_admdvs as insu_admdvs,
    insu_emp_info_b.org_vali_stas as org_vali_stas,
    insu_emp_info_b.legrep_name as legrep_name,
    insu_emp_info_b.legrep_cert_type as legrep_cert_type,
    insu_emp_info_b.legrep_certno as legrep_certno,
    insu_emp_info_b.orgcode_issu_emp as orgcode_issu_emp,
    insu_emp_info_b.vali_flag as vali_flag,
    insu_emp_info_b.crte_time as crte_time,
    insu_emp_info_b.updt_time as updt_time,
    insu_emp_info_b.crter_id as crter_id,
    insu_emp_info_b.crter_name as crter_name,
    insu_emp_info_b.crte_optins_no as crte_optins_no,
    insu_emp_info_b.opter_id as opter_id,
    insu_emp_info_b.opt_time as opt_time,
    insu_emp_info_b.optins_no as optins_no,
    insu_emp_info_b.ver as ver,
    insu_emp_info_b.sync_prnt_flag as sync_prnt_flag,
    insu_emp_info_b.memo as memo,
    insu_emp_info_b.biz_date as biz_date,
    insu_emp_info_b.deleted_time as deleted_time,
    insu_emp_info_b.deleted as deleted,
    insu_emp_info_b.exch_updt_time as exch_updt_time,
    insu_emp_info_b.subsys_codg as subsys_codg,
    insu_emp_info_b.loc_admdvs as admdvs,
    insu_emp_info_b.entp_spec_flag as entp_spec_flag,
    insu_emp_info_b.poolarea_no as poolarea_no,
    insu_emp_info_b.emp_entt_codg as emp_entt_codg,
    insu_emp_info_b.legrep_tel as legrep_tel,
    insu_emp_info_b.opter_name as opter_name
from src_data.insu_emp_info_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table psn_insu_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    psn_insu_d.psn_insu_rlts_id as psn_insu_rlts_id,
    psn_insu_d.emp_no as emp_no,
    psn_insu_d.psn_no as psn_no,
    psn_insu_d.insutype as insutype,
    psn_insu_d.crt_insu_date as crt_insu_date,
    psn_insu_d.paus_insu_date as paus_insu_date,
    psn_insu_d.psn_insu_stas as psn_insu_stas,
    psn_insu_d.insutype_retr_flag as insutype_retr_flag,
    psn_insu_d.psn_type as psn_type,
    psn_insu_d.clct_way as clct_way,
    psn_insu_d.emp_fom as emp_fom,
    psn_insu_d.max_acctprd as max_acctprd,
    psn_insu_d.acct_crtn_ym as acct_crtn_ym,
    psn_insu_d.fst_insu_ym as fst_insu_ym,
    psn_insu_d.psn_insu_date as psn_insu_date,
    psn_insu_d.clct_rule_type_codg as clct_rule_type_codg,
    psn_insu_d.clctstd_crtf_rule_codg as clctstd_crtf_rule_codg,
    psn_insu_d.hi_type as hi_type,
    psn_insu_d.poolarea_no as poolarea_no,
    psn_insu_d.opt_chnl as opt_chnl,
    psn_insu_d.optins_no as optins_no,
    psn_insu_d.opter_id as opter_id,
    psn_insu_d.opter_name as opter_name,
    psn_insu_d.crter_id as crter_id,
    psn_insu_d.crter_name as crter_name,
    psn_insu_d.crte_time as crte_time,
    psn_insu_d.updt_time as updt_time,
    psn_insu_d.psn_insu_mgt_eid as psn_insu_mgt_eid,
    psn_insu_d.retr_trt_enjymnt_flag as retr_trt_enjymnt_flag,
    psn_insu_d.retr_acct_enjymnt_flag as retr_acct_enjymnt_flag,
    psn_insu_d.biz_date as biz_date,
    psn_insu_d.deleted_time as deleted_time,
    psn_insu_d.deleted as deleted,
    psn_insu_d.exch_updt_time as exch_updt_time,
    psn_insu_d.subsys_codg as subsys_codg,
    psn_insu_d.insu_admdvs as admdvs,
    psn_insu_d.opt_time as opt_time,
    psn_insu_d.crte_optins_no as crte_optins_no,
    psn_insu_d.quts_type as quts_type,
    psn_insu_d.retr_trt_begn_date as retr_trt_begn_date,
    psn_insu_d.insu_admdvs as insu_admdvs
from src_data.psn_insu_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table psn_clctstd_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    psn_clctstd_d.may_psn_clctstd as may_psn_clctstd,
    psn_clctstd_d.jan_clctstd_rule_codg as jan_clctstd_rule_codg,
    psn_clctstd_d.nov_wag as nov_wag,
    psn_clctstd_d.psn_insu_rlts_id as psn_insu_rlts_id,
    psn_clctstd_d.dec_wag as dec_wag,
    psn_clctstd_d.may_wag as may_wag,
    psn_clctstd_d.july_wag as july_wag,
    psn_clctstd_d.sept_clctstd_rule_codg as sept_clctstd_rule_codg,
    psn_clctstd_d.psn_clctstd_sn as psn_clctstd_sn,
    psn_clctstd_d.emp_no as emp_no,
    psn_clctstd_d.psn_no as psn_no,
    psn_clctstd_d.insutype as insutype,
    psn_clctstd_d.year as year,
    psn_clctstd_d.insutype_retr_flag as insutype_retr_flag,
    psn_clctstd_d.jan_wag as jan_wag,
    psn_clctstd_d.jan_psn_clctstd as jan_psn_clctstd,
    psn_clctstd_d.feb_wag as feb_wag,
    psn_clctstd_d.feb_clctstd_rule_codg as feb_clctstd_rule_codg,
    psn_clctstd_d.feb_psn_clctstd as feb_psn_clctstd,
    psn_clctstd_d.mar_wag as mar_wag,
    psn_clctstd_d.mar_clctstd_rule_codg as mar_clctstd_rule_codg,
    psn_clctstd_d.mar_psn_clctstd as mar_psn_clctstd,
    psn_clctstd_d.apr_wag as apr_wag,
    psn_clctstd_d.apr_clctstd_rule_codg as apr_clctstd_rule_codg,
    psn_clctstd_d.apr_psn_clctstd as apr_psn_clctstd,
    psn_clctstd_d.may_clctstd_rule_codg as may_clctstd_rule_codg,
    psn_clctstd_d.june_wag as june_wag,
    psn_clctstd_d.june_clctstd_rule_codg as june_clctstd_rule_codg,
    psn_clctstd_d.june_psn_clctstd as june_psn_clctstd,
    psn_clctstd_d.july_clctstd_rule_codg as july_clctstd_rule_codg,
    psn_clctstd_d.july_psn_clctstd as july_psn_clctstd,
    psn_clctstd_d.aug_wag as aug_wag,
    psn_clctstd_d.aug_clctstd_rule_codg as aug_clctstd_rule_codg,
    psn_clctstd_d.aug_psn_clctstd as aug_psn_clctstd,
    psn_clctstd_d.sept_wag as sept_wag,
    psn_clctstd_d.sept_psn_clctstd as sept_psn_clctstd,
    psn_clctstd_d.oct_wag as oct_wag,
    psn_clctstd_d.oct_clctstd_rule_codg as oct_clctstd_rule_codg,
    psn_clctstd_d.oct_psn_clctstd as oct_psn_clctstd,
    psn_clctstd_d.nov_clctstd_rule_codg as nov_clctstd_rule_codg,
    psn_clctstd_d.nov_psn_clctstd as nov_psn_clctstd,
    psn_clctstd_d.dec_clctstd_rule_codg as dec_clctstd_rule_codg,
    psn_clctstd_d.dec_psn_clctstd as dec_psn_clctstd,
    psn_clctstd_d.insu_admdvs as insu_admdvs,
    psn_clctstd_d.poolarea_no as poolarea_no,
    psn_clctstd_d.opt_chnl as opt_chnl,
    psn_clctstd_d.optins_no as optins_no,
    psn_clctstd_d.opter_id as opter_id,
    psn_clctstd_d.opter_name as opter_name,
    psn_clctstd_d.opt_time as opt_time,
    psn_clctstd_d.crte_optins_no as crte_optins_no,
    psn_clctstd_d.crter_id as crter_id,
    psn_clctstd_d.crter_name as crter_name,
    psn_clctstd_d.crte_time as crte_time,
    psn_clctstd_d.updt_time as updt_time,
    psn_clctstd_d.wag_dcla_mgt_eid as wag_dcla_mgt_eid,
    psn_clctstd_d.biz_date as biz_date,
    psn_clctstd_d.deleted_time as deleted_time,
    psn_clctstd_d.deleted as deleted,
    psn_clctstd_d.exch_updt_time as exch_updt_time,
    psn_clctstd_d.subsys_codg as subsys_codg,
    psn_clctstd_d.insu_admdvs as admdvs
from src_data.psn_clctstd_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table psn_info_b partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    psn_info_b.psn_no as psn_no,
    psn_info_b.psn_mgtcode as psn_mgtcode,
    psn_info_b.psn_name as psn_name,
    psn_info_b.gend as gend,
    psn_info_b.brdy as brdy,
    psn_info_b.file_brdy as file_brdy,
    psn_info_b.psn_cert_type as psn_cert_type,
    psn_info_b.certno as certno,
    psn_info_b.hsecfc as hsecfc,
    psn_info_b.tel as tel,
    psn_info_b.mob as mob,
    psn_info_b.naty as naty,
    psn_info_b.email as email,
    psn_info_b.resd_natu as resd_natu,
    psn_info_b.hsreg_addr_poscode as hsreg_addr_poscode,
    psn_info_b.live_admdvs as live_admdvs,
    psn_info_b.live_addr as live_addr,
    psn_info_b.live_addr_poscode as live_addr_poscode,
    psn_info_b.memo as memo,
    psn_info_b.surv_stas as surv_stas,
    psn_info_b.mul_prov_mnt_flag as mul_prov_mnt_flag,
    psn_info_b.retr_type as retr_type,
    psn_info_b.grad_schl as grad_schl,
    psn_info_b.educ as educ,
    psn_info_b.pro_tech_duty_lv as pro_tech_duty_lv,
    psn_info_b.nat_prfs_qua_lv as nat_prfs_qua_lv,
    psn_info_b.vali_flag as vali_flag,
    psn_info_b.updt_time as updt_time,
    psn_info_b.crter_id as crter_id,
    psn_info_b.crter_name as crter_name,
    psn_info_b.opter_id as opter_id,
    psn_info_b.opter_name as opter_name,
    psn_info_b.opt_time as opt_time,
    psn_info_b.ver as ver,
    psn_info_b.cpr_flag as cpr_flag,
    psn_info_b.poolarea_no as poolarea_no,
    psn_info_b.chk_time as chk_time,
    psn_info_b.biz_date as biz_date,
    psn_info_b.deleted_time as deleted_time,
    psn_info_b.deleted as deleted,
    psn_info_b.subsys_codg as subsys_codg,
    psn_info_b.resd_loc_admdvs as admdvs,
    psn_info_b.nat_regn_code as nat_regn_code,
    psn_info_b.resdbook_no as resdbook_no,
    psn_info_b.crte_optins_no as crte_optins_no,
    psn_info_b.exch_updt_time as exch_updt_time,
    psn_info_b.polstas as polstas,
    psn_info_b.mrg_stas as mrg_stas,
    psn_info_b.fst_patc_job_date as fst_patc_job_date,
    psn_info_b.hlcon as hlcon,
    psn_info_b.admdut as admdut,
    psn_info_b.crte_time as crte_time,
    psn_info_b.optins_no as optins_no,
    psn_info_b.alis as alis,
    psn_info_b.hsreg_addr as hsreg_addr,
    psn_info_b.chk_chnl as chk_chnl,
    psn_info_b.resd_loc_admdvs as resd_loc_admdvs
from src_data.psn_info_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table rsdt_psn_clct_detl_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    rsdt_psn_clct_detl_d.opter_name as opter_name,
    rsdt_psn_clct_detl_d.updt_time as updt_time,
    rsdt_psn_clct_detl_d.opt_time as opt_time,
    rsdt_psn_clct_detl_d.psn_no as psn_no,
    rsdt_psn_clct_detl_d.rsdt_clct_detl_id as rsdt_clct_detl_id,
    rsdt_psn_clct_detl_d.clct_bill_id as clct_bill_id,
    rsdt_psn_clct_detl_d.emp_no as emp_no,
    rsdt_psn_clct_detl_d.psn_insu_rlts_id as psn_insu_rlts_id,
    rsdt_psn_clct_detl_d.insutype as insutype,
    rsdt_psn_clct_detl_d.psn_type as psn_type,
    rsdt_psn_clct_detl_d.insu_idet as insu_idet,
    rsdt_psn_clct_detl_d.psn_idet_type as psn_idet_type,
    rsdt_psn_clct_detl_d.elec_taxrpt_no as elec_taxrpt_no,
    rsdt_psn_clct_detl_d.cashym as cashym,
    rsdt_psn_clct_detl_d.accrym_begn as accrym_begn,
    rsdt_psn_clct_detl_d.accrym_end as accrym_end,
    rsdt_psn_clct_detl_d.psn_clct_amt as psn_clct_amt,
    rsdt_psn_clct_detl_d.revs_flag as revs_flag,
    rsdt_psn_clct_detl_d.clct_flag as clct_flag,
    rsdt_psn_clct_detl_d.clct_type as clct_type,
    rsdt_psn_clct_detl_d.clct_time as clct_time,
    rsdt_psn_clct_detl_d.quot_clct_flag as quot_clct_flag,
    rsdt_psn_clct_detl_d.intsury_time as intsury_time,
    rsdt_psn_clct_detl_d.ursn_time as ursn_time,
    rsdt_psn_clct_detl_d.dcla_prd as dcla_prd,
    rsdt_psn_clct_detl_d.insutype_retr_flag as insutype_retr_flag,
    rsdt_psn_clct_detl_d.poolarea_no as poolarea_no,
    rsdt_psn_clct_detl_d.taxdept_code as taxdept_code,
    rsdt_psn_clct_detl_d.init_rsdt_clct_detl_id as init_rsdt_clct_detl_id,
    rsdt_psn_clct_detl_d.plan_bchno as plan_bchno,
    rsdt_psn_clct_detl_d.crte_optins_no as crte_optins_no,
    rsdt_psn_clct_detl_d.crter_id as crter_id,
    rsdt_psn_clct_detl_d.crter_name as crter_name,
    rsdt_psn_clct_detl_d.crte_time as crte_time,
    rsdt_psn_clct_detl_d.optins_no as optins_no,
    rsdt_psn_clct_detl_d.opter_id as opter_id,
    rsdt_psn_clct_detl_d.finsubs_amt as finsubs_amt,
    rsdt_psn_clct_detl_d.oth_clct_amt as oth_clct_amt,
    rsdt_psn_clct_detl_d.clct_sumamt as clct_sumamt,
    rsdt_psn_clct_detl_d.send_flag as send_flag,
    rsdt_psn_clct_detl_d.clct_rule_type_codg as clct_rule_type_codg,
    rsdt_psn_clct_detl_d.psn_clct_detl_id as psn_clct_detl_id,
    rsdt_psn_clct_detl_d.biz_date as biz_date,
    rsdt_psn_clct_detl_d.deleted_time as deleted_time,
    rsdt_psn_clct_detl_d.deleted as deleted,
    rsdt_psn_clct_detl_d.exch_updt_time as exch_updt_time,
    rsdt_psn_clct_detl_d.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.rsdt_psn_clct_detl_d as t
LEFT JOIN
(
SELECT
psn_info_b.psn_no,
psn_info_b.admdvs,
psn_info_b.crte_time,
ROW_NUMBER() OVER(PARTITION BY psn_info_b.psn_no ORDER BY psn_info_b.crte_time DESC) AS rn
FROM
psn_info_b
) AS RB
ON
rsdt_psn_clct_detl_d.psn_no = RB.psn_no AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table staf_psn_clct_detl_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    staf_psn_clct_detl_d.emp_clct_detl_id as emp_clct_detl_id,
    staf_psn_clct_detl_d.clct_bill_id as clct_bill_id,
    staf_psn_clct_detl_d.emp_no as emp_no,
    staf_psn_clct_detl_d.psn_no as psn_no,
    staf_psn_clct_detl_d.cashym as cashym,
    staf_psn_clct_detl_d.accrym_begn as accrym_begn,
    staf_psn_clct_detl_d.insutype as insutype,
    staf_psn_clct_detl_d.psn_type as psn_type,
    staf_psn_clct_detl_d.psn_idet_type as psn_idet_type,
    staf_psn_clct_detl_d.insu_idet as insu_idet,
    staf_psn_clct_detl_d.emp_clctstd as emp_clctstd,
    staf_psn_clct_detl_d.psn_clctstd as psn_clctstd,
    staf_psn_clct_detl_d.wag as wag,
    staf_psn_clct_detl_d.quot_clct_flag as quot_clct_flag,
    staf_psn_clct_detl_d.emp_clct_paraval as emp_clct_paraval,
    staf_psn_clct_detl_d.emp_clct_amt as emp_clct_amt,
    staf_psn_clct_detl_d.emp_clct_into_acct_amt as emp_clct_into_acct_amt,
    staf_psn_clct_detl_d.psn_clct_paraval as psn_clct_paraval,
    staf_psn_clct_detl_d.psn_into_paraval as psn_into_paraval,
    staf_psn_clct_detl_d.psn_clct_amt as psn_clct_amt,
    staf_psn_clct_detl_d.finsubs_traf_amt as finsubs_traf_amt,
    staf_psn_clct_detl_d.oth_clct_traf_amt as oth_clct_traf_amt,
    staf_psn_clct_detl_d.oth_clct_amt as oth_clct_amt,
    staf_psn_clct_detl_d.finsubs_amt as finsubs_amt,
    staf_psn_clct_detl_d.traf_sumamt as traf_sumamt,
    staf_psn_clct_detl_d.clct_sumamt as clct_sumamt,
    staf_psn_clct_detl_d.inte as inte,
    staf_psn_clct_detl_d.latefee as latefee,
    staf_psn_clct_detl_d.clct_flag as clct_flag,
    staf_psn_clct_detl_d.clct_time as clct_time,
    staf_psn_clct_detl_d.arvler as arvler,
    staf_psn_clct_detl_d.arvl_bchno as arvl_bchno,
    staf_psn_clct_detl_d.revs_flag as revs_flag,
    staf_psn_clct_detl_d.trafer as trafer,
    staf_psn_clct_detl_d.intsury_time as intsury_time,
    staf_psn_clct_detl_d.ursn_time as ursn_time,
    staf_psn_clct_detl_d.dcla_prd as dcla_prd,
    staf_psn_clct_detl_d.elec_taxrpt_no as elec_taxrpt_no,
    staf_psn_clct_detl_d.peawkr_flag as peawkr_flag,
    staf_psn_clct_detl_d.poolarea_no as poolarea_no,
    staf_psn_clct_detl_d.insutype_retr_flag as insutype_retr_flag,
    staf_psn_clct_detl_d.taxdept_code as taxdept_code,
    staf_psn_clct_detl_d.plan_bchno as plan_bchno,
    staf_psn_clct_detl_d.psn_insu_rlts_id as psn_insu_rlts_id,
    staf_psn_clct_detl_d.insu_clct_mons as insu_clct_mons,
    staf_psn_clct_detl_d.init_psn_no as init_psn_no,
    staf_psn_clct_detl_d.accter as accter,
    staf_psn_clct_detl_d.crte_optins_no as crte_optins_no,
    staf_psn_clct_detl_d.crter_id as crter_id,
    staf_psn_clct_detl_d.crter_name as crter_name,
    staf_psn_clct_detl_d.crte_time as crte_time,
    staf_psn_clct_detl_d.optins_no as optins_no,
    staf_psn_clct_detl_d.opter_id as opter_id,
    staf_psn_clct_detl_d.opter_name as opter_name,
    staf_psn_clct_detl_d.opt_time as opt_time,
    staf_psn_clct_detl_d.updt_time as updt_time,
    staf_psn_clct_detl_d.bill_flag as bill_flag,
    staf_psn_clct_detl_d.send_flag as send_flag,
    staf_psn_clct_detl_d.biz_date as biz_date,
    staf_psn_clct_detl_d.deleted_time as deleted_time,
    staf_psn_clct_detl_d.deleted as deleted,
    staf_psn_clct_detl_d.exch_updt_time as exch_updt_time,
    staf_psn_clct_detl_d.subsys_codg as subsys_codg,
    RB.admdvs as admdvs,
    staf_psn_clct_detl_d.clct_type as clct_type,
    staf_psn_clct_detl_d.clctstd_crtf_rule_codg as clctstd_crtf_rule_codg,
    staf_psn_clct_detl_d.psn_clct_into_acct_amt as psn_clct_into_acct_amt,
    staf_psn_clct_detl_d.clct_rule_type_codg as clct_rule_type_codg,
    staf_psn_clct_detl_d.accrym_end as accrym_end,
    staf_psn_clct_detl_d.traf_time as traf_time,
    staf_psn_clct_detl_d.traf_flag as traf_flag,
    staf_psn_clct_detl_d.emp_into_paraval as emp_into_paraval,
    staf_psn_clct_detl_d.init_psn_clct_detl_id as init_psn_clct_detl_id
from src_data.staf_psn_clct_detl_d as t
LEFT JOIN
(
SELECT
psn_info_b.psn_no,
psn_info_b.admdvs,
psn_info_b.crte_time,
ROW_NUMBER() OVER(PARTITION BY psn_info_b.psn_no ORDER BY psn_info_b.crte_time DESC) AS rn
FROM
psn_info_b
) AS RB
ON
rsdt_psn_clct_detl_d.psn_no = RB.psn_no AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table setl_diag_list_d partition(dt)
select 
    concat(biz_date,subsys_codg,rid) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    setl_diag_list_d.diag_name as diag_name,
    setl_diag_list_d.crter_name as crter_name,
    setl_diag_list_d.opt_time as opt_time,
    setl_diag_list_d.optins_no as optins_no,
    setl_diag_list_d.poolarea_no as poolarea_no,
    setl_diag_list_d.evt_inst_id as evt_inst_id,
    setl_diag_list_d.biz_date as biz_date,
    setl_diag_list_d.deleted_time as deleted_time,
    setl_diag_list_d.deleted as deleted,
    setl_diag_list_d.exch_updt_time as exch_updt_time,
    setl_diag_list_d.subsys_codg as subsys_codg,
    setl_diag_list_d.admdvs as admdvs,
    setl_diag_list_d.diag_info_id as diag_info_id,
    setl_diag_list_d.mdtrt_id as mdtrt_id,
    setl_diag_list_d.psn_no as psn_no,
    setl_diag_list_d.inout_diag_type as inout_diag_type,
    setl_diag_list_d.diag_type as diag_type,
    setl_diag_list_d.maindiag_flag as maindiag_flag,
    setl_diag_list_d.diag_srt_no as diag_srt_no,
    setl_diag_list_d.diag_code as diag_code,
    setl_diag_list_d.adm_cond as adm_cond,
    setl_diag_list_d.diag_dept as diag_dept,
    setl_diag_list_d.diag_dr_code as diag_dr_code,
    setl_diag_list_d.diag_dr_name as diag_dr_name,
    setl_diag_list_d.diag_time as diag_time,
    setl_diag_list_d.vali_flag as vali_flag,
    setl_diag_list_d.updt_time as updt_time,
    setl_diag_list_d.crter_id as crter_id,
    setl_diag_list_d.crte_time as crte_time,
    setl_diag_list_d.crte_optins_no as crte_optins_no,
    setl_diag_list_d.opter_id as opter_id,
    setl_diag_list_d.opter_name as opter_name
from src_data.setl_diag_list_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table rx_setl_info_d partition(dt)
select 
    concat(biz_date,subsys_codg,rx_setl_info_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    rx_setl_info_d.setl_id as setl_id,
    rx_setl_info_d.admdvs as admdvs,
    rx_setl_info_d.hi_rxno as hi_rxno,
    rx_setl_info_d.mdtrt_id as mdtrt_id,
    rx_setl_info_d.prsc_fixmedins_code as prsc_fixmedins_code,
    rx_setl_info_d.fixmedins_code as fixmedins_code,
    rx_setl_info_d.fixmedins_name as fixmedins_name,
    rx_setl_info_d.hsecfc as hsecfc,
    rx_setl_info_d.setl_time as setl_time,
    rx_setl_info_d.hi_feesetl_flag as hi_feesetl_flag,
    rx_setl_info_d.mmp_flag as mmp_flag,
    rx_setl_info_d.medfee_sumamt as medfee_sumamt,
    rx_setl_info_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    rx_setl_info_d.inscp_scp_amt as inscp_scp_amt,
    rx_setl_info_d.act_pay_dedc as act_pay_dedc,
    rx_setl_info_d.pool_prop_selfpay as pool_prop_selfpay,
    rx_setl_info_d.cvlserv_pay as cvlserv_pay,
    rx_setl_info_d.hifes_pay as hifes_pay,
    rx_setl_info_d.hifmi_pay as hifmi_pay,
    rx_setl_info_d.hifob_pay as hifob_pay,
    rx_setl_info_d.maf_pay as maf_pay,
    rx_setl_info_d.oth_pay as oth_pay,
    rx_setl_info_d.acct_mulaid_pay as acct_mulaid_pay,
    rx_setl_info_d.psn_cash_pay as psn_cash_pay,
    rx_setl_info_d.hi_sumfee as hi_sumfee,
    rx_setl_info_d.psn_part_amt as psn_part_amt,
    rx_setl_info_d.clr_type as clr_type,
    rx_setl_info_d.insutype as insutype,
    rx_setl_info_d.psn_no as psn_no,
    rx_setl_info_d.poolarea_no as poolarea_no,
    rx_setl_info_d.crte_time as crte_time,
    rx_setl_info_d.updt_time as updt_time,
    rx_setl_info_d.biz_date as biz_date,
    rx_setl_info_d.deleted_time as deleted_time,
    rx_setl_info_d.deleted as deleted,
    rx_setl_info_d.exch_updt_time as exch_updt_time,
    rx_setl_info_d.subsys_codg as subsys_codg,
    rx_setl_info_d.rx_setl_info_id as rx_setl_info_id,
    rx_setl_info_d.hifp_pay as hifp_pay,
    rx_setl_info_d.med_type as med_type,
    rx_setl_info_d.refd_setl_flag as refd_setl_flag,
    rx_setl_info_d.acct_pay as acct_pay,
    rx_setl_info_d.overlmt_selfpay as overlmt_selfpay,
    rx_setl_info_d.preselfpay_amt as preselfpay_amt,
    rx_setl_info_d.fund_pay_sumamt as fund_pay_sumamt,
    rx_setl_info_d.insu_plc_no as insu_plc_no,
    rx_setl_info_d.feedetl_sn as feedetl_sn,
    rx_setl_info_d.fixmedins_poolarea_no as fixmedins_poolarea_no
from src_data.rx_setl_info_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table rx_setl_drug_d partition(dt)
select 
    concat(biz_date,rx_setl_drug_id,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    rx_setl_drug_d.med_list_codg as med_list_codg,
    rx_setl_drug_d.admdvs as admdvs,
    rx_setl_drug_d.rx_setl_info_id as rx_setl_info_id,
    rx_setl_drug_d.medins_list_codg as medins_list_codg,
    rx_setl_drug_d.prsc_med_list_codg as prsc_med_list_codg,
    rx_setl_drug_d.hi_rxno as hi_rxno,
    rx_setl_drug_d.fixmedins_name as fixmedins_name,
    rx_setl_drug_d.drug_genname as drug_genname,
    rx_setl_drug_d.drug_spec as drug_spec,
    rx_setl_drug_d.drug_dosform as drug_dosform,
    rx_setl_drug_d.manu_lotnum as manu_lotnum,
    rx_setl_drug_d.prdr_name as prdr_name,
    rx_setl_drug_d.bchno as bchno,
    rx_setl_drug_d.cnt as cnt,
    rx_setl_drug_d.drug_emp as drug_emp,
    rx_setl_drug_d.det_item_fee_sumamt as det_item_fee_sumamt,
    rx_setl_drug_d.vali_flag as vali_flag,
    rx_setl_drug_d.crte_time as crte_time,
    rx_setl_drug_d.updt_time as updt_time,
    rx_setl_drug_d.biz_date as biz_date,
    rx_setl_drug_d.deleted_time as deleted_time,
    rx_setl_drug_d.deleted as deleted,
    rx_setl_drug_d.exch_updt_time as exch_updt_time,
    rx_setl_drug_d.rx_setl_drug_id as rx_setl_drug_id,
    rx_setl_drug_d.pric as pric,
    rx_setl_drug_d.aprvno as aprvno,
    rx_setl_drug_d.drug_prodname as drug_prodname,
    rx_setl_drug_d.fixmedins_code as fixmedins_code,
    rx_setl_drug_d.subsys_codg as subsys_codg
from src_data.rx_setl_drug_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table rx_indx_info_b partition(dt)
select 
    concat(biz_date,subsys_codg,rx_indx_info_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    rx_indx_info_b.admdvs as admdvs,
    rx_indx_info_b.hi_rxno as hi_rxno,
    rx_indx_info_b.hsecfc as hsecfc,
    rx_indx_info_b.rx_trace_code as rx_trace_code,
    rx_indx_info_b.cert_type as cert_type,
    rx_indx_info_b.psn_name as psn_name,
    rx_indx_info_b.med_type as med_type,
    rx_indx_info_b.rx_stas_codg as rx_stas_codg,
    rx_indx_info_b.rx_stas_name as rx_stas_name,
    rx_indx_info_b.rx_used_stas_codg as rx_used_stas_codg,
    rx_indx_info_b.rx_used_stas_name as rx_used_stas_name,
    rx_indx_info_b.rx_chk_stas_codg as rx_chk_stas_codg,
    rx_indx_info_b.rx_chk_stas_name as rx_chk_stas_name,
    rx_indx_info_b.fixmedins_code as fixmedins_code,
    rx_indx_info_b.fixmedins_name as fixmedins_name,
    rx_indx_info_b.chk_fixmedins_code as chk_fixmedins_code,
    rx_indx_info_b.chk_fixmedins_name as chk_fixmedins_name,
    rx_indx_info_b.setl_fixmedins_code as setl_fixmedins_code,
    rx_indx_info_b.setl_fixmedins_name as setl_fixmedins_name,
    rx_indx_info_b.prsc_dept_name as prsc_dept_name,
    rx_indx_info_b.prsc_dept_code as prsc_dept_code,
    rx_indx_info_b.prsc_dr_name as prsc_dr_name,
    rx_indx_info_b.maindiag_code as maindiag_code,
    rx_indx_info_b.maindiag_name as maindiag_name,
    rx_indx_info_b.mdtrt_time as mdtrt_time,
    rx_indx_info_b.prsc_time as prsc_time,
    rx_indx_info_b.rx_used_time as rx_used_time,
    rx_indx_info_b.rx_chk_time as rx_chk_time,
    rx_indx_info_b.insu_plc_no as insu_plc_no,
    rx_indx_info_b.phac_poolarea_no as phac_poolarea_no,
    rx_indx_info_b.setl_insuplc_no as setl_insuplc_no,
    rx_indx_info_b.souc_poolarea_no as souc_poolarea_no,
    rx_indx_info_b.crte_time as crte_time,
    rx_indx_info_b.updt_time as updt_time,
    rx_indx_info_b.biz_date as biz_date,
    rx_indx_info_b.deleted_time as deleted_time,
    rx_indx_info_b.deleted as deleted,
    rx_indx_info_b.exch_updt_time as exch_updt_time,
    rx_indx_info_b.subsys_codg as subsys_codg,
    rx_indx_info_b.rx_indx_info_id as rx_indx_info_id,
    rx_indx_info_b.out_rx_flag as out_rx_flag,
    rx_indx_info_b.vali_end_time as vali_end_time,
    rx_indx_info_b.certno as certno,
    rx_indx_info_b.rx_hsecfc as rx_hsecfc,
    rx_indx_info_b.hosp_poolarea_no as hosp_poolarea_no,
    rx_indx_info_b.poolarea_no as poolarea_no
from src_data.rx_indx_info_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table dip_fund_setl_ext_d partition(dt)
select 
    concat(psn_no,fund_setl_info_id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    dip_fund_setl_ext_d.psn_no as psn_no,
    dip_fund_setl_ext_d.fund_setl_info_id as fund_setl_info_id,
    dip_fund_setl_ext_d.setl_id as setl_id,
    dip_fund_setl_ext_d.fixmedins_code as fixmedins_code,
    dip_fund_setl_ext_d.fixmedins_name as fixmedins_name,
    dip_fund_setl_ext_d.fixmedins_lv as fixmedins_lv,
    dip_fund_setl_ext_d.grp_diag_code as grp_diag_code,
    dip_fund_setl_ext_d.grp_diag_name as grp_diag_name,
    dip_fund_setl_ext_d.schm_detl_id as schm_detl_id,
    dip_fund_setl_ext_d.est_schm_detl_list_id as est_schm_detl_list_id,
    dip_fund_setl_ext_d.grp_dise_sco as grp_dise_sco,
    dip_fund_setl_ext_d.act_setl_sco as act_setl_sco,
    dip_fund_setl_ext_d.pre_setl_pt_val as pre_setl_pt_val,
    dip_fund_setl_ext_d.pay_std as pay_std,
    dip_fund_setl_ext_d.insutype as insutype,
    dip_fund_setl_ext_d.medfee_sumamt as medfee_sumamt,
    dip_fund_setl_ext_d.hifp_pay as hifp_pay,
    dip_fund_setl_ext_d.hifob_pay as hifob_pay,
    dip_fund_setl_ext_d.hifmi_pay as hifmi_pay,
    dip_fund_setl_ext_d.cvlserv_pay as cvlserv_pay,
    dip_fund_setl_ext_d.dip_hifp_pay as dip_hifp_pay,
    dip_fund_setl_ext_d.dip_hifmi_pay as dip_hifmi_pay,
    dip_fund_setl_ext_d.dip_cvlserv_pay as dip_cvlserv_pay,
    dip_fund_setl_ext_d.dip_maf_pay as dip_maf_pay,
    dip_fund_setl_ext_d.psn_selfpay as psn_selfpay,
    dip_fund_setl_ext_d.dedc_std as dedc_std,
    dip_fund_setl_ext_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    dip_fund_setl_ext_d.inscp_amt as inscp_amt,
    dip_fund_setl_ext_d.fund_pay_sumamt as fund_pay_sumamt,
    dip_fund_setl_ext_d.setl_end_date as setl_end_date,
    dip_fund_setl_ext_d.grp_flag as grp_flag,
    dip_fund_setl_ext_d.norm_flag as norm_flag,
    dip_fund_setl_ext_d.dise_list_type as dise_list_type,
    dip_fund_setl_ext_d.dise_sco as dise_sco,
    dip_fund_setl_ext_d.drug_sco as drug_sco,
    dip_fund_setl_ext_d.mcs_sco as mcs_sco,
    dip_fund_setl_ext_d.oprn_grp_code as oprn_grp_code,
    dip_fund_setl_ext_d.oprn_grp_name as oprn_grp_name,
    dip_fund_setl_ext_d.sco_schm_id as sco_schm_id,
    dip_fund_setl_ext_d.dept_cof as dept_cof,
    dip_fund_setl_ext_d.dise_lv_cof as dise_lv_cof,
    dip_fund_setl_ext_d.fixmedins_cof as fixmedins_cof,
    dip_fund_setl_ext_d.updt_cof as updt_cof,
    dip_fund_setl_ext_d.oprn_lv as oprn_lv,
    dip_fund_setl_ext_d.vali_flag as vali_flag,
    dip_fund_setl_ext_d.remarks as remarks,
    dip_fund_setl_ext_d.dyna_remarks as dyna_remarks,
    dip_fund_setl_ext_d.crte_optins_no as crte_optins_no,
    dip_fund_setl_ext_d.crter_id as crter_id,
    dip_fund_setl_ext_d.crter_name as crter_name,
    dip_fund_setl_ext_d.crte_time as crte_time,
    dip_fund_setl_ext_d.updt_time as updt_time,
    dip_fund_setl_ext_d.opter_name as opter_name,
    dip_fund_setl_ext_d.opt_time as opt_time,
    dip_fund_setl_ext_d.optins_no as optins_no,
    dip_fund_setl_ext_d.admdvs as admdvs,
    dip_fund_setl_ext_d.insu_admdvs as insu_admdvs,
    dip_fund_setl_ext_d.chk_type as chk_type,
    dip_fund_setl_ext_d.dip_dise_grp_code as dip_dise_grp_code,
    dip_fund_setl_ext_d.dip_dise_grp_name as dip_dise_grp_name,
    dip_fund_setl_ext_d.tcm_adt_dise_used_flag as tcm_adt_dise_used_flag,
    dip_fund_setl_ext_d.tcm_adt_dise_name as tcm_adt_dise_name,
    dip_fund_setl_ext_d.tcm_adt_dise_cof as tcm_adt_dise_cof,
    dip_fund_setl_ext_d.sco_cof as sco_cof,
    dip_fund_setl_ext_d.sco_stas as sco_stas,
    dip_fund_setl_ext_d.med_type as med_type,
    dip_fund_setl_ext_d.clr_type as clr_type,
    dip_fund_setl_ext_d.cvlserv_flag as cvlserv_flag,
    dip_fund_setl_ext_d.sp_psn_type as sp_psn_type,
    dip_fund_setl_ext_d.emp_type as emp_type,
    dip_fund_setl_ext_d.econ_type as econ_type,
    dip_fund_setl_ext_d.afil_indu as afil_indu,
    dip_fund_setl_ext_d.afil_rlts as afil_rlts,
    dip_fund_setl_ext_d.clr_way as clr_way,
    dip_fund_setl_ext_d.sp_markup_sco as sp_markup_sco,
    dip_fund_setl_ext_d.sp_item_markup as sp_item_markup,
    dip_fund_setl_ext_d.asst_list_flag as asst_list_flag,
    dip_fund_setl_ext_d.asst_list_detl as asst_list_detl,
    dip_fund_setl_ext_d.pay_loc as pay_loc,
    dip_fund_setl_ext_d.biz_date as biz_date,
    dip_fund_setl_ext_d.deleted_time as deleted_time,
    dip_fund_setl_ext_d.deleted as deleted,
    dip_fund_setl_ext_d.exch_updt_time as exch_updt_time,
    dip_fund_setl_ext_d.clr_optins as clr_optins,
    dip_fund_setl_ext_d.oprn_cof as oprn_cof,
    dip_fund_setl_ext_d.emp_mgt_type as emp_mgt_type,
    dip_fund_setl_ext_d.subsys_codg as subsys_codg,
    dip_fund_setl_ext_d.dise_list_name as dise_list_name,
    dip_fund_setl_ext_d.tcm_dise_flag as tcm_dise_flag,
    dip_fund_setl_ext_d.opter_id as opter_id,
    dip_fund_setl_ext_d.nuld_flag as nuld_flag,
    dip_fund_setl_ext_d.act_clr_ym as act_clr_ym,
    dip_fund_setl_ext_d.dise_list_code as dise_list_code,
    dip_fund_setl_ext_d.tcm_dise_cof as tcm_dise_cof,
    dip_fund_setl_ext_d.dip_hifob_pay as dip_hifob_pay,
    dip_fund_setl_ext_d.mon_pre_pay_info_id as mon_pre_pay_info_id,
    dip_fund_setl_ext_d.dip_hifdm_pay as dip_hifdm_pay,
    dip_fund_setl_ext_d.dip_hifes_pay as dip_hifes_pay,
    dip_fund_setl_ext_d.tcm_adt_dise_code as tcm_adt_dise_code,
    dip_fund_setl_ext_d.othfund_pay as othfund_pay,
    dip_fund_setl_ext_d.psn_type as psn_type
from src_data.dip_fund_setl_ext_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table bydise_setl_reg_d partition(dt)
select 
    concat(trt_dcla_detl_sn,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    bydise_setl_reg_d.appy_date as appy_date,
    bydise_setl_reg_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    bydise_setl_reg_d.bydise_setl_dise_name as bydise_setl_dise_name,
    bydise_setl_reg_d.memo as memo,
    bydise_setl_reg_d.opt_time as opt_time,
    bydise_setl_reg_d.biz_date as biz_date,
    bydise_setl_reg_d.insutype as insutype,
    bydise_setl_reg_d.dcla_souc as dcla_souc,
    bydise_setl_reg_d.psn_no as psn_no,
    bydise_setl_reg_d.psn_insu_rlts_id as psn_insu_rlts_id,
    bydise_setl_reg_d.psn_cert_type as psn_cert_type,
    bydise_setl_reg_d.certno as certno,
    bydise_setl_reg_d.psn_name as psn_name,
    bydise_setl_reg_d.gend as gend,
    bydise_setl_reg_d.naty as naty,
    bydise_setl_reg_d.brdy as brdy,
    bydise_setl_reg_d.tel as tel,
    bydise_setl_reg_d.addr as addr,
    bydise_setl_reg_d.insu_admdvs as insu_admdvs,
    bydise_setl_reg_d.emp_no as emp_no,
    bydise_setl_reg_d.emp_name as emp_name,
    bydise_setl_reg_d.bydise_setl_list_code as bydise_setl_list_code,
    bydise_setl_reg_d.oprn_oprt_code as oprn_oprt_code,
    bydise_setl_reg_d.oprn_oprt_name as oprn_oprt_name,
    bydise_setl_reg_d.fixmedins_code as fixmedins_code,
    bydise_setl_reg_d.fixmedins_name as fixmedins_name,
    bydise_setl_reg_d.hosp_lv as hosp_lv,
    bydise_setl_reg_d.fix_blng_admdvs as fix_blng_admdvs,
    bydise_setl_reg_d.appy_rea as appy_rea,
    bydise_setl_reg_d.agnter_name as agnter_name,
    bydise_setl_reg_d.agnter_cert_type as agnter_cert_type,
    bydise_setl_reg_d.agnter_certno as agnter_certno,
    bydise_setl_reg_d.agnter_tel as agnter_tel,
    bydise_setl_reg_d.agnter_addr as agnter_addr,
    bydise_setl_reg_d.agnter_rlts as agnter_rlts,
    bydise_setl_reg_d.begndate as begndate,
    bydise_setl_reg_d.enddate as enddate,
    bydise_setl_reg_d.vali_flag as vali_flag,
    bydise_setl_reg_d.updt_time as updt_time,
    bydise_setl_reg_d.crter_id as crter_id,
    bydise_setl_reg_d.crter_name as crter_name,
    bydise_setl_reg_d.crte_time as crte_time,
    bydise_setl_reg_d.crte_optins_no as crte_optins_no,
    bydise_setl_reg_d.opter_id as opter_id,
    bydise_setl_reg_d.opter_name as opter_name,
    bydise_setl_reg_d.optins_no as optins_no,
    bydise_setl_reg_d.poolarea_no as poolarea_no,
    bydise_setl_reg_d.deleted_time as deleted_time,
    bydise_setl_reg_d.deleted as deleted,
    bydise_setl_reg_d.exch_updt_time as exch_updt_time,
    bydise_setl_reg_d.subsys_codg as subsys_codg,
    bydise_setl_reg_d.insu_admdvs as admdvs
from src_data.bydise_setl_reg_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table setl_d partition(dt)
select 
    concat(subsys_codg,rid,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    setl_d.mdtrt_id as mdtrt_id,
    setl_d.dise_name as dise_name,
    setl_d.subsys_codg as subsys_codg,
    setl_d.insu_admdvs as admdvs,
    setl_d.medins_setl_id as medins_setl_id,
    setl_d.othfund_pay as othfund_pay,
    setl_d.manl_reim_rea as manl_reim_rea,
    setl_d.clr_optins as clr_optins,
    setl_d.psn_type as psn_type,
    setl_d.mdtrt_cert_type as mdtrt_cert_type,
    setl_d.cvlserv_pay as cvlserv_pay,
    setl_d.poolarea_no as poolarea_no,
    setl_d.econ_type as econ_type,
    setl_d.afil_rlts as afil_rlts,
    setl_d.setl_type as setl_type,
    setl_d.medfee_sumamt as medfee_sumamt,
    setl_d.refd_setl_flag as refd_setl_flag,
    setl_d.setl_id as setl_id,
    setl_d.init_setl_id as init_setl_id,
    setl_d.psn_no as psn_no,
    setl_d.psn_insu_rlts_id as psn_insu_rlts_id,
    setl_d.psn_name as psn_name,
    setl_d.psn_cert_type as psn_cert_type,
    setl_d.certno as certno,
    setl_d.gend as gend,
    setl_d.naty as naty,
    setl_d.brdy as brdy,
    setl_d.age as age,
    setl_d.insutype as insutype,
    setl_d.cvlserv_flag as cvlserv_flag,
    setl_d.cvlserv_lv as cvlserv_lv,
    setl_d.sp_psn_type as sp_psn_type,
    setl_d.sp_psn_type_lv as sp_psn_type_lv,
    setl_d.clct_grde as clct_grde,
    setl_d.flxempe_flag as flxempe_flag,
    setl_d.nwb_flag as nwb_flag,
    setl_d.insu_admdvs as insu_admdvs,
    setl_d.emp_no as emp_no,
    setl_d.emp_name as emp_name,
    setl_d.emp_type as emp_type,
    setl_d.afil_indu as afil_indu,
    setl_d.emp_mgt_type as emp_mgt_type,
    setl_d.pay_loc as pay_loc,
    setl_d.fixmedins_code as fixmedins_code,
    setl_d.fixmedins_name as fixmedins_name,
    setl_d.hosp_lv as hosp_lv,
    setl_d.fix_blng_admdvs as fix_blng_admdvs,
    setl_d.lmtpric_hosp_lv as lmtpric_hosp_lv,
    setl_d.setl_time as setl_time,
    setl_d.dedc_hosp_lv as dedc_hosp_lv,
    setl_d.begndate as begndate,
    setl_d.enddate as enddate,
    setl_d.mdtrt_cert_no as mdtrt_cert_no,
    setl_d.med_type as med_type,
    setl_d.clr_type as clr_type,
    setl_d.clr_way as clr_way,
    setl_d.psn_setlway as psn_setlway,
    setl_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    setl_d.overlmt_selfpay as overlmt_selfpay,
    setl_d.preselfpay_amt as preselfpay_amt,
    setl_d.inscp_amt as inscp_amt,
    setl_d.dedc_std as dedc_std,
    setl_d.crt_dedc as crt_dedc,
    setl_d.act_pay_dedc as act_pay_dedc,
    setl_d.hifp_pay as hifp_pay,
    setl_d.pool_prop_selfpay as pool_prop_selfpay,
    setl_d.hi_agre_sumfee as hi_agre_sumfee,
    setl_d.hifes_pay as hifes_pay,
    setl_d.hifmi_pay as hifmi_pay,
    setl_d.hifob_pay as hifob_pay,
    setl_d.hifdm_pay as hifdm_pay,
    setl_d.maf_pay as maf_pay,
    setl_d.fund_pay_sumamt as fund_pay_sumamt,
    setl_d.psn_pay as psn_pay,
    setl_d.acct_pay as acct_pay,
    setl_d.cash_payamt as cash_payamt,
    setl_d.ownpay_hosp_part as ownpay_hosp_part,
    setl_d.balc as balc,
    setl_d.acct_mulaid_pay as acct_mulaid_pay,
    setl_d.cal_ipt_cnt as cal_ipt_cnt,
    setl_d.setl_cashpay_way as setl_cashpay_way,
    setl_d.year as year,
    setl_d.dise_no as dise_no,
    setl_d.invono as invono,
    setl_d.vali_flag as vali_flag,
    setl_d.memo as memo,
    setl_d.updt_time as updt_time,
    setl_d.crter_id as crter_id,
    setl_d.crter_name as crter_name,
    setl_d.crte_time as crte_time,
    setl_d.crte_optins_no as crte_optins_no,
    setl_d.opter_id as opter_id,
    setl_d.opter_name as opter_name,
    setl_d.opt_time as opt_time,
    setl_d.optins_no as optins_no,
    setl_d.mid_setl_flag as mid_setl_flag,
    setl_d.acct_used_flag as acct_used_flag,
    setl_d.quts_type as quts_type,
    setl_d.bydise_setl_payamt as bydise_setl_payamt,
    setl_d.exct_item_fund_payamt as exct_item_fund_payamt,
    setl_d.biz_date as biz_date,
    setl_d.deleted_time as deleted_time,
    setl_d.deleted as deleted,
    setl_d.exch_updt_time as exch_updt_time
from src_data.setl_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table mdtrt_d partition(dt)
select 
    concat(subsys_codg,rid,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    mdtrt_d.age as age,
    mdtrt_d.lmtpric_hosp_lv as lmtpric_hosp_lv,
    mdtrt_d.geso_val as geso_val,
    mdtrt_d.birctrl_type as birctrl_type,
    mdtrt_d.quts_type as quts_type,
    mdtrt_d.psn_name as psn_name,
    mdtrt_d.sp_psn_type as sp_psn_type,
    mdtrt_d.pre_pay_flag as pre_pay_flag,
    mdtrt_d.opter_name as opter_name,
    mdtrt_d.subsys_codg as subsys_codg,
    mdtrt_d.insu_admdvs as admdvs,
    mdtrt_d.psn_insu_rlts_id as psn_insu_rlts_id,
    mdtrt_d.crter_id as crter_id,
    mdtrt_d.psn_type as psn_type,
    mdtrt_d.inhosp_stas as inhosp_stas,
    mdtrt_d.opt_time as opt_time,
    mdtrt_d.certno as certno,
    mdtrt_d.brdy as brdy,
    mdtrt_d.hosp_lv as hosp_lv,
    mdtrt_d.oprn_oprt_name as oprn_oprt_name,
    mdtrt_d.mdtrt_id as mdtrt_id,
    mdtrt_d.medins_setl_id as medins_setl_id,
    mdtrt_d.psn_no as psn_no,
    mdtrt_d.psn_cert_type as psn_cert_type,
    mdtrt_d.gend as gend,
    mdtrt_d.naty as naty,
    mdtrt_d.coner_name as coner_name,
    mdtrt_d.tel as tel,
    mdtrt_d.addr as addr,
    mdtrt_d.insutype as insutype,
    mdtrt_d.cvlserv_flag as cvlserv_flag,
    mdtrt_d.cvlserv_lv as cvlserv_lv,
    mdtrt_d.sp_psn_type_lv as sp_psn_type_lv,
    mdtrt_d.clct_grde as clct_grde,
    mdtrt_d.flxempe_flag as flxempe_flag,
    mdtrt_d.nwb_flag as nwb_flag,
    mdtrt_d.insu_admdvs as insu_admdvs,
    mdtrt_d.emp_no as emp_no,
    mdtrt_d.emp_name as emp_name,
    mdtrt_d.emp_type as emp_type,
    mdtrt_d.econ_type as econ_type,
    mdtrt_d.afil_indu as afil_indu,
    mdtrt_d.afil_rlts as afil_rlts,
    mdtrt_d.emp_mgt_type as emp_mgt_type,
    mdtrt_d.pay_loc as pay_loc,
    mdtrt_d.fixmedins_code as fixmedins_code,
    mdtrt_d.fixmedins_name as fixmedins_name,
    mdtrt_d.fix_blng_admdvs as fix_blng_admdvs,
    mdtrt_d.mdtrt_cert_type as mdtrt_cert_type,
    mdtrt_d.dedc_hosp_lv as dedc_hosp_lv,
    mdtrt_d.begntime as begntime,
    mdtrt_d.endtime as endtime,
    mdtrt_d.mdtrt_cert_no as mdtrt_cert_no,
    mdtrt_d.med_type as med_type,
    mdtrt_d.rloc_type as rloc_type,
    mdtrt_d.ars_year_ipt_flag as ars_year_ipt_flag,
    mdtrt_d.year as year,
    mdtrt_d.refl_old_mdtrt_id as refl_old_mdtrt_id,
    mdtrt_d.ipt_otp_no as ipt_otp_no,
    mdtrt_d.medrcdno as medrcdno,
    mdtrt_d.chfpdr_code as chfpdr_code,
    mdtrt_d.adm_diag_dscr as adm_diag_dscr,
    mdtrt_d.adm_dept_codg as adm_dept_codg,
    mdtrt_d.adm_dept_name as adm_dept_name,
    mdtrt_d.adm_bed as adm_bed,
    mdtrt_d.wardarea_bed as wardarea_bed,
    mdtrt_d.traf_dept_flag as traf_dept_flag,
    mdtrt_d.dscg_maindiag_code as dscg_maindiag_code,
    mdtrt_d.dscg_dept_codg as dscg_dept_codg,
    mdtrt_d.dscg_dept_name as dscg_dept_name,
    mdtrt_d.dscg_bed as dscg_bed,
    mdtrt_d.dscg_way as dscg_way,
    mdtrt_d.main_cond_dscr as main_cond_dscr,
    mdtrt_d.dise_no as dise_no,
    mdtrt_d.dise_name as dise_name,
    mdtrt_d.oprn_oprt_code as oprn_oprt_code,
    mdtrt_d.otp_diag_info as otp_diag_info,
    mdtrt_d.die_date as die_date,
    mdtrt_d.ipt_days as ipt_days,
    mdtrt_d.fetts as fetts,
    mdtrt_d.fetus_cnt as fetus_cnt,
    mdtrt_d.matn_type as matn_type,
    mdtrt_d.prey_time as prey_time,
    mdtrt_d.latechb_flag as latechb_flag,
    mdtrt_d.pret_flag as pret_flag,
    mdtrt_d.fpsc_no as fpsc_no,
    mdtrt_d.birctrl_matn_date as birctrl_matn_date,
    mdtrt_d.cop_flag as cop_flag,
    mdtrt_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    mdtrt_d.vali_flag as vali_flag,
    mdtrt_d.memo as memo,
    mdtrt_d.updt_time as updt_time,
    mdtrt_d.crter_name as crter_name,
    mdtrt_d.crte_time as crte_time,
    mdtrt_d.crte_optins_no as crte_optins_no,
    mdtrt_d.opter_id as opter_id,
    mdtrt_d.optins_no as optins_no,
    mdtrt_d.poolarea_no as poolarea_no,
    mdtrt_d.chfpdr_name as chfpdr_name,
    mdtrt_d.dscg_maindiag_name as dscg_maindiag_name,
    mdtrt_d.adm_caty as adm_caty,
    mdtrt_d.dscg_caty as dscg_caty,
    mdtrt_d.ttp_pay_flag as ttp_pay_flag,
    mdtrt_d.ttp_pay_prop as ttp_pay_prop,
    mdtrt_d.dise_type_code as dise_type_code,
    mdtrt_d.same_dise_adm_flag as same_dise_adm_flag,
    mdtrt_d.biz_date as biz_date,
    mdtrt_d.deleted_time as deleted_time,
    mdtrt_d.deleted as deleted,
    mdtrt_d.exch_updt_time as exch_updt_time
from src_data.mdtrt_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table opsp_reg_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    opsp_reg_d.appy_date as appy_date,
    opsp_reg_d.appy_rea as appy_rea,
    opsp_reg_d.agnter_name as agnter_name,
    opsp_reg_d.agnter_cert_type as agnter_cert_type,
    opsp_reg_d.agnter_certno as agnter_certno,
    opsp_reg_d.agnter_tel as agnter_tel,
    opsp_reg_d.agnter_addr as agnter_addr,
    opsp_reg_d.agnter_rlts as agnter_rlts,
    opsp_reg_d.enddate as enddate,
    opsp_reg_d.vali_flag as vali_flag,
    opsp_reg_d.updt_time as updt_time,
    opsp_reg_d.crter_id as crter_id,
    opsp_reg_d.crter_name as crter_name,
    opsp_reg_d.crte_time as crte_time,
    opsp_reg_d.crte_optins_no as crte_optins_no,
    opsp_reg_d.opter_id as opter_id,
    opsp_reg_d.opt_time as opt_time,
    opsp_reg_d.poolarea_no as poolarea_no,
    opsp_reg_d.selfdef_splm_flag as selfdef_splm_flag,
    opsp_reg_d.biz_date as biz_date,
    opsp_reg_d.deleted_time as deleted_time,
    opsp_reg_d.deleted as deleted,
    opsp_reg_d.exch_updt_time as exch_updt_time,
    opsp_reg_d.subsys_codg as subsys_codg,
    opsp_reg_d.insu_admdvs as admdvs,
    opsp_reg_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    opsp_reg_d.psn_no as psn_no,
    opsp_reg_d.opsp_dise_code as opsp_dise_code,
    opsp_reg_d.begndate as begndate,
    opsp_reg_d.dcla_souc as dcla_souc,
    opsp_reg_d.insutype as insutype,
    opsp_reg_d.psn_insu_rlts_id as psn_insu_rlts_id,
    opsp_reg_d.dise_type_code as dise_type_code,
    opsp_reg_d.opsp_dise_name as opsp_dise_name,
    opsp_reg_d.certno as certno,
    opsp_reg_d.psn_name as psn_name,
    opsp_reg_d.gend as gend,
    opsp_reg_d.tel as tel,
    opsp_reg_d.addr as addr,
    opsp_reg_d.insu_admdvs as insu_admdvs,
    opsp_reg_d.emp_no as emp_no,
    opsp_reg_d.ide_fixmedins_no as ide_fixmedins_no,
    opsp_reg_d.ide_fixmedins_name as ide_fixmedins_name,
    opsp_reg_d.hosp_ide_date as hosp_ide_date,
    opsp_reg_d.diag_dr_name as diag_dr_name,
    opsp_reg_d.emp_name as emp_name,
    opsp_reg_d.opter_name as opter_name,
    opsp_reg_d.optins_no as optins_no,
    opsp_reg_d.psn_cert_type as psn_cert_type,
    opsp_reg_d.brdy as brdy,
    opsp_reg_d.diag_dr_code as diag_dr_code,
    opsp_reg_d.memo as memo,
    opsp_reg_d.naty as naty
from src_data.opsp_reg_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table trum_chk_reg_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trum_chk_reg_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    trum_chk_reg_d.certno as certno,
    trum_chk_reg_d.biz_used_flag as biz_used_flag,
    trum_chk_reg_d.updt_time as updt_time,
    trum_chk_reg_d.insutype as insutype,
    trum_chk_reg_d.emp_name as emp_name,
    trum_chk_reg_d.agnter_addr as agnter_addr,
    trum_chk_reg_d.chk_pay_flag as chk_pay_flag,
    trum_chk_reg_d.dcla_souc as dcla_souc,
    trum_chk_reg_d.psn_no as psn_no,
    trum_chk_reg_d.psn_insu_rlts_id as psn_insu_rlts_id,
    trum_chk_reg_d.begndate as begndate,
    trum_chk_reg_d.enddate as enddate,
    trum_chk_reg_d.psn_cert_type as psn_cert_type,
    trum_chk_reg_d.psn_name as psn_name,
    trum_chk_reg_d.gend as gend,
    trum_chk_reg_d.addr as addr,
    trum_chk_reg_d.naty as naty,
    trum_chk_reg_d.brdy as brdy,
    trum_chk_reg_d.tel as tel,
    trum_chk_reg_d.insu_admdvs as insu_admdvs,
    trum_chk_reg_d.emp_no as emp_no,
    trum_chk_reg_d.mdtrt_id as mdtrt_id,
    trum_chk_reg_d.setl_id as setl_id,
    trum_chk_reg_d.fixmedins_code as fixmedins_code,
    trum_chk_reg_d.fixmedins_name as fixmedins_name,
    trum_chk_reg_d.hosp_lv as hosp_lv,
    trum_chk_reg_d.fix_blng_admdvs as fix_blng_admdvs,
    trum_chk_reg_d.trum_part as trum_part,
    trum_chk_reg_d.trum_time as trum_time,
    trum_chk_reg_d.trum_site as trum_site,
    trum_chk_reg_d.trum_rea as trum_rea,
    trum_chk_reg_d.adm_mtd as adm_mtd,
    trum_chk_reg_d.adm_time as adm_time,
    trum_chk_reg_d.adm_diag_dscr as adm_diag_dscr,
    trum_chk_reg_d.agnter_name as agnter_name,
    trum_chk_reg_d.agnter_cert_type as agnter_cert_type,
    trum_chk_reg_d.agnter_certno as agnter_certno,
    trum_chk_reg_d.agnter_tel as agnter_tel,
    trum_chk_reg_d.agnter_rlts as agnter_rlts,
    trum_chk_reg_d.vali_flag as vali_flag,
    trum_chk_reg_d.memo as memo,
    trum_chk_reg_d.crter_id as crter_id,
    trum_chk_reg_d.crter_name as crter_name,
    trum_chk_reg_d.crte_time as crte_time,
    trum_chk_reg_d.crte_optins_no as crte_optins_no,
    trum_chk_reg_d.opter_id as opter_id,
    trum_chk_reg_d.opter_name as opter_name,
    trum_chk_reg_d.opt_time as opt_time,
    trum_chk_reg_d.optins_no as optins_no,
    trum_chk_reg_d.poolarea_no as poolarea_no,
    trum_chk_reg_d.ttp_pay_prop as ttp_pay_prop,
    trum_chk_reg_d.biz_date as biz_date,
    trum_chk_reg_d.deleted_time as deleted_time,
    trum_chk_reg_d.deleted as deleted,
    trum_chk_reg_d.exch_updt_time as exch_updt_time,
    trum_chk_reg_d.subsys_codg as subsys_codg,
    trum_chk_reg_d.insu_admdvs as admdvs
from src_data.trum_chk_reg_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table refl_appy_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    refl_appy_d.tel as tel,
    refl_appy_d.agnter_name as agnter_name,
    refl_appy_d.agnter_addr as agnter_addr,
    refl_appy_d.certno as certno,
    refl_appy_d.emp_name as emp_name,
    refl_appy_d.refl_rea as refl_rea,
    refl_appy_d.dcla_souc as dcla_souc,
    refl_appy_d.hosp_lv as hosp_lv,
    refl_appy_d.diag_name as diag_name,
    refl_appy_d.mdtrtarea_admdvs as mdtrtarea_admdvs,
    refl_appy_d.refl_opnn as refl_opnn,
    refl_appy_d.opter_name as opter_name,
    refl_appy_d.psn_name as psn_name,
    refl_appy_d.brdy as brdy,
    refl_appy_d.refl_setl_flag as refl_setl_flag,
    refl_appy_d.exch_updt_time as exch_updt_time,
    refl_appy_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    refl_appy_d.insutype as insutype,
    refl_appy_d.psn_no as psn_no,
    refl_appy_d.psn_insu_rlts_id as psn_insu_rlts_id,
    refl_appy_d.psn_cert_type as psn_cert_type,
    refl_appy_d.gend as gend,
    refl_appy_d.addr as addr,
    refl_appy_d.insu_admdvs as insu_admdvs,
    refl_appy_d.emp_no as emp_no,
    refl_appy_d.fix_blng_admdvs as fix_blng_admdvs,
    refl_appy_d.fixmedins_code as fixmedins_code,
    refl_appy_d.fixmedins_name as fixmedins_name,
    refl_appy_d.diag_code as diag_code,
    refl_appy_d.drord as drord,
    refl_appy_d.dise_cond_dscr as dise_cond_dscr,
    refl_appy_d.reflin_medins_no as reflin_medins_no,
    refl_appy_d.reflin_medins_name as reflin_medins_name,
    refl_appy_d.out_flag as out_flag,
    refl_appy_d.refl_date as refl_date,
    refl_appy_d.agnter_cert_type as agnter_cert_type,
    refl_appy_d.agnter_certno as agnter_certno,
    refl_appy_d.agnter_tel as agnter_tel,
    refl_appy_d.agnter_rlts as agnter_rlts,
    refl_appy_d.begndate as begndate,
    refl_appy_d.enddate as enddate,
    refl_appy_d.refl_old_mdtrt_id as refl_old_mdtrt_id,
    refl_appy_d.setl_id as setl_id,
    refl_appy_d.mdtrt_id as mdtrt_id,
    refl_appy_d.hosp_agre_refl_flag as hosp_agre_refl_flag,
    refl_appy_d.vali_flag as vali_flag,
    refl_appy_d.memo as memo,
    refl_appy_d.updt_time as updt_time,
    refl_appy_d.crter_id as crter_id,
    refl_appy_d.crter_name as crter_name,
    refl_appy_d.crte_time as crte_time,
    refl_appy_d.crte_optins_no as crte_optins_no,
    refl_appy_d.opter_id as opter_id,
    refl_appy_d.opt_time as opt_time,
    refl_appy_d.optins_no as optins_no,
    refl_appy_d.poolarea_no as poolarea_no,
    refl_appy_d.refl_fil_type as refl_fil_type,
    refl_appy_d.allo_setl_cnt as allo_setl_cnt,
    refl_appy_d.biz_date as biz_date,
    refl_appy_d.deleted_time as deleted_time,
    refl_appy_d.deleted as deleted,
    refl_appy_d.subsys_codg as subsys_codg,
    refl_appy_d.insu_admdvs as admdvs
from src_data.refl_appy_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table eps_setl_acct_info_b partition(dt)
select 
    concat(acct_sn,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    eps_setl_acct_info_b.acct_sn as acct_sn,
    eps_setl_acct_info_b.admdvs as admdvs,
    eps_setl_acct_info_b.orgname as orgname,
    eps_setl_acct_info_b.insutype_code as insutype_code,
    eps_setl_acct_info_b.bankacct as bankacct,
    eps_setl_acct_info_b.bank as bank,
    eps_setl_acct_info_b.acctname as acctname,
    eps_setl_acct_info_b.setl_cent_no as setl_cent_no,
    eps_setl_acct_info_b.acct_rpot_addr as acct_rpot_addr,
    eps_setl_acct_info_b.same_bank_flag as same_bank_flag,
    eps_setl_acct_info_b.samecity_flag as samecity_flag,
    eps_setl_acct_info_b.bankcode as bankcode,
    eps_setl_acct_info_b.crter_id as crter_id,
    eps_setl_acct_info_b.crter_name as crter_name,
    eps_setl_acct_info_b.crte_time as crte_time,
    eps_setl_acct_info_b.updt_time as updt_time,
    eps_setl_acct_info_b.poolarea_no as poolarea_no,
    eps_setl_acct_info_b.vali_flag as vali_flag,
    eps_setl_acct_info_b.biz_date as biz_date,
    eps_setl_acct_info_b.deleted_time as deleted_time,
    eps_setl_acct_info_b.deleted as deleted,
    eps_setl_acct_info_b.subsys_codg as subsys_codg,
    eps_setl_acct_info_b.orgcode as orgcode,
    eps_setl_acct_info_b.phone as phone,
    eps_setl_acct_info_b.acct_blng_place as acct_blng_place,
    eps_setl_acct_info_b.crte_optins_no as crte_optins_no,
    eps_setl_acct_info_b.exch_updt_time as exch_updt_time,
    eps_setl_acct_info_b.insutype_name as insutype_name,
    eps_setl_acct_info_b.setl_cent_name as setl_cent_name
from src_data.eps_setl_acct_info_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table trt_setl_fund_sbit_d partition(dt)
select 
    concat(biz_date,subsys_codg,psn_no,fund_pay_type,setl_id,poolarea_fund_pay_type) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trt_setl_fund_sbit_d.biz_date as biz_date,
    trt_setl_fund_sbit_d.admdvs as admdvs,
    trt_setl_fund_sbit_d.subsys_codg as subsys_codg,
    trt_setl_fund_sbit_d.deleted_time as deleted_time,
    trt_setl_fund_sbit_d.clr_type_lv2 as clr_type_lv2,
    trt_setl_fund_sbit_d.psn_insu_rlts_id as psn_insu_rlts_id,
    trt_setl_fund_sbit_d.psn_no as psn_no,
    trt_setl_fund_sbit_d.crter_name as crter_name,
    trt_setl_fund_sbit_d.crter_id as crter_id,
    trt_setl_fund_sbit_d.crte_optins_no as crte_optins_no,
    trt_setl_fund_sbit_d.fund_pay_type as fund_pay_type,
    trt_setl_fund_sbit_d.fund_payamt as fund_payamt,
    trt_setl_fund_sbit_d.fixmedins_code as fixmedins_code,
    trt_setl_fund_sbit_d.mdtrt_id as mdtrt_id,
    trt_setl_fund_sbit_d.year as year,
    trt_setl_fund_sbit_d.ym as ym,
    trt_setl_fund_sbit_d.updt_time as updt_time,
    trt_setl_fund_sbit_d.vali_flag as vali_flag,
    trt_setl_fund_sbit_d.crt_payb_lmt_amt as crt_payb_lmt_amt,
    trt_setl_fund_sbit_d.inscp_amt as inscp_amt,
    trt_setl_fund_sbit_d.opter_name as opter_name,
    trt_setl_fund_sbit_d.opter_id as opter_id,
    trt_setl_fund_sbit_d.opt_time as opt_time,
    trt_setl_fund_sbit_d.optins_no as optins_no,
    trt_setl_fund_sbit_d.setl_id as setl_id,
    trt_setl_fund_sbit_d.setl_proc_info as setl_proc_info,
    trt_setl_fund_sbit_d.poolarea_fund_pay_type as poolarea_fund_pay_type,
    trt_setl_fund_sbit_d.poolarea_fund_pay_name as poolarea_fund_pay_name,
    trt_setl_fund_sbit_d.exch_updt_time as exch_updt_time,
    trt_setl_fund_sbit_d.deleted as deleted,
    trt_setl_fund_sbit_d.pay_prop as pay_prop,
    trt_setl_fund_sbit_d.poolarea_no as poolarea_no,
    trt_setl_fund_sbit_d.crte_time as crte_time
from src_data.trt_setl_fund_sbit_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table setl_detl_d partition(dt)
select 
    concat(biz_date,subsys_codg,setl_detl_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    setl_detl_d.exra_serv_flag as exra_serv_flag,
    setl_detl_d.sprt_reate as sprt_reate,
    setl_detl_d.comb_flag as comb_flag,
    setl_detl_d.adjust_flag as adjust_flag,
    setl_detl_d.chker_begin_time as chker_begin_time,
    setl_detl_d.prcs_stas as prcs_stas,
    setl_detl_d.used as used,
    setl_detl_d.invo_supplement_flag as invo_supplement_flag,
    setl_detl_d.admorg_code as admorg_code,
    setl_detl_d.admorg_name as admorg_name,
    setl_detl_d.mcs_comb_prod_detail_id as mcs_comb_prod_detail_id,
    setl_detl_d.mcs_comb_id as mcs_comb_id,
    setl_detl_d.comb_ver as comb_ver,
    setl_detl_d.opter_name as opter_name,
    setl_detl_d.opter_id as opter_id,
    setl_detl_d.opt_time as opt_time,
    setl_detl_d.optins_no as optins_no,
    setl_detl_d.init_para_id as init_para_id,
    setl_detl_d.setl_pric as setl_pric,
    setl_detl_d.setl_invo_apply_id as setl_invo_apply_id,
    setl_detl_d.setl_ym as setl_ym,
    setl_detl_d.adjm_setl_sumpric as adjm_setl_sumpric,
    setl_detl_d.setlway as setlway,
    setl_detl_d.setl_node_stas as setl_node_stas,
    setl_detl_d.ord_code as ord_code,
    setl_detl_d.ord_souc as ord_souc,
    setl_detl_d.ot_npay_stas as ot_npay_stas,
    setl_detl_d.delventp_code as delventp_code,
    setl_detl_d.delventp_name as delventp_name,
    setl_detl_d.settle_submit_time as settle_submit_time,
    setl_detl_d.delventp_stmt_stas as delventp_stmt_stas,
    setl_detl_d.comb_cnt as comb_cnt,
    setl_detl_d.pre_chk_flag as pre_chk_flag,
    setl_detl_d.item_codg as item_codg,
    setl_detl_d.pre_chk_time as pre_chk_time,
    setl_detl_d.biz_date as biz_date,
    setl_detl_d.exch_updt_time as exch_updt_time,
    setl_detl_d.subsys_codg as subsys_codg,
    setl_detl_d.deleted_time as deleted_time,
    setl_detl_d.manu_lotnum as manu_lotnum,
    setl_detl_d.prnt_medins_code as prnt_medins_code,
    setl_detl_d.prnt_medins_name as prnt_medins_name,
    setl_detl_d.biz_id as biz_id,
    setl_detl_d.seld_id as seld_id,
    setl_detl_d.main_parts as main_parts,
    setl_detl_d.detl_cpr_fail_fld as detl_cpr_fail_fld,
    setl_detl_d.detl_cpr_stas as detl_cpr_stas,
    setl_detl_d.snap_id as snap_id,
    setl_detl_d.prod_name as prod_name,
    setl_detl_d.prod_type as prod_type,
    setl_detl_d.prod_id as prod_id,
    setl_detl_d.exra_serv_pric as exra_serv_pric,
    setl_detl_d.crter_id as crter_id,
    setl_detl_d.fchker as fchker,
    setl_detl_d.chk_opnn as chk_opnn,
    setl_detl_d.chk_time as chk_time,
    setl_detl_d.fchk_time as fchk_time,
    setl_detl_d.medins_code as medins_code,
    setl_detl_d.medins_name as medins_name,
    setl_detl_d.setl_detl_medins_stas as setl_detl_medins_stas,
    setl_detl_d.medins_setldoc_id as medins_setldoc_id,
    setl_detl_d.hosp_list_id as hosp_list_id,
    setl_detl_d.ori_setl_detl_id as ori_setl_detl_id,
    setl_detl_d.invo_cplt_flag as invo_cplt_flag,
    setl_detl_d.hosp_bidprcu_item_id as hosp_bidprcu_item_id,
    setl_detl_d.invo_date as invo_date,
    setl_detl_d.rchk_opnn as rchk_opnn,
    setl_detl_d.memo as memo,
    setl_detl_d.rchker as rchker,
    setl_detl_d.review_failed as review_failed,
    setl_detl_d.chker as chker,
    setl_detl_d.chktime as chktime,
    setl_detl_d.chk_org as chk_org,
    setl_detl_d.stmt_id as stmt_id,
    setl_detl_d.stroom_id as stroom_id,
    setl_detl_d.fchker_id as fchker_id,
    setl_detl_d.chker_id as chker_id,
    setl_detl_d.rchker_id as rchker_id,
    setl_detl_d.wait_chker_name as wait_chker_name,
    setl_detl_d.wait_chker_id as wait_chker_id,
    setl_detl_d.wait_rchk_id as wait_rchk_id,
    setl_detl_d.check_bchno as check_bchno,
    setl_detl_d.fail_chk_rea as fail_chk_rea,
    setl_detl_d.pubonln_stas as pubonln_stas,
    setl_detl_d.paydoc_id as paydoc_id,
    setl_detl_d.payins_code as payins_code,
    setl_detl_d.payins_name as payins_name,
    setl_detl_d.crte_time as crte_time,
    setl_detl_d.updt_time as updt_time,
    setl_detl_d.crte_flag as crte_flag,
    setl_detl_d.invalid_data_last_chk_rea as invalid_data_last_chk_rea,
    setl_detl_d.invalid_data_fchker as invalid_data_fchker,
    setl_detl_d.invalid_data_fchk_opnn as invalid_data_fchk_opnn,
    setl_detl_d.invalid_data_fchk_time as invalid_data_fchk_time,
    setl_detl_d.invalid_data_prcs_stas as invalid_data_prcs_stas,
    setl_detl_d.invalid_data_chker as invalid_data_chker,
    setl_detl_d.invalid_data_chk_opnn as invalid_data_chk_opnn,
    setl_detl_d.invalid_data_chk_time as invalid_data_chk_time,
    setl_detl_d.invalid_data_rchk_opnn as invalid_data_rchk_opnn,
    setl_detl_d.invalid_data_fail_chk_rea as invalid_data_fail_chk_rea,
    setl_detl_d.invalid_data_submitter as invalid_data_submitter,
    setl_detl_d.invalid_data_submit_time as invalid_data_submit_time,
    setl_detl_d.invalid_data_declare as invalid_data_declare,
    setl_detl_d.invd_flag as invd_flag,
    setl_detl_d.adm_re_check_opinion as adm_re_check_opinion,
    setl_detl_d.setl_detl_adm_stas as setl_detl_adm_stas,
    setl_detl_d.setl_detl_biz_type as setl_detl_biz_type,
    setl_detl_d.crter_name as crter_name,
    setl_detl_d.medins_stmt_stas as medins_stmt_stas,
    setl_detl_d.wait_rchk_name as wait_rchk_name,
    setl_detl_d.shpp_retn_time as shpp_retn_time,
    setl_detl_d.expy_end_date as expy_end_date,
    setl_detl_d.setl_sumpric as setl_sumpric,
    setl_detl_d.setl_cnt as setl_cnt,
    setl_detl_d.invo_amt as invo_amt,
    setl_detl_d.invalid_data_setl_node_stas as invalid_data_setl_node_stas,
    setl_detl_d.setl_detl_id as setl_detl_id,
    setl_detl_d.splm_flag as splm_flag,
    setl_detl_d.itemname as itemname,
    setl_detl_d.deleted as deleted,
    setl_detl_d.last_chk_rea as last_chk_rea,
    setl_detl_d.prod_code as prod_code,
    setl_detl_d.invalid_data_rchk_time as invalid_data_rchk_time,
    setl_detl_d.setldoc_id as setldoc_id,
    setl_detl_d.admdvs as admdvs,
    setl_detl_d.crte_optins_no as crte_optins_no,
    setl_detl_d.samp_flag as samp_flag,
    setl_detl_d.rchk_time as rchk_time,
    setl_detl_d.invalid_data_rchker as invalid_data_rchker
from src_data.setl_detl_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table mdcs_fund_setl_list_chrgitm_d partition(dt)
select 
    concat(setl_list_chrgitm_id,psn_no,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    mdcs_fund_setl_list_chrgitm_d.setl_list_chrgitm_id as setl_list_chrgitm_id,
    mdcs_fund_setl_list_chrgitm_d.psn_no as psn_no,
    mdcs_fund_setl_list_chrgitm_d.item_sumamt as item_sumamt,
    mdcs_fund_setl_list_chrgitm_d.item_claa_amt as item_claa_amt,
    mdcs_fund_setl_list_chrgitm_d.item_clab_amt as item_clab_amt,
    mdcs_fund_setl_list_chrgitm_d.item_ownpay_amt as item_ownpay_amt,
    mdcs_fund_setl_list_chrgitm_d.item_oth_amt as item_oth_amt,
    mdcs_fund_setl_list_chrgitm_d.vali_flag as vali_flag,
    mdcs_fund_setl_list_chrgitm_d.updt_time as updt_time,
    mdcs_fund_setl_list_chrgitm_d.crter_id as crter_id,
    mdcs_fund_setl_list_chrgitm_d.crter_name as crter_name,
    mdcs_fund_setl_list_chrgitm_d.crte_time as crte_time,
    mdcs_fund_setl_list_chrgitm_d.crte_optins_no as crte_optins_no,
    mdcs_fund_setl_list_chrgitm_d.opter_id as opter_id,
    mdcs_fund_setl_list_chrgitm_d.opter_name as opter_name,
    mdcs_fund_setl_list_chrgitm_d.opt_time as opt_time,
    mdcs_fund_setl_list_chrgitm_d.optins_no as optins_no,
    mdcs_fund_setl_list_chrgitm_d.poolarea_no as poolarea_no,
    mdcs_fund_setl_list_chrgitm_d.sindise_code_name as sindise_code_name,
    mdcs_fund_setl_list_chrgitm_d.daysrg_code_name as daysrg_code_name,
    mdcs_fund_setl_list_chrgitm_d.sync_time as sync_time,
    mdcs_fund_setl_list_chrgitm_d.biz_date as biz_date,
    mdcs_fund_setl_list_chrgitm_d.deleted_time as deleted_time,
    mdcs_fund_setl_list_chrgitm_d.deleted as deleted,
    mdcs_fund_setl_list_chrgitm_d.exch_updt_time as exch_updt_time,
    mdcs_fund_setl_list_chrgitm_d.subsys_codg as subsys_codg,
    mdcs_fund_setl_list_chrgitm_d.setl_id as setl_id,
    mdcs_fund_setl_list_chrgitm_d.admdvs as admdvs,
    mdcs_fund_setl_list_chrgitm_d.med_chrgitm_type as med_chrgitm_type,
    mdcs_fund_setl_list_chrgitm_d.mdtrt_id as mdtrt_id
from src_data.mdcs_fund_setl_list_chrgitm_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table mdcs_fund_setl_list_diag_d partition(dt)
select 
    concat(psn_no,biz_date,subsys_codg,setl_list_diag_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    mdcs_fund_setl_list_diag_d.admdvs as admdvs,
    mdcs_fund_setl_list_diag_d.mdtrt_id as mdtrt_id,
    mdcs_fund_setl_list_diag_d.psn_no as psn_no,
    mdcs_fund_setl_list_diag_d.maindiag_flag as maindiag_flag,
    mdcs_fund_setl_list_diag_d.diag_code as diag_code,
    mdcs_fund_setl_list_diag_d.adm_cond_type as adm_cond_type,
    mdcs_fund_setl_list_diag_d.vali_flag as vali_flag,
    mdcs_fund_setl_list_diag_d.updt_time as updt_time,
    mdcs_fund_setl_list_diag_d.crter_id as crter_id,
    mdcs_fund_setl_list_diag_d.crte_time as crte_time,
    mdcs_fund_setl_list_diag_d.crte_optins_no as crte_optins_no,
    mdcs_fund_setl_list_diag_d.opter_id as opter_id,
    mdcs_fund_setl_list_diag_d.opter_name as opter_name,
    mdcs_fund_setl_list_diag_d.optins_no as optins_no,
    mdcs_fund_setl_list_diag_d.poolarea_no as poolarea_no,
    mdcs_fund_setl_list_diag_d.biz_date as biz_date,
    mdcs_fund_setl_list_diag_d.deleted_time as deleted_time,
    mdcs_fund_setl_list_diag_d.deleted as deleted,
    mdcs_fund_setl_list_diag_d.exch_updt_time as exch_updt_time,
    mdcs_fund_setl_list_diag_d.setl_id as setl_id,
    mdcs_fund_setl_list_diag_d.subsys_codg as subsys_codg,
    mdcs_fund_setl_list_diag_d.sync_time as sync_time,
    mdcs_fund_setl_list_diag_d.setl_list_diag_id as setl_list_diag_id,
    mdcs_fund_setl_list_diag_d.diag_type as diag_type,
    mdcs_fund_setl_list_diag_d.crter_name as crter_name,
    mdcs_fund_setl_list_diag_d.opt_time as opt_time,
    mdcs_fund_setl_list_diag_d.diag_name as diag_name
from src_data.mdcs_fund_setl_list_diag_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table hf_setl_list_info partition(dt)
select 
    concat(biz_date,subsys_codg,id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    hf_setl_list_info.mdtrt_id as mdtrt_id,
    hf_setl_list_info.setl_id as setl_id,
    hf_setl_list_info.fixmedins_code as fixmedins_code,
    hf_setl_list_info.fixmedins_name as fixmedins_name,
    hf_setl_list_info.hi_setl_lv as hi_setl_lv,
    hf_setl_list_info.hi_no as hi_no,
    hf_setl_list_info.medcasno as medcasno,
    hf_setl_list_info.psn_no as psn_no,
    hf_setl_list_info.psn_name as psn_name,
    hf_setl_list_info.brdy as brdy,
    hf_setl_list_info.age as age,
    hf_setl_list_info.nwb_age as nwb_age,
    hf_setl_list_info.patn_cert_type as patn_cert_type,
    hf_setl_list_info.certno as certno,
    hf_setl_list_info.adm_time as adm_time,
    hf_setl_list_info.dscg_time as dscg_time,
    hf_setl_list_info.adm_caty as adm_caty,
    hf_setl_list_info.refldept_dept as refldept_dept,
    hf_setl_list_info.dscg_caty as dscg_caty,
    hf_setl_list_info.act_ipt_days as act_ipt_days,
    hf_setl_list_info.diag_code_cnt as diag_code_cnt,
    hf_setl_list_info.oprn_oprt_code_cnt as oprn_oprt_code_cnt,
    hf_setl_list_info.dscg_way as dscg_way,
    hf_setl_list_info.days_rinp_flag_31 as days_rinp_flag_31,
    hf_setl_list_info.chfpdr_code as chfpdr_code,
    hf_setl_list_info.chfpdr_name as chfpdr_name,
    hf_setl_list_info.psn_selfpay as psn_selfpay,
    hf_setl_list_info.psn_ownpay as psn_ownpay,
    hf_setl_list_info.acct_pay as acct_pay,
    hf_setl_list_info.psn_cashpay as psn_cashpay,
    hf_setl_list_info.hi_type as hi_type,
    hf_setl_list_info.main_oprn_code as main_oprn_code,
    hf_setl_list_info.psn_type as psn_type,
    hf_setl_list_info.main_diag_code as main_diag_code,
    hf_setl_list_info.main_diag_name as main_diag_name,
    hf_setl_list_info.main_oprn_name as main_oprn_name,
    hf_setl_list_info.bed_fee as bed_fee,
    hf_setl_list_info.examine_fee as examine_fee,
    hf_setl_list_info.inspect_fee as inspect_fee,
    hf_setl_list_info.assay_fee as assay_fee,
    hf_setl_list_info.trt_fee as trt_fee,
    hf_setl_list_info.rgtrt_fee as rgtrt_fee,
    hf_setl_list_info.nurs_fee as nurs_fee,
    hf_setl_list_info.med_mat_fee as med_mat_fee,
    hf_setl_list_info.ordn_diag_trt_fee as ordn_diag_trt_fee,
    hf_setl_list_info.rgst_serv_fee as rgst_serv_fee,
    hf_setl_list_info.oth_fee as oth_fee,
    hf_setl_list_info.medfee_sumamt as medfee_sumamt,
    hf_setl_list_info.hi_agre_sumfee as hi_agre_sumfee,
    hf_setl_list_info.fund_pay_sumamt as fund_pay_sumamt,
    hf_setl_list_info.case_type as case_type,
    hf_setl_list_info.hosp_lv as hosp_lv,
    hf_setl_list_info.coefficient as coefficient,
    hf_setl_list_info.quality_grade as quality_grade,
    hf_setl_list_info.sub_cat_code as sub_cat_code,
    hf_setl_list_info.sub_cat_name as sub_cat_name,
    hf_setl_list_info.concat_oprn_code as concat_oprn_code,
    hf_setl_list_info.concat_oprn_name as concat_oprn_name,
    hf_setl_list_info.dise_id as dise_id,
    hf_setl_list_info.dise_type as dise_type,
    hf_setl_list_info.group_flag as group_flag,
    hf_setl_list_info.classify_reason as classify_reason,
    hf_setl_list_info.cell as cell,
    hf_setl_list_info.rw as rw,
    hf_setl_list_info.price as price,
    hf_setl_list_info.bed_type as bed_type,
    hf_setl_list_info.insutype as insutype,
    hf_setl_list_info.insu_admdvs as insu_admdvs,
    hf_setl_list_info.fix_blng_admdvs as fix_blng_admdvs,
    hf_setl_list_info.setl_begn_date as setl_begn_date,
    hf_setl_list_info.setl_end_date as setl_end_date,
    hf_setl_list_info.setl_stas as setl_stas,
    hf_setl_list_info.setl_time as setl_time,
    hf_setl_list_info.clr_time as clr_time,
    hf_setl_list_info.crte_time as crte_time,
    hf_setl_list_info.updt_time as updt_time,
    hf_setl_list_info.delete_flag as delete_flag,
    hf_setl_list_info.biz_date as biz_date,
    hf_setl_list_info.deleted_time as deleted_time,
    hf_setl_list_info.deleted as deleted,
    hf_setl_list_info.exch_updt_time as exch_updt_time,
    hf_setl_list_info.subsys_codg as subsys_codg,
    hf_setl_list_info.insu_admdvs as admdvs,
    hf_setl_list_info.hi_paymtd as hi_paymtd,
    hf_setl_list_info.tcmherb_fee as tcmherb_fee,
    hf_setl_list_info.clr_stas as clr_stas,
    hf_setl_list_info.wm_fee as wm_fee,
    hf_setl_list_info.tcmpat_fee as tcmpat_fee,
    hf_setl_list_info.poolarea_no as poolarea_no,
    hf_setl_list_info.id as id,
    hf_setl_list_info.gend as gend
from src_data.hf_setl_list_info as t
where 1=1
) as tab

-- ================================================
insert overwrite table ma_setl_info_ext_d partition(dt)
select 
    concat(biz_date,subsys_codg,setl_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ma_setl_info_ext_d.insu_admdvs as insu_admdvs,
    ma_setl_info_ext_d.mdtrt_id as mdtrt_id,
    ma_setl_info_ext_d.psn_no as psn_no,
    ma_setl_info_ext_d.psn_insu_rlts_id as psn_insu_rlts_id,
    ma_setl_info_ext_d.psn_name as psn_name,
    ma_setl_info_ext_d.fixmedins_code as fixmedins_code,
    ma_setl_info_ext_d.fixmedins_name as fixmedins_name,
    ma_setl_info_ext_d.polin_psn_pay as polin_psn_pay,
    ma_setl_info_ext_d.ma_admdvs as ma_admdvs,
    ma_setl_info_ext_d.mat_idet_type as mat_idet_type,
    ma_setl_info_ext_d.mat_idet_code as mat_idet_code,
    ma_setl_info_ext_d.psn_cert_type as psn_cert_type,
    ma_setl_info_ext_d.certno as certno,
    ma_setl_info_ext_d.tel as tel,
    ma_setl_info_ext_d.addr as addr,
    ma_setl_info_ext_d.fix_blng_admdvs as fix_blng_admdvs,
    ma_setl_info_ext_d.emp_no as emp_no,
    ma_setl_info_ext_d.emp_name as emp_name,
    ma_setl_info_ext_d.insutype as insutype,
    ma_setl_info_ext_d.begndate as begndate,
    ma_setl_info_ext_d.enddate as enddate,
    ma_setl_info_ext_d.psn_type as psn_type,
    ma_setl_info_ext_d.refd_setl_flag as refd_setl_flag,
    ma_setl_info_ext_d.clr_way as clr_way,
    ma_setl_info_ext_d.setl_type as setl_type,
    ma_setl_info_ext_d.psn_setlway as psn_setlway,
    ma_setl_info_ext_d.medfee_sumamt as medfee_sumamt,
    ma_setl_info_ext_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    ma_setl_info_ext_d.overlmt_selfpay as overlmt_selfpay,
    ma_setl_info_ext_d.preselfpay_amt as preselfpay_amt,
    ma_setl_info_ext_d.inscp_amt as inscp_amt,
    ma_setl_info_ext_d.hi_agre_sumfee as hi_agre_sumfee,
    ma_setl_info_ext_d.cvlserv_pay as cvlserv_pay,
    ma_setl_info_ext_d.hifes_pay as hifes_pay,
    ma_setl_info_ext_d.hifob_pay as hifob_pay,
    ma_setl_info_ext_d.hifdm_pay as hifdm_pay,
    ma_setl_info_ext_d.othfund_pay as othfund_pay,
    ma_setl_info_ext_d.fund_pay_sumamt as fund_pay_sumamt,
    ma_setl_info_ext_d.psn_pay as psn_pay,
    ma_setl_info_ext_d.acct_pay as acct_pay,
    ma_setl_info_ext_d.cash_payamt as cash_payamt,
    ma_setl_info_ext_d.setl_time as setl_time,
    ma_setl_info_ext_d.year as year,
    ma_setl_info_ext_d.dise_no as dise_no,
    ma_setl_info_ext_d.dise_name as dise_name,
    ma_setl_info_ext_d.vali_flag as vali_flag,
    ma_setl_info_ext_d.memo as memo,
    ma_setl_info_ext_d.updt_time as updt_time,
    ma_setl_info_ext_d.crter_id as crter_id,
    ma_setl_info_ext_d.crter_name as crter_name,
    ma_setl_info_ext_d.crte_time as crte_time,
    ma_setl_info_ext_d.crte_optins_no as crte_optins_no,
    ma_setl_info_ext_d.opter_name as opter_name,
    ma_setl_info_ext_d.opt_time as opt_time,
    ma_setl_info_ext_d.optins_no as optins_no,
    ma_setl_info_ext_d.poolarea_no as poolarea_no,
    ma_setl_info_ext_d.biz_date as biz_date,
    ma_setl_info_ext_d.deleted_time as deleted_time,
    ma_setl_info_ext_d.deleted as deleted,
    ma_setl_info_ext_d.exch_updt_time as exch_updt_time,
    ma_setl_info_ext_d.subsys_codg as subsys_codg,
    ma_setl_info_ext_d.insu_admdvs as admdvs,
    ma_setl_info_ext_d.acct_mulaid_pay as acct_mulaid_pay,
    ma_setl_info_ext_d.setl_id as setl_id,
    ma_setl_info_ext_d.gend as gend,
    ma_setl_info_ext_d.pay_loc as pay_loc,
    ma_setl_info_ext_d.maf_pay as maf_pay,
    ma_setl_info_ext_d.hifp_pay as hifp_pay,
    ma_setl_info_ext_d.hifmi_pay as hifmi_pay,
    ma_setl_info_ext_d.med_type as med_type,
    ma_setl_info_ext_d.opter_id as opter_id
from src_data.ma_setl_info_ext_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table ag_gs_complaint partition(dt)
select 
    concat(id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_gs_complaint.deleted as deleted,
    ag_gs_complaint.id as id,
    ag_gs_complaint.zfzt as zfzt,
    ag_gs_complaint.tsdh as tsdh,
    ag_gs_complaint.tsdz as tsdz,
    ag_gs_complaint.qtfs as qtfs,
    ag_gs_complaint.cret_user as cret_user,
    ag_gs_complaint.cret_date as cret_date,
    ag_gs_complaint.updt_user as updt_user,
    ag_gs_complaint.updt_date as updt_date,
    ag_gs_complaint.biz_date as biz_date,
    ag_gs_complaint.deleted_time as deleted_time,
    ag_gs_complaint.exch_updt_time as exch_updt_time,
    ag_gs_complaint.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.ag_gs_complaint as t
LEFT JOIN
(
SELECT
report.id,
report.cret_admdvs as admdvs,
report.cret_date,
ROW_NUMBER() OVER(PARTITION BY report.id ORDER BY report.cret_date DESC) AS rn
FROM
report
) AS RB
ON
ag_gs_complaint.id = RB.id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table trns_entp_cred_lv_d partition(dt)
select 
    concat(biz_date,subsys_codg,entp_cred_lv_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_entp_cred_lv_d.biz_date as biz_date,
    trns_entp_cred_lv_d.evaluate_time as evaluate_time,
    trns_entp_cred_lv_d.admdvs as admdvs,
    trns_entp_cred_lv_d.subsys_codg as subsys_codg,
    trns_entp_cred_lv_d.deleted_time as deleted_time,
    trns_entp_cred_lv_d.deleted as deleted,
    trns_entp_cred_lv_d.item_type as item_type,
    trns_entp_cred_lv_d.cred_lv as cred_lv,
    trns_entp_cred_lv_d.rep_time as rep_time,
    trns_entp_cred_lv_d.crter_id as crter_id,
    trns_entp_cred_lv_d.crte_optins_no as crte_optins_no,
    trns_entp_cred_lv_d.oprt_name as oprt_name,
    trns_entp_cred_lv_d.oprt_id as oprt_id,
    trns_entp_cred_lv_d.oprt_account as oprt_account,
    trns_entp_cred_lv_d.oprt_time as oprt_time,
    trns_entp_cred_lv_d.crte_time as crte_time,
    trns_entp_cred_lv_d.opter_id as opter_id,
    trns_entp_cred_lv_d.invd_flag as invd_flag,
    trns_entp_cred_lv_d.opter_name as opter_name,
    trns_entp_cred_lv_d.optins_no as optins_no,
    trns_entp_cred_lv_d.exch_updt_time as exch_updt_time,
    trns_entp_cred_lv_d.entp_cred_lv_id as entp_cred_lv_id,
    trns_entp_cred_lv_d.opt_time as opt_time,
    trns_entp_cred_lv_d.updt_time as updt_time,
    trns_entp_cred_lv_d.uscc as uscc,
    trns_entp_cred_lv_d.dcla_entp_name as dcla_entp_name,
    trns_entp_cred_lv_d.crter_name as crter_name
from src_data.trns_entp_cred_lv_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table report partition(dt)
select 
    concat(biz_date,subsys_codg,id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    report.report_country as report_country,
    report.biz_date as biz_date,
    report.name as name,
    report.handle_person as handle_person,
    report.handle_event as handle_event,
    report.has_report as has_report,
    report.turn_user_admdvs as turn_user_admdvs,
    report.sign_val as sign_val,
    report.subsys_codg as subsys_codg,
    report.cret_admdvs as admdvs,
    report.turn_back as turn_back,
    report.handle_type as handle_type,
    report.is_agree as is_agree,
    report.id as id,
    report.code as code,
    report.idcard as idcard,
    report.openid as openid,
    report.address as address,
    report.mail as mail,
    report.phone as phone,
    report.ipaddress as ipaddress,
    report.report_type as report_type,
    report.report_addr as report_addr,
    report.report_obj_code as report_obj_code,
    report.report_obj as report_obj,
    report.report_name as report_name,
    report.report_event as report_event,
    report.report_event_text as report_event_text,
    report.report_title as report_title,
    report.report_memo as report_memo,
    report.is_anonym as is_anonym,
    report.channel as channel,
    report.source as source,
    report.region as region,
    report.terminal_type as terminal_type,
    report.equipment_type as equipment_type,
    report.electronic_archives_no as electronic_archives_no,
    report.turn_status as turn_status,
    report.biz_status as biz_status,
    report.report_date as report_date,
    report.reward as reward,
    report.recovery as recovery,
    report.chargeback as chargeback,
    report.penalize as penalize,
    report.reward_content as reward_content,
    report.turn_time as turn_time,
    report.turn_mechanism_name as turn_mechanism_name,
    report.turn_mechanism as turn_mechanism,
    report.turn_outside_name as turn_outside_name,
    report.turn_region_name as turn_region_name,
    report.turn_region as turn_region,
    report.turn_handle_time as turn_handle_time,
    report.turn_end_time as turn_end_time,
    report.turn_user_id as turn_user_id,
    report.turn_user_name as turn_user_name,
    report.turn_user_phone as turn_user_phone,
    report.event_type as event_type,
    report.handle_result as handle_result,
    report.handle_verified as handle_verified,
    report.handle_reason as handle_reason,
    report.handle_person_id as handle_person_id,
    report.handle_person_admdvs as handle_person_admdvs,
    report.memo as memo,
    report.opinions as opinions,
    report.time_limit as time_limit,
    report.appeal_org as appeal_org,
    report.is_delay as is_delay,
    report.is_notified as is_notified,
    report.notified_date as notified_date,
    report.cret_user as cret_user,
    report.updt_user as updt_user,
    report.cret_date as cret_date,
    report.updt_date as updt_date,
    report.cret_admdvs as cret_admdvs,
    report.admdvs_path as admdvs_path,
    report.is_delete as is_delete,
    report.remake as remake,
    report.secret as secret,
    report.deleted_time as deleted_time,
    report.deleted as deleted,
    report.exch_updt_time as exch_updt_time
from src_data.report as t
where 1=1
) as tab

-- ================================================
insert overwrite table ag_ad_punish_hearing_report partition(dt)
select 
    concat(report_id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_ad_punish_hearing_report.report_way as report_way,
    ag_ad_punish_hearing_report.hear_officer as hear_officer,
    ag_ad_punish_hearing_report.agent as agent,
    ag_ad_punish_hearing_report.third_party as third_party,
    ag_ad_punish_hearing_report.deleted_time as deleted_time,
    ag_ad_punish_hearing_report.report_place as report_place,
    ag_ad_punish_hearing_report.cret_date as cret_date,
    ag_ad_punish_hearing_report.handle_person_name as handle_person_name,
    ag_ad_punish_hearing_report.report_id as report_id,
    ag_ad_punish_hearing_report.accept_id as accept_id,
    ag_ad_punish_hearing_report.report_time as report_time,
    ag_ad_punish_hearing_report.report_host as report_host,
    ag_ad_punish_hearing_report.recorder as recorder,
    ag_ad_punish_hearing_report.interpreter as interpreter,
    ag_ad_punish_hearing_report.unit_charge_person as unit_charge_person,
    ag_ad_punish_hearing_report.handle_advice as handle_advice,
    ag_ad_punish_hearing_report.other_people as other_people,
    ag_ad_punish_hearing_report.basic_information as basic_information,
    ag_ad_punish_hearing_report.other_matters as other_matters,
    ag_ad_punish_hearing_report.is_delete as is_delete,
    ag_ad_punish_hearing_report.cret_user as cret_user,
    ag_ad_punish_hearing_report.party_person as party_person,
    ag_ad_punish_hearing_report.examiner_id as examiner_id,
    ag_ad_punish_hearing_report.examiner_name as examiner_name,
    ag_ad_punish_hearing_report.handle_person_id as handle_person_id,
    ag_ad_punish_hearing_report.updt_user as updt_user,
    ag_ad_punish_hearing_report.updt_date as updt_date,
    ag_ad_punish_hearing_report.biz_date as biz_date,
    ag_ad_punish_hearing_report.deleted as deleted,
    ag_ad_punish_hearing_report.exch_updt_time as exch_updt_time,
    ag_ad_punish_hearing_report.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.ag_ad_punish_hearing_report as t
LEFT JOIN
(
SELECT
ag_ad_accept.accept_id,
ag_ad_accept.cret_admdvs as admdvs,
ag_ad_accept.cret_date,
ROW_NUMBER() OVER(PARTITION BY ag_ad_accept.accept_id ORDER BY ag_ad_accept.cret_date DESC) AS rn
FROM
ag_ad_accept
) AS RB
ON
ag_ad_punish_hearing_report.accept_id = RB.accept_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table ag_ad_check_report partition(dt)
select 
    concat(biz_date,report_id,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_ad_check_report.complate_time as complate_time,
    ag_ad_check_report.check_record as check_record,
    ag_ad_check_report.biz_date as biz_date,
    ag_ad_check_report.advice_content as advice_content,
    ag_ad_check_report.report_id as report_id,
    ag_ad_check_report.accept_id as accept_id,
    ag_ad_check_report.check_project as check_project,
    ag_ad_check_report.check_time as check_time,
    ag_ad_check_report.check_appraise as check_appraise,
    ag_ad_check_report.check_problem as check_problem,
    ag_ad_check_report.advice_code as advice_code,
    ag_ad_check_report.transfer_unit as transfer_unit,
    ag_ad_check_report.clause_array as clause_array,
    ag_ad_check_report.cret_user as cret_user,
    ag_ad_check_report.cret_date as cret_date,
    ag_ad_check_report.updt_user as updt_user,
    ag_ad_check_report.updt_date as updt_date,
    ag_ad_check_report.deleted_time as deleted_time,
    ag_ad_check_report.deleted as deleted,
    ag_ad_check_report.exch_updt_time as exch_updt_time,
    ag_ad_check_report.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.ag_ad_check_report as t
LEFT JOIN
(
SELECT
ag_ad_accept.accept_id,
ag_ad_accept.cret_admdvs as admdvs,
ag_ad_accept.cret_date,
ROW_NUMBER() OVER(PARTITION BY ag_ad_accept.accept_id ORDER BY ag_ad_accept.cret_date DESC) AS rn
FROM
ag_ad_accept
) AS RB
ON
ag_ad_check_report.accept_id = RB.accept_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table ag_ad_accept partition(dt)
select 
    concat(biz_date,subsys_codg,accept_id) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_ad_accept.updt_user as updt_user,
    ag_ad_accept.cret_user_id as cret_user_id,
    ag_ad_accept.unit_person_name as unit_person_name,
    ag_ad_accept.party_id as party_id,
    ag_ad_accept.cret_user as cret_user,
    ag_ad_accept.cret_date as cret_date,
    ag_ad_accept.unit_person as unit_person,
    ag_ad_accept.check_captain as check_captain,
    ag_ad_accept.check_captain_name as check_captain_name,
    ag_ad_accept.is_delete as is_delete,
    ag_ad_accept.submit_time as submit_time,
    ag_ad_accept.updt_date as updt_date,
    ag_ad_accept.workflow as workflow,
    ag_ad_accept.state as state,
    ag_ad_accept.end_date as end_date,
    ag_ad_accept.edoc_info_id as edoc_info_id,
    ag_ad_accept.check_type as check_type,
    ag_ad_accept.cret_admdvs as cret_admdvs,
    ag_ad_accept.clue_no as clue_no,
    ag_ad_accept.enforce_law_item_array as enforce_law_item_array,
    ag_ad_accept.biz_date as biz_date,
    ag_ad_accept.deleted_time as deleted_time,
    ag_ad_accept.deleted as deleted,
    ag_ad_accept.exch_updt_time as exch_updt_time,
    ag_ad_accept.subsys_codg as subsys_codg,
    ag_ad_accept.cret_admdvs as admdvs,
    ag_ad_accept.accept_id as accept_id,
    ag_ad_accept.case_code as case_code,
    ag_ad_accept.case_source as case_source,
    ag_ad_accept.check_time as check_time,
    ag_ad_accept.content_target as content_target,
    ag_ad_accept.clause_array as clause_array,
    ag_ad_accept.unit as unit,
    ag_ad_accept.unit_name as unit_name
from src_data.ag_ad_accept as t
where 1=1
) as tab

-- ================================================
insert overwrite table ag_ad_check_packet partition(dt)
select 
    concat(packet_id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_ad_check_packet.updt_date as updt_date,
    ag_ad_check_packet.packet_id as packet_id,
    ag_ad_check_packet.accept_id as accept_id,
    ag_ad_check_packet.recovery_funds as recovery_funds,
    ag_ad_check_packet.punish_money as punish_money,
    ag_ad_check_packet.protest_money as protest_money,
    ag_ad_check_packet.reward_money as reward_money,
    ag_ad_check_packet.brief_content as brief_content,
    ag_ad_check_packet.packet_result as packet_result,
    ag_ad_check_packet.packet_content as packet_content,
    ag_ad_check_packet.cret_user as cret_user,
    ag_ad_check_packet.cret_date as cret_date,
    ag_ad_check_packet.updt_user as updt_user,
    ag_ad_check_packet.typical_flag as typical_flag,
    ag_ad_check_packet.biz_date as biz_date,
    ag_ad_check_packet.deleted_time as deleted_time,
    ag_ad_check_packet.deleted as deleted,
    ag_ad_check_packet.exch_updt_time as exch_updt_time,
    ag_ad_check_packet.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.ag_ad_check_packet as t
LEFT JOIN
(
SELECT
ag_ad_accept.accept_id,
ag_ad_accept.cret_admdvs as admdvs,
ag_ad_accept.cret_date,
ROW_NUMBER() OVER(PARTITION BY ag_ad_accept.accept_id ORDER BY ag_ad_accept.cret_date DESC) AS rn
FROM
ag_ad_accept
) AS RB
ON
ag_ad_check_packet.accept_id = RB.accept_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table ag_ad_check_handle partition(dt)
select 
    concat(handle_id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    ag_ad_check_handle.handle_id as handle_id,
    ag_ad_check_handle.accept_id as accept_id,
    ag_ad_check_handle.evidence_array as evidence_array,
    ag_ad_check_handle.party_reply_code as party_reply_code,
    ag_ad_check_handle.party_reply_content as party_reply_content,
    ag_ad_check_handle.delivery_way as delivery_way,
    ag_ad_check_handle.extenuate_reason as extenuate_reason,
    ag_ad_check_handle.transfer_unit as transfer_unit,
    ag_ad_check_handle.handle_type as handle_type,
    ag_ad_check_handle.handle_content as handle_content,
    ag_ad_check_handle.clause_array as clause_array,
    ag_ad_check_handle.order_change as order_change,
    ag_ad_check_handle.cret_user as cret_user,
    ag_ad_check_handle.cret_date as cret_date,
    ag_ad_check_handle.updt_user as updt_user,
    ag_ad_check_handle.updt_date as updt_date,
    ag_ad_check_handle.biz_date as biz_date,
    ag_ad_check_handle.deleted_time as deleted_time,
    ag_ad_check_handle.exch_updt_time as exch_updt_time,
    ag_ad_check_handle.subsys_codg as subsys_codg,
    RB.admdvs as admdvs,
    ag_ad_check_handle.deleted as deleted,
    ag_ad_check_handle.illegal_act as illegal_act,
    ag_ad_check_handle.delivery_time as delivery_time
from src_data.ag_ad_check_handle as t
LEFT JOIN
(
SELECT
ag_ad_accept.accept_id,
ag_ad_accept.cret_admdvs as admdvs,
ag_ad_accept.cret_date,
ROW_NUMBER() OVER(PARTITION BY ag_ad_accept.accept_id ORDER BY ag_ad_accept.cret_date DESC) AS rn
FROM
ag_ad_accept
) AS RB
ON
ag_ad_check_handle.accept_id = RB.accept_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table trns_ord_c partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_ord_c.mcs_comb_id as mcs_comb_id,
    trns_ord_c.crte_time as crte_time,
    trns_ord_c.ext_code as ext_code,
    trns_ord_c.ord_id as ord_id,
    trns_ord_c.prt_flag as prt_flag,
    trns_ord_c.ord_code as ord_code,
    trns_ord_c.ord_sumamt as ord_sumamt,
    trns_ord_c.addr_id as addr_id,
    trns_ord_c.pubonln_delv_ext_id as pubonln_delv_ext_id,
    trns_ord_c.medins_code as medins_code,
    trns_ord_c.medins_name as medins_name,
    trns_ord_c.delventp_code as delventp_code,
    trns_ord_c.delventp_name as delventp_name,
    trns_ord_c.admorg_code as admorg_code,
    trns_ord_c.admorg_name as admorg_name,
    trns_ord_c.read_flag as read_flag,
    trns_ord_c.read_time as read_time,
    trns_ord_c.send_time as send_time,
    trns_ord_c.shp_time as shp_time,
    trns_ord_c.splm_inpt_flag as splm_inpt_flag,
    trns_ord_c.shp_stas as shp_stas,
    trns_ord_c.shpp_stas as shpp_stas,
    trns_ord_c.purc_plan_id as purc_plan_id,
    trns_ord_c.purc_plan_code as purc_plan_code,
    trns_ord_c.itemname as itemname,
    trns_ord_c.invottl as invottl,
    trns_ord_c.docmker as docmker,
    trns_ord_c.memo as memo,
    trns_ord_c.cncl_time as cncl_time,
    trns_ord_c.shpp_time as shpp_time,
    trns_ord_c.splm_inpt_retn_time as splm_inpt_retn_time,
    trns_ord_c.plan_detl_memo as plan_detl_memo,
    trns_ord_c.prod_type as prod_type,
    trns_ord_c.delventp_cnfm_shpp_time as delventp_cnfm_shpp_time,
    trns_ord_c.crter_id as crter_id,
    trns_ord_c.crter_name as crter_name,
    trns_ord_c.crte_optins_no as crte_optins_no,
    trns_ord_c.opter_id as opter_id,
    trns_ord_c.opter_name as opter_name,
    trns_ord_c.optins_no as optins_no,
    trns_ord_c.opt_time as opt_time,
    trns_ord_c.updt_time as updt_time,
    trns_ord_c.invd_flag as invd_flag,
    trns_ord_c.shp_end_time as shp_end_time,
    trns_ord_c.shpp_end_time as shpp_end_time,
    trns_ord_c.eval_flag as eval_flag,
    trns_ord_c.his_ord_no as his_ord_no,
    trns_ord_c.splm_inpt_retn_reason as splm_inpt_retn_reason,
    trns_ord_c.comb_flag as comb_flag,
    trns_ord_c.exra_serv_flag as exra_serv_flag,
    trns_ord_c.exra_serv_pric as exra_serv_pric,
    trns_ord_c.comb_purc_cnt as comb_purc_cnt,
    trns_ord_c.comb_shp_cnt as comb_shp_cnt,
    trns_ord_c.comb_shpp_cnt as comb_shpp_cnt,
    trns_ord_c.comb_retn_cnt as comb_retn_cnt,
    trns_ord_c.comb_rtnb_cnt as comb_rtnb_cnt,
    trns_ord_c.acp_flag as acp_flag,
    trns_ord_c.acp_time as acp_time,
    trns_ord_c.ord_souc as ord_souc,
    trns_ord_c.biz_date as biz_date,
    trns_ord_c.deleted_time as deleted_time,
    trns_ord_c.deleted as deleted,
    trns_ord_c.exch_updt_time as exch_updt_time,
    trns_ord_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.trns_ord_c as t
LEFT JOIN
(
SELECT
trns_medins_hi_rlts_d.tenant_id,
trns_medins_hi_rlts_d.admdvs,
trns_medins_hi_rlts_d.cret_date,
ROW_NUMBER() OVER(PARTITION BY trns_medins_hi_rlts_d.tenant_id ORDER BY trns_medins_hi_rlts_d.cret_date DESC) AS rn
FROM
trns_medins_hi_rlts_d
) AS RB
ON
trns_ord_c.tenant_id = RB.tenant_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table trns_purc_plan_detl_c partition(dt)
select 
    concat(purc_plan_det_id,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_purc_plan_detl_c.admorg_code as admorg_code,
    trns_purc_plan_detl_c.purc_pric as purc_pric,
    trns_purc_plan_detl_c.delventp_code as delventp_code,
    trns_purc_plan_detl_c.purc_cnt as purc_cnt,
    trns_purc_plan_detl_c.shpp_time as shpp_time,
    trns_purc_plan_detl_c.opt_time as opt_time,
    trns_purc_plan_detl_c.purc_plan_det_id as purc_plan_det_id,
    trns_purc_plan_detl_c.purc_plan_id as purc_plan_id,
    trns_purc_plan_detl_c.prod_name as prod_name,
    trns_purc_plan_detl_c.itemname as itemname,
    trns_purc_plan_detl_c.pubonln_pric as pubonln_pric,
    trns_purc_plan_detl_c.medins_code as medins_code,
    trns_purc_plan_detl_c.medins_name as medins_name,
    trns_purc_plan_detl_c.admorg_name as admorg_name,
    trns_purc_plan_detl_c.delventp_name as delventp_name,
    trns_purc_plan_detl_c.prod_id as prod_id,
    trns_purc_plan_detl_c.hosp_bidprcu_item_id as hosp_bidprcu_item_id,
    trns_purc_plan_detl_c.hosp_list_id as hosp_list_id,
    trns_purc_plan_detl_c.pubonln_delv_ext_id as pubonln_delv_ext_id,
    trns_purc_plan_detl_c.plan_detl_memo as plan_detl_memo,
    trns_purc_plan_detl_c.trns_data_souc as trns_data_souc,
    trns_purc_plan_detl_c.shp_time as shp_time,
    trns_purc_plan_detl_c.crter_id as crter_id,
    trns_purc_plan_detl_c.crter_name as crter_name,
    trns_purc_plan_detl_c.crte_optins_no as crte_optins_no,
    trns_purc_plan_detl_c.crte_time as crte_time,
    trns_purc_plan_detl_c.opter_id as opter_id,
    trns_purc_plan_detl_c.opter_name as opter_name,
    trns_purc_plan_detl_c.optins_no as optins_no,
    trns_purc_plan_detl_c.updt_time as updt_time,
    trns_purc_plan_detl_c.invd_flag as invd_flag,
    trns_purc_plan_detl_c.biz_date as biz_date,
    trns_purc_plan_detl_c.deleted_time as deleted_time,
    trns_purc_plan_detl_c.deleted as deleted,
    trns_purc_plan_detl_c.exch_updt_time as exch_updt_time,
    trns_purc_plan_detl_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.trns_purc_plan_detl_c as t
LEFT JOIN
(
SELECT
toc_inner.purc_plan_id,
toc_inner.ord_id,
toc_inner.crte_time, 
ROW_NUMBER() OVER(PARTITION BY toc_inner.purc_plan_id ORDER BY toc_inner.order_time DESC) AS rn_ord
FROM
trns_ord_c AS toc_inner
) AS toc
ON
trns_purc_plan_detl_c .purc_plan_id = toc.purc_plan_id AND toc.rn_ord = 1
LEFT JOIN
(
SELECT
ptrb_inner.ord_id,
ptrb_inner.admdvs,
ptrb_inner.crte_time, 
ROW_NUMBER() OVER(PARTITION BY ptrb_inner.ord_id ORDER BY ptrb_inner.create_time DESC) AS rn_ptrb
FROM
pric_task_rcd_b AS ptrb_inner
) AS RB
ON
toc.ord_id = RB.ord_id AND RB.rn_ptrb = 1;
where 1=1
) as tab

-- ================================================
insert overwrite table trns_purc_plan_c partition(dt)
select 
    concat(subsys_codg,purc_plan_id,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_purc_plan_c.prnt_entp_code as prnt_entp_code,
    trns_purc_plan_c.plan_shpp_time as plan_shpp_time,
    trns_purc_plan_c.send_time as send_time,
    trns_purc_plan_c.medins_code as medins_code,
    trns_purc_plan_c.crte_time as crte_time,
    trns_purc_plan_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs,
    trns_purc_plan_c.purc_plan_id as purc_plan_id,
    trns_purc_plan_c.purc_type as purc_type,
    trns_purc_plan_c.plan_sumamt as plan_sumamt,
    trns_purc_plan_c.purc_plan_code as purc_plan_code,
    trns_purc_plan_c.invottl as invottl,
    trns_purc_plan_c.purc_plan_addr_id as purc_plan_addr_id,
    trns_purc_plan_c.medins_name as medins_name,
    trns_purc_plan_c.admorg_code as admorg_code,
    trns_purc_plan_c.admorg_name as admorg_name,
    trns_purc_plan_c.purc_plan_name as purc_plan_name,
    trns_purc_plan_c.plandoc_type as plandoc_type,
    trns_purc_plan_c.chk_stas as chk_stas,
    trns_purc_plan_c.plan_type as plan_type,
    trns_purc_plan_c.plan_time as plan_time,
    trns_purc_plan_c.sender as sender,
    trns_purc_plan_c.plan_memo as plan_memo,
    trns_purc_plan_c.splm_flag as splm_flag,
    trns_purc_plan_c.crter_id as crter_id,
    trns_purc_plan_c.crter_name as crter_name,
    trns_purc_plan_c.crte_optins_no as crte_optins_no,
    trns_purc_plan_c.opter_id as opter_id,
    trns_purc_plan_c.opter_name as opter_name,
    trns_purc_plan_c.optins_no as optins_no,
    trns_purc_plan_c.updt_time as updt_time,
    trns_purc_plan_c.opt_time as opt_time,
    trns_purc_plan_c.invd_flag as invd_flag,
    trns_purc_plan_c.biz_date as biz_date,
    trns_purc_plan_c.deleted_time as deleted_time,
    trns_purc_plan_c.deleted as deleted,
    trns_purc_plan_c.exch_updt_time as exch_updt_time
from src_data.trns_purc_plan_c as t
LEFT JOIN
(
SELECT
toc_inner.purc_plan_id,
toc_inner.ord_id,
toc_inner.crte_time, 
ROW_NUMBER() OVER(PARTITION BY toc_inner.purc_plan_id ORDER BY toc_inner.order_time DESC) AS rn_ord
FROM
trns_ord_c AS toc_inner
) AS toc
ON
trns_purc_plan_c .purc_plan_id = toc.purc_plan_id AND toc.rn_ord = 1
LEFT JOIN
(
SELECT
ptrb_inner.ord_id,
ptrb_inner.admdvs,
ptrb_inner.crte_time, 
ROW_NUMBER() OVER(PARTITION BY ptrb_inner.ord_id ORDER BY ptrb_inner.create_time DESC) AS rn_ptrb
FROM
pric_task_rcd_b AS ptrb_inner
) AS RB
ON
toc.ord_id = RB.ord_id AND RB.rn_ptrb = 1;
where 1=1
) as tab

-- ================================================
insert overwrite table trns_prod_shp_c partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_prod_shp_c.hosp_bidprcu_item_id as hosp_bidprcu_item_id,
    trns_prod_shp_c.pubonln_stas as pubonln_stas,
    trns_prod_shp_c.invo_amt as invo_amt,
    trns_prod_shp_c.shp_stas as shp_stas,
    trns_prod_shp_c.manu_lotnum as manu_lotnum,
    trns_prod_shp_c.outsto_time as outsto_time,
    trns_prod_shp_c.invd_flag as invd_flag,
    trns_prod_shp_c.ext_updt_time as ext_updt_time,
    trns_prod_shp_c.shpp_cnt as shpp_cnt,
    trns_prod_shp_c.cncl_type as cncl_type,
    trns_prod_shp_c.optins_no as optins_no,
    trns_prod_shp_c.snap_id as snap_id,
    trns_prod_shp_c.splt_code as splt_code,
    trns_prod_shp_c.medins_code as medins_code,
    trns_prod_shp_c.shp_id as shp_id,
    trns_prod_shp_c.shp_code as shp_code,
    trns_prod_shp_c.ord_id as ord_id,
    trns_prod_shp_c.ord_code as ord_code,
    trns_prod_shp_c.pubonln_delv_ext_id as pubonln_delv_ext_id,
    trns_prod_shp_c.ord_detl_id as ord_detl_id,
    trns_prod_shp_c.medins_name as medins_name,
    trns_prod_shp_c.delventp_code as delventp_code,
    trns_prod_shp_c.delventp_name as delventp_name,
    trns_prod_shp_c.admorg_code as admorg_code,
    trns_prod_shp_c.admorg_name as admorg_name,
    trns_prod_shp_c.prod_id as prod_id,
    trns_prod_shp_c.prod_name as prod_name,
    trns_prod_shp_c.purc_cnt as purc_cnt,
    trns_prod_shp_c.purcpric as purcpric,
    trns_prod_shp_c.shp_cnt as shp_cnt,
    trns_prod_shp_c.shp_pric as shp_pric,
    trns_prod_shp_c.shp_time as shp_time,
    trns_prod_shp_c.shpp_time as shpp_time,
    trns_prod_shp_c.send_time as send_time,
    trns_prod_shp_c.rtnb_cnt as rtnb_cnt,
    trns_prod_shp_c.retn_cnt as retn_cnt,
    trns_prod_shp_c.avl_shp_cnt as avl_shp_cnt,
    trns_prod_shp_c.shp_amt as shp_amt,
    trns_prod_shp_c.shpp_amt as shpp_amt,
    trns_prod_shp_c.expy_endtime as expy_endtime,
    trns_prod_shp_c.purc_amt as purc_amt,
    trns_prod_shp_c.ord_sumamt as ord_sumamt,
    trns_prod_shp_c.cncl_time as cncl_time,
    trns_prod_shp_c.prod_type as prod_type,
    trns_prod_shp_c.hosp_list_id as hosp_list_id,
    trns_prod_shp_c.itemname as itemname,
    trns_prod_shp_c.delventp_cnfm_shpp_stas as delventp_cnfm_shpp_stas,
    trns_prod_shp_c.delventp_cnfm_shpp_time as delventp_cnfm_shpp_time,
    trns_prod_shp_c.plan_detl_memo as plan_detl_memo,
    trns_prod_shp_c.shp_memo as shp_memo,
    trns_prod_shp_c.read_time as read_time,
    trns_prod_shp_c.read_flag as read_flag,
    trns_prod_shp_c.crter_id as crter_id,
    trns_prod_shp_c.crter_name as crter_name,
    trns_prod_shp_c.crte_optins_no as crte_optins_no,
    trns_prod_shp_c.crte_time as crte_time,
    trns_prod_shp_c.opter_id as opter_id,
    trns_prod_shp_c.opter_name as opter_name,
    trns_prod_shp_c.splm_flag as splm_flag,
    trns_prod_shp_c.opt_time as opt_time,
    trns_prod_shp_c.updt_time as updt_time,
    trns_prod_shp_c.tenant_id as tenant_id,
    trns_prod_shp_c.avl_shpp_cnt as avl_shpp_cnt,
    trns_prod_shp_c.erp_shp_id as erp_shp_id,
    trns_prod_shp_c.deal_drug_snap_id as deal_drug_snap_id,
    trns_prod_shp_c.ext_code as ext_code,
    trns_prod_shp_c.place_ord_time as place_ord_time,
    trns_prod_shp_c.his_ord_no as his_ord_no,
    trns_prod_shp_c.his_ord_detl_no as his_ord_detl_no,
    trns_prod_shp_c.reject_time as reject_time,
    trns_prod_shp_c.reject_reason as reject_reason,
    trns_prod_shp_c.stroom_id as stroom_id,
    trns_prod_shp_c.seld_id as seld_id,
    trns_prod_shp_c.comb_flag as comb_flag,
    trns_prod_shp_c.mcs_comb_id as mcs_comb_id,
    trns_prod_shp_c.expy_end_date as expy_end_date,
    trns_prod_shp_c.invo_date as invo_date,
    trns_prod_shp_c.adjust_flag as adjust_flag,
    trns_prod_shp_c.ord_souc as ord_souc,
    trns_prod_shp_c.biz_date as biz_date,
    trns_prod_shp_c.deleted_time as deleted_time,
    trns_prod_shp_c.deleted as deleted,
    trns_prod_shp_c.exch_updt_time as exch_updt_time,
    trns_prod_shp_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.trns_prod_shp_c as t
LEFT JOIN
(
SELECT
trns_medins_hi_rlts_d.tenant_id,
trns_medins_hi_rlts_d.admdvs,
trns_medins_hi_rlts_d.cret_date,
ROW_NUMBER() OVER(PARTITION BY trns_medins_hi_rlts_d.tenant_id ORDER BY trns_medins_hi_rlts_d.cret_date DESC) AS rn
FROM
trns_medins_hi_rlts_d
) AS RB
ON
trns_prod_shp_c.tenant_id = RB.tenant_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table trns_prod_retn_c partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_prod_retn_c.hosp_bidprcu_item_id as hosp_bidprcu_item_id,
    trns_prod_retn_c.opter_id as opter_id,
    trns_prod_retn_c.admorg_code as admorg_code,
    trns_prod_retn_c.retn_cnt as retn_cnt,
    trns_prod_retn_c.rtnb_cnt as rtnb_cnt,
    trns_prod_retn_c.admorg_name as admorg_name,
    trns_prod_retn_c.medins_retn_time as medins_retn_time,
    trns_prod_retn_c.adjust_flag as adjust_flag,
    trns_prod_retn_c.cncl_time as cncl_time,
    trns_prod_retn_c.exch_updt_time as exch_updt_time,
    trns_prod_retn_c.ord_detl_id as ord_detl_id,
    trns_prod_retn_c.retn_id as retn_id,
    trns_prod_retn_c.retn_code as retn_code,
    trns_prod_retn_c.ord_code as ord_code,
    trns_prod_retn_c.medins_name as medins_name,
    trns_prod_retn_c.shp_code as shp_code,
    trns_prod_retn_c.shp_id as shp_id,
    trns_prod_retn_c.medins_code as medins_code,
    trns_prod_retn_c.delventp_code as delventp_code,
    trns_prod_retn_c.delventp_name as delventp_name,
    trns_prod_retn_c.prod_id as prod_id,
    trns_prod_retn_c.prod_name as prod_name,
    trns_prod_retn_c.shp_cnt as shp_cnt,
    trns_prod_retn_c.shp_pric as shp_pric,
    trns_prod_retn_c.shpp_cnt as shpp_cnt,
    trns_prod_retn_c.hosp_list_id as hosp_list_id,
    trns_prod_retn_c.retn_chk_stas as retn_chk_stas,
    trns_prod_retn_c.medins_retn_rea as medins_retn_rea,
    trns_prod_retn_c.delventp_fail_rea as delventp_fail_rea,
    trns_prod_retn_c.delventp_pass_time as delventp_pass_time,
    trns_prod_retn_c.delventp_fail_time as delventp_fail_time,
    trns_prod_retn_c.manu_lotnum as manu_lotnum,
    trns_prod_retn_c.crter_id as crter_id,
    trns_prod_retn_c.crter_name as crter_name,
    trns_prod_retn_c.crte_optins_no as crte_optins_no,
    trns_prod_retn_c.crte_time as crte_time,
    trns_prod_retn_c.opter_name as opter_name,
    trns_prod_retn_c.optins_no as optins_no,
    trns_prod_retn_c.prod_type as prod_type,
    trns_prod_retn_c.opt_time as opt_time,
    trns_prod_retn_c.updt_time as updt_time,
    trns_prod_retn_c.invd_flag as invd_flag,
    trns_prod_retn_c.tenant_id as tenant_id,
    trns_prod_retn_c.deal_drug_snap_id as deal_drug_snap_id,
    trns_prod_retn_c.ext_code as ext_code,
    trns_prod_retn_c.ext_updt_time as ext_updt_time,
    trns_prod_retn_c.snap_id as snap_id,
    trns_prod_retn_c.seld_id as seld_id,
    trns_prod_retn_c.comb_flag as comb_flag,
    trns_prod_retn_c.stroom_id as stroom_id,
    trns_prod_retn_c.mcs_comb_id as mcs_comb_id,
    trns_prod_retn_c.comb_ver as comb_ver,
    trns_prod_retn_c.biz_date as biz_date,
    trns_prod_retn_c.deleted_time as deleted_time,
    trns_prod_retn_c.deleted as deleted,
    trns_prod_retn_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs
from src_data.trns_prod_retn_c as t
LEFT JOIN
(
SELECT
trns_medins_hi_rlts_d.tenant_id,
trns_medins_hi_rlts_d.admdvs,
trns_medins_hi_rlts_d.cret_date,
ROW_NUMBER() OVER(PARTITION BY trns_medins_hi_rlts_d.tenant_id ORDER BY trns_medins_hi_rlts_d.cret_date DESC) AS rn
FROM
trns_medins_hi_rlts_d
) AS RB
ON
trns_prod_retn_c.tenant_id = RB.tenant_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table trns_ord_detl_c partition(dt)
select 
    concat(biz_date,subsys_codg,rid) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trns_ord_detl_c.acp_time as acp_time,
    trns_ord_detl_c.crte_optins_no as crte_optins_no,
    trns_ord_detl_c.tenant_id as tenant_id,
    trns_ord_detl_c.ext_code as ext_code,
    trns_ord_detl_c.main_parts as main_parts,
    trns_ord_detl_c.area_code as area_code,
    trns_ord_detl_c.acp_flag as acp_flag,
    trns_ord_detl_c.ord_detl_stas as ord_detl_stas,
    trns_ord_detl_c.medins_name as medins_name,
    trns_ord_detl_c.itemname as itemname,
    trns_ord_detl_c.biz_date as biz_date,
    trns_ord_detl_c.hosp_bidprcu_item_id as hosp_bidprcu_item_id,
    trns_ord_detl_c.crte_time as crte_time,
    trns_ord_detl_c.item_codg as item_codg,
    trns_ord_detl_c.exra_serv_pric as exra_serv_pric,
    trns_ord_detl_c.seld_id as seld_id,
    trns_ord_detl_c.deleted as deleted,
    trns_ord_detl_c.read_time as read_time,
    trns_ord_detl_c.crter_id as crter_id,
    trns_ord_detl_c.opt_time as opt_time,
    trns_ord_detl_c.delventp_code as delventp_code,
    trns_ord_detl_c.prod_type as prod_type,
    trns_ord_detl_c.snap_id as snap_id,
    trns_ord_detl_c.pubonln_stas as pubonln_stas,
    trns_ord_detl_c.outsto_cnt as outsto_cnt,
    trns_ord_detl_c.deal_drug_snap_id as deal_drug_snap_id,
    trns_ord_detl_c.ext_updt_time as ext_updt_time,
    trns_ord_detl_c.cncl_time as cncl_time,
    trns_ord_detl_c.cncl_type as cncl_type,
    trns_ord_detl_c.purc_plan_det_id as purc_plan_det_id,
    trns_ord_detl_c.purc_plan_id as purc_plan_id,
    trns_ord_detl_c.list_attr_code as list_attr_code,
    trns_ord_detl_c.prod_tag as prod_tag,
    trns_ord_detl_c.nego_tag as nego_tag,
    trns_ord_detl_c.prov_amt_tag as prov_amt_tag,
    trns_ord_detl_c.his_ord_detl_no as his_ord_detl_no,
    trns_ord_detl_c.shp_time as shp_time,
    trns_ord_detl_c.shpp_time as shpp_time,
    trns_ord_detl_c.comb_flag as comb_flag,
    trns_ord_detl_c.mcs_comb_id as mcs_comb_id,
    trns_ord_detl_c.exra_serv_flag as exra_serv_flag,
    trns_ord_detl_c.mcs_comb_prod_detail_id as mcs_comb_prod_detail_id,
    trns_ord_detl_c.comb_cnt as comb_cnt,
    trns_ord_detl_c.sprt_reate as sprt_reate,
    trns_ord_detl_c.used as used,
    trns_ord_detl_c.stroom_id as stroom_id,
    trns_ord_detl_c.place_ord_time as place_ord_time,
    trns_ord_detl_c.un_pubonln_rea as un_pubonln_rea,
    trns_ord_detl_c.detl_info as detl_info,
    trns_ord_detl_c.delv_outsto_liable as delv_outsto_liable,
    trns_ord_detl_c.delv_outsto_rea as delv_outsto_rea,
    trns_ord_detl_c.deleted_time as deleted_time,
    trns_ord_detl_c.exch_updt_time as exch_updt_time,
    trns_ord_detl_c.subsys_codg as subsys_codg,
    RB.admdvs as admdvs,
    trns_ord_detl_c.ord_detl_id as ord_detl_id,
    trns_ord_detl_c.ord_id as ord_id,
    trns_ord_detl_c.ord_code as ord_code,
    trns_ord_detl_c.prod_name as prod_name,
    trns_ord_detl_c.purc_cnt as purc_cnt,
    trns_ord_detl_c.purc_pric as purc_pric,
    trns_ord_detl_c.prod_id as prod_id,
    trns_ord_detl_c.read_flag as read_flag,
    trns_ord_detl_c.hosp_list_id as hosp_list_id,
    trns_ord_detl_c.crter_name as crter_name,
    trns_ord_detl_c.opter_id as opter_id,
    trns_ord_detl_c.opter_name as opter_name,
    trns_ord_detl_c.optins_no as optins_no,
    trns_ord_detl_c.updt_time as updt_time,
    trns_ord_detl_c.invd_flag as invd_flag,
    trns_ord_detl_c.medins_code as medins_code,
    trns_ord_detl_c.delventp_name as delventp_name,
    trns_ord_detl_c.shp_cnt as shp_cnt,
    trns_ord_detl_c.shpp_cnt as shpp_cnt,
    trns_ord_detl_c.retn_cnt as retn_cnt,
    trns_ord_detl_c.plan_detl_memo as plan_detl_memo,
    trns_ord_detl_c.send_time as send_time,
    trns_ord_detl_c.outsto_time as outsto_time
from src_data.trns_ord_detl_c as t
LEFT JOIN
(
SELECT
trns_medins_hi_rlts_d.tenant_id,
trns_medins_hi_rlts_d.admdvs,
trns_medins_hi_rlts_d.cret_date,
ROW_NUMBER() OVER(PARTITION BY trns_medins_hi_rlts_d.tenant_id ORDER BY trns_medins_hi_rlts_d.cret_date DESC) AS rn
FROM
trns_medins_hi_rlts_d
) AS RB
ON
trns_ord_detl_c.tenant_id = RB.tenant_id AND RB.rn = 1
where 1=1
) as tab

-- ================================================
insert overwrite table mcs_pubonln_trade_d partition(dt)
select 
    concat(subsys_codg,pubonln_rslt_id,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    mcs_pubonln_trade_d.exch_updt_time as exch_updt_time,
    mcs_pubonln_trade_d.admdvs as admdvs,
    mcs_pubonln_trade_d.subsys_codg as subsys_codg,
    mcs_pubonln_trade_d.mcs_regno as mcs_regno,
    mcs_pubonln_trade_d.deleted as deleted,
    mcs_pubonln_trade_d.pubonln_rslt_id as pubonln_rslt_id,
    mcs_pubonln_trade_d.crte_time as crte_time,
    mcs_pubonln_trade_d.deleted_time as deleted_time,
    mcs_pubonln_trade_d.biz_date as biz_date
from src_data.mcs_pubonln_trade_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table drug_pubonln_rslt_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    drug_pubonln_rslt_d.rute_code as rute_code,
    drug_pubonln_rslt_d.prod_souc_name as prod_souc_name,
    drug_pubonln_rslt_d.ypid as ypid,
    drug_pubonln_rslt_d.prod_name as prod_name,
    drug_pubonln_rslt_d.rute_name as rute_name,
    drug_pubonln_rslt_d.cal_spec_code as cal_spec_code,
    drug_pubonln_rslt_d.cal_spec_name as cal_spec_name,
    drug_pubonln_rslt_d.dayuse as dayuse,
    drug_pubonln_rslt_d.convrat as convrat,
    drug_pubonln_rslt_d.market_permit_holder as market_permit_holder,
    drug_pubonln_rslt_d.min_rpup_prc as min_rpup_prc,
    drug_pubonln_rslt_d.diffpric_dosform_code as diffpric_dosform_code,
    drug_pubonln_rslt_d.diffpric_dosform as diffpric_dosform,
    drug_pubonln_rslt_d.big_pac as big_pac,
    drug_pubonln_rslt_d.big_pac_unt as big_pac_unt,
    drug_pubonln_rslt_d.lowpricmed_flag as lowpricmed_flag,
    drug_pubonln_rslt_d.essdrug_seq as essdrug_seq,
    drug_pubonln_rslt_d.med_code as med_code,
    drug_pubonln_rslt_d.county_product_code as county_product_code,
    drug_pubonln_rslt_d.pubonln_stas as pubonln_stas,
    drug_pubonln_rslt_d.npubonln_type as npubonln_type,
    drug_pubonln_rslt_d.npubonln_rea as npubonln_rea,
    drug_pubonln_rslt_d.cancel_pubonln_time as cancel_pubonln_time,
    drug_pubonln_rslt_d.tenditm_id as tenditm_id,
    drug_pubonln_rslt_d.tenditm_name as tenditm_name,
    drug_pubonln_rslt_d.blng_item as blng_item,
    drug_pubonln_rslt_d.drug_identification as drug_identification,
    drug_pubonln_rslt_d.compete_type as compete_type,
    drug_pubonln_rslt_d.consistency_evaluation_flag as consistency_evaluation_flag,
    drug_pubonln_rslt_d.druglist_code as druglist_code,
    drug_pubonln_rslt_d.grpno as grpno,
    drug_pubonln_rslt_d.tpclprod as tpclprod,
    drug_pubonln_rslt_d.latest_pubonln_pric as latest_pubonln_pric,
    drug_pubonln_rslt_d.pubonln_pric as pubonln_pric,
    drug_pubonln_rslt_d.max_lmtpric as max_lmtpric,
    drug_pubonln_rslt_d.lmtpric as lmtpric,
    drug_pubonln_rslt_d.max_lmtpric_type as max_lmtpric_type,
    drug_pubonln_rslt_d.toplmtp_remark as toplmtp_remark,
    drug_pubonln_rslt_d.price_comparison as price_comparison,
    drug_pubonln_rslt_d.max_lmtpric_parity as max_lmtpric_parity,
    drug_pubonln_rslt_d.max_lmtpric_parity_type as max_lmtpric_parity_type,
    drug_pubonln_rslt_d.aprvno_expy_begntime as aprvno_expy_begntime,
    drug_pubonln_rslt_d.aprvno_expy_endtime as aprvno_expy_endtime,
    drug_pubonln_rslt_d.aprvno_att as aprvno_att,
    drug_pubonln_rslt_d.regno as regno,
    drug_pubonln_rslt_d.regcert_expy_begntime as regcert_expy_begntime,
    drug_pubonln_rslt_d.regcert_expy_endtime as regcert_expy_endtime,
    drug_pubonln_rslt_d.regcert_att as regcert_att,
    drug_pubonln_rslt_d.prod_manl_att as prod_manl_att,
    drug_pubonln_rslt_d.prod_img_att as prod_img_att,
    drug_pubonln_rslt_d.trt_attr_code as trt_attr_code,
    drug_pubonln_rslt_d.trt_attr_att as trt_attr_att,
    drug_pubonln_rslt_d.orrs_flag as orrs_flag,
    drug_pubonln_rslt_d.orrs_type as orrs_type,
    drug_pubonln_rslt_d.orrs_attr_att as orrs_attr_att,
    drug_pubonln_rslt_d.fda_flag as fda_flag,
    drug_pubonln_rslt_d.fda_attr_att as fda_attr_att,
    drug_pubonln_rslt_d.cons_eval_flag as cons_eval_flag,
    drug_pubonln_rslt_d.cons_eval_att as cons_eval_att,
    drug_pubonln_rslt_d.druglist_att as druglist_att,
    drug_pubonln_rslt_d.ref_prep_att as ref_prep_att,
    drug_pubonln_rslt_d.non_cptn_flag as non_cptn_flag,
    drug_pubonln_rslt_d.non_cptn_attr as non_cptn_attr,
    drug_pubonln_rslt_d.non_cptn_attr_att as non_cptn_attr_att,
    drug_pubonln_rslt_d.sp_drug_type as sp_drug_type,
    drug_pubonln_rslt_d.sp_drug_att as sp_drug_att,
    drug_pubonln_rslt_d.insn_flag as insn_flag,
    drug_pubonln_rslt_d.osm_pre as osm_pre,
    drug_pubonln_rslt_d.lowga_flag as lowga_flag,
    drug_pubonln_rslt_d.amac_type as amac_type,
    drug_pubonln_rslt_d.poa_att as poa_att,
    drug_pubonln_rslt_d.poa_expy_begntime as poa_expy_begntime,
    drug_pubonln_rslt_d.poa_expy_endtime as poa_expy_endtime,
    drug_pubonln_rslt_d.gmp_cont as gmp_cont,
    drug_pubonln_rslt_d.qua_ext_cont as qua_ext_cont,
    drug_pubonln_rslt_d.att as att,
    drug_pubonln_rslt_d.uscc as uscc,
    drug_pubonln_rslt_d.dcla_entp_name as dcla_entp_name,
    drug_pubonln_rslt_d.prxy_entp_code as prxy_entp_code,
    drug_pubonln_rslt_d.prxy_entp_name as prxy_entp_name,
    drug_pubonln_rslt_d.changed_time as changed_time,
    drug_pubonln_rslt_d.changed_content as changed_content,
    drug_pubonln_rslt_d.memo as memo,
    drug_pubonln_rslt_d.efcc_type as efcc_type,
    drug_pubonln_rslt_d.pubonln_type as pubonln_type,
    drug_pubonln_rslt_d.seld_stas as seld_stas,
    drug_pubonln_rslt_d.adjm_chk_stas as adjm_chk_stas,
    drug_pubonln_rslt_d.admdvs as admdvs,
    drug_pubonln_rslt_d.admdvs_name as admdvs_name,
    drug_pubonln_rslt_d.item_drug_ful_info_id as item_drug_ful_info_id,
    drug_pubonln_rslt_d.purc_totlcnt as purc_totlcnt,
    drug_pubonln_rslt_d.drug_expy as drug_expy,
    drug_pubonln_rslt_d.bidprcu_datatype as bidprcu_datatype,
    drug_pubonln_rslt_d.hi_pay_std as hi_pay_std,
    drug_pubonln_rslt_d.bkls_flag as bkls_flag,
    drug_pubonln_rslt_d.bkls_begntime as bkls_begntime,
    drug_pubonln_rslt_d.bkls_endtime as bkls_endtime,
    drug_pubonln_rslt_d.isu_prov_code as isu_prov_code,
    drug_pubonln_rslt_d.selc_stas as selc_stas,
    drug_pubonln_rslt_d.pric_sort as pric_sort,
    drug_pubonln_rslt_d.match_stas as match_stas,
    drug_pubonln_rslt_d.ext_goods_id as ext_goods_id,
    drug_pubonln_rslt_d.ext_prod_id as ext_prod_id,
    drug_pubonln_rslt_d.ext_prxy_id as ext_prxy_id,
    drug_pubonln_rslt_d.nat_prov_min_pubonln_pric as nat_prov_min_pubonln_pric,
    drug_pubonln_rslt_d.prcunt_name as prcunt_name,
    drug_pubonln_rslt_d.fil_no as fil_no,
    drug_pubonln_rslt_d.fil_time as fil_time,
    drug_pubonln_rslt_d.outsto_remark as outsto_remark,
    drug_pubonln_rslt_d.tenant_id as tenant_id,
    drug_pubonln_rslt_d.crte_time as crte_time,
    drug_pubonln_rslt_d.crter_id as crter_id,
    drug_pubonln_rslt_d.crte_optins_no as crte_optins_no,
    drug_pubonln_rslt_d.opter_id as opter_id,
    drug_pubonln_rslt_d.opter_name as opter_name,
    drug_pubonln_rslt_d.optins_no as optins_no,
    drug_pubonln_rslt_d.updt_time as updt_time,
    drug_pubonln_rslt_d.invd_flag as invd_flag,
    drug_pubonln_rslt_d.biz_date as biz_date,
    drug_pubonln_rslt_d.deleted_time as deleted_time,
    drug_pubonln_rslt_d.deleted as deleted,
    drug_pubonln_rslt_d.exch_updt_time as exch_updt_time,
    drug_pubonln_rslt_d.pubonln_rslt_id as pubonln_rslt_id,
    drug_pubonln_rslt_d.ext_code as ext_code,
    drug_pubonln_rslt_d.adjm_type as adjm_type,
    drug_pubonln_rslt_d.drug_code as drug_code,
    drug_pubonln_rslt_d.old_sys_prod_id as old_sys_prod_id,
    drug_pubonln_rslt_d.drugstdcode as drugstdcode,
    drug_pubonln_rslt_d.nat_drug_no as nat_drug_no,
    drug_pubonln_rslt_d.drug_name as drug_name,
    drug_pubonln_rslt_d.drug_name_pinyin as drug_name_pinyin,
    drug_pubonln_rslt_d.genname_code as genname_code,
    drug_pubonln_rslt_d.genname as genname,
    drug_pubonln_rslt_d.dosform_code as dosform_code,
    drug_pubonln_rslt_d.dosform_name as dosform_name,
    drug_pubonln_rslt_d.spec_name as spec_name,
    drug_pubonln_rslt_d.minunt_name as minunt_name,
    drug_pubonln_rslt_d.minpacunt_code as minpacunt_code,
    drug_pubonln_rslt_d.pac as pac,
    drug_pubonln_rslt_d.pacmatl_code as pacmatl_code,
    drug_pubonln_rslt_d.pacmatl_name as pacmatl_name,
    drug_pubonln_rslt_d.pacmatl as pacmatl,
    drug_pubonln_rslt_d.bas_medn_flag as bas_medn_flag,
    drug_pubonln_rslt_d.acdbas_code as acdbas_code,
    drug_pubonln_rslt_d.acdbas_name as acdbas_name,
    drug_pubonln_rslt_d.otc_attr_code as otc_attr_code,
    drug_pubonln_rslt_d.otc_attr_name as otc_attr_name,
    drug_pubonln_rslt_d.prodentp_code as prodentp_code,
    drug_pubonln_rslt_d.prodentp_name as prodentp_name,
    drug_pubonln_rslt_d.subpck_entp_name as subpck_entp_name,
    drug_pubonln_rslt_d.drug_attr_name as drug_attr_name,
    drug_pubonln_rslt_d.prod_souc_code as prod_souc_code,
    drug_pubonln_rslt_d.vita_type as vita_type,
    drug_pubonln_rslt_d.minpacunt_name as minpacunt_name,
    drug_pubonln_rslt_d.subpck_entp_code as subpck_entp_code,
    drug_pubonln_rslt_d.druglist_name as druglist_name,
    drug_pubonln_rslt_d.grp_id as grp_id,
    drug_pubonln_rslt_d.tpclprod_modi_flag as tpclprod_modi_flag,
    drug_pubonln_rslt_d.latest_lmtpric as latest_lmtpric,
    drug_pubonln_rslt_d.aprvno as aprvno,
    drug_pubonln_rslt_d.nsugar_flag as nsugar_flag,
    drug_pubonln_rslt_d.syn_flag as syn_flag,
    drug_pubonln_rslt_d.spec_code as spec_code,
    drug_pubonln_rslt_d.drug_public_inquiry as drug_public_inquiry,
    drug_pubonln_rslt_d.sp_drug_flag as sp_drug_flag,
    drug_pubonln_rslt_d.bez_musk_type as bez_musk_type,
    drug_pubonln_rslt_d.minunt_code as minunt_code,
    drug_pubonln_rslt_d.minprc as minprc,
    drug_pubonln_rslt_d.entp_quo_parity as entp_quo_parity,
    drug_pubonln_rslt_d.druglist_flag as druglist_flag,
    drug_pubonln_rslt_d.ref_prep_flag as ref_prep_flag,
    drug_pubonln_rslt_d.latest_modify_time as latest_modify_time,
    drug_pubonln_rslt_d.opt_time as opt_time,
    drug_pubonln_rslt_d.qlv_srt as qlv_srt,
    drug_pubonln_rslt_d.prov_lmtpric as prov_lmtpric,
    drug_pubonln_rslt_d.ext_product_id as ext_product_id,
    drug_pubonln_rslt_d.crter_name as crter_name,
    drug_pubonln_rslt_d.subsys_codg as subsys_codg
from src_data.drug_pubonln_rslt_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table tran_admdvs_open_a partition(dt)
select 
    concat(subsys_codg,rid,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    tran_admdvs_open_a.admdvs_name as admdvs_name,
    tran_admdvs_open_a.memo as memo,
    tran_admdvs_open_a.exch_updt_time as exch_updt_time,
    tran_admdvs_open_a.ver as ver,
    tran_admdvs_open_a.deleted_time as deleted_time,
    tran_admdvs_open_a.subsys_codg as subsys_codg,
    tran_admdvs_open_a.admdvs as admdvs,
    tran_admdvs_open_a.insu_open_sign as insu_open_sign,
    tran_admdvs_open_a.fix_open_sign as fix_open_sign,
    tran_admdvs_open_a.tel as tel,
    tran_admdvs_open_a.coner as coner,
    tran_admdvs_open_a.mbb_tel as mbb_tel,
    tran_admdvs_open_a.inhos_open_sign as inhos_open_sign,
    tran_admdvs_open_a.outpa_open_sign as outpa_open_sign,
    tran_admdvs_open_a.spout_open_sign as spout_open_sign,
    tran_admdvs_open_a.vali_flag as vali_flag,
    tran_admdvs_open_a.chg_begntime as chg_begntime,
    tran_admdvs_open_a.crte_time as crte_time,
    tran_admdvs_open_a.updt_time as updt_time,
    tran_admdvs_open_a.crter_id as crter_id,
    tran_admdvs_open_a.crter_name as crter_name,
    tran_admdvs_open_a.opter_id as opter_id,
    tran_admdvs_open_a.opter_name as opter_name,
    tran_admdvs_open_a.opt_time as opt_time,
    tran_admdvs_open_a.optins_no as optins_no,
    tran_admdvs_open_a.biz_date as biz_date,
    tran_admdvs_open_a.deleted as deleted
from src_data.tran_admdvs_open_a as t
where 1=1
) as tab

-- ================================================
insert overwrite table outmed_setl_d partition(dt)
select 
    concat(biz_date,rid,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    outmed_setl_d.flxempe_flag as flxempe_flag,
    outmed_setl_d.afil_indu as afil_indu,
    outmed_setl_d.pay_loc as pay_loc,
    outmed_setl_d.setl_time as setl_time,
    outmed_setl_d.certno as certno,
    outmed_setl_d.insu_admdvs as insu_admdvs,
    outmed_setl_d.fixmedins_name as fixmedins_name,
    outmed_setl_d.biz_date as biz_date,
    outmed_setl_d.init_setl_id as init_setl_id,
    outmed_setl_d.balc as balc,
    outmed_setl_d.crte_optins_no as crte_optins_no,
    outmed_setl_d.pool_prop_selfpay as pool_prop_selfpay,
    outmed_setl_d.acct_mulaid_pay as acct_mulaid_pay,
    outmed_setl_d.mdtrt_cert_no as mdtrt_cert_no,
    outmed_setl_d.refd_setl_flag as refd_setl_flag,
    outmed_setl_d.memo as memo,
    outmed_setl_d.setl_id as setl_id,
    outmed_setl_d.clr_optins as clr_optins,
    outmed_setl_d.medins_setl_id as medins_setl_id,
    outmed_setl_d.mdtrt_id as mdtrt_id,
    outmed_setl_d.psn_name as psn_name,
    outmed_setl_d.psn_no as psn_no,
    outmed_setl_d.psn_insu_rlts_id as psn_insu_rlts_id,
    outmed_setl_d.psn_cert_type as psn_cert_type,
    outmed_setl_d.gend as gend,
    outmed_setl_d.naty as naty,
    outmed_setl_d.brdy as brdy,
    outmed_setl_d.age as age,
    outmed_setl_d.insutype as insutype,
    outmed_setl_d.psn_type as psn_type,
    outmed_setl_d.cvlserv_flag as cvlserv_flag,
    outmed_setl_d.cvlserv_lv as cvlserv_lv,
    outmed_setl_d.sp_psn_type as sp_psn_type,
    outmed_setl_d.sp_psn_type_lv as sp_psn_type_lv,
    outmed_setl_d.clct_grde as clct_grde,
    outmed_setl_d.nwb_flag as nwb_flag,
    outmed_setl_d.emp_no as emp_no,
    outmed_setl_d.emp_name as emp_name,
    outmed_setl_d.emp_type as emp_type,
    outmed_setl_d.econ_type as econ_type,
    outmed_setl_d.afil_rlts as afil_rlts,
    outmed_setl_d.emp_mgt_type as emp_mgt_type,
    outmed_setl_d.fixmedins_code as fixmedins_code,
    outmed_setl_d.hosp_lv as hosp_lv,
    outmed_setl_d.fix_blng_admdvs as fix_blng_admdvs,
    outmed_setl_d.lmtpric_hosp_lv as lmtpric_hosp_lv,
    outmed_setl_d.dedc_hosp_lv as dedc_hosp_lv,
    outmed_setl_d.begndate as begndate,
    outmed_setl_d.enddate as enddate,
    outmed_setl_d.mdtrt_cert_type as mdtrt_cert_type,
    outmed_setl_d.med_type as med_type,
    outmed_setl_d.setl_type as setl_type,
    outmed_setl_d.clr_type as clr_type,
    outmed_setl_d.clr_way as clr_way,
    outmed_setl_d.overlmt_selfpay as overlmt_selfpay,
    outmed_setl_d.psn_setlway as psn_setlway,
    outmed_setl_d.medfee_sumamt as medfee_sumamt,
    outmed_setl_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    outmed_setl_d.preselfpay_amt as preselfpay_amt,
    outmed_setl_d.inscp_amt as inscp_amt,
    outmed_setl_d.dedc_std as dedc_std,
    outmed_setl_d.crt_dedc as crt_dedc,
    outmed_setl_d.act_pay_dedc as act_pay_dedc,
    outmed_setl_d.hifp_pay as hifp_pay,
    outmed_setl_d.hi_agre_sumfee as hi_agre_sumfee,
    outmed_setl_d.cvlserv_pay as cvlserv_pay,
    outmed_setl_d.hifes_pay as hifes_pay,
    outmed_setl_d.hifmi_pay as hifmi_pay,
    outmed_setl_d.hifob_pay as hifob_pay,
    outmed_setl_d.hifdm_pay as hifdm_pay,
    outmed_setl_d.maf_pay as maf_pay,
    outmed_setl_d.othfund_pay as othfund_pay,
    outmed_setl_d.fund_pay_sumamt as fund_pay_sumamt,
    outmed_setl_d.psn_pay as psn_pay,
    outmed_setl_d.acct_pay as acct_pay,
    outmed_setl_d.cash_payamt as cash_payamt,
    outmed_setl_d.ownpay_hosp_part as ownpay_hosp_part,
    outmed_setl_d.cal_ipt_cnt as cal_ipt_cnt,
    outmed_setl_d.setl_cashpay_way as setl_cashpay_way,
    outmed_setl_d.year as year,
    outmed_setl_d.dise_no as dise_no,
    outmed_setl_d.dise_name as dise_name,
    outmed_setl_d.invono as invono,
    outmed_setl_d.manl_reim_rea as manl_reim_rea,
    outmed_setl_d.vali_flag as vali_flag,
    outmed_setl_d.updt_time as updt_time,
    outmed_setl_d.crter_id as crter_id,
    outmed_setl_d.crter_name as crter_name,
    outmed_setl_d.crte_time as crte_time,
    outmed_setl_d.opter_id as opter_id,
    outmed_setl_d.opter_name as opter_name,
    outmed_setl_d.opt_time as opt_time,
    outmed_setl_d.optins_no as optins_no,
    outmed_setl_d.poolarea_no as poolarea_no,
    outmed_setl_d.deleted_time as deleted_time,
    outmed_setl_d.deleted as deleted,
    outmed_setl_d.exch_updt_time as exch_updt_time,
    outmed_setl_d.subsys_codg as subsys_codg,
    outmed_setl_d.insu_admdvs as admdvs
from src_data.outmed_setl_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table outmed_mdtrt_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    outmed_mdtrt_d.mdtrt_id as mdtrt_id,
    outmed_mdtrt_d.medins_setl_id as medins_setl_id,
    outmed_mdtrt_d.psn_no as psn_no,
    outmed_mdtrt_d.psn_insu_rlts_id as psn_insu_rlts_id,
    outmed_mdtrt_d.psn_cert_type as psn_cert_type,
    outmed_mdtrt_d.psn_name as psn_name,
    outmed_mdtrt_d.gend as gend,
    outmed_mdtrt_d.naty as naty,
    outmed_mdtrt_d.brdy as brdy,
    outmed_mdtrt_d.coner_name as coner_name,
    outmed_mdtrt_d.tel as tel,
    outmed_mdtrt_d.addr as addr,
    outmed_mdtrt_d.insutype as insutype,
    outmed_mdtrt_d.cvlserv_lv as cvlserv_lv,
    outmed_mdtrt_d.sp_psn_type as sp_psn_type,
    outmed_mdtrt_d.sp_psn_type_lv as sp_psn_type_lv,
    outmed_mdtrt_d.emp_no as emp_no,
    outmed_mdtrt_d.flxempe_flag as flxempe_flag,
    outmed_mdtrt_d.nwb_flag as nwb_flag,
    outmed_mdtrt_d.insu_admdvs as insu_admdvs,
    outmed_mdtrt_d.emp_name as emp_name,
    outmed_mdtrt_d.econ_type as econ_type,
    outmed_mdtrt_d.afil_indu as afil_indu,
    outmed_mdtrt_d.afil_rlts as afil_rlts,
    outmed_mdtrt_d.emp_mgt_type as emp_mgt_type,
    outmed_mdtrt_d.pay_loc as pay_loc,
    outmed_mdtrt_d.fixmedins_code as fixmedins_code,
    outmed_mdtrt_d.fixmedins_name as fixmedins_name,
    outmed_mdtrt_d.hosp_lv as hosp_lv,
    outmed_mdtrt_d.fix_blng_admdvs as fix_blng_admdvs,
    outmed_mdtrt_d.lmtpric_hosp_lv as lmtpric_hosp_lv,
    outmed_mdtrt_d.dedc_hosp_lv as dedc_hosp_lv,
    outmed_mdtrt_d.begntime as begntime,
    outmed_mdtrt_d.endtime as endtime,
    outmed_mdtrt_d.mdtrt_cert_type as mdtrt_cert_type,
    outmed_mdtrt_d.med_type as med_type,
    outmed_mdtrt_d.rloc_type as rloc_type,
    outmed_mdtrt_d.pre_pay_flag as pre_pay_flag,
    outmed_mdtrt_d.year as year,
    outmed_mdtrt_d.ipt_otp_no as ipt_otp_no,
    outmed_mdtrt_d.medrcdno as medrcdno,
    outmed_mdtrt_d.chfpdr_code as chfpdr_code,
    outmed_mdtrt_d.adm_diag_dscr as adm_diag_dscr,
    outmed_mdtrt_d.adm_dept_codg as adm_dept_codg,
    outmed_mdtrt_d.adm_dept_name as adm_dept_name,
    outmed_mdtrt_d.adm_bed as adm_bed,
    outmed_mdtrt_d.wardarea_bed as wardarea_bed,
    outmed_mdtrt_d.traf_dept_flag as traf_dept_flag,
    outmed_mdtrt_d.dscg_maindiag_code as dscg_maindiag_code,
    outmed_mdtrt_d.dscg_dept_codg as dscg_dept_codg,
    outmed_mdtrt_d.dise_no as dise_no,
    outmed_mdtrt_d.dise_name as dise_name,
    outmed_mdtrt_d.oprn_oprt_name as oprn_oprt_name,
    outmed_mdtrt_d.otp_diag_info as otp_diag_info,
    outmed_mdtrt_d.die_date as die_date,
    outmed_mdtrt_d.ipt_days as ipt_days,
    outmed_mdtrt_d.fpsc_no as fpsc_no,
    outmed_mdtrt_d.matn_type as matn_type,
    outmed_mdtrt_d.birctrl_type as birctrl_type,
    outmed_mdtrt_d.latechb_flag as latechb_flag,
    outmed_mdtrt_d.geso_val as geso_val,
    outmed_mdtrt_d.fetts as fetts,
    outmed_mdtrt_d.fetus_cnt as fetus_cnt,
    outmed_mdtrt_d.pret_flag as pret_flag,
    outmed_mdtrt_d.prey_time as prey_time,
    outmed_mdtrt_d.birctrl_matn_date as birctrl_matn_date,
    outmed_mdtrt_d.cop_flag as cop_flag,
    outmed_mdtrt_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    outmed_mdtrt_d.vali_flag as vali_flag,
    outmed_mdtrt_d.updt_time as updt_time,
    outmed_mdtrt_d.crter_id as crter_id,
    outmed_mdtrt_d.crter_name as crter_name,
    outmed_mdtrt_d.crte_optins_no as crte_optins_no,
    outmed_mdtrt_d.opter_id as opter_id,
    outmed_mdtrt_d.opter_name as opter_name,
    outmed_mdtrt_d.opt_time as opt_time,
    outmed_mdtrt_d.optins_no as optins_no,
    outmed_mdtrt_d.poolarea_no as poolarea_no,
    outmed_mdtrt_d.chfpdr_name as chfpdr_name,
    outmed_mdtrt_d.dscg_maindiag_name as dscg_maindiag_name,
    outmed_mdtrt_d.adm_caty as adm_caty,
    outmed_mdtrt_d.dscg_caty as dscg_caty,
    outmed_mdtrt_d.biz_date as biz_date,
    outmed_mdtrt_d.deleted_time as deleted_time,
    outmed_mdtrt_d.exch_updt_time as exch_updt_time,
    outmed_mdtrt_d.clct_grde as clct_grde,
    outmed_mdtrt_d.refl_old_mdtrt_id as refl_old_mdtrt_id,
    outmed_mdtrt_d.oprn_oprt_code as oprn_oprt_code,
    outmed_mdtrt_d.certno as certno,
    outmed_mdtrt_d.cvlserv_flag as cvlserv_flag,
    outmed_mdtrt_d.ars_year_ipt_flag as ars_year_ipt_flag,
    outmed_mdtrt_d.age as age,
    outmed_mdtrt_d.psn_type as psn_type,
    outmed_mdtrt_d.mdtrt_cert_no as mdtrt_cert_no,
    outmed_mdtrt_d.dscg_dept_name as dscg_dept_name,
    outmed_mdtrt_d.memo as memo,
    outmed_mdtrt_d.deleted as deleted,
    outmed_mdtrt_d.emp_type as emp_type,
    outmed_mdtrt_d.dscg_bed as dscg_bed,
    outmed_mdtrt_d.dscg_way as dscg_way,
    outmed_mdtrt_d.main_cond_dscr as main_cond_dscr,
    outmed_mdtrt_d.inhosp_stas as inhosp_stas,
    outmed_mdtrt_d.crte_time as crte_time,
    outmed_mdtrt_d.subsys_codg as subsys_codg,
    outmed_mdtrt_d.insu_admdvs as admdvs
from src_data.outmed_mdtrt_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table tran_outmed_mmmt_d partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    tran_outmed_mmmt_d.crte_optins_no as crte_optins_no,
    tran_outmed_mmmt_d.optins_no as optins_no,
    tran_outmed_mmmt_d.brdy as brdy,
    tran_outmed_mmmt_d.insuplc_admdvs as insuplc_admdvs,
    tran_outmed_mmmt_d.psn_no as psn_no,
    tran_outmed_mmmt_d.cert_type as cert_type,
    tran_outmed_mmmt_d.certno as certno,
    tran_outmed_mmmt_d.psn_name as psn_name,
    tran_outmed_mmmt_d.gend as gend,
    tran_outmed_mmmt_d.begndate as begndate,
    tran_outmed_mmmt_d.enddate as enddate,
    tran_outmed_mmmt_d.opsp_dise as opsp_dise,
    tran_outmed_mmmt_d.vali_flag as vali_flag,
    tran_outmed_mmmt_d.opsp_crtf_qua_no as opsp_crtf_qua_no,
    tran_outmed_mmmt_d.updt_time as updt_time,
    tran_outmed_mmmt_d.crter_id as crter_id,
    tran_outmed_mmmt_d.crter_name as crter_name,
    tran_outmed_mmmt_d.crte_time as crte_time,
    tran_outmed_mmmt_d.opter_id as opter_id,
    tran_outmed_mmmt_d.opter_name as opter_name,
    tran_outmed_mmmt_d.opt_time as opt_time,
    tran_outmed_mmmt_d.poolarea_no as poolarea_no,
    tran_outmed_mmmt_d.biz_date as biz_date,
    tran_outmed_mmmt_d.deleted_time as deleted_time,
    tran_outmed_mmmt_d.deleted as deleted,
    tran_outmed_mmmt_d.exch_updt_time as exch_updt_time,
    tran_outmed_mmmt_d.subsys_codg as subsys_codg,
    tran_outmed_mmmt_d.insuplc_admdvs as admdvs
from src_data.tran_outmed_mmmt_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table trans_clr_setl_d partition(dt)
select 
    concat(subsys_codg,rid,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    trans_clr_setl_d.psn_no as psn_no,
    trans_clr_setl_d.dedc_std as dedc_std,
    trans_clr_setl_d.nwb_flag as nwb_flag,
    trans_clr_setl_d.enddate as enddate,
    trans_clr_setl_d.dise_name as dise_name,
    trans_clr_setl_d.invono as invono,
    trans_clr_setl_d.crter_id as crter_id,
    trans_clr_setl_d.insu_admdvs as insu_admdvs,
    trans_clr_setl_d.clr_way as clr_way,
    trans_clr_setl_d.hifp_pay as hifp_pay,
    trans_clr_setl_d.hifes_pay as hifes_pay,
    trans_clr_setl_d.hifdm_pay as hifdm_pay,
    trans_clr_setl_d.manl_reim_rea as manl_reim_rea,
    trans_clr_setl_d.memo as memo,
    trans_clr_setl_d.exch_updt_time as exch_updt_time,
    trans_clr_setl_d.subsys_codg as subsys_codg,
    trans_clr_setl_d.insu_admdvs as admdvs,
    trans_clr_setl_d.emp_no as emp_no,
    trans_clr_setl_d.afil_rlts as afil_rlts,
    trans_clr_setl_d.psn_insu_rlts_id as psn_insu_rlts_id,
    trans_clr_setl_d.psn_setlway as psn_setlway,
    trans_clr_setl_d.updt_time as updt_time,
    trans_clr_setl_d.setl_id as setl_id,
    trans_clr_setl_d.clr_optins as clr_optins,
    trans_clr_setl_d.medins_setl_id as medins_setl_id,
    trans_clr_setl_d.init_setl_id as init_setl_id,
    trans_clr_setl_d.mdtrt_id as mdtrt_id,
    trans_clr_setl_d.clr_appy_evt_id as clr_appy_evt_id,
    trans_clr_setl_d.psn_name as psn_name,
    trans_clr_setl_d.psn_cert_type as psn_cert_type,
    trans_clr_setl_d.certno as certno,
    trans_clr_setl_d.gend as gend,
    trans_clr_setl_d.naty as naty,
    trans_clr_setl_d.brdy as brdy,
    trans_clr_setl_d.age as age,
    trans_clr_setl_d.insutype as insutype,
    trans_clr_setl_d.psn_type as psn_type,
    trans_clr_setl_d.cvlserv_flag as cvlserv_flag,
    trans_clr_setl_d.cvlserv_lv as cvlserv_lv,
    trans_clr_setl_d.sp_psn_type as sp_psn_type,
    trans_clr_setl_d.sp_psn_type_lv as sp_psn_type_lv,
    trans_clr_setl_d.clct_grde as clct_grde,
    trans_clr_setl_d.flxempe_flag as flxempe_flag,
    trans_clr_setl_d.emp_name as emp_name,
    trans_clr_setl_d.emp_type as emp_type,
    trans_clr_setl_d.econ_type as econ_type,
    trans_clr_setl_d.afil_indu as afil_indu,
    trans_clr_setl_d.emp_mgt_type as emp_mgt_type,
    trans_clr_setl_d.pay_loc as pay_loc,
    trans_clr_setl_d.fixmedins_code as fixmedins_code,
    trans_clr_setl_d.fixmedins_name as fixmedins_name,
    trans_clr_setl_d.hosp_lv as hosp_lv,
    trans_clr_setl_d.fix_blng_admdvs as fix_blng_admdvs,
    trans_clr_setl_d.lmtpric_hosp_lv as lmtpric_hosp_lv,
    trans_clr_setl_d.dedc_hosp_lv as dedc_hosp_lv,
    trans_clr_setl_d.begndate as begndate,
    trans_clr_setl_d.setl_time as setl_time,
    trans_clr_setl_d.mdtrt_cert_type as mdtrt_cert_type,
    trans_clr_setl_d.mdtrt_cert_no as mdtrt_cert_no,
    trans_clr_setl_d.med_type as med_type,
    trans_clr_setl_d.setl_type as setl_type,
    trans_clr_setl_d.medfee_sumamt as medfee_sumamt,
    trans_clr_setl_d.clr_type as clr_type,
    trans_clr_setl_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    trans_clr_setl_d.overlmt_selfpay as overlmt_selfpay,
    trans_clr_setl_d.preselfpay_amt as preselfpay_amt,
    trans_clr_setl_d.inscp_amt as inscp_amt,
    trans_clr_setl_d.crt_dedc as crt_dedc,
    trans_clr_setl_d.act_pay_dedc as act_pay_dedc,
    trans_clr_setl_d.pool_prop_selfpay as pool_prop_selfpay,
    trans_clr_setl_d.hi_agre_sumfee as hi_agre_sumfee,
    trans_clr_setl_d.cvlserv_pay as cvlserv_pay,
    trans_clr_setl_d.hifmi_pay as hifmi_pay,
    trans_clr_setl_d.hifob_pay as hifob_pay,
    trans_clr_setl_d.maf_pay as maf_pay,
    trans_clr_setl_d.othfund_pay as othfund_pay,
    trans_clr_setl_d.fund_pay_sumamt as fund_pay_sumamt,
    trans_clr_setl_d.psn_pay as psn_pay,
    trans_clr_setl_d.acct_pay as acct_pay,
    trans_clr_setl_d.cash_payamt as cash_payamt,
    trans_clr_setl_d.ownpay_hosp_part as ownpay_hosp_part,
    trans_clr_setl_d.balc as balc,
    trans_clr_setl_d.acct_mulaid_pay as acct_mulaid_pay,
    trans_clr_setl_d.refd_setl_flag as refd_setl_flag,
    trans_clr_setl_d.cal_ipt_cnt as cal_ipt_cnt,
    trans_clr_setl_d.setl_cashpay_way as setl_cashpay_way,
    trans_clr_setl_d.year as year,
    trans_clr_setl_d.dise_no as dise_no,
    trans_clr_setl_d.vali_flag as vali_flag,
    trans_clr_setl_d.crter_name as crter_name,
    trans_clr_setl_d.crte_time as crte_time,
    trans_clr_setl_d.crte_optins_no as crte_optins_no,
    trans_clr_setl_d.opter_id as opter_id,
    trans_clr_setl_d.opter_name as opter_name,
    trans_clr_setl_d.opt_time as opt_time,
    trans_clr_setl_d.optins_no as optins_no,
    trans_clr_setl_d.poolarea_no as poolarea_no,
    trans_clr_setl_d.medins_stmt_flag as medins_stmt_flag,
    trans_clr_setl_d.quts_type as quts_type,
    trans_clr_setl_d.bydise_setl_payamt as bydise_setl_payamt,
    trans_clr_setl_d.exct_item_fund_payamt as exct_item_fund_payamt,
    trans_clr_setl_d.clr_ym as clr_ym,
    trans_clr_setl_d.n_msgid as n_msgid,
    trans_clr_setl_d.setl_sn as setl_sn,
    trans_clr_setl_d.mdtrt_seq as mdtrt_seq,
    trans_clr_setl_d.biz_date as biz_date,
    trans_clr_setl_d.deleted_time as deleted_time,
    trans_clr_setl_d.deleted as deleted
from src_data.trans_clr_setl_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table out_appy_d partition(dt)
select 
    concat(rid,subsys_codg,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    out_appy_d.psn_no as psn_no,
    out_appy_d.trt_dcla_detl_sn as trt_dcla_detl_sn,
    out_appy_d.insutype as insutype,
    out_appy_d.psn_insu_rlts_id as psn_insu_rlts_id,
    out_appy_d.psn_cert_type as psn_cert_type,
    out_appy_d.certno as certno,
    out_appy_d.psn_name as psn_name,
    out_appy_d.gend as gend,
    out_appy_d.naty as naty,
    out_appy_d.brdy as brdy,
    out_appy_d.tel as tel,
    out_appy_d.insu_admdvs as insu_admdvs,
    out_appy_d.emp_no as emp_no,
    out_appy_d.emp_name as emp_name,
    out_appy_d.rloc_admdvs as rloc_admdvs,
    out_appy_d.rloc_coty_type as rloc_coty_type,
    out_appy_d.rloc_hsorg_name as rloc_hsorg_name,
    out_appy_d.rloc_hsorg_coner as rloc_hsorg_coner,
    out_appy_d.out_onln_way as out_onln_way,
    out_appy_d.rloc_rea as rloc_rea,
    out_appy_d.resout_addr as resout_addr,
    out_appy_d.memo as memo,
    out_appy_d.agnter_name as agnter_name,
    out_appy_d.agnter_certno as agnter_certno,
    out_appy_d.agnter_tel as agnter_tel,
    out_appy_d.agnter_addr as agnter_addr,
    out_appy_d.agnter_rlts as agnter_rlts,
    out_appy_d.enddate as enddate,
    out_appy_d.out_fil_upld_stas as out_fil_upld_stas,
    out_appy_d.att_cnt as att_cnt,
    out_appy_d.vali_flag as vali_flag,
    out_appy_d.crter_id as crter_id,
    out_appy_d.crter_name as crter_name,
    out_appy_d.crte_time as crte_time,
    out_appy_d.crte_optins_no as crte_optins_no,
    out_appy_d.opter_id as opter_id,
    out_appy_d.opter_name as opter_name,
    out_appy_d.opt_time as opt_time,
    out_appy_d.optins_no as optins_no,
    out_appy_d.poolarea_no as poolarea_no,
    out_appy_d.trafout_fixmedins_code as trafout_fixmedins_code,
    out_appy_d.trafout_fixmedins_name as trafout_fixmedins_name,
    out_appy_d.bankcode as bankcode,
    out_appy_d.bank_type_code as bank_type_code,
    out_appy_d.acctname as acctname,
    out_appy_d.deleted_time as deleted_time,
    out_appy_d.deleted as deleted,
    out_appy_d.exch_updt_time as exch_updt_time,
    out_appy_d.subsys_codg as subsys_codg,
    out_appy_d.insu_admdvs as admdvs,
    out_appy_d.begndate as begndate,
    out_appy_d.retn_flag as retn_flag,
    out_appy_d.dcla_souc as dcla_souc,
    out_appy_d.rloc_hsorg_tel as rloc_hsorg_tel,
    out_appy_d.agnter_cert_type as agnter_cert_type,
    out_appy_d.bankacct as bankacct,
    out_appy_d.updt_time as updt_time,
    out_appy_d.bank_samecity_out_flag as bank_samecity_out_flag,
    out_appy_d.biz_date as biz_date
from src_data.out_appy_d as t
where 1=1
) as tab

-- ================================================
insert overwrite table opsp_dise_list_b partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    opsp_dise_list_b.admdvs as admdvs,
    opsp_dise_list_b.crter_id as crter_id,
    opsp_dise_list_b.vali_flag as vali_flag,
    opsp_dise_list_b.tram_data_id as tram_data_id,
    opsp_dise_list_b.dise_cont as dise_cont,
    opsp_dise_list_b.deleted_time as deleted_time,
    opsp_dise_list_b.opsp_dise_code as opsp_dise_code,
    opsp_dise_list_b.opsp_dise_majcls_name as opsp_dise_majcls_name,
    opsp_dise_list_b.opsp_dise_subd_clss_name as opsp_dise_subd_clss_name,
    opsp_dise_list_b.memo as memo,
    opsp_dise_list_b.crte_time as crte_time,
    opsp_dise_list_b.updt_time as updt_time,
    opsp_dise_list_b.crter_name as crter_name,
    opsp_dise_list_b.crte_optins_no as crte_optins_no,
    opsp_dise_list_b.opter_id as opter_id,
    opsp_dise_list_b.opter_name as opter_name,
    opsp_dise_list_b.opt_time as opt_time,
    opsp_dise_list_b.optins_no as optins_no,
    opsp_dise_list_b.ver as ver,
    opsp_dise_list_b.ver_name as ver_name,
    opsp_dise_list_b.trt_guide_pagen as trt_guide_pagen,
    opsp_dise_list_b.trt_guide_elecacs as trt_guide_elecacs,
    opsp_dise_list_b.opsp_dise_name as opsp_dise_name,
    opsp_dise_list_b.opsp_dise_majcls_code as opsp_dise_majcls_code,
    opsp_dise_list_b.invd_time as invd_time,
    opsp_dise_list_b.isu_flag as isu_flag,
    opsp_dise_list_b.efft_time as efft_time,
    opsp_dise_list_b.dif_dscr as dif_dscr,
    opsp_dise_list_b.biz_date as biz_date,
    opsp_dise_list_b.deleted as deleted,
    opsp_dise_list_b.exch_updt_time as exch_updt_time,
    opsp_dise_list_b.subsys_codg as subsys_codg
from src_data.opsp_dise_list_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table medins_info_b partition(dt)
select 
    concat(rid,biz_date,subsys_codg) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    medins_info_b.medins_code as medins_code,
    medins_info_b.uscc as uscc,
    medins_info_b.medins_name as medins_name,
    medins_info_b.medins_abbr as medins_abbr,
    medins_info_b.fax_no as fax_no,
    medins_info_b.main_resper as main_resper,
    medins_info_b.admdvs as admdvs,
    medins_info_b.addr as addr,
    medins_info_b.medins_type as medins_type,
    medins_info_b.medins_type_name as medins_type_name,
    medins_info_b.fixmedins_type as fixmedins_type,
    medins_info_b.reg_stas as reg_stas,
    medins_info_b.enrd_staf_psncnt as enrd_staf_psncnt,
    medins_info_b.hosp_dept_cnt as hosp_dept_cnt,
    medins_info_b.hosp_key_dept_cnt as hosp_key_dept_cnt,
    medins_info_b.senr_profttl_psncnt as senr_profttl_psncnt,
    medins_info_b.mid_profttl_psncnt as mid_profttl_psncnt,
    medins_info_b.pro_techstf_psncnt as pro_techstf_psncnt,
    medins_info_b.depsenr_profttl_psncnt as depsenr_profttl_psncnt,
    medins_info_b.aprv_bed_cnt as aprv_bed_cnt,
    medins_info_b.biz_psncnt as biz_psncnt,
    medins_info_b.oth_psncnt as oth_psncnt,
    medins_info_b.biz_area as biz_area,
    medins_info_b.hosp_lv as hosp_lv,
    medins_info_b.lnt as lnt,
    medins_info_b.lat as lat,
    medins_info_b.endtime as endtime,
    medins_info_b.memo as memo,
    medins_info_b.grst_hosp_flag as grst_hosp_flag,
    medins_info_b.cred_lv as cred_lv,
    medins_info_b.medins_natu as medins_natu,
    medins_info_b.cred_lv_name as cred_lv_name,
    medins_info_b.prnt_medins_code as prnt_medins_code,
    medins_info_b.vali_flag as vali_flag,
    medins_info_b.medins_natu_name as medins_natu_name,
    medins_info_b.npmo_flag as npmo_flag,
    medins_info_b.crter_id as crter_id,
    medins_info_b.crter_name as crter_name,
    medins_info_b.crte_time as crte_time,
    medins_info_b.updt_time as updt_time,
    medins_info_b.crte_optins_no as crte_optins_no,
    medins_info_b.opter_id as opter_id,
    medins_info_b.opter_name as opter_name,
    medins_info_b.opt_time as opt_time,
    medins_info_b.optins_no as optins_no,
    medins_info_b.ver as ver,
    medins_info_b.sync_prnt_flag as sync_prnt_flag,
    medins_info_b.medins_grade as medins_grade,
    medins_info_b.ver_rid as ver_rid,
    medins_info_b.ver_name as ver_name,
    medins_info_b.medins_info_id as medins_info_id,
    medins_info_b.legent_name as legent_name,
    medins_info_b.legrep_name as legrep_name,
    medins_info_b.medins_prac_lic_regno as medins_prac_lic_regno,
    medins_info_b.biznat as biznat,
    medins_info_b.afil_rlts as afil_rlts,
    medins_info_b.trtitem as trtitem,
    medins_info_b.medins_prac_lic_expy as medins_prac_lic_expy,
    medins_info_b.bank_name as bank_name,
    medins_info_b.bankacct as bankacct,
    medins_info_b.bank as bank,
    medins_info_b.inchg_hosp_resper_name as inchg_hosp_resper_name,
    medins_info_b.hi_resper_name as hi_resper_name,
    medins_info_b.hi_resper_tel as hi_resper_tel,
    medins_info_b.hi_tel as hi_tel,
    medins_info_b.hi_email as hi_email,
    medins_info_b.medins_cert_elec_doc as medins_cert_elec_doc,
    medins_info_b.medins_prac_lic_elec_doc as medins_prac_lic_elec_doc,
    medins_info_b.asgcode_dr_cnt as asgcode_dr_cnt,
    medins_info_b.asgcode_nurs_cnt as asgcode_nurs_cnt,
    medins_info_b.act_addr_info as act_addr_info,
    medins_info_b.ec_open_flag as ec_open_flag,
    medins_info_b.dif_dscr as dif_dscr,
    medins_info_b.biz_date as biz_date,
    medins_info_b.deleted_time as deleted_time,
    medins_info_b.deleted as deleted,
    medins_info_b.exch_updt_time as exch_updt_time,
    medins_info_b.subsys_codg as subsys_codg,
    medins_info_b.econ_type as econ_type,
    medins_info_b.inchg_hosp_resper_tel as inchg_hosp_resper_tel,
    medins_info_b.act_addr_code as act_addr_code,
    medins_info_b.medinslv as medinslv,
    medins_info_b.begntime as begntime
from src_data.medins_info_b as t
where 1=1
) as tab

-- ================================================
insert overwrite table clred_setl_funds_sum_d partition(dt)
select 
    concat(subsys_codg,fixmedins_code,clr_setl_fund_id,biz_date) as rid
    ,tab.*
    ,substr(crte_time,1,10) as dt
from 
(
select  
    clred_setl_funds_sum_d.hosp_lv as hosp_lv,
    clred_setl_funds_sum_d.fixmedins_pay_type as fixmedins_pay_type,
    clred_setl_funds_sum_d.dise_no as dise_no,
    clred_setl_funds_sum_d.dise_name as dise_name,
    clred_setl_funds_sum_d.union_id as union_id,
    clred_setl_funds_sum_d.union_name as union_name,
    clred_setl_funds_sum_d.fixmedins_attr as fixmedins_attr,
    clred_setl_funds_sum_d.chk_det_amt as chk_det_amt,
    clred_setl_funds_sum_d.setl_supy_amt as setl_supy_amt,
    clred_setl_funds_sum_d.fund_payamt_310201 as fund_payamt_310201,
    clred_setl_funds_sum_d.fund_payamt_310205 as fund_payamt_310205,
    clred_setl_funds_sum_d.fund_payamt_310301 as fund_payamt_310301,
    clred_setl_funds_sum_d.fund_payamt_320101 as fund_payamt_320101,
    clred_setl_funds_sum_d.fund_payamt_330101 as fund_payamt_330101,
    clred_setl_funds_sum_d.fund_payamt_390201 as fund_payamt_390201,
    clred_setl_funds_sum_d.fund_payamt_340101 as fund_payamt_340101,
    clred_setl_funds_sum_d.fund_payamt_350101 as fund_payamt_350101,
    clred_setl_funds_sum_d.fund_payamt_390101 as fund_payamt_390101,
    clred_setl_funds_sum_d.fund_payamt_510101 as fund_payamt_510101,
    clred_setl_funds_sum_d.fund_payamt_99999601 as fund_payamt_99999601,
    clred_setl_funds_sum_d.fund_payamt_999997 as fund_payamt_999997,
    clred_setl_funds_sum_d.fund_payamt_99999721 as fund_payamt_99999721,
    clred_setl_funds_sum_d.fund_payamt_99999722 as fund_payamt_99999722,
    clred_setl_funds_sum_d.fund_payamt_99999723 as fund_payamt_99999723,
    clred_setl_funds_sum_d.fund_payamt_99999725 as fund_payamt_99999725,
    clred_setl_funds_sum_d.fund_payamt_99999726 as fund_payamt_99999726,
    clred_setl_funds_sum_d.fund_payamt_99999764 as fund_payamt_99999764,
    clred_setl_funds_sum_d.fund_payamt_99999728 as fund_payamt_99999728,
    clred_setl_funds_sum_d.fund_payamt_99999729 as fund_payamt_99999729,
    clred_setl_funds_sum_d.fund_payamt_99999730 as fund_payamt_99999730,
    clred_setl_funds_sum_d.fund_payamt_99999761 as fund_payamt_99999761,
    clred_setl_funds_sum_d.fund_payamt_99999755 as fund_payamt_99999755,
    clred_setl_funds_sum_d.fund_payamt_99999762 as fund_payamt_99999762,
    clred_setl_funds_sum_d.fund_payamt_99999763 as fund_payamt_99999763,
    clred_setl_funds_sum_d.fund_payamt_999998 as fund_payamt_999998,
    clred_setl_funds_sum_d.fund_payamt_999999 as fund_payamt_999999,
    clred_setl_funds_sum_d.fund_payamt_31020402 as fund_payamt_31020402,
    clred_setl_funds_sum_d.fund_payamt_370102 as fund_payamt_370102,
    clred_setl_funds_sum_d.fund_payamt_99999754 as fund_payamt_99999754,
    clred_setl_funds_sum_d.clr_type as clr_type,
    clred_setl_funds_sum_d.clr_way as clr_way,
    clred_setl_funds_sum_d.clr_type_lv2 as clr_type_lv2,
    clred_setl_funds_sum_d.clr_appy_evt_id as clr_appy_evt_id,
    clred_setl_funds_sum_d.clr_stas as clr_stas,
    clred_setl_funds_sum_d.fee_clr_ext_id as fee_clr_ext_id,
    clred_setl_funds_sum_d.dpst_sumamt as dpst_sumamt,
    clred_setl_funds_sum_d.ds_mon_settle_id as ds_mon_settle_id,
    clred_setl_funds_sum_d.deleted_time as deleted_time,
    clred_setl_funds_sum_d.deleted as deleted,
    clred_setl_funds_sum_d.exch_updt_time as exch_updt_time,
    clred_setl_funds_sum_d.subsys_codg as subsys_codg,
    clred_setl_funds_sum_d.pay_loc as pay_loc,
    clred_setl_funds_sum_d.insu_admdvs as insu_admdvs,
    clred_setl_funds_sum_d.fixmedins_code as fixmedins_code,
    clred_setl_funds_sum_d.fixmedins_name as fixmedins_name,
    clred_setl_funds_sum_d.fix_blng_admdvs as fix_blng_admdvs,
    clred_setl_funds_sum_d.med_type as med_type,
    clred_setl_funds_sum_d.med_type_name as med_type_name,
    clred_setl_funds_sum_d.setl_ym as setl_ym,
    clred_setl_funds_sum_d.medfee_sumamt as medfee_sumamt,
    clred_setl_funds_sum_d.fulamt_ownpay_amt as fulamt_ownpay_amt,
    clred_setl_funds_sum_d.preselfpay_amt as preselfpay_amt,
    clred_setl_funds_sum_d.inscp_amt as inscp_amt,
    clred_setl_funds_sum_d.hifp_pay as hifp_pay,
    clred_setl_funds_sum_d.hi_agre_sumfee as hi_agre_sumfee,
    clred_setl_funds_sum_d.cvlserv_pay as cvlserv_pay,
    clred_setl_funds_sum_d.hifes_pay as hifes_pay,
    clred_setl_funds_sum_d.hifmi_pay as hifmi_pay,
    clred_setl_funds_sum_d.hifob_pay as hifob_pay,
    clred_setl_funds_sum_d.hifdm_pay as hifdm_pay,
    clred_setl_funds_sum_d.maf_pay as maf_pay,
    clred_setl_funds_sum_d.othfund_pay as othfund_pay,
    clred_setl_funds_sum_d.psn_pay as psn_pay,
    clred_setl_funds_sum_d.acct_pay as acct_pay,
    clred_setl_funds_sum_d.cash_payamt as cash_payamt,
    clred_setl_funds_sum_d.ownpay_hosp_part as ownpay_hosp_part,
    clred_setl_funds_sum_d.acct_mulaid_pay as acct_mulaid_pay,
    clred_setl_funds_sum_d.updt_time as updt_time,
    clred_setl_funds_sum_d.crter_id as crter_id,
    clred_setl_funds_sum_d.crter_name as crter_name,
    clred_setl_funds_sum_d.crte_time as crte_time,
    clred_setl_funds_sum_d.crte_optins_no as crte_optins_no,
    clred_setl_funds_sum_d.opter_id as opter_id,
    clred_setl_funds_sum_d.opter_name as opter_name,
    clred_setl_funds_sum_d.opt_time as opt_time,
    clred_setl_funds_sum_d.optins_no as optins_no,
    clred_setl_funds_sum_d.poolarea_no as poolarea_no,
    clred_setl_funds_sum_d.exct_item_fund_payamt as exct_item_fund_payamt,
    clred_setl_funds_sum_d.vali_flag as vali_flag,
    clred_setl_funds_sum_d.clr_setl_fund_id as clr_setl_fund_id,
    clred_setl_funds_sum_d.biz_date as biz_date,
    clred_setl_funds_sum_d.overlmt_selfpay as overlmt_selfpay,
    clred_setl_funds_sum_d.fund_pay_sumamt as fund_pay_sumamt,
    clred_setl_funds_sum_d.bydise_setl_payamt as bydise_setl_payamt,
    clred_setl_funds_sum_d.ipt_days as ipt_days,
    clred_setl_funds_sum_d.fee_type as fee_type,
    clred_setl_funds_sum_d.insutype as insutype,
    clred_setl_funds_sum_d.psn_type as psn_type,
    clred_setl_funds_sum_d.clr_ym as clr_ym,
    clred_setl_funds_sum_d.fund_payamt_610101 as fund_payamt_610101,
    clred_setl_funds_sum_d.fund_payamt_99999724 as fund_payamt_99999724,
    clred_setl_funds_sum_d.setl_type as setl_type,
    clred_setl_funds_sum_d.fund_payamt_310101 as fund_payamt_310101,
    clred_setl_funds_sum_d.fund_payamt_320201 as fund_payamt_320201,
    clred_setl_funds_sum_d.fund_payamt_999719 as fund_payamt_999719,
    clred_setl_funds_sum_d.bus_supy_amt as bus_supy_amt,
    clred_setl_funds_sum_d.admdvs as admdvs,
    clred_setl_funds_sum_d.mdtrt_cnt as mdtrt_cnt,
    clred_setl_funds_sum_d.fund_payamt_31020401 as fund_payamt_31020401
from src_data.clred_setl_funds_sum_d as t
where 1=1
) as tab

-- ================================================
