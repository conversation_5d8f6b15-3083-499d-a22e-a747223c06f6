# 质控问题清单模板json转excel.py

- **文件路径**: `D:\work\demo\福建\py\质控问题清单模板json转excel.py`
- **文件大小**: 40KB
- **代码行数**: 901行
- **函数数量**: 15个
- **类数量**: 0个

## 脚本功能

将质控json数据填充到Excel模板中的多个sheet页中
写一个将txt内容填充到excel的脚本,'D:\\work\\demo\\福建\\py\\质控问题清单模板json转excel.py'
excel模板路径'D:\\work\\demo\\福建\\质控模板\\福建-医院质控问题清单模板.xlsx'
txt路径'D:\\work\\demo\\福建\\质控json'下所有.txt文件，txt文件的格式是json
excel文件说明：
'医院列表'sheet页不需要操作，取'行政区划'和'医疗机构名称'列，获取医院和行政区划的映射关系，存起来供程序后面调用
其他所有sheet页，分别代表各个行政区划的数据，逐个填充
操作步骤：
只读取txt文件中的rows节点，rows下有多条数据，对应不同行政区划的多个医院数据，但是rows中没有行政区划和医院的关系，所以使用前面存储的对应关系，将数据通过医院名称按照行政区划分组，
先获取key'hosname'（只保留key'hosname'对应的值不为空的数据），对应的value和'医院列表'sheet页的'医疗机构名称'列匹配获取对应的'行政区划','行政区划'的值和sheet页的名称是对应的，将相同行政区划的数据填充到对应的sheet页中。
逐个遍历所有txt文件，文件名截取掉'coreflddq_'前缀后的文本就是表名和excel中的'表名'列匹配，excel的'问题说明'列的值对应的是rows中的key，匹配完表名列和问题说明列后，用key'hosname'的值匹配excel中第一行的表头，获取表头所在的列的列数值，将'问题说明'列的值对应的是rows中的key所对应的值填充到对应的列（这么做的原因是医院名称是两个单元合并的，下面对应质控结果和问题描述两列，我只需要将数据填充到质控结果列）。
总结就是，通过'hosname'的值和医院列表的映射关系来锁定要操作的sheet页，通过表名和问题说明列来锁定要操作的行，通过匹配的'hosname'的值来锁定要填充数据的列。遍历所有txt文件，将对应数据填充到对应的excel中。
将结果输出到'D:\\work\\demo\\福建\\质控结果\\已填充_福建-医院质控问题清单.xlsx'

将质控json数据填充到Excel模板中的多个sheet页中

读取excel模板'D:\\work\\demo\\福建\\质控模板\\福建-医院质控问题清单模板.xlsx'
处理'D:\\work\\demo\\福建\\质控json'下所有.txt文件（JSON格式）
输出结果到'D:\\work\\demo\\福建\\质控结果\\已填充_福建-医院质控问题清单.xlsx'

操作流程：
1. 从模板中读取"医院列表"sheet，获取医院与行政区划的映射关系
2. 处理所有txt文件的JSON数据，提取rows节点
3. 根据医院名称和行政区划映射，将数据填充到对应的sheet页中
4. 在sheet页中，根据表名和问题说明定位行，根据医院名称定位列，进行数据填充

## 依赖项

```python
import os
import json
import pandas as pd
import logging
import datetime
import shutil
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from copy import copy
import re
import traceback
import traceback
import traceback
import traceback
import traceback
import traceback
import traceback
import traceback
import traceback
import traceback
```

## 主要函数

### `get_hospital_area_mapping`

从Excel模板中读取"医院列表"sheet，建立医院名称到行政区划的映射

Args:
    template_path (str): Excel模板文件路径

Returns:
    dict: 医院名称到行政区划的映射字典

### `extract_table_name`

从txt文件名中提取表名（去除coreflddq_前缀和.txt后缀）

Args:
    txt_filename (str): txt文件名

Returns:
    str: 提取的表名，如果格式不匹配则返回None

### `load_json_data`

加载JSON文件，并提取rows节点数据，过滤hosname不为空的记录

Args:
    json_file_path (str): JSON文件路径

Returns:
    list: rows节点中hosname不为空的数据列表
    list: JSON文件中的列名列表（用于后续匹配问题说明）

### `find_row_index_by_table_and_problem`

在DataFrame中查找匹配表名和问题说明的行索引

Args:
    df (pandas.DataFrame): 工作表数据
    table_name (str): 表名
    problem_desc (str): 问题说明

Returns:
    int: 匹配行的索引，如果未找到则返回None

### `initialize_output_excel`

初始化输出Excel，复制模板的结构和样式

Args:
    template_path (str): 模板Excel文件路径
    output_path (str): 输出Excel文件路径

Returns:
    dict: 包含所有sheet页DataFrame的字典，格式为{sheet_name: df}

### `unmerge_all_header_cells`

取消第一行的所有合并单元格，并记录原始合并信息用于后续恢复

Args:
    ws: openpyxl工作表对象

Returns:
    list: 原始合并单元格范围列表，用于后续恢复

### `restore_header_merged_cells`

恢复第一行的合并单元格格式

Args:
    ws: openpyxl工作表对象
    header_merged_ranges: 原始合并单元格范围列表

Returns:
    bool: 是否成功恢复

### `find_hospital_column_precise`

精确定位医院对应的质控结果列
支持两种格式：
1. 垂直格式：第1行医院名称，第2行"质控结果"/"问题描述"
2. 水平格式：第1行医院名称，第2行其他内容

Args:
    ws: openpyxl工作表对象
    hospital_name (str): 医院名称

Returns:
    int: 质控结果列的索引（从1开始），如果未找到则返回None

### `find_hospital_column`

在DataFrame中查找医院名称对应的"质控结果"列索引
支持垂直格式：第1行医院名称，第2行"质控结果"

Args:
    df (pandas.DataFrame): 工作表数据
    hospital_name (str): 医院名称

Returns:
    tuple: (列名, 列索引)，如果未找到则返回(None, None)

### `map_json_field_to_problem_desc`

将JSON字段名映射为Excel中的"问题说明"列值
处理特殊映射关系，如'cnt'映射到'数据量'
支持不区分大小写和部分匹配

Args:
    field_name (str): JSON中的字段名

Returns:
    str: 对应的Excel"问题说明"列的值

### `standardize_area_name`

标准化区域名称，去除数字和其他非必要字符
例如："厦门市17家" -> "厦门市"
      "福州市（xx区）" -> "福州市"

Args:
    area_name (str): 原始区域名称

Returns:
    str: 标准化后的区域名称

### `build_area_sheet_mapping`

构建从标准化区域名称到原始sheet名称的映射

Args:
    sheet_names (list): Excel中的sheet名称列表

Returns:
    dict: 标准化区域名称到原始sheet名称的映射

### `process_json_file`

处理单个JSON文件，填充数据到相应的Excel工作表

Args:
    json_file_path (str): JSON文件路径
    all_sheets (dict): 包含所有工作表DataFrame的字典
    hospital_to_area (dict): 医院名称到行政区划的映射
    area_sheet_mapping (dict): 标准化区域名称到原始sheet名称的映射

Returns:
    dict: 更新后的工作表DataFrame字典
    int: 成功填充的数据条数

### `save_all_sheets`

将所有工作表保存到Excel文件，保留合并单元格

Args:
    all_sheets (dict): 包含所有工作表DataFrame的字典
    output_path (str): 输出Excel文件路径

Returns:
    bool: 操作是否成功

### `main`

主函数，协调整个处理流程

## 使用示例

```python
def main():
    """
    主函数，协调整个处理流程
    """
    try:
        start_time = datetime.datetime.now()
        logging.info("=== 开始处理质控问题清单模板JSON转Excel ===")
        logging.info(f"Excel模板文件: {TEMPLATE_PATH}")
        logging.info(f"JSON数据目录: {JSON_DIR}")
        logging.info(f"输出文件路径: {OUTPUT_PATH}")

        # 第1步: 获取医院与行政区划的映射关系
        hospital_to_area = get_hospital_area_mapping(TEMPLATE_PATH)
        if not hospital_to_area:
            logging.error("无法获取医院与行政区划的映射关系，程序终止")
            return False

        # 第2步: 初始化输出Excel
        all_sheets = initialize_output_excel(TEMPLATE_PATH, OUTPUT_PATH)
        if not all_sheets:
            logging.error("初始化输出Excel失败，程序终止")
            return False
            
        # 第2.5步: 构建区域映射关系（标准化的区域名称到原始sheet名称的映射）
        area_sheet_mapping = build_area_sheet_mapping(list(all_sheets.keys()))
        logging.info(f"区域映射关系构建完成，共 {len(area_sheet_mapping)} 个映射")

        # 第3步: 获取所有txt文件路径
        txt_files = []
        try:
            txt_files = [os.path.join(JSON_DIR, f) for f in os.listdir(JSON_DIR)
                        if f.endswith('.txt') and f.startswith('coreflddq_')]
            logging.info(f"找到 {len(txt_files)} 个txt文件")
        except Exception as e:
            logging.error(f"读取JSON目录失败: {str(e)}")
            return False

        if not txt_files:
            logging.warning(f"在 {JSON_DIR} 中未找到符合条件的txt文件")
            return False

        # 第4步: 处理所有txt文件
        total_fill_count = 0
        for json_file_path in txt_files:
            all_sheets, fill_count = process_json_file(json_file_path, all_sheets, hospital_to_area, area_sheet_mapping)
            total_fill_count += fill_count

        logging.info(f"所有文件处理完成，共填充 {total_fill_count} 条数据")

        # 第5步: 保存结果
        if save_all_sheets(all_sheets, OUTPUT_PATH):
            end_time = datetime.datetime.now()
            elapsed_time = end_time - start_time
            logging.info(f"数据已成功保存到 {OUTPUT_PATH}")
            logging.info(f"总耗时: {elapsed_time}")
            return True
        else:
            logging.error("保存结果失败")
            return False

    except Exception as e:
        logging.error(f"程序执行时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False
    finally:
        logging.info("=== 处理结束 ===")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


