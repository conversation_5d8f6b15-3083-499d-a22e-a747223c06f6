# 字典抽取-岳群.py

- **文件路径**: `D:\work\demo\福建\py\字典抽取-岳群.py`
- **文件大小**: 7KB
- **代码行数**: 194行
- **函数数量**: 8个
- **类数量**: 0个

## 脚本功能

处理字典抽取-岳群相关任务，处理Excel或CSV等表格数据，生成或处理Word文档，使用正则表达式进行文本处理

## 依赖项

```python
import os
from docx import Document
import pandas as pd
import re
from datetime import datetime
from tqdm import tqdm
import traceback
```

## 主要函数

### `clean_text`

清理文本，去除多余的空白字符

### `clean_title`

清理标题，去除数字编号和特殊字符

### `get_all_tables_and_titles`

获取文档中所有的表格和对应的标题

### `is_valid_table`

检查是否是有效的表格

### `process_table`

处理单个表格

### `extract_tables`

从Word文档中提取表格数据

### `process_word_file`

处理Word文档并将数据保存到Excel

## 使用示例

```python
def main():
    # 设置文件路径
    word_path = r"D:\work\demo\福建\福建省三医联动平台_数据采集标准规范-值域代码-征求意见稿-0902-岳群.docx"
    excel_path = r"D:\work\demo\福建\值域代码-岳群.xlsx"
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"开始处理: {start_time}")
    
    # 处理文件
    success = process_word_file(word_path, excel_path)
    
    # 记录结束时间
    end_time = datetime.now()
    print(f"\n处理完成: {end_time}")
    print(f"总耗时: {end_time - start_time}")
    
    if success:
        print("文件处理成功！")
    else:
        print("文件处理失败！")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


