# 制作模板.py

- **文件路径**: `D:\work\demo\福建\py\制作模板.py`
- **文件大小**: 4KB
- **代码行数**: 95行
- **函数数量**: 2个
- **类数量**: 0个

## 脚本功能

处理制作模板相关任务，生成或处理Word文档，生成或导出数据

## 依赖项

```python
import os
from docx import Document
import shutil
from datetime import datetime
```

## 主要函数

### `remove_tables_and_titles_from_doc`

从Word文档中删除除前两个表格外的所有表格

Args:
    input_path: 输入文件完整路径
    output_path: 输出文件完整路径
    filename: 文件名

Returns:
    bool: 处理是否成功

### `process_all_docs`

递归处理所有文档，保持文件夹结构

## 使用示例

```python
if __name__ == "__main__":
    print("开始处理文档...")
    process_all_docs()
    print("处理结束")
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


