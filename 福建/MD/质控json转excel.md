# 质控json转excel.py

- **文件路径**: `D:\work\demo\福建\py\质控json转excel.py`
- **文件大小**: 22KB
- **代码行数**: 507行
- **函数数量**: 10个
- **类数量**: 0个

## 脚本功能

将质控json数据填充到Excel模板中
写一个将json内容填充到excel的脚本,'D:\\work\\demo\\福建\\py\\质控json转excel.py'
excel模板路径'D:\\work\\demo\\福建\\质控模板'下的所有.xlsx文件
json路径'D:\\work\\demo\\福建\\质控json'下所有.json文件
操作步骤：按顺序打开excel文件，逐个读取sheet页名称，提取名称中的英文并去除两端空格，在英文名前端拼接'coreflddq_'，用拼接后的文本和json文件名称做比对，获取对应表名所对应的json文件
如'【病案手术记录表】 case_operate_record' 提取出case_operate_record，匹配的json文件就是coreflddq_case_operate_record.json
只读取json文件中的rows节点，rows下有多条数据，对应excel中的多行数据，先获取key'hosname'，对应的value和'机构名称'列匹配，则这条json对应的就是该医院所在行，再用json中每条数据的key的值去和excel表头对比，匹配上了，则将对应key的value填写在excel对应的行中。

调整填充数据的逻辑，先删除所有模板中'行政区划'列为'宁德市'的行，从json数据出发，获取key'hosname'数据不为空的所有数据块，在将数据按顺序填充到excel中，

## 依赖项

```python
import os
import json
import re
import pandas as pd
from openpyxl import load_workbook
import logging
import datetime
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment, Protection, Color
from openpyxl.utils.dataframe import dataframe_to_rows
from copy import copy
import traceback
import traceback
import traceback
```

## 主要函数

### `get_header_style`

获取第一个Excel文件第一个sheet页的第一行样式
提取的样式信息包括：字体(font)、背景颜色(fill)、边框(border)、对齐方式(alignment)、
数字格式(number_format)、保护设置(protection)等

Args:
    excel_dir (str): Excel文件目录

Returns:
    list: 包含样式信息的列表，每个元素是一个单元格的样式字典

### `extract_english_name`

从sheet名称中提取英文部分
例如：从"【病案手术记录表】 case_operate_record"中提取"case_operate_record"

Args:
    sheet_name (str): Excel工作表名称

Returns:
    str: 提取的英文名称，如果没有找到则返回None

### `get_json_filename`

根据sheet的英文名生成对应的JSON文件名

Args:
    sheet_english_name (str): sheet的英文名

Returns:
    str: 对应的JSON文件名

### `find_json_file`

在json目录中查找对应的json文件

Args:
    json_dir (str): JSON文件目录
    sheet_english_name (str): 工作表的英文名称

Returns:
    str: JSON文件的完整路径，如果未找到则返回None

### `load_json_data`

加载JSON文件，并提取rows节点数据

Args:
    json_file_path (str): JSON文件路径

Returns:
    list: rows节点的数据列表

### `get_field_mapping`

获取JSON字段名和Excel列名的映射关系

Returns:
    dict: JSON字段名到Excel列名的映射字典

### `find_hospital_row`

[已废弃] 根据医院名称查找对应的Excel行
新的逻辑中不再需要此函数，因为我们现在是从JSON数据出发，而不是从Excel行匹配

Args:
    df (pandas.DataFrame): Excel数据表
    hospital_name (str): 医院名称

Returns:
    int: 匹配的行索引，如果未找到则返回None

### `fill_excel_with_json_data`

将JSON数据填充到Excel表格中

Args:
    excel_file (str): Excel文件路径
    sheet_name (str): 工作表名称
    json_file_path (str): JSON文件路径
    header_styles (list): 表头样式列表，用于应用到DataFrame的表头
    output_file (str, optional): 输出文件路径，默认为None时不保存

Returns:
    pandas.DataFrame: 处理后的Excel数据

### `process_excel_file`

处理单个Excel文件，匹配JSON并填充数据

Args:
    excel_file_path (str): Excel文件路径
    header_styles (list): 表头样式列表，用于应用到表头行

Returns:
    bool: 处理成功返回True，否则返回False

### `main`

主函数

## 使用示例

```python
def main():
    """主函数"""
    start_time = datetime.datetime.now()
    logging.info("=== 开始处理质控JSON转Excel ===")
    logging.info(f"Excel模板目录: {EXCEL_DIR}")
    logging.info(f"JSON数据目录: {JSON_DIR}")
    logging.info(f"输出目录: {OUTPUT_DIR}")
    
    try:
        # 获取所有Excel文件
        excel_files = [os.path.join(EXCEL_DIR, f) for f in os.listdir(EXCEL_DIR) 
                      if f.endswith('.xlsx') and not f.startswith('~$')]  # 排除临时文件
        
        if not excel_files:
            logging.warning(f"在 {EXCEL_DIR} 中未找到Excel文件")
            return
        
        logging.info(f"找到 {len(excel_files)} 个Excel文件")
        
        # 获取第一个Excel文件的第一个sheet的第一行样式
        logging.info("获取第一个Excel文件的第一行样式")
        header_styles = get_header_style(EXCEL_DIR)
        if header_styles:
            logging.info(f"成功获取第一行样式，将应用到所有输出文件")
        else:
            logging.warning(f"未能获取第一行样式，输出文件将使用默认样式")
        
        # 处理每个Excel文件
        success_count = 0
        for excel_file in excel_files:
            if process_excel_file(excel_file, header_styles):
                success_count += 1
        
        # 总结
        end_time = datetime.datetime.now()
        elapsed_time = end_time - start_time
        logging.info(f"处理完成，成功处理 {success_count}/{len(excel_files)} 个文件")
        logging.info(f"总耗时: {elapsed_time}")
    
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
    
    logging.info("=== 处理结束 ===")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


