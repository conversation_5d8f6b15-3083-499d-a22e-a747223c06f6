# 添加公共列-new.py

- **文件路径**: `D:\work\demo\福建\py\添加公共列-new.py`
- **文件大小**: 5KB
- **代码行数**: 119行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

向数据中添加公共列-new，处理Excel或CSV等表格数据，详细操作Excel文件，生成或导出数据

## 依赖项

```python
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Alignment
from copy import copy
from typing import List, Dict
```

## 主要函数

### `copy_cell_format`

复制单元格格式的辅助函数

### `get_sheet_data`

预先读取工作表数据到内存

## 使用示例

```python
if __name__ == "__main__":
    process_excel_file()
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


