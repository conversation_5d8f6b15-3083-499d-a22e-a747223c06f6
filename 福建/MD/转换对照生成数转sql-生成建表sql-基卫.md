# 转换对照生成数转sql-生成建表sql-基卫.py

- **文件路径**: `D:\work\demo\福建\py\转换对照生成数转sql-生成建表sql-基卫.py`
- **文件大小**: 6KB
- **代码行数**: 124行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

处理转换对照生成数转sql-生成建表sql-基卫相关任务，处理Excel或CSV等表格数据，使用正则表达式进行文本处理，生成或导出数据

## 依赖项

```python
import pandas as pd
import os
import datetime
import re
```

## 主要函数

*未识别到函数文档*

## 使用示例

```python
def main():
    # 路径设置
    excel_path = r'D:\work\demo\福建\转换对照生成数转sql模板.xlsx'
    base_path = r'D:\work\demo\福建\建表sql'
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path, f"基卫databend建表语句{current_date}.sql")

    try:
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path, sheet_name=0)
        # 检查必要列
        required_cols = ['业务大类','文件名称','表中文名','表名','数据项','字段名','数据类型','长度','是否主键','填报要求','说明','是否字典']
        for col in required_cols:
            if col not in df.columns:
                raise Exception(f"缺少必要列: {col}")

        # 检查表名是否有重复
        unique_tables = df['表名'].unique()
        if len(unique_tables) != len(set(unique_tables)):
            print("警告：Excel中存在重复的表名！")
            # 打印重复的表名
            duplicates = df['表名'].value_counts()
            print("重复的表名：")
            for table_name, count in duplicates.items():
                if count > 1:
                    print(f"{table_name}: {count}次")
            return

        # 保持表顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables and pd.notna(table_name):
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 生成SQL
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
            for table_name in table_order:
                group = df[df['表名'] == table_name]
                table_comment = str(group.iloc[0]['表中文名']).strip()
                sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` (\n"
                for _, row in group.iterrows():
                    field_name = str(row['字段名']).strip()
                    data_type = get_databend_type(row['数据类型'], row['长度'])
                    comment = clean_comment(row['数据项'])
                    # 去掉comment的空格
                    comment = comment.replace(' ', '')
                    nullable = '不能为空' in str(row['填报要求'])
                    sql += f"    `{field_name}` {data_type}"
                    if nullable:
                        sql += " NOT NULL"
                    if comment:
                        sql += f" COMMENT '{comment}'"
                    sql += ",\n"
                sql = sql.rstrip(",\n") + "\n"
                sql += f") COMMENT='{table_comment}';\n\n"
                f.write(sql)
        print(f"SQL文件已生成: {output_path}")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


