# 生成四类alert脚本.py

- **文件路径**: `D:\work\demo\福建\py\生成四类alert脚本.py`
- **文件大小**: 24KB
- **代码行数**: 477行
- **函数数量**: 6个
- **类数量**: 0个

## 脚本功能

根据数据库类型生成ALTER语句

Args:
    df: DataFrame
    db_type: 数据库类型（mysql/sqlserver/oracle/gauss）
    
Returns:
    str: 生成的SQL语句

## 依赖项

```python
import pandas as pd
import os
import datetime
import re
import traceback
```

## 主要函数

### `get_mysql_alter_sql`

生成MySQL的ALTER语句

Args:
    table_name: 表名
    field_name: 字段名
    data_type: 数据类型
    length: 原长度
    modify_purpose: 修改目的（长度/类型/注释）
    length_modify_suggestion: 长度修改建议
    comment: 字段注释
    
Returns:
    str: MySQL的ALTER语句

### `get_sqlserver_alter_sql`

生成SQL Server的ALTER语句

Args:
    table_name: 表名
    field_name: 字段名
    data_type: 数据类型
    length: 原长度
    modify_purpose: 修改目的（长度/类型/注释）
    length_modify_suggestion: 长度修改建议
    comment: 字段注释
    
Returns:
    str: SQL Server的ALTER语句

### `get_oracle_alter_sql`

生成Oracle的ALTER语句

Args:
    table_name: 表名
    field_name: 字段名
    data_type: 数据类型
    length: 原长度
    modify_purpose: 修改目的（长度/类型/注释）
    length_modify_suggestion: 长度修改建议
    comment: 字段注释
    
Returns:
    str: Oracle的ALTER语句

### `get_gauss_alter_sql`

生成高斯数据库的ALTER语句

Args:
    table_name: 表名
    field_name: 字段名
    data_type: 数据类型
    length: 原长度
    modify_purpose: 修改目的（长度/类型/注释）
    length_modify_suggestion: 长度修改建议
    comment: 字段注释
    
Returns:
    str: 高斯数据库的ALTER语句

### `generate_alter_sql`

根据数据库类型生成ALTER语句

Args:
    df: DataFrame
    db_type: 数据库类型（mysql/sqlserver/oracle/gauss）
    
Returns:
    str: 生成的SQL语句

## 使用示例

```python
def main():
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, 'alertsql模板.xlsx')
    output_dir = os.path.join(base_path, '建表sql', 'alertsql')
    
    print(f"Excel文件路径: {excel_path}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    print(f"已确保输出目录存在")
    
    # 获取当前日期
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    
    try:
        # 读取Excel文件的第一个sheet页
        print(f"正在读取Excel文件: {excel_path}")
        if not os.path.exists(excel_path):
            print(f"错误：找不到文件 {excel_path}")
            return
            
        df = pd.read_excel(excel_path, sheet_name=0)
        print(f"成功读取Excel文件，数据行数: {len(df)}")
        
        # 检查DataFrame是否为空
        if df.empty:
            print("错误：Excel文件为空或没有数据")
            return
            
        # 生成并保存四种数据库的ALTER语句
        for db_type, db_name in [
            ('mysql', 'mysql5.7'), 
            ('sqlserver', 'sqlserver2017'), 
            ('oracle', 'oracle11g'), 
            ('gauss', 'guass5.0')
        ]:
            # 生成SQL语句
            print(f"正在生成{db_name}的ALTER语句...")
            sql = generate_alter_sql(df, db_type)
            
            # 保存到文件
            output_path = os.path.join(output_dir, f"{db_name}_{current_date}.sql")
            with open(output_path, 'w', encoding='utf-8') as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f'-- 生成时间：{current_time}\n')
                f.write(f'-- {db_name}数据库ALTER语句\n\n')
                f.write(sql)
            
            print(f"成功生成{db_name}的ALTER语句: {output_path}")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {excel_path}")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")
        print(traceback.format_exc())

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


