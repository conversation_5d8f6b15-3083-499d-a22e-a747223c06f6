# 生成高斯数据库建表语句-精简版.py

- **文件路径**: `D:\work\demo\福建\py\生成高斯数据库建表语句-精简版.py`
- **文件大小**: 7KB
- **代码行数**: 144行
- **函数数量**: 0个
- **类数量**: 0个

## 脚本功能

根据Excel模板生成高斯数据库数据库的建表SQL语句，处理Excel或CSV等表格数据，生成或导出数据

## 依赖项

```python
import pandas as pd
import os
import datetime
```

## 主要函数

*未识别到函数文档*

## 使用示例

```python
def main():
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"高斯_{current_date}_精简版.sql")
    try:
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 仅包含建表及注释SQL，无存储过程和日志表\n\n')
            for table_counter, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                sql, table_name = generate_create_table_sql(group, table_counter)
                f.write(sql)
        print(f"\n成功生成SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("输出文件仅包含建表和注释SQL，无存储过程和日志表")
    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


