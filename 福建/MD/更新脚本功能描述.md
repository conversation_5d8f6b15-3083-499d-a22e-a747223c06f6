# 更新脚本功能描述.py

- **文件路径**: `D:\work\demo\福建\py\更新脚本功能描述.py`
- **文件大小**: 12KB
- **代码行数**: 298行
- **函数数量**: 6个
- **类数量**: 0个

## 脚本功能

本脚本用于分析福建/py目录下的Python文件，提取更完整的功能描述，并更新福建/MD目录下对应的Markdown文档中的功能描述部分。该脚本采用多种策略提取功能描述：
1. 尝试获取模块级文档字符串
2. 尝试获取main函数的文档字符串
3. 尝试获取其他重要函数（如含generate, create, process等关键词）的文档字符串
4. 若以上均无结果，通过智能分析文件内容（导入语句、文件操作等）和文件名生成功能描述

## 依赖项

```python
import os
import re
import ast
import logging
from typing import Dict, List, Optional, Tuple
import traceback
```

## 主要函数

### `analyze_python_file`

分析Python文件，提取完整的功能描述。采用多种策略：先查找模块文档字符串，若无再查找main函数文档字符串，再查找重要函数文档字符串，最后通过内容分析。

Args:
    file_path: Python文件的路径
    
Returns:
    str: 提取的功能描述

### `analyze_file_content`

通过文件内容分析脚本功能。检查导入语句、文件路径、输出操作等，根据这些信息推断脚本功能。

Args:
    file_path: 文件路径
    content: 文件内容
    
Returns:
    str: 功能描述

### `generate_description_from_filename`

根据文件名生成基本的功能描述。对常见命名模式（如生成*建表语句、*转*、*json转excel等）进行特殊处理。

Args:
    filename: 文件名
    
Returns:
    str: 基本功能描述

### `update_markdown_file`

更新Markdown文件中的功能描述部分。安全处理特殊字符，采用分割字符串而非正则表达式的方式定位和替换内容。

Args:
    md_file: Markdown文件路径
    description: 新的功能描述

### `main`

主函数，扫描目录中的Python脚本并更新对应的MD文档。扫描福建/py目录下所有Python文件，提取功能描述，并更新对应MD文件。

## 使用示例

```python
def main():
    """
    主函数，扫描目录并更新文档
    """
    logging.info("开始扫描Python脚本并更新文档...")
    
    # 获取所有py文件
    py_files = []
    for root, _, files in os.walk(PY_DIR):
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    logging.info(f"找到 {len(py_files)} 个Python脚本")
    
    # 更新每个脚本对应的文档
    for py_file in py_files:
        # 构建对应的MD文件路径
        rel_path = os.path.relpath(py_file, PY_DIR)
        base_name = os.path.splitext(os.path.basename(rel_path))[0]
        md_file = os.path.join(MD_DIR, f"{base_name}.md")
        
        if os.path.exists(md_file):
            # 分析Python文件，提取功能描述
            description = analyze_python_file(py_file)
            
            # 更新Markdown文件
            update_markdown_file(md_file, description)
        else:
            logging.warning(f"未找到对应的Markdown文档: {md_file}")
    
    logging.info("文档更新完成!")
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库（ast、logging等）
- 脚本依赖于Python文件中的文档字符串，如果缺少文档字符串，将通过内容分析生成功能描述
- 脚本使用了ast模块解析Python代码，如遇语法错误会回退到简单的文本分析
- 在更新MD文件时使用了安全的字符串分割方式，避免了正则表达式可能带来的转义字符问题

