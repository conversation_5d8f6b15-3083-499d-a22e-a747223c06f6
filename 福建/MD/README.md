# 福建项目脚本文档

本目录包含对福建项目中所有Python脚本的文档说明。这些文档自动从脚本中提取，包括功能描述、依赖项、主要函数和使用示例等信息。

## 文档分类

### 数据库建表脚本
这些脚本用于生成不同数据库的建表SQL语句：

- [生成高斯数据库建表语句](生成高斯数据库建表语句.md) - 生成GaussDB的建表SQL语句
- [生成高斯数据库建表语句-精简版](生成高斯数据库建表语句-精简版.md) - 生成简化版的GaussDB建表SQL语句
- [生成达梦建表语句](生成达梦建表语句.md) - 生成达梦数据库的建表SQL语句
- [生成mysql建表语句](生成mysql建表语句.md) - 生成MySQL的建表SQL语句
- [生成oracle建表语句](生成oracle建表语句.md) - 生成Oracle的建表SQL语句
- [生成sqlserver建表语句](生成sqlserver建表语句.md) - 生成SQL Server的建表SQL语句
- [生成databend建表语句](生成databend建表语句.md) - 生成Databend的建表SQL语句
- [生成全部建表sql](生成全部建表sql.md) - 批量生成所有类型数据库的建表SQL语句

### 数据库转换脚本
这些脚本用于在不同数据库方言之间转换SQL：

- [mysql转其他库](mysql转其他库.md) - 将MySQL语法转换为其他数据库方言(Oracle、SQL Server、GaussDB等)

### 质控相关脚本
这些脚本用于处理质控数据和生成质控SQL：

- [质控json转excel](质控json转excel.md) - 将质控相关的JSON数据填充到Excel模板中
- [质控问题清单模板json转excel](质控问题清单模板json转excel.md) - 处理质控问题清单的JSON数据转Excel
- [医院184质控sql添加包装_mysql](医院184质控sql添加包装_mysql.md) - 为MySQL生成医院质控SQL包装
- [医院184质控sql添加包装_oracle11g](医院184质控sql添加包装_oracle11g.md) - 为Oracle 11g生成医院质控SQL包装
- [医院184质控sql添加包装_sqlserver2017](医院184质控sql添加包装_sqlserver2017.md) - 为SQL Server 2017生成医院质控SQL包装
- [医院184质控sql添加包装_guass5.0](医院184质控sql添加包装_guass5.0.md) - 为GaussDB 5.0生成医院质控SQL包装
- [生成四类alert脚本](生成四类alert脚本.md) - 生成四种类型的告警脚本

### 数据转换和处理脚本
这些脚本用于数据格式转换和处理：

- [转换对照生成数转sql-生成转换sql-基卫](转换对照生成数转sql-生成转换sql-基卫.md) - 为基卫数据生成转换SQL
- [转换对照生成数转sql-生成转换sql-医保](转换对照生成数转sql-生成转换sql-医保.md) - 为医保数据生成转换SQL
- [转换对照生成数转sql-生成建表sql-基卫](转换对照生成数转sql-生成建表sql-基卫.md) - 为基卫数据生成建表SQL
- [转换对照生成数转sql-生成建表sql-医保](转换对照生成数转sql-生成建表sql-医保.md) - 为医保数据生成建表SQL
- [转换对照生成数转sql-处理字典关联-基卫](转换对照生成数转sql-处理字典关联-基卫.md) - 处理基卫数据的字典关联
- [中文名转换英文名](中文名转换英文名.md) - 将中文表名、字段名转换为英文名称
- [字典抽取-岳群](字典抽取-岳群.md) - 从文档中抽取数据字典

### Excel处理脚本
这些脚本用于处理Excel文件：

- [excel生成](excel生成.md) - 生成Excel文件
- [excel生成31分册](excel生成31分册.md) - 生成31个分册Excel文件
- [基卫平台数据结构提取](基卫平台数据结构提取.md) - 从基卫平台提取数据结构到Excel
- [三医项目_数据采集标准规范20250421提取](三医项目_数据采集标准规范20250421提取.md) - 提取三医项目数据采集标准
- [添加公共列](添加公共列.md) - 向Excel表格添加公共列
- [添加公共列-new](添加公共列-new.md) - 添加公共列的新版本
- [添加行](添加行.md) - 向Excel表格添加行
- [31个数据标准添加公共列](31个数据标准添加公共列.md) - 为31个数据标准添加公共列
- [最小采集方案excel生成](最小采集方案excel生成.md) - 生成最小采集方案Excel
- [制作模板](制作模板.md) - 制作Excel模板
- [convert_to_excel](convert_to_excel.md) - 将数据转换为Excel格式

### Word处理脚本
这些脚本用于处理Word文件：

- [word生成](word生成.md) - 生成Word文档
- [word生成批注](word生成批注.md) - 生成带有批注的Word文档
- [更新word](更新word.md) - 更新Word文档内容
- [31个数据标准word生成](31个数据标准word生成.md) - 生成31个数据标准的Word文档

### 数据检查和验证脚本
这些脚本用于检查和验证数据：

- [检测mysql字段长度](检测mysql字段长度.md) - 检测MySQL表中字段的长度
- [oracle字段长度变更](oracle字段长度变更.md) - 更改Oracle表中字段的长度
- [oracle更新sql](oracle更新sql.md) - 生成Oracle的更新SQL语句
- [获取表名和字段名](获取表名和字段名.md) - 从数据库或文件中获取表名和字段名
- [generate_wide_table](generate_wide_table.md) - 生成宽表
- [check_code_name](check_code_name.md) - 检查代码名称

### ID处理脚本
这些脚本用于处理ID相关内容：

- [处理rid](处理rid.md) - 处理RID字段
- [31个数据标准处理rid](31个数据标准处理rid.md) - 为31个数据标准处理RID字段

### 测试脚本
这些脚本用于测试各种功能：

- [test](test.md) - 通用测试脚本
- [test_simple](test_simple.md) - 简单测试
- [test_complex_regex](test_complex_regex.md) - 复杂正则表达式测试
- [test_date_conversion](test_date_conversion.md) - 日期转换测试
- [test_gaussdb](test_gaussdb.md) - GaussDB功能测试
- [test_process_single_sql](test_process_single_sql.md) - 单SQL处理测试
- [test_user_example](test_user_example.md) - 用户示例测试
- [test_user_sql](test_user_sql.md) - 用户SQL测试
- [final_test](final_test.md) - 最终测试

### 工具脚本
这些脚本用于辅助开发和文档生成：

- [生成脚本说明文档](生成脚本说明文档.md) - 生成本目录中的脚本文档(MD文件)
- [更新脚本功能描述](更新脚本功能描述.md) - 分析Python脚本并更新MD文档中的功能描述部分

## 使用方法

1. 浏览上述分类，找到您需要的脚本功能
2. 点击链接查看详细文档
3. 文档包含脚本功能、依赖项、主要函数和使用示例等信息

## 注意事项

- 运行脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确
- 部分脚本可能需要特定的输入文件或环境配置

*文档更新时间: 2025-07-11* 