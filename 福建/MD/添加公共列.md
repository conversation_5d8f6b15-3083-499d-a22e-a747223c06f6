# 添加公共列.py

- **文件路径**: `D:\work\demo\福建\py\添加公共列.py`
- **文件大小**: 6KB
- **代码行数**: 136行
- **函数数量**: 2个
- **类数量**: 0个

## 脚本功能

向数据中添加公共列，处理Excel或CSV等表格数据，详细操作Excel文件，生成或导出数据

## 依赖项

```python
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Alignment, Color
from copy import copy
```

## 主要函数

### `copy_row_with_formatting`

复制行数据并保持原格式

Args:

    source_ws: 源工作表
    target_ws: 目标工作表
    source_row: 源行号
    target_row: 目标行号

## 使用示例

```python
if __name__ == "__main__":
    process_excel_file()
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


