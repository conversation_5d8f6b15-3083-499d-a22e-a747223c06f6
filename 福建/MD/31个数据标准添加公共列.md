# 31个数据标准添加公共列.py

- **文件路径**: `D:\work\demo\福建\py\31个数据标准添加公共列.py`
- **文件大小**: 8KB
- **代码行数**: 198行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

处理Excel文件，在每个表的前后添加公共字段，并保持正确的格式

## 依赖项

```python
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Alignment, Color
from copy import copy
import time
import logging
```

## 主要函数

### `copy_row_with_formatting`

复制行数据并保持原格式

Args:
    source_ws: 源工作表
    target_ws: 目标工作表
    source_row: 源行号
    target_row: 目标行号

### `has_valid_data`

检查工作表的指定列是否有有效数据

Args:
    worksheet: 要检查的工作表
    column: 要检查的列号（默认是H列）

Returns:
    bool: 如果列中有非空数据返回True，否则返回False

### `process_excel_file`

处理Excel文件，在每个表的前后添加公共字段，并保持正确的格式

## 使用示例

```python
if __name__ == "__main__":
    process_excel_file()
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


