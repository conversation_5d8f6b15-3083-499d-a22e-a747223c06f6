# 最小采集方案excel生成.py

- **文件路径**: `D:\work\demo\福建\py\最小采集方案excel生成.py`
- **文件大小**: 10KB
- **代码行数**: 223行
- **函数数量**: 4个
- **类数量**: 0个

## 脚本功能

从Word文档中提取所有表格数据，从第二章开始，跳过每个文件的前两个表格

## 依赖项

```python
import docx
import pandas as pd
import os
from datetime import date
import re
```

## 主要函数

### `clean_table_name`

清理表名，去除章节编号

### `extract_tables_from_word`

从Word文档中提取所有表格数据，从第二章开始，跳过每个文件的前两个表格

### `get_file_number`

从文件名中提取顿号前的数字

### `word_to_excel`

将所有Word文档中的表格按顺序合并到Excel文件中

## 使用示例

```python
if __name__ == "__main__":
    word_to_excel()
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


