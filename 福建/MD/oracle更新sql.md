# oracle更新sql.py

- **文件路径**: `D:\work\demo\福建\py\oracle更新sql.py`
- **文件大小**: 5KB
- **代码行数**: 117行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

更新sql数据，处理Excel或CSV等表格数据，使用正则表达式进行文本处理，生成或导出数据

## 依赖项

```python
import pandas as pd
import os
import datetime
import re
```

## 主要函数

### `get_oracle_type`

将Excel中的数据类型转换为Oracle数据类型

Args:
    data_type: Excel中的数据类型
    length: 长度字段的值

Returns:
    str: Oracle数据类型

## 使用示例

```python
if __name__ == "__main__":
    generate_oracle_sql()
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


