# generate_wide_table.py

- **文件路径**: `D:\work\demo\福建\py\generate_wide_table.py`
- **文件大小**: 6KB
- **代码行数**: 168行
- **函数数量**: 4个
- **类数量**: 0个

## 脚本功能

处理generate_wide_table相关任务，处理Excel或CSV等表格数据，操作Excel文件，生成或导出数据

## 依赖项

```python
import pandas as pd
import os
from collections import defaultdict
```

## 主要函数

### `read_excel_data`

读取Excel文件数据

### `process_table_data`

处理表格数据，按表名分组

### `generate_sql`

生成SQL语句

## 使用示例

```python
def main():
    input_file = "../宽表设计.xlsx"

    # 读取Excel数据
    df, relation_df = read_excel_data(input_file)

    # 打印读取到的数据
    print("读取到的表数据：")
    print(df)
    print("\n读取到的关联关系：")
    print(relation_df)

    # 处理数据
    tables_by_name, relation_df = process_table_data(df, relation_df)

    # 打印处理后的表结构
    print("\n处理后的表结构：")
    for table_name, tables in tables_by_name.items():
        print(f"\n表名: {table_name}")
        for table in tables:
            print(f"表中文名: {table['table_cn_name']}")
            print("字段列表:")
            for field in table['fields']:
                print(f"  - {field['字段名']}")

    # 生成SQL
    sql_statements, main_table_name = generate_sql(tables_by_name, relation_df)
    output_file = f"fct0_{main_table_name}.sql"

    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(sql_statements)

    print(f"\nSQL文件已生成: {output_file}")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


