# 生成脚本说明文档.py

- **文件路径**: `D:\work\demo\福建\py\生成脚本说明文档.py`
- **文件大小**: 10KB
- **代码行数**: 281行
- **函数数量**: 6个
- **类数量**: 0个

## 脚本功能

本脚本用于扫描福建/py目录下的所有Python脚本，
并在福建/MD目录下为每个脚本生成对应的Markdown文件说明其功能和用法

## 依赖项

```python
import os
import re
import ast
from typing import Dict, List, Tuple, Optional
import logging
import datetime
import traceback
```

## 主要函数

### `extract_docstring`

提取Python文件中的文档字符串和函数信息

Args:
    file_path: Python文件的路径

Returns:
    Tuple[Optional[str], Dict[str, str]]: 模块文档字符串和函数信息的字典

### `extract_imports`

提取Python文件中的导入语句

Args:
    file_path: Python文件的路径

Returns:
    List[str]: 导入语句列表

### `extract_main_function`

提取Python文件中的main函数或主执行代码

Args:
    file_path: Python文件的路径

Returns:
    Optional[str]: main函数代码或None

### `get_file_stats`

获取文件的统计信息

Args:
    file_path: 文件的路径

Returns:
    Dict: 包含文件统计信息的字典

### `generate_markdown_doc`

为Python脚本生成Markdown文档

Args:
    py_file: Python文件的路径
    md_file: 输出的Markdown文件路径

### `main`

主函数，扫描目录并生成文档

## 使用示例

```python
def main():
    """
    主函数，扫描目录并生成文档
    """
    logging.info("开始扫描Python脚本并生成文档...")
    
    # 获取所有py文件
    py_files = []
    for root, _, files in os.walk(PY_DIR):
        for file in files:
            if file.endswith('.py'):
                py_files.append(os.path.join(root, file))
    
    logging.info(f"找到 {len(py_files)} 个Python脚本")
    
    # 为每个脚本生成文档
    for py_file in py_files:
        # 构建对应的MD文件路径
        rel_path = os.path.relpath(py_file, PY_DIR)
        base_name = os.path.splitext(os.path.basename(rel_path))[0]
        md_file = os.path.join(MD_DIR, f"{base_name}.md")
        
        generate_markdown_doc(py_file, md_file)
    
    logging.info("文档生成完成!")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


