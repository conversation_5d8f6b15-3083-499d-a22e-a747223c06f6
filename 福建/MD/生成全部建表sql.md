# 生成全部建表sql.py

- **文件路径**: `D:\work\demo\福建\py\生成全部建表sql.py`
- **文件大小**: 2KB
- **代码行数**: 39行
- **函数数量**: 1个
- **类数量**: 0个

## 脚本功能

处理生成全部建表sql相关任务，生成或导出数据

## 依赖项

```python
import subprocess
import os
```

## 主要函数

### `run_script`

调用指定的 Python 脚本。

## 使用示例

```python
if __name__ == "__main__":
    base_path = r"D:\work\demo\福建\py"
    mysql_script = os.path.join(base_path, "生成mysql建表语句.py")
    oracle_script = os.path.join(base_path, "生成oracle建表语句.py")
    sqlserver_script = os.path.join(base_path, "生成sqlserver建表语句.py")
    guass_script = os.path.join(base_path, "生成高斯数据库建表语句-精简版.py")

    run_script(mysql_script)
    run_script(oracle_script)
    run_script(sqlserver_script)
    run_script(guass_script)

    print("所有建表语句生成脚本已执行完毕。")
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


