# 生成sqlserver建表语句.py

- **文件路径**: `D:\work\demo\福建\py\生成sqlserver建表语句.py`
- **文件大小**: 12KB
- **代码行数**: 362行
- **函数数量**: 4个
- **类数量**: 0个

## 脚本功能

根据DataFrame生成SQL Server 2017兼容的建表SQL语句

Args:
    df: 包含表结构的DataFrame
    table_counter: 表序号，用于生成唯一的约束名和索引名

Returns:
    tuple: (建表SQL语句, 表名)

## 依赖项

```python
import pandas as pd
import os
import datetime
```

## 主要函数

### `get_sqlserver_type`

将Excel中的数据类型转换为SQL Server 2017兼容的数据类型

Args:
    data_type: Excel中的数据类型
    length: 长度字段的值

Returns:
    str: SQL Server 2017兼容的数据类型

### `clean_comment_text`

清理注释文本中的特殊字符

Args:
    text: 原始文本

Returns:
    str: 清理后的文本

### `generate_create_table_sql`

根据DataFrame生成SQL Server 2017兼容的建表SQL语句

Args:
    df: 包含表结构的DataFrame
    table_counter: 表序号，用于生成唯一的约束名和索引名

Returns:
    tuple: (建表SQL语句, 表名)

### `main`

主函数

## 使用示例

```python
def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"sqlserver{current_date}.sql")

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建\n\n')

            # 创建日志表
            f.write("""
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


