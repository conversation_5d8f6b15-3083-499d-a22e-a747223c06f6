# 中文名转换英文名.py

- **文件路径**: `D:\work\demo\福建\py\中文名转换英文名.py`
- **文件大小**: 4KB
- **代码行数**: 115行
- **函数数量**: 4个
- **类数量**: 0个

## 脚本功能

预处理中文名称：
1. 去掉所有中英文括号及其中的内容
2. 去掉所有'-'符号
3. 去掉所有空格
4. 去掉所有中英文冒号和顿号

## 依赖项

```python
import pandas as pd
import jieba
import re
```

## 主要函数

### `load_abbreviations`

加载缩略语对照表

### `preprocess_chinese_name`

预处理中文名称：
1. 去掉所有中英文括号及其中的内容
2. 去掉所有'-'符号
3. 去掉所有空格
4. 去掉所有中英文冒号和顿号

### `convert_chinese_to_english`

将中文转换为英文，并返回未找到的中文词

## 使用示例

```python
def main():
    # 文件路径
    abbr_excel_path = r'D:\work\demo\福建\数据组-缩略语-20250414165127.xlsx'
    input_file_path = r'D:\work\demo\福建\中文名转换英文名.txt'
    output_excel_path = r'D:\work\demo\福建\中文名转换英文名结果.xlsx'

    # 加载缩略语对照表
    abbr_dict = load_abbreviations(abbr_excel_path)

    # 读取待转换的中文名
    with open(input_file_path, 'r', encoding='utf-8') as f:
        chinese_names = [line.strip() for line in f if line.strip()]

    # 转换每个中文名
    results = []
    all_not_found_words = set()  # 使用集合去重

    for name in chinese_names:
        english_name, not_found_words = convert_chinese_to_english(name, abbr_dict)
        results.append({
            '中文名': name,
            '英文名': english_name
        })
        # 添加未找到的中文词到集合中
        all_not_found_words.update(not_found_words)

    # 创建DataFrame并保存到Excel
    df_results = pd.DataFrame(results)

    # 创建未找到缩略语的中文词DataFrame
    df_not_found = pd.DataFrame({'未找到缩略语的中文词': list(all_not_found_words)})

    # 使用ExcelWriter保存多个sheet
    with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
        df_results.to_excel(writer, sheet_name='转换结果', index=False)
        df_not_found.to_excel(writer, sheet_name='未找到缩略语', index=False)

    print(f"转换完成，结果已保存到: {output_excel_path}")
    print(f"共有 {len(all_not_found_words)} 个中文词未找到对应缩略语")


```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


