# 生成达梦建表语句.py

- **文件路径**: `D:\work\demo\福建\py\生成达梦建表语句.py`
- **文件大小**: 9KB
- **代码行数**: 233行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

根据DataFrame生成达梦数据库建表SQL语句

Args:
    df: 包含表结构的DataFrame
    table_counter: 表序号，用于生成唯一的约束名和索引名

Returns:
    tuple: (建表SQL语句, 表名)

## 依赖项

```python
import pandas as pd
import os
import datetime
```

## 主要函数

### `get_dm_type`

将Excel中的数据类型转换为达梦数据库(DM8)数据类型

Args:
    data_type: Excel中的数据类型
    length: 长度字段的值

Returns:
    str: 达梦数据库数据类型

### `generate_create_table_sql`

根据DataFrame生成达梦数据库建表SQL语句

Args:
    df: 包含表结构的DataFrame
    table_counter: 表序号，用于生成唯一的约束名和索引名

Returns:
    tuple: (建表SQL语句, 表名)

### `main`

主函数

## 使用示例

```python
def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"dm8_{current_date}.sql")

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序，转换为小写
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            table_name = table_name.strip().lower()  # 统一使用小写表名
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本为达梦DM8建表SQL\n\n')

            # 为每个表添加建表语句
            for idx, table_name in enumerate(table_order, 1):
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                # 传递表序号以生成唯一的约束名和索引名
                sql, table_name = generate_create_table_sql(group, idx)  # 现在返回的表名已经是小写的

                # 添加表分隔注释
                f.write(f"""-- -------------------------------------------
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


