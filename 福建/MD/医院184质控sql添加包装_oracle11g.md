# 医院184质控sql添加包装_oracle11g.py

- **文件路径**: `D:\work\demo\福建\py\医院184质控sql添加包装_oracle11g.py`
- **文件大小**: 12KB
- **代码行数**: 387行
- **函数数量**: 6个
- **类数量**: 0个

## 脚本功能

脚本名称：医院184质控sql添加包装_oracle11g.py
描述：从Excel文件中读取SQL查询语句，并生成带有错误处理机制的Oracle 11g执行文件
作者：chenlu
日期：2025-07-04

## 依赖项

```python
import pandas as pd
import re
import os
import logging
import datetime
from pathlib import Path
```

## 主要函数

### `read_sql_queries_from_excel`

从Excel文件中读取SQL查询

Args:
    excel_path (str): Excel文件路径
    
Returns:
    list: SQL查询列表

### `extract_metadata_from_sql`

从SQL查询中提取元数据：tabname, rule_no, rule_desc

Args:
    sql_query (str): SQL查询语句
    
Returns:
    dict: 包含tabname, rule_no, rule_desc的字典

### `escape_sql_string`

转义SQL字符串中的单引号

Args:
    s (str): 需要转义的字符串
    
Returns:
    str: 转义后的字符串

### `generate_sql_script`

生成包含错误处理机制的Oracle 11g执行文件

Args:
    sql_queries (list): SQL查询列表
    
Returns:
    str: 生成的SQL脚本

### `write_sql_script_to_file`

将生成的SQL脚本写入文件

Args:
    sql_script (str): 生成的SQL脚本
    output_path (str): 输出文件路径

### `main`

主函数

## 使用示例

```python
def main():
    """
    主函数
    """
    # 配置输入和输出路径
    excel_path = r"D:\work\demo\福建\质控模板\医院184质控sql添加包装模板.xlsx"
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = f"D:\work\demo\福建\建表sql\质控sql\oracle11g_184质控_记录结果版_{current_date}.sql"
    
    logger.info("脚本开始执行")
    logger.info(f"输入Excel文件: {excel_path}")
    logger.info(f"输出SQL文件: {output_path}")
    
    try:
        # 1. 读取Excel文件中的SQL查询
        sql_queries = read_sql_queries_from_excel(excel_path)
        
        # 2. 测试元数据提取功能
        if sql_queries:
            logger.info("测试元数据提取功能:")
            sample_sql = sql_queries[0]
            metadata = extract_metadata_from_sql(sample_sql)
            logger.info(f"  - 表名 (tabname): {metadata['tabname']}")
            logger.info(f"  - 规则编号 (rule_no): {metadata['rule_no']}")
            logger.info(f"  - 规则描述 (rule_desc): {metadata['rule_desc']}")
        
        # 3. 生成SQL脚本
        sql_script = generate_sql_script(sql_queries)
        
        # 4. 将SQL脚本写入文件
        write_sql_script_to_file(sql_script, output_path)
        
        logger.info("脚本执行完成")
    except Exception as e:
        logger.error(f"脚本执行出错: {str(e)}")
        return 1
    
    return 0

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


