# mysql转其他库.py

- **文件路径**: `D:\work\demo\福建\py\mysql转其他库.py`
- **文件大小**: 72KB
- **代码行数**: 1254行
- **函数数量**: 28个
- **类数量**: 0个

## 脚本功能

主函数，定义要处理的文件列表并调用处理函数。

## 依赖项

```python
import pandas as pd
import re
import os
import os
import datetime
import glob
import os
import pandas as pd
```

## 主要函数

### `convert_date_literals_for_oracle`

将 SQL 查询中的日期字面量转换为 Oracle 的 TO_DATE 函数格式。

Args:
    query (str): 要转换的 SQL 查询语句。
    
Returns:
    str: 转换后的 SQL 查询语句。

### `convert_date_literals_for_sqlserver`

将 SQL 查询中的日期字面量转换为 SQL Server 的 CONVERT 函数格式。

Args:
    query (str): 要转换的 SQL 查询语句。
    
Returns:
    str: 转换后的 SQL 查询语句。

### `convert_date_literals_for_gaussdb`

将 SQL 查询中的日期字面量转换为 GaussDB 的 TO_DATE/TO_TIMESTAMP 函数格式。

Args:
    query (str): 要转换的 SQL 查询语句。
    
Returns:
    str: 转换后的 SQL 查询语句。

### `translate_sql`

将 MySQL 查询语句转换为指定的目标数据库方言。

Args:
    mysql_query (str): 要转换的 MySQL 查询语句。
    target_dialect (str): 目标数据库方言 ('oracle', 'sqlserver', 'gaussdb')。

Returns:
    str: 转换后的 SQL 查询语句。

### `process_file`

读取、转换和保存单个 Excel 文件。

Args:
    file_path (str): 输入的 Excel 文件路径。
    rules_column (str): 包含SQL规则的列名。

### `log_conversion`

转换 SQL 并记录日志。

Args:
    sql (str): 要转换的 SQL 语句。
    dialect (str): 目标数据库方言。
    log_file (file): 日志文件对象。

Returns:
    str: 转换后的 SQL 语句。

### `process_single_sql`

直接处理单个 SQL 语句，转换为各种方言并输出结果。

Args:
    sql (str): 要转换的 SQL 语句。
    output_file (str, optional): 输出文件路径。如果提供，结果将写入该文件。

### `clear_output_files`

清除之前生成的输出文件。

Args:
    base_dir (str, optional): 基础目录。如果不提供，将使用默认目录。

### `main`

主函数，定义要处理的文件列表并调用处理函数。

### `test_sql_conversion`

测试 SQL 转换功能

### `test_user_case`

专门测试用户提供的案例

### `test_process_flow`

测试整个转换流程

### `convert_regex_to_sqlserver_pattern`

将正则表达式模式转换为SQL Server可以使用的模式

Args:
    pattern_str (str): 正则表达式模式字符串（已去除引号）
    
Returns:
    tuple: (转换后的SQL Server模式, 是否需要使用复杂表达式, SQL Server表达式)

## 使用示例

```python
def main():
    """
    主函数，定义要处理的文件列表并调用处理函数。
    """
    print("\n===== 开始执行主程序 =====")
    base_dir = r'D:\work\demo\福建'

    # 清除之前的输出文件
    clear_output_files(base_dir)

    # 处理用户提供的案例
    print("\n===== 测试用户案例 - REGEXP = 0 形式 =====")
    user_case = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\\d{9}$' = 0"
    process_single_sql(user_case)
    
    # 处理用户提供的 not REGEXP 形式案例
    print("\n===== 测试用户案例 - not REGEXP 形式 =====")
    user_case_not_regexp = "SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\\d{9}$'"
    process_single_sql(user_case_not_regexp)

    # 测试 TIMESTAMPDIFF 函数转换
    print("\n===== 测试用户案例 - TIMESTAMPDIFF 函数 =====")
    timestampdiff_case = "SELECT TIMESTAMPDIFF(DAY, admission_date, discharge_date) as stay_days FROM patient_info"
    process_single_sql(timestampdiff_case)
    
    # 测试 DATEDIFF 函数错误修复
    print("\n===== 测试用户案例 - DATEDIFF 函数错误修复 =====")
    datediff_case = "SELECT 'YD01202506175124','患者过敏记录（PATIENT_ALLERGY_RECORD）中的数据上传时间（UPLOAD_TIME）与数据更新时间（UPDT_TIME）超过27小时',COUNT(1) FROM patient_allergy_record A where 1=1 and DATEDIFF(CASE WHEN 'HOUR' = 'SECOND' THEN 'SECOND' WHEN 'HOUR' = 'MINUTE' THEN 'MINUTE' WHEN 'HOUR' = 'HOUR' THEN 'HOUR' WHEN 'HOUR' = 'DAY' THEN 'DAY' WHEN 'HOUR' = 'WEEK' THEN 'WEEK' WHEN 'HOUR' = 'MONTH' THEN 'MONTH' WHEN 'HOUR' = 'QUARTER' THEN 'QUARTER' WHEN 'HOUR' = 'YEAR' THEN 'YEAR' END, A.updt_time, A.upload_time)>27;"
    process_single_sql(datediff_case)

    # 处理 Excel 文件
    files_to_process = [
        os.path.join(base_dir, '附件1：医院数据质量校验规则-第一批次-医院自检-20250620.xlsx'),
        os.path.join(base_dir, '附件2：医院数据质量校验规则-第一批次-贴源库质控-20250620.xlsx')
    ]

    for file_path in files_to_process:
        if os.path.exists(file_path):
            process_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

    print("\n===== 主程序执行完毕 =====")


```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


