# 三医项目_数据采集标准规范20250421提取.py

- **文件路径**: `D:\work\demo\福建\py\三医项目_数据采集标准规范20250421提取.py`
- **文件大小**: 7KB
- **代码行数**: 151行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

从三医项目_数据采集标准规范20250421中提取数据，处理Excel或CSV等表格数据，生成或处理Word文档，使用正则表达式进行文本处理，生成或导出数据

## 依赖项

```python
import os
from docx import Document
import pandas as pd
import re
import traceback
```

## 主要函数

### `clean_title`

清理表名，去除章节编号和特殊字符

## 使用示例

```python
def main():
    input_dir = r'D:\work\demo\福建\待提取'
    output_file = r'D:\work\demo\福建\三医项目_数据采集标准规范20250421(公卫数据).xlsx'

    all_results = []

    # 处理目录下所有的Word文件
    word_files = [f for f in os.listdir(input_dir) if f.endswith('.docx')]
    print(f"找到 {len(word_files)} 个Word文件")

    for filename in word_files:
        file_path = os.path.join(input_dir, filename)
        try:
            results = process_word_file(file_path)
            all_results.extend(results)
            print(f"成功处理文件: {filename}")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

    if all_results:
        # 创建DataFrame并保存到Excel
        df = pd.DataFrame(all_results)
        df.to_excel(output_file, index=False)
        print(f"数据已成功导出到: {output_file}")
        print(f"总共提取了 {len(all_results)} 条记录")
    else:
        print("未找到符合条件的表格数据")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


