# oracle字段长度变更.py

- **文件路径**: `D:\work\demo\福建\py\oracle字段长度变更.py`
- **文件大小**: 5KB
- **代码行数**: 112行
- **函数数量**: 6个
- **类数量**: 0个

## 脚本功能

处理oracle字段长度变更相关任务，处理Excel或CSV等表格数据，生成或导出数据

## 依赖项

```python
import pandas as pd
import os
from collections import defaultdict
from datetime import datetime
import re
```

## 主要函数

*未识别到函数文档*

## 使用示例

```python
def main():
    try:
        print("开始处理字段长度变更...")

        # 读取Excel文件
        fields_to_change, template_df = read_excel_files()

        # 支持的数据库类型
        # db_types = ['oracle', 'mysql', 'sqlserver']
        db_types = ['oracle']

        # 为每种数据库类型生成SQL文件
        for db_type in db_types:
            print(f"正在生成 {db_type.upper()} 的SQL脚本...")
            sql_by_file = generate_alter_statements(fields_to_change, template_df, db_type)
            write_sql_file(sql_by_file, db_type)

        print("处理完成！SQL语句已生成到 D:\work\demo\福建 目录下")

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")

```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


