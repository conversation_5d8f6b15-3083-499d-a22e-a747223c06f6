# 更新word.py

- **文件路径**: `D:\work\demo\福建\py\更新word.py`
- **文件大小**: 5KB
- **代码行数**: 141行
- **函数数量**: 3个
- **类数量**: 0个

## 脚本功能

更新word数据，处理Excel或CSV等表格数据，生成或处理Word文档，使用正则表达式进行文本处理，生成或导出数据

## 依赖项

```python
import os
from docx import Document
import pandas as pd
from docx.shared import Pt
```

## 主要函数

### `load_replacement_map`

从Excel文件加载替换对照关系

Args:
    excel_path: Excel文件路径

Returns:
    dict: 替换对照字典，key为要替换的文本，value为替换后的文本

### `replace_text_in_doc`

根据替换规则更新Word文档中的文本

Args:
    input_path: 输入文件完整路径
    output_path: 输出文件完整路径
    filename: 文件名
    replacement_map: 替换对照字典

Returns:
    bool: 处理是否成功

### `process_all_docs`

处理所有文档

## 使用示例

```python
if __name__ == "__main__":
    print("开始处理文档...")
    process_all_docs()
    print("处理结束")
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


