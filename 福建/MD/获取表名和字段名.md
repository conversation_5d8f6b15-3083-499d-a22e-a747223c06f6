# 获取表名和字段名.py

- **文件路径**: `D:\work\demo\福建\py\获取表名和字段名.py`
- **文件大小**: 5KB
- **代码行数**: 155行
- **函数数量**: 9个
- **类数量**: 0个

## 脚本功能

处理获取表名和字段名相关任务，详细操作Excel文件，使用正则表达式进行文本处理，处理SQL文件，生成或导出数据

## 依赖项

```python
import re
import openpyxl
import re
import openpyxl
```

## 主要函数

*未识别到函数文档*

## 使用示例

```python
def main():
    input_file = "D:\work\demo\福建\宽表sql.sql"
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            sql = f.read()
    except FileNotFoundError:
        print(f"未找到文件: {input_file}")
        return

    parsed_fields = parse_sql_fields(sql)
    import re
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


