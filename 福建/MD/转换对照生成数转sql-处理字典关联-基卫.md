# 转换对照生成数转sql-处理字典关联-基卫.py

- **文件路径**: `D:\work\demo\福建\py\转换对照生成数转sql-处理字典关联-基卫.py`
- **文件大小**: 13KB
- **代码行数**: 206行
- **函数数量**: 2个
- **类数量**: 0个

## 脚本功能

提示词：
写一个py脚本'转换对照生成数转sql-处理字典关联-基卫.py'  对excel表格中的内容进行加工， excel路径D:\\work\\demo\\福建\\转换对照生成数转sql模板.xlsx
读取第一个sheet页内容，excel内容包括以下列：责任人  业务大类    文件名称    表全名     表中文名    表名      是否字典    字段中文名-元数据项      数据项     字段名     数据类型    长度      填报要求    是否字典    引用业务表   引用业务数据项 数据加工方式  主副表关联过滤 复杂关联质控规则描述      引用业务字段名 引用业务数据类型        引用业务数据长度        引用业务数据填报要求      辅助列业务表&业务字段     是否主键    是否索引    说明      字典匹配    拼接表字段   与分册对比   表中文名数据项 是否重复项   字段名小写   判断字段名有无大写       表名小写    判断表名有无大写

读取'引用业务数据项'列，对内容去掉空格和换行符，判断内容是否等于'dic_val_name'或者'dic_val_code':

当内容等于'dic_val_name'时，获取该行的'表名'列的值为table，'字段名'列的值为name，'引用业务表'列的值为table_name,
对name的值进行处理，去掉空格和换行符'_name'替换成'_code',获取新值 code，
将表名列=table，字段名列=code，匹配到行，获取该行的'引用业务字段名'列的值，去掉空格和换行符，作为新值 old_code，
对字典值名称行的'主副表关联过滤'列进行赋值，值的内容为
left join data_dic_a as 'table_name' on 'table_name'.dic_type_code=''code'' and 'table_name'.dic_val_code=t.'old_code'；

当内容等于'dic_val_code'时，获取该行的'表名'列的值为table，'字段名'列的值为code，'引用业务表'列的值为table_name,
对code的值进行处理，去掉空格和换行符'_code'替换成'_name',获取新值 name,
将表名列=table，字段名列=name，匹配到行，获取该行的'引用业务字段名'列的值，去掉空格和换行符，作为新值 old_name，
对字典值名称行的'主副表关联过滤'列进行赋值，值的内容为
left join data_dic_a as 'table_name' on 'table_name'.dic_type_code=''code'' and 'table_name'.dic_val_name=t.'old_name'；

## 依赖项

```python
import pandas as pd
import re
```

## 主要函数

### `clean_text_val`

清除文本中的所有空白字符（空格、换行符、制表符等）。
如果输入为NA或None，则返回空字符串。

### `process_excel_and_generate_sql`

读取Excel文件，根据指定规则处理数据，并生成SQL关联语句。

Args:
    file_path (str): 输入的Excel文件路径。
    output_file_path (str): 处理后保存的Excel文件路径。

## 使用示例

```python
if __name__ == '__main__':
    # 定义Excel文件路径
    # 使用r""定义原始字符串，避免转义问题，或使用双反斜杠"\\"
    excel_file_input_path = r"D:\work\demo\福建\转换对照生成数转sql模板.xlsx"

    # 定义输出文件路径（在原文件名后添加_processed）
    if '.xlsx' in excel_file_input_path:
        excel_file_output_path = excel_file_input_path.replace('.xlsx', '_processed.xlsx')
    elif '.xls' in excel_file_input_path:  # 也可处理 .xls 后缀
        excel_file_output_path = excel_file_input_path.replace('.xls', '_processed.xls')
    else:  # 如果是没有标准Excel后缀的文件名，则简单追加
        excel_file_output_path = excel_file_input_path + '_processed'

    print(f"输入文件: {excel_file_input_path}")
    print(f"输出文件: {excel_file_output_path}")

    process_excel_and_generate_sql(excel_file_input_path, excel_file_output_path)
```

## 注意事项

- 运行此脚本前请确保已安装所需的依赖库
- 请确保文件路径和权限设置正确


