原始 SQL: SELECT count(*) FROM patient_basic_info A where 1=1 and A<PERSON>EMPR_TEL not REGEXP '^1[3456789]\d{9}$'

Oracle 11g: SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\d{9}$')

SQL Server 2017: SELECT count(*) FROM patient_basic_info A where 1=1 and /* SQL Server 正则表达式转换 */ A.EMPR_TEL NOT LIKE '1[3456789]\d{9}'

GaussDB 5.0: SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL !~ '^1[3456789]\d{9}$'


=== 转换说明 ===
该 SQL 包含正则表达式 (REGEXP) 操作

Oracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能

GaussDB: 已转换为 ~ 或 !~ 操作符，GaussDB 使用这些操作符进行正则表达式匹配
