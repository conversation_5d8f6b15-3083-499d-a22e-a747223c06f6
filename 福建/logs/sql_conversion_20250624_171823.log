原始 SQL: SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL not REGEXP '^1[3456789]\d{9}$'

Oracle 11g: SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\d{9}$')

SQL Server 2017: SELECT count(*) FROM patient_basic_info A where 1=1 and /* SQL Server 正则表达式转换 */ CASE WHEN A.EMPR_TEL LIKE '1[3456789]\d{9}' THEN 0 ELSE 1 END = 1

GaussDB 5.0: SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL !~ '^1[3456789]\d{9}$'


=== 转换说明 ===
该 SQL 包含正则表达式 (REGEXP) 操作

Oracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能

SQL Server: 由于 SQL Server 不支持原生正则表达式，已转换为 CASE WHEN 和 LIKE 的组合
注意: 此转换仅为近似处理，不支持完整的正则表达式语法
建议: 对于复杂的正则表达式模式，请考虑以下方案：
  1. 使用 SQL Server CLR 自定义函数实现正则表达式功能
  2. 在应用层处理正则表达式逻辑
  3. 使用多个 LIKE 条件组合模拟简单的正则表达式

GaussDB: 已转换为 ~ 或 !~ 操作符，GaussDB 使用这些操作符进行正则表达式匹配
