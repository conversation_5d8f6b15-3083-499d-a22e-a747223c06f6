原始 SQL: SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR A.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0 OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')

Oracle 11g: SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR NOT REGEXP_LIKE(A.coner_cert_no, '^[0-9]{17}[0-9X]$') OR SUBSTR(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')

SQL Server 2017: SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR /* 身份证号码验证 */ (LEN(A.coner_cert_no) = 18 AND CASE WHEN A.coner_cert_no LIKE '[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9X]' THEN 1 ELSE 0 END = 1) OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')

GaussDB 5.0: SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR A.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0 OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')


=== 转换说明 ===
该 SQL 包含正则表达式 (REGEXP) 操作

Oracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能

SQL Server: 已识别到身份证号码验证模式，转换为特定的验证逻辑
- 使用 LEN() 函数检查长度是否为18位
- 使用 LIKE 模式匹配检查格式是否符合身份证号码规范
- 已转换为 SQL Server 兼容的语法，可以直接执行

GaussDB: 保留原始 REGEXP 语法，GaussDB 支持类似 MySQL 的正则表达式
