#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查生成的SQL文件的编码和内容
"""

import os

# 文件路径
file_path = r"D:\work\demo\福建\建表sql\质控sql\mysql184质控-记录结果版.sql"

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"文件不存在: {file_path}")
    exit(1)

# 检查文件大小
file_size = os.path.getsize(file_path)
print(f"文件大小: {file_size} 字节")

# 尝试以不同的编码读取文件
encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb18030']

for encoding in encodings:
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read(1000)  # 只读取前1000个字符
            print(f"\n使用 {encoding} 编码读取的内容:")
            print("=" * 50)
            print(content)
            print("=" * 50)
            print(f"成功使用 {encoding} 编码读取")
            break
    except UnicodeDecodeError:
        print(f"使用 {encoding} 编码读取失败")
    except Exception as e:
        print(f"读取文件时出错: {str(e)}") 