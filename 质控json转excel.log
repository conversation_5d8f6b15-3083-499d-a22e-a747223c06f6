2025-07-04 15:25:59,939 - INFO - === 开始处理质控JSON转Excel ===
2025-07-04 15:25:59,939 - INFO - Excel模板目录: D:\work\demo\福建\质控模板
2025-07-04 15:25:59,939 - INFO - JSON数据目录: D:\work\demo\福建\质控json
2025-07-04 15:25:59,939 - INFO - 输出目录: D:\work\demo\福建\质控结果
2025-07-04 15:25:59,939 - INFO - 找到 4 个Excel文件
2025-07-04 15:25:59,940 - INFO - 获取第一个Excel文件的第一行样式
2025-07-04 15:25:59,940 - INFO - 正在获取 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx 的第一行样式
2025-07-04 15:26:00,047 - INFO - 已成功获取第一行样式，共 50 个单元格
2025-07-04 15:26:00,047 - INFO - 成功获取第一行样式，将应用到所有输出文件
2025-07-04 15:26:00,047 - INFO - 正在处理Excel文件: 住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:00,089 - INFO - 正在处理工作表: 【住院患者入院记录表】inp_admission_record
2025-07-04 15:26:00,090 - INFO - 找到匹配的文件: coreflddq_inp_admission_record.txt
2025-07-04 15:26:00,090 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,129 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 15:26:00,130 - INFO - 删除后行数: 0
2025-07-04 15:26:00,131 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:00,171 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-04 15:26:00,188 - INFO - 已应用表头样式到工作表 【住院患者入院记录表】inp_admission_record
2025-07-04 15:26:00,190 - INFO - 已自动调整工作表 【住院患者入院记录表】inp_admission_record 的列宽
2025-07-04 15:26:00,190 - INFO - 工作表 【住院患者入院记录表】inp_admission_record 处理成功，总行数: 23
2025-07-04 15:26:00,190 - INFO - 正在处理工作表: 【住院患者出院记录表】inp_discharge_record
2025-07-04 15:26:00,190 - INFO - 找到匹配的文件: coreflddq_inp_discharge_record.txt
2025-07-04 15:26:00,190 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,222 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 15:26:00,223 - INFO - 删除后行数: 0
2025-07-04 15:26:00,224 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:00,266 - INFO - 已添加 23 条医院数据，共更新 1081 个单元格
2025-07-04 15:26:00,285 - INFO - 已应用表头样式到工作表 【住院患者出院记录表】inp_discharge_record
2025-07-04 15:26:00,287 - INFO - 已自动调整工作表 【住院患者出院记录表】inp_discharge_record 的列宽
2025-07-04 15:26:00,287 - INFO - 工作表 【住院患者出院记录表】inp_discharge_record 处理成功，总行数: 23
2025-07-04 15:26:00,287 - INFO - 正在处理工作表: 【住院诊断记录表】inp_diagnosis_record
2025-07-04 15:26:00,287 - INFO - 找到匹配的文件: coreflddq_inp_diagnosis_record.txt
2025-07-04 15:26:00,288 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,317 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:00,318 - INFO - 删除后行数: 0
2025-07-04 15:26:00,319 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:00,354 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-04 15:26:00,365 - INFO - 已应用表头样式到工作表 【住院诊断记录表】inp_diagnosis_record
2025-07-04 15:26:00,367 - INFO - 已自动调整工作表 【住院诊断记录表】inp_diagnosis_record 的列宽
2025-07-04 15:26:00,367 - INFO - 工作表 【住院诊断记录表】inp_diagnosis_record 处理成功，总行数: 24
2025-07-04 15:26:00,367 - INFO - 正在处理工作表: 【住院医嘱明细表】inp_order_detail
2025-07-04 15:26:00,367 - INFO - 找到匹配的文件: coreflddq_inp_order_detail.txt
2025-07-04 15:26:00,367 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,397 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-04 15:26:00,398 - INFO - 删除后行数: 0
2025-07-04 15:26:00,399 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 15:26:00,437 - INFO - 已添加 22 条医院数据，共更新 902 个单元格
2025-07-04 15:26:00,469 - INFO - 已应用表头样式到工作表 【住院医嘱明细表】inp_order_detail
2025-07-04 15:26:00,470 - INFO - 已自动调整工作表 【住院医嘱明细表】inp_order_detail 的列宽
2025-07-04 15:26:00,471 - INFO - 工作表 【住院医嘱明细表】inp_order_detail 处理成功，总行数: 22
2025-07-04 15:26:00,471 - INFO - 正在处理工作表: 【住院费用明细表】inp_charge_detail
2025-07-04 15:26:00,471 - INFO - 找到匹配的文件: coreflddq_inp_charge_detail.txt
2025-07-04 15:26:00,471 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,501 - INFO - 删除行政区划不为空的所有行，删除前行数: 34
2025-07-04 15:26:00,502 - INFO - 删除后行数: 0
2025-07-04 15:26:00,503 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:00,544 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-04 15:26:00,558 - INFO - 已应用表头样式到工作表 【住院费用明细表】inp_charge_detail
2025-07-04 15:26:00,560 - INFO - 已自动调整工作表 【住院费用明细表】inp_charge_detail 的列宽
2025-07-04 15:26:00,560 - INFO - 工作表 【住院费用明细表】inp_charge_detail 处理成功，总行数: 24
2025-07-04 15:26:00,560 - INFO - 正在处理工作表: 【住院结算主表】inp_settle_master
2025-07-04 15:26:00,560 - INFO - 找到匹配的文件: coreflddq_inp_settle_master.txt
2025-07-04 15:26:00,561 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,595 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:00,596 - INFO - 删除后行数: 0
2025-07-04 15:26:00,597 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:00,648 - INFO - 已添加 24 条医院数据，共更新 1512 个单元格
2025-07-04 15:26:00,669 - INFO - 已应用表头样式到工作表 【住院结算主表】inp_settle_master
2025-07-04 15:26:00,671 - INFO - 已自动调整工作表 【住院结算主表】inp_settle_master 的列宽
2025-07-04 15:26:00,671 - INFO - 工作表 【住院结算主表】inp_settle_master 处理成功，总行数: 24
2025-07-04 15:26:00,671 - INFO - 正在处理工作表: 【住院结算明细表】inp_settle_detail
2025-07-04 15:26:00,672 - INFO - 找到匹配的文件: coreflddq_inp_settle_detail.txt
2025-07-04 15:26:00,672 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,702 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-04 15:26:00,703 - INFO - 删除后行数: 0
2025-07-04 15:26:00,703 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:00,733 - INFO - 已添加 23 条医院数据，共更新 391 个单元格
2025-07-04 15:26:00,743 - INFO - 已应用表头样式到工作表 【住院结算明细表】inp_settle_detail
2025-07-04 15:26:00,744 - INFO - 已自动调整工作表 【住院结算明细表】inp_settle_detail 的列宽
2025-07-04 15:26:00,744 - INFO - 工作表 【住院结算明细表】inp_settle_detail 处理成功，总行数: 23
2025-07-04 15:26:00,744 - INFO - 正在处理工作表: 住院结算费用明inp_settle_charge_detail
2025-07-04 15:26:00,745 - INFO - 找到匹配的文件: coreflddq_inp_settle_charge_detail.txt
2025-07-04 15:26:00,745 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,776 - INFO - 删除行政区划不为空的所有行，删除前行数: 25
2025-07-04 15:26:00,777 - INFO - 删除后行数: 23
2025-07-04 15:26:00,778 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:00,821 - INFO - 已添加 23 条医院数据，共更新 1127 个单元格
2025-07-04 15:26:00,849 - INFO - 已应用表头样式到工作表 住院结算费用明inp_settle_charge_detail
2025-07-04 15:26:00,852 - INFO - 已自动调整工作表 住院结算费用明inp_settle_charge_detail 的列宽
2025-07-04 15:26:00,852 - INFO - 工作表 住院结算费用明inp_settle_charge_detail 处理成功，总行数: 46
2025-07-04 15:26:00,852 - INFO - 正在处理工作表: 住院转科记录表inp_transfer_dept_record
2025-07-04 15:26:00,852 - INFO - 找到匹配的文件: coreflddq_inp_transfer_dept_record.txt
2025-07-04 15:26:00,852 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:00,904 - INFO - 删除行政区划不为空的所有行，删除前行数: 17
2025-07-04 15:26:00,905 - INFO - 删除后行数: 0
2025-07-04 15:26:00,905 - INFO - 从JSON中提取了 11 条有效医院数据
2025-07-04 15:26:00,920 - INFO - 已添加 11 条医院数据，共更新 253 个单元格
2025-07-04 15:26:00,930 - INFO - 已应用表头样式到工作表 住院转科记录表inp_transfer_dept_record
2025-07-04 15:26:00,931 - INFO - 已自动调整工作表 住院转科记录表inp_transfer_dept_record 的列宽
2025-07-04 15:26:00,931 - INFO - 工作表 住院转科记录表inp_transfer_dept_record 处理成功，总行数: 11
2025-07-04 15:26:01,103 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_住院业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:01,103 - INFO - 正在处理Excel文件: 医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:01,135 - INFO - 正在处理工作表: 【检验申请表】 lis_request_form
2025-07-04 15:26:01,135 - INFO - 找到匹配的文件: coreflddq_lis_request_form.txt
2025-07-04 15:26:01,135 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,170 - INFO - 删除行政区划不为空的所有行，删除前行数: 23
2025-07-04 15:26:01,171 - INFO - 删除后行数: 0
2025-07-04 15:26:01,172 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-04 15:26:01,199 - INFO - 已添加 17 条医院数据，共更新 595 个单元格
2025-07-04 15:26:01,209 - INFO - 已应用表头样式到工作表 【检验申请表】 lis_request_form
2025-07-04 15:26:01,211 - INFO - 已自动调整工作表 【检验申请表】 lis_request_form 的列宽
2025-07-04 15:26:01,211 - INFO - 工作表 【检验申请表】 lis_request_form 处理成功，总行数: 17
2025-07-04 15:26:01,211 - INFO - 正在处理工作表: 【检验报告主表】 lis_report_master
2025-07-04 15:26:01,211 - INFO - 找到匹配的文件: coreflddq_lis_report_master.txt
2025-07-04 15:26:01,212 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,251 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 15:26:01,251 - INFO - 删除后行数: 0
2025-07-04 15:26:01,252 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 15:26:01,293 - INFO - 已添加 22 条医院数据，共更新 968 个单元格
2025-07-04 15:26:01,310 - INFO - 已应用表头样式到工作表 【检验报告主表】 lis_report_master
2025-07-04 15:26:01,311 - INFO - 已自动调整工作表 【检验报告主表】 lis_report_master 的列宽
2025-07-04 15:26:01,312 - INFO - 工作表 【检验报告主表】 lis_report_master 处理成功，总行数: 22
2025-07-04 15:26:01,312 - INFO - 正在处理工作表: 【检验报告细表】 lis_report_detail
2025-07-04 15:26:01,312 - INFO - 找到匹配的文件: coreflddq_lis_report_detail.txt
2025-07-04 15:26:01,312 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,370 - INFO - 删除行政区划不为空的所有行，删除前行数: 28
2025-07-04 15:26:01,370 - INFO - 删除后行数: 0
2025-07-04 15:26:01,371 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 15:26:01,405 - INFO - 已添加 22 条医院数据，共更新 792 个单元格
2025-07-04 15:26:01,419 - INFO - 已应用表头样式到工作表 【检验报告细表】 lis_report_detail
2025-07-04 15:26:01,421 - INFO - 已自动调整工作表 【检验报告细表】 lis_report_detail 的列宽
2025-07-04 15:26:01,421 - INFO - 工作表 【检验报告细表】 lis_report_detail 处理成功，总行数: 22
2025-07-04 15:26:01,421 - INFO - 正在处理工作表: 【检验细菌结果表】 lis_bacteria_result
2025-07-04 15:26:01,421 - INFO - 找到匹配的文件: coreflddq_lis_bacteria_result.txt
2025-07-04 15:26:01,421 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,456 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-04 15:26:01,457 - INFO - 删除后行数: 0
2025-07-04 15:26:01,458 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-04 15:26:01,475 - INFO - 已添加 12 条医院数据，共更新 348 个单元格
2025-07-04 15:26:01,485 - INFO - 已应用表头样式到工作表 【检验细菌结果表】 lis_bacteria_result
2025-07-04 15:26:01,486 - INFO - 已自动调整工作表 【检验细菌结果表】 lis_bacteria_result 的列宽
2025-07-04 15:26:01,486 - INFO - 工作表 【检验细菌结果表】 lis_bacteria_result 处理成功，总行数: 12
2025-07-04 15:26:01,487 - INFO - 正在处理工作表: 【检验药敏结果表】 lis_drug_sensitivity
2025-07-04 15:26:01,487 - INFO - 找到匹配的文件: coreflddq_lis_drug_sensitivity.txt
2025-07-04 15:26:01,487 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,521 - INFO - 删除行政区划不为空的所有行，删除前行数: 14
2025-07-04 15:26:01,522 - INFO - 删除后行数: 0
2025-07-04 15:26:01,522 - INFO - 从JSON中提取了 8 条有效医院数据
2025-07-04 15:26:01,534 - INFO - 已添加 8 条医院数据，共更新 248 个单元格
2025-07-04 15:26:01,544 - INFO - 已应用表头样式到工作表 【检验药敏结果表】 lis_drug_sensitivity
2025-07-04 15:26:01,545 - INFO - 已自动调整工作表 【检验药敏结果表】 lis_drug_sensitivity 的列宽
2025-07-04 15:26:01,545 - INFO - 工作表 【检验药敏结果表】 lis_drug_sensitivity 处理成功，总行数: 8
2025-07-04 15:26:01,545 - INFO - 正在处理工作表: 【检查申请表】 exam_request_form
2025-07-04 15:26:01,545 - INFO - 找到匹配的文件: coreflddq_exam_request_form.txt
2025-07-04 15:26:01,545 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,579 - INFO - 删除行政区划不为空的所有行，删除前行数: 13
2025-07-04 15:26:01,580 - INFO - 删除后行数: 0
2025-07-04 15:26:01,581 - INFO - 从JSON中提取了 7 条有效医院数据
2025-07-04 15:26:01,591 - INFO - 已添加 7 条医院数据，共更新 231 个单元格
2025-07-04 15:26:01,601 - INFO - 已应用表头样式到工作表 【检查申请表】 exam_request_form
2025-07-04 15:26:01,602 - INFO - 已自动调整工作表 【检查申请表】 exam_request_form 的列宽
2025-07-04 15:26:01,603 - INFO - 工作表 【检查申请表】 exam_request_form 处理成功，总行数: 7
2025-07-04 15:26:01,603 - INFO - 正在处理工作表: 【检查报告主表】 exam_report_master
2025-07-04 15:26:01,603 - INFO - 找到匹配的文件: coreflddq_exam_report_master.txt
2025-07-04 15:26:01,603 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,656 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-04 15:26:01,657 - INFO - 删除后行数: 0
2025-07-04 15:26:01,658 - INFO - 从JSON中提取了 17 条有效医院数据
2025-07-04 15:26:01,689 - INFO - 已添加 17 条医院数据，共更新 850 个单元格
2025-07-04 15:26:01,707 - INFO - 已应用表头样式到工作表 【检查报告主表】 exam_report_master
2025-07-04 15:26:01,708 - INFO - 已自动调整工作表 【检查报告主表】 exam_report_master 的列宽
2025-07-04 15:26:01,708 - INFO - 工作表 【检查报告主表】 exam_report_master 处理成功，总行数: 17
2025-07-04 15:26:01,709 - INFO - 正在处理工作表: 【检查报告细表】 exam_report_detail
2025-07-04 15:26:01,709 - INFO - 找到匹配的文件: coreflddq_exam_report_detail.txt
2025-07-04 15:26:01,709 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,744 - INFO - 删除行政区划不为空的所有行，删除前行数: 24
2025-07-04 15:26:01,746 - INFO - 删除后行数: 0
2025-07-04 15:26:01,746 - INFO - 从JSON中提取了 18 条有效医院数据
2025-07-04 15:26:01,771 - INFO - 已添加 18 条医院数据，共更新 450 个单元格
2025-07-04 15:26:01,782 - INFO - 已应用表头样式到工作表 【检查报告细表】 exam_report_detail
2025-07-04 15:26:01,783 - INFO - 已自动调整工作表 【检查报告细表】 exam_report_detail 的列宽
2025-07-04 15:26:01,783 - INFO - 工作表 【检查报告细表】 exam_report_detail 处理成功，总行数: 18
2025-07-04 15:26:01,783 - INFO - 正在处理工作表: 【检查报告部位表】 exam_report_part
2025-07-04 15:26:01,784 - INFO - 找到匹配的文件: coreflddq_exam_report_part.txt
2025-07-04 15:26:01,784 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,825 - INFO - 删除行政区划不为空的所有行，删除前行数: 18
2025-07-04 15:26:01,826 - INFO - 删除后行数: 0
2025-07-04 15:26:01,826 - INFO - 从JSON中提取了 12 条有效医院数据
2025-07-04 15:26:01,842 - INFO - 已添加 12 条医院数据，共更新 252 个单元格
2025-07-04 15:26:01,850 - INFO - 已应用表头样式到工作表 【检查报告部位表】 exam_report_part
2025-07-04 15:26:01,852 - INFO - 已自动调整工作表 【检查报告部位表】 exam_report_part 的列宽
2025-07-04 15:26:01,852 - INFO - 工作表 【检查报告部位表】 exam_report_part 处理成功，总行数: 12
2025-07-04 15:26:01,852 - INFO - 正在处理工作表: 【病理标本记录表】 exam_sample_record
2025-07-04 15:26:01,852 - INFO - 找到匹配的文件: coreflddq_exam_sample_record.txt
2025-07-04 15:26:01,853 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:01,941 - INFO - 删除行政区划不为空的所有行，删除前行数: 12
2025-07-04 15:26:01,941 - INFO - 删除后行数: 0
2025-07-04 15:26:01,942 - INFO - 从JSON中提取了 6 条有效医院数据
2025-07-04 15:26:01,952 - INFO - 已添加 6 条医院数据，共更新 186 个单元格
2025-07-04 15:26:01,962 - INFO - 已应用表头样式到工作表 【病理标本记录表】 exam_sample_record
2025-07-04 15:26:01,963 - INFO - 已自动调整工作表 【病理标本记录表】 exam_sample_record 的列宽
2025-07-04 15:26:01,963 - INFO - 工作表 【病理标本记录表】 exam_sample_record 处理成功，总行数: 6
2025-07-04 15:26:02,132 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_医技业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:02,133 - INFO - 正在处理Excel文件: 病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:02,156 - INFO - 正在处理工作表: 【病案首页基本信息】 case_base_info
2025-07-04 15:26:02,156 - INFO - 找到匹配的文件: coreflddq_case_base_info.txt
2025-07-04 15:26:02,156 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,195 - INFO - 删除行政区划不为空的所有行，删除前行数: 27
2025-07-04 15:26:02,196 - INFO - 删除后行数: 0
2025-07-04 15:26:02,197 - INFO - 从JSON中提取了 21 条有效医院数据
2025-07-04 15:26:02,273 - INFO - 已添加 21 条医院数据，共更新 2058 个单元格
2025-07-04 15:26:02,297 - INFO - 已应用表头样式到工作表 【病案首页基本信息】 case_base_info
2025-07-04 15:26:02,301 - INFO - 已自动调整工作表 【病案首页基本信息】 case_base_info 的列宽
2025-07-04 15:26:02,301 - INFO - 工作表 【病案首页基本信息】 case_base_info 处理成功，总行数: 21
2025-07-04 15:26:02,301 - INFO - 正在处理工作表: 【病案诊断记录表】 case_diagnosis_record
2025-07-04 15:26:02,301 - INFO - 找到匹配的文件: coreflddq_case_diagnosis_record.txt
2025-07-04 15:26:02,301 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,330 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:02,330 - INFO - 删除后行数: 0
2025-07-04 15:26:02,331 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 15:26:02,361 - INFO - 已添加 22 条医院数据，共更新 550 个单元格
2025-07-04 15:26:02,373 - INFO - 已应用表头样式到工作表 【病案诊断记录表】 case_diagnosis_record
2025-07-04 15:26:02,374 - INFO - 已自动调整工作表 【病案诊断记录表】 case_diagnosis_record 的列宽
2025-07-04 15:26:02,374 - INFO - 工作表 【病案诊断记录表】 case_diagnosis_record 处理成功，总行数: 22
2025-07-04 15:26:02,374 - INFO - 正在处理工作表: 【病案手术记录表】 case_operate_record
2025-07-04 15:26:02,375 - INFO - 找到匹配的文件: coreflddq_case_operate_record.txt
2025-07-04 15:26:02,375 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,402 - INFO - 删除行政区划不为空的所有行，删除前行数: 22
2025-07-04 15:26:02,402 - INFO - 删除后行数: 0
2025-07-04 15:26:02,403 - INFO - 从JSON中提取了 14 条有效医院数据
2025-07-04 15:26:02,426 - INFO - 已添加 14 条医院数据，共更新 406 个单元格
2025-07-04 15:26:02,436 - INFO - 已应用表头样式到工作表 【病案手术记录表】 case_operate_record
2025-07-04 15:26:02,437 - INFO - 已自动调整工作表 【病案手术记录表】 case_operate_record 的列宽
2025-07-04 15:26:02,437 - INFO - 工作表 【病案手术记录表】 case_operate_record 处理成功，总行数: 14
2025-07-04 15:26:02,438 - INFO - 正在处理工作表: 【病案费用记录表】 case_fee_record
2025-07-04 15:26:02,438 - INFO - 找到匹配的文件: coreflddq_case_fee_record.txt
2025-07-04 15:26:02,438 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,467 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:02,468 - INFO - 删除后行数: 0
2025-07-04 15:26:02,469 - INFO - 从JSON中提取了 22 条有效医院数据
2025-07-04 15:26:02,508 - INFO - 已添加 22 条医院数据，共更新 1012 个单元格
2025-07-04 15:26:02,524 - INFO - 已应用表头样式到工作表 【病案费用记录表】 case_fee_record
2025-07-04 15:26:02,526 - INFO - 已自动调整工作表 【病案费用记录表】 case_fee_record 的列宽
2025-07-04 15:26:02,526 - INFO - 工作表 【病案费用记录表】 case_fee_record 处理成功，总行数: 22
2025-07-04 15:26:02,600 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_病案管理分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:02,601 - INFO - 正在处理Excel文件: 门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:02,652 - INFO - 正在处理工作表: Sheet1
2025-07-04 15:26:02,652 - WARNING - 未找到匹配的文件: coreflddq_Sheet1.txt
2025-07-04 15:26:02,652 - WARNING - 未找到对应的JSON文件，跳过工作表 Sheet1
2025-07-04 15:26:02,685 - INFO - 正在处理工作表: 【门急诊挂号记录表】outp_register_record
2025-07-04 15:26:02,686 - INFO - 找到匹配的文件: coreflddq_outp_register_record.txt
2025-07-04 15:26:02,686 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,721 - INFO - 删除行政区划不为空的所有行，删除前行数: 32
2025-07-04 15:26:02,722 - INFO - 删除后行数: 0
2025-07-04 15:26:02,722 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 15:26:02,756 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-04 15:26:02,767 - INFO - 已应用表头样式到工作表 【门急诊挂号记录表】outp_register_record
2025-07-04 15:26:02,768 - INFO - 已自动调整工作表 【门急诊挂号记录表】outp_register_record 的列宽
2025-07-04 15:26:02,768 - INFO - 工作表 【门急诊挂号记录表】outp_register_record 处理成功，总行数: 25
2025-07-04 15:26:02,769 - INFO - 正在处理工作表: 【门急诊就诊记录表】outp_visit_record
2025-07-04 15:26:02,769 - INFO - 找到匹配的文件: coreflddq_outp_visit_record.txt
2025-07-04 15:26:02,769 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,810 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-04 15:26:02,811 - INFO - 删除后行数: 0
2025-07-04 15:26:02,812 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 15:26:02,853 - INFO - 已添加 25 条医院数据，共更新 1000 个单元格
2025-07-04 15:26:02,868 - INFO - 已应用表头样式到工作表 【门急诊就诊记录表】outp_visit_record
2025-07-04 15:26:02,870 - INFO - 已自动调整工作表 【门急诊就诊记录表】outp_visit_record 的列宽
2025-07-04 15:26:02,870 - INFO - 工作表 【门急诊就诊记录表】outp_visit_record 处理成功，总行数: 25
2025-07-04 15:26:02,871 - INFO - 正在处理工作表: 【门急诊断记录表】outp_diagnosis_record
2025-07-04 15:26:02,871 - INFO - 找到匹配的文件: coreflddq_outp_diagnosis_record.txt
2025-07-04 15:26:02,871 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:02,924 - INFO - 删除行政区划不为空的所有行，删除前行数: 31
2025-07-04 15:26:02,925 - INFO - 删除后行数: 0
2025-07-04 15:26:02,925 - INFO - 从JSON中提取了 25 条有效医院数据
2025-07-04 15:26:02,960 - INFO - 已添加 25 条医院数据，共更新 600 个单元格
2025-07-04 15:26:02,972 - INFO - 已应用表头样式到工作表 【门急诊断记录表】outp_diagnosis_record
2025-07-04 15:26:02,973 - INFO - 已自动调整工作表 【门急诊断记录表】outp_diagnosis_record 的列宽
2025-07-04 15:26:02,974 - INFO - 工作表 【门急诊断记录表】outp_diagnosis_record 处理成功，总行数: 25
2025-07-04 15:26:02,974 - INFO - 正在处理工作表: 【门诊医嘱主表】outp_order_master
2025-07-04 15:26:02,974 - INFO - 找到匹配的文件: coreflddq_outp_order_master.txt
2025-07-04 15:26:02,974 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:03,013 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:03,013 - INFO - 删除后行数: 0
2025-07-04 15:26:03,014 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:03,047 - INFO - 已添加 24 条医院数据，共更新 600 个单元格
2025-07-04 15:26:03,059 - INFO - 已应用表头样式到工作表 【门诊医嘱主表】outp_order_master
2025-07-04 15:26:03,060 - INFO - 已自动调整工作表 【门诊医嘱主表】outp_order_master 的列宽
2025-07-04 15:26:03,060 - INFO - 工作表 【门诊医嘱主表】outp_order_master 处理成功，总行数: 24
2025-07-04 15:26:03,060 - INFO - 正在处理工作表: 【门诊医嘱明细表】outp_order_detail
2025-07-04 15:26:03,060 - INFO - 找到匹配的文件: coreflddq_outp_order_detail.txt
2025-07-04 15:26:03,060 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:03,101 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 15:26:03,101 - INFO - 删除后行数: 0
2025-07-04 15:26:03,102 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:03,138 - INFO - 已添加 23 条医院数据，共更新 874 个单元格
2025-07-04 15:26:03,153 - INFO - 已应用表头样式到工作表 【门诊医嘱明细表】outp_order_detail
2025-07-04 15:26:03,154 - INFO - 已自动调整工作表 【门诊医嘱明细表】outp_order_detail 的列宽
2025-07-04 15:26:03,154 - INFO - 工作表 【门诊医嘱明细表】outp_order_detail 处理成功，总行数: 23
2025-07-04 15:26:03,154 - INFO - 正在处理工作表: 【门诊费用明细表】outp_charge_detail
2025-07-04 15:26:03,154 - INFO - 找到匹配的文件: coreflddq_outp_charge_detail.txt
2025-07-04 15:26:03,155 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:03,194 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:03,195 - INFO - 删除后行数: 0
2025-07-04 15:26:03,195 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:03,231 - INFO - 已添加 24 条医院数据，共更新 816 个单元格
2025-07-04 15:26:03,245 - INFO - 已应用表头样式到工作表 【门诊费用明细表】outp_charge_detail
2025-07-04 15:26:03,246 - INFO - 已自动调整工作表 【门诊费用明细表】outp_charge_detail 的列宽
2025-07-04 15:26:03,247 - INFO - 工作表 【门诊费用明细表】outp_charge_detail 处理成功，总行数: 24
2025-07-04 15:26:03,247 - INFO - 正在处理工作表: 【门诊结算主表】outp_settle_master
2025-07-04 15:26:03,247 - INFO - 找到匹配的文件: coreflddq_outp_settle_master.txt
2025-07-04 15:26:03,247 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:03,305 - INFO - 删除行政区划不为空的所有行，删除前行数: 30
2025-07-04 15:26:03,306 - INFO - 删除后行数: 0
2025-07-04 15:26:03,307 - INFO - 从JSON中提取了 24 条有效医院数据
2025-07-04 15:26:03,355 - INFO - 已添加 24 条医院数据，共更新 1536 个单元格
2025-07-04 15:26:03,376 - INFO - 已应用表头样式到工作表 【门诊结算主表】outp_settle_master
2025-07-04 15:26:03,378 - INFO - 已自动调整工作表 【门诊结算主表】outp_settle_master 的列宽
2025-07-04 15:26:03,378 - INFO - 工作表 【门诊结算主表】outp_settle_master 处理成功，总行数: 24
2025-07-04 15:26:03,378 - INFO - 正在处理工作表: 【门诊结算明细表】outp_settle_detail
2025-07-04 15:26:03,378 - INFO - 找到匹配的文件: coreflddq_outp_settle_detail.txt
2025-07-04 15:26:03,378 - INFO - 开始按照新的填充逻辑处理数据: 删除行政区划不为空的行，从JSON数据添加行
2025-07-04 15:26:03,415 - INFO - 删除行政区划不为空的所有行，删除前行数: 29
2025-07-04 15:26:03,416 - INFO - 删除后行数: 0
2025-07-04 15:26:03,416 - INFO - 从JSON中提取了 23 条有效医院数据
2025-07-04 15:26:03,445 - INFO - 已添加 23 条医院数据，共更新 437 个单元格
2025-07-04 15:26:03,454 - INFO - 已应用表头样式到工作表 【门诊结算明细表】outp_settle_detail
2025-07-04 15:26:03,455 - INFO - 已自动调整工作表 【门诊结算明细表】outp_settle_detail 的列宽
2025-07-04 15:26:03,455 - INFO - 工作表 【门诊结算明细表】outp_settle_detail 处理成功，总行数: 23
2025-07-04 15:26:03,612 - INFO - 已保存处理后的Excel文件: D:\work\demo\福建\质控结果\已填充_门急诊业务分册-机构-数据质量清单模版V1-全部医院.xlsx
2025-07-04 15:26:03,612 - INFO - 处理完成，成功处理 4/4 个文件
2025-07-04 15:26:03,613 - INFO - 总耗时: 0:00:03.673514
2025-07-04 15:26:03,613 - INFO - === 处理结束 ===
