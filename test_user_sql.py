from 福建.py.质控mysql转其他库 import translate_sql
import os

# 创建logs目录（如果不存在）
os.makedirs(os.path.join(os.path.dirname(os.path.abspath(__file__)), "福建", "logs"), exist_ok=True)

# 用户提供的SQL
user_sql = """SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR A.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0 OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')"""

# 处理SQL并手动生成日志文件
output_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "福建", "logs", "user_sql_conversion.log")

# 转换为各种方言
oracle_sql = translate_sql(user_sql, 'oracle')
sqlserver_sql = translate_sql(user_sql, 'sqlserver')
gaussdb_sql = translate_sql(user_sql, 'gaussdb')

print(f"\n===== 处理单个 SQL 语句 =====")
print(f"原始 SQL: {user_sql}")
print(f"\nOracle 11g: {oracle_sql}")
print(f"\nSQL Server 2017: {sqlserver_sql}")
print(f"\nGaussDB 5.0: {gaussdb_sql}")

# 手动写入日志文件
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(f"原始 SQL: {user_sql}\n\n")
    f.write(f"Oracle 11g: {oracle_sql}\n\n")
    f.write(f"SQL Server 2017: {sqlserver_sql}\n\n")
    f.write(f"GaussDB 5.0: {gaussdb_sql}\n")
    
    # 添加转换说明
    f.write("\n\n=== 转换说明 ===\n")
    f.write("该 SQL 包含正则表达式 (REGEXP) 操作\n")
    
    if 'REGEXP_LIKE' in oracle_sql:
        f.write("\nOracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能\n")
    
    if '/* 身份证号码验证 */' in sqlserver_sql:
        f.write("\nSQL Server: 已识别到身份证号码验证模式，转换为特定的验证逻辑\n")
        f.write("- 使用 LEN() 函数检查长度是否为18位\n")
        f.write("- 使用 LIKE 模式匹配检查格式是否符合身份证号码规范\n")
        f.write("- 已转换为 SQL Server 兼容的语法，可以直接执行\n")
    
    if 'REGEXP' in gaussdb_sql:
        f.write("\nGaussDB: 保留原始 REGEXP 语法，GaussDB 支持类似 MySQL 的正则表达式\n")

print(f"\n结果已保存到文件: {output_file}")