名称 代码 数值类型 是否为空 注释
字段名称 字段描述 字段类型 必填 说明 



数据项	字段名	数据类型	长度	填报要求 是否主键	说明


写一个'基卫平台数据结构提取.py'脚本 解析'D:\work\demo\福建\待提取'文件夹下的的word文件，提取word中所有表头在以下两种形式的表格
名称 代码 数值类型 是否为空 注释
字段名称 字段描述 字段类型 必填 说明 
这两类的表格内容和表格标题，表格的标题为表头上方的标题，标题需要去掉开头的数字，只保留数字后的内容
将提取的内容输出到'D:\work\demo\福建\基卫平台数据结构提取.xlsx'中
excel的表头为 表全名 表中文名 表名 数据项	字段名	数据类型	长度	填报要求 是否主键	说明
对应关系为
表全名：word中的表标题
数据项：名称/字段描述
字段名：代码/名称
数据类型：数值类型/字段类型 
填报要求：注释/说明
