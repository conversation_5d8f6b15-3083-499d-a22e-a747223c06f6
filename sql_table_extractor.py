#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import logging
import sqlparse
from typing import Set, List, Dict
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SQLTableExtractor:
    """SQL表名提取器，用于从SQL语句中提取表名"""
    
    def __init__(self):
        """初始化SQL表名提取器"""
        self.tables: Set[str] = set()
        self.schema_prefixes = {
            'INSUCENT_DB.', 'CUSTCENT_DB.', 'BASINFOCENT_DB.',
            'POLCENT_DB.', 'DATACENT_DB.'
        }
        self._table_cache: Dict[str, Set[str]] = {}
        
    def extract_tables(self, sql_text: str) -> List[str]:
        """从SQL文本中提取表名
        
        Args:
            sql_text: SQL查询语句
            
        Returns:
            提取出的表名列表
        """
        # 重置表集合
        self.tables = set()
        
        # 检查缓存
        cache_key = hash(sql_text)
        if cache_key in self._table_cache:
            return sorted(list(self._table_cache[cache_key]))
        
        try:
            # 预处理SQL
            sql_text = self._preprocess_sql(sql_text)
            
            # 解析SQL
            parsed = sqlparse.parse(sql_text)
            for statement in parsed:
                self._process_statement(statement)
                
            # 更新缓存
            self._table_cache[cache_key] = self.tables.copy()
            
            return sorted(list(self.tables))
        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
            return []
            
    def _preprocess_sql(self, sql_text: str) -> str:
        """预处理SQL文本
        
        Args:
            sql_text: 原始SQL文本
            
        Returns:
            处理后的SQL文本
        """
        # 标准化空白字符
        sql_text = re.sub(r'\s+', ' ', sql_text.strip())
        
        # 处理括号
        sql_text = re.sub(r'\(\s+', '(', sql_text)
        sql_text = re.sub(r'\s+\)', ')', sql_text)
        
        return sql_text
        
    def _clean_table_name(self, table_name: str) -> str:
        """清理表名，移除schema前缀和别名
        
        Args:
            table_name: 原始表名
            
        Returns:
            清理后的表名
        """
        if not table_name:
            return ""
            
        # 转换为大写进行处理
        table_name = table_name.upper()
        
        # 移除schema前缀
        for prefix in self.schema_prefixes:
            if table_name.startswith(prefix):
                table_name = table_name[len(prefix):]
                break
                
        # 移除别名
        parts = table_name.split()
        if len(parts) > 1:
            # 检查第二部分是否是关键字
            if not any(keyword in parts[1] for keyword in 
                      ['JOIN', 'WHERE', 'ON', 'AND', 'OR', 'GROUP', 'ORDER', 'HAVING']):
                table_name = parts[0]
                
        # 移除引号和空白
        table_name = table_name.strip('`"\' ')
        
        return table_name
        
    def _process_statement(self, statement):
        """处理SQL语句
        
        Args:
            statement: SQL语句对象
        """
        if not statement.is_group:
            return
            
        for token in statement.tokens:
            self._process_token(token)
            
    def _process_token(self, token):
        """处理SQL token
        
        Args:
            token: SQL token对象
        """
        # 跳过注释和空白
        if token.is_whitespace or token.is_comment:
            return
            
        # 处理FROM子句
        if token.is_keyword and token.value.upper() == 'FROM':
            next_token = self._get_next_token(token)
            if next_token:
                if next_token.is_group:
                    # 处理子查询
                    self._process_subquery(next_token)
                else:
                    # 处理表名
                    table_name = self._clean_table_name(next_token.value)
                    if table_name:
                        self.tables.add(table_name)
                        
        # 处理JOIN
        elif token.is_keyword and 'JOIN' in token.value.upper():
            next_token = self._get_next_token(token)
            if next_token:
                if next_token.is_group:
                    self._process_subquery(next_token)
                else:
                    table_name = self._clean_table_name(next_token.value)
                    if table_name:
                        self.tables.add(table_name)
                        
        # 处理IN和EXISTS
        elif token.is_keyword and token.value.upper() in ('IN', 'EXISTS', 'NOT EXISTS'):
            next_token = self._get_next_token(token)
            if next_token and next_token.is_group:
                self._process_subquery(next_token)
                
        # 递归处理子组
        elif token.is_group:
            self._process_statement(token)
            
    def _process_subquery(self, token):
        """处理子查询
        
        Args:
            token: 子查询token
        """
        if token.is_group:
            # 如果是SELECT开头的子查询
            first_token = token.tokens[0] if token.tokens else None
            if first_token and first_token.value.upper() == 'SELECT':
                self._process_statement(token)
            else:
                # 递归处理其他组类型
                for sub_token in token.tokens:
                    if sub_token.is_group:
                        self._process_subquery(sub_token)
                        
    def _get_next_token(self, token):
        """获取下一个有效token
        
        Args:
            token: 当前token
            
        Returns:
            下一个有效token或None
        """
        parent = token.parent
        if not parent:
            return None
            
        found_current = False
        for t in parent.tokens:
            if found_current:
                if not (t.is_whitespace or t.is_comment):
                    return t
            elif t == token:
                found_current = True
                
        return None
        
    def extract_tables_from_file(self, file_path: str, batch_size: int = 1000) -> List[str]:
        """从SQL文件中提取表名
        
        Args:
            file_path: SQL文件路径
            batch_size: 批处理大小
            
        Returns:
            提取出的表名列表
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return []
                
            all_tables = set()
            current_sql = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        current_sql.append(line)
                        
                    # 当遇到分号或达到批处理大小时处理
                    if line.endswith(';') or len(current_sql) >= batch_size:
                        if current_sql:
                            sql_text = ' '.join(current_sql)
                            tables = self.extract_tables(sql_text)
                            all_tables.update(tables)
                            current_sql = []
                            
                # 处理最后一批
                if current_sql:
                    sql_text = ' '.join(current_sql)
                    tables = self.extract_tables(sql_text)
                    all_tables.update(tables)
                    
            return sorted(list(all_tables))
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            return []

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Extract table names from SQL file')
    parser.add_argument('sql_file', help='Path to the SQL file')
    parser.add_argument('--batch-size', type=int, default=1000,
                      help='Batch size for processing (default: 1000)')
    
    args = parser.parse_args()
    
    extractor = SQLTableExtractor()
    tables = extractor.extract_tables_from_file(args.sql_file, args.batch_size)
    
    if tables:
        print("\nExtracted table names:")
        for table in tables:
            print(f"- {table}")
    else:
        print("No tables found or error occurred")

if __name__ == '__main__':
    main() 