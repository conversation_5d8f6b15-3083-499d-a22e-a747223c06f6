createtab_stmt,
CREATE EXTERNAL TABLE `RTAL_PHAC_B_paimon`(,
  `rtal_phac_code` string COMMENT 'from deserializer', ,
  `rtal_phac_name` string COMMENT 'from deserializer', ,
  `rtal_phac_abbr` string COMMENT 'from deserializer', ,
  `prnt_rtal_phac_code` string COMMENT 'from deserializer', ,
  `itro` string COMMENT 'from deserializer', ,
  `uscc` string COMMENT 'from deserializer', ,
  `tel` string COMMENT 'from deserializer', ,
  `lnt` string COMMENT 'from deserializer', ,
  `lat` string COMMENT 'from deserializer', ,
  `admdvs` string COMMENT 'from deserializer', ,
  `addr` string COMMENT 'from deserializer', ,
  `begntime` timestamp COMMENT 'from deserializer', ,
  `endtime` timestamp COMMENT 'from deserializer', ,
  `brch_flag` string COMMENT 'from deserializer', ,
  `drug_biz_lic_no` string COMMENT 'from deserializer', ,
  `memo` string COMMENT 'from deserializer', ,
  `drug_biz_scp` string COMMENT 'from deserializer', ,
  `econ_type` string COMMENT 'from deserializer', ,
  `phar_psncnt` int COMMENT 'from deserializer', ,
  `phac_chan_type` string COMMENT 'from deserializer', ,
  `equ_biz_lic_no` string COMMENT 'from deserializer', ,
  `cred_lv` string COMMENT 'from deserializer', ,
  `cred_lv_name` string COMMENT 'from deserializer', ,
  `vali_flag` string COMMENT 'from deserializer', ,
  `rid` string COMMENT 'from deserializer', ,
  `crte_time` timestamp COMMENT 'from deserializer', ,
  `updt_time` timestamp COMMENT 'from deserializer', ,
  `crter_id` string COMMENT 'from deserializer', ,
  `crter_name` string COMMENT 'from deserializer', ,
  `crte_optins_no` string COMMENT 'from deserializer', ,
  `opter_id` string COMMENT 'from deserializer', ,
  `opter_name` string COMMENT 'from deserializer', ,
  `opt_time` timestamp COMMENT 'from deserializer', ,
  `optins_no` string COMMENT 'from deserializer', ,
  `ver` string COMMENT 'from deserializer', ,
  `sync_prnt_flag` string COMMENT 'from deserializer', ,
  `ver_rid` string COMMENT 'from deserializer', ,
  `ver_name` string COMMENT 'from deserializer', ,
  `rtal_phac_id` string COMMENT 'from deserializer', ,
  `biz_stas` string COMMENT 'from deserializer', ,
  `legrep_name` string COMMENT 'from deserializer', ,
  `legrep_cert_type` string COMMENT 'from deserializer', ,
  `legrep_certno` string COMMENT 'from deserializer', ,
  `regcap` string COMMENT 'from deserializer', ,
  `biz_lic_expy_begntime` timestamp COMMENT 'from deserializer', ,
  `biz_lic_expy_endtime` timestamp COMMENT 'from deserializer', ,
  `biz_way` string COMMENT 'from deserializer', ,
  `entp_resper` string COMMENT 'from deserializer', ,
  `hi_coner_name` string COMMENT 'from deserializer', ,
  `hi_coner_tel` string COMMENT 'from deserializer', ,
  `rtal_phac_email` string COMMENT 'from deserializer', ,
  `bank_name` string COMMENT 'from deserializer', ,
  `bankacct` string COMMENT 'from deserializer', ,
  `bank` string COMMENT 'from deserializer', ,
  `cert_elec_doc` string COMMENT 'from deserializer', ,
  `drug_biz_lic_elec_doc` string COMMENT 'from deserializer', ,
  `act_addr_info` string COMMENT 'from deserializer', ,
  `act_addr_code` string COMMENT 'from deserializer', ,
  `ec_open_flag` string COMMENT 'from deserializer', ,
  `dif_dscr` string COMMENT 'from deserializer', ,
  `slicetag` string COMMENT 'from deserializer', ,
  `gx_op_code` string COMMENT 'from deserializer', ,
  `gx_op_time` timestamp COMMENT 'from deserializer'),
ROW FORMAT SERDE ,
  'org.apache.paimon.hive.PaimonSerDe' ,
STORED BY ,
  'org.apache.paimon.hive.PaimonStorageHandler' ,
WITH SERDEPROPERTIES ( ,
  'serialization.format'='1'),
LOCATION,
  'hdfs://ns1/domain/ns1/scs_cd_data_hudi/hd_rtal_phac_b',
TBLPROPERTIES (,
  'bucketing_version'='2', ,
  'transient_lastDdlTime'='**********'),
