import requests
import time
import threading
import json
from concurrent.futures import ThreadPoolExecutor
import statistics
import argparse
import os
from datetime import datetime
#另外一个是sql文件解析出100个sql   这设置c 10  就是这100个sql 每次选出10个执行
#python sql_stress_dic.py -f test.sql -c 2 -l D:\python\stress_test.log
# 配置参数
REQUEST_URL = "https://10.108.138.10:9437/dataDev/query/submitQuery"
CALLBACK_URL = "https://10.108.138.10:9437/dataDev/query/getQueryLog"
COOKIES = {"controller-platform-Token": "7e7daaba-f1a5-4ace-8c2c-5afcb6a2d9e9"}
HEADERS = {"Content-Type": "application/json;charset=UTF-8"}

# 请求体模板
REQUEST_BODY_TEMPLATE = {
    "projectId": "59",
    "tenantId": "13",
    "tenantName": "成都医保",
    "projectCode": "drsjcx",
    "projectName": "东软数据查询",
    "projectRoleId": "235",
    "setConfigStr": "set hive.tez.container.size=16384;\nset tez.task.resource.memory.mb=16384;\nset tez.am.resource.memory.mb=16384;\nset hive.groupby.skewindata=false;",
    "dbName": "drsjcx",
    "dbId": "22cbd0c5e0a24c25a2e18b7e89c64f27",
    "schemaName": "",
    "dataSourceId": "5",
    "innerSource": False,
    "env": "2",
    "tempTag": "1",
    "engineeType": "hive",
    "clusterId": "5",
    "hadoopAccount": "prd_cdyb_drsjcx",
    "queueName": "data_static",
    "dataType": 1,
    "geerEncrypt": False
}

# 全局统计变量
stats_lock = threading.Lock()
test_results = {}

# 日志记录函数
def log_query_result(query_name, stats, log_file):
    """将查询结果写入日志文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"\n=== {timestamp} - {query_name} ===\n"
    log_entry += f"总请求数: {stats['total_requests']}\n"
    log_entry += f"成功请求数: {stats['success_count']}\n"
    log_entry += f"失败请求数: {stats['failure_count']}\n"
    log_entry += f"总耗时: {stats['total_duration']:.2f}秒\n"
    
    if stats['success_count'] > 0:
        log_entry += f"平均响应时间: {stats['avg_response_time']:.2f}秒\n"
        log_entry += f"最小响应时间: {stats['min_response_time']:.2f}秒\n"
        log_entry += f"最大响应时间: {stats['max_response_time']:.2f}秒\n"
        log_entry += f"中位数响应时间: {stats['median_response_time']:.2f}秒\n"
        log_entry += f"95百分位响应时间: {stats['p95_response_time']:.2f}秒\n"
    
    # 添加错误日志记录
    if stats.get('error'):
        log_entry += f"\n错误信息: {stats['error']}\n"
        if 'logs' in stats and stats['logs']:
            log_entry += "详细日志:\n"
            for log in stats['logs']:
                log_entry += f"- {log}\n"
    
    # 同时输出到控制台和文件
    print(log_entry)
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(log_entry)

def load_sql_queries(file_path):
    """从文件中加载SQL查询语句"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"SQL文件不存在: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按分号分割SQL语句，并过滤空语句
    queries = [q.strip() for q in content.split(';') if q.strip()]
    return queries

def execute_single_query(sql_query, query_name):
    """执行单个SQL查询"""
    start_time = time.time()
    request_body = REQUEST_BODY_TEMPLATE.copy()
    request_body["code"] = sql_query
    
    try:
        # 发送初始请求
        response = requests.post(
            REQUEST_URL,
            headers=HEADERS,
            cookies=COOKIES,
            json=request_body,
            verify=False
        )
        
        if response.status_code != 200:
            print(f"请求失败[{query_name}]: {response.status_code} - {response.text}")
            return {
                "query": sql_query,
                "success": False,
                "total_duration": time.time() - start_time,
                "error": f"请求失败: {response.status_code}"
            }
        
        task_code = response.json()["data"]["taskCode"]
        
        # 轮询回调接口
        while True:
            callback_params = {
                "taskCode": task_code,
                "engineeType": "hive",
                "currentLine": 1
            }
            
            callback_response = requests.get(
                CALLBACK_URL,
                params=callback_params,
                cookies=COOKIES,
                verify=False
            )
            
            if callback_response.status_code != 200:
                print(f"回调请求失败[{query_name}]: {callback_response.status_code} - {callback_response.text}")
                return {
                    "query": sql_query,
                    "success": False,
                    "total_duration": time.time() - start_time,
                    "error": f"回调请求失败: {callback_response.status_code}"
                }
            
            data = callback_response.json()["data"]
            
            # 检查taskStatus = 3的情况（接口异常）
            if data["taskStatus"] == 3:
                error_logs = data.get("logs", [])
                error_message = "\n".join(error_logs) if error_logs else "接口发生异常"
                print(f"SQL执行异常[{query_name}]: {error_message}")
                return {
                    "query": sql_query,
                    "success": False,
                    "total_duration": time.time() - start_time,
                    "error": error_message,
                    "logs": error_logs
                }
            
            if data["taskStatus"] == 0:
                break
                
            time.sleep(1)  # 每秒检查一次
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"请求完成[{query_name}]: 耗时 {duration:.2f}秒")
        
        return {
            "query": sql_query,
            "success": True,
            "total_duration": duration,
            "response_time": duration
        }
        
    except Exception as e:
        print(f"请求异常[{query_name}]: {str(e)}")
        return {
            "query": sql_query,
            "success": False,
            "total_duration": time.time() - start_time,
            "error": str(e)
        }

def execute_sql_query(sql_query, query_name, concurrency):
    """执行单个SQL查询的压测"""
    start_time = time.time()
    request_body = REQUEST_BODY_TEMPLATE.copy()
    request_body["code"] = sql_query
    
    response_times = []
    success_count = 0
    failure_count = 0
    
    def worker(worker_id):
        nonlocal success_count, failure_count
        worker_start = time.time()
        
        try:
            # 发送初始请求
            response = requests.post(
                REQUEST_URL,
                headers=HEADERS,
                cookies=COOKIES,
                json=request_body,
                verify=False
            )
            
            if response.status_code != 200:
                with stats_lock:
                    failure_count += 1
                    print(f"请求失败[{query_name}-{worker_id}]: {response.status_code} - {response.text}")
                return None
            
            task_code = response.json()["data"]["taskCode"]
            
            # 轮询回调接口
            while True:
                callback_params = {
                    "taskCode": task_code,
                    "engineeType": "hive",
                    "currentLine": 1
                }
                
                callback_response = requests.get(
                    CALLBACK_URL,
                    params=callback_params,
                    cookies=COOKIES,
                    verify=False
                )
                
                if callback_response.status_code != 200:
                    with stats_lock:
                        failure_count += 1
                        print(f"回调请求失败[{query_name}-{worker_id}]: {callback_response.status_code} - {callback_response.text}")
                    return None
                
                data = callback_response.json()["data"]
                if data["taskStatus"] == 0:
                    break
                    
                time.sleep(1)  # 每秒检查一次
            
            worker_end = time.time()
            duration = worker_end - worker_start
            
            with stats_lock:
                success_count += 1
                response_times.append(duration)
                print(f"请求完成[{query_name}-{worker_id}]: 耗时 {duration:.2f}秒")
            
            return duration
            
        except Exception as e:
            with stats_lock:
                failure_count += 1
                print(f"请求异常[{query_name}-{worker_id}]: {str(e)}")
            return None
    
    # 创建线程池执行并发请求
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(worker, i+1) for i in range(concurrency)]
        
        # 等待所有请求完成
        for future in futures:
            future.result()
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 计算统计指标
    stats = {
        "query": sql_query,
        "total_requests": concurrency,
        "success_count": success_count,
        "failure_count": failure_count,
        "total_duration": total_duration,
        "response_times": response_times
    }
    
    if success_count > 0:
        stats["avg_response_time"] = statistics.mean(response_times)
        stats["min_response_time"] = min(response_times)
        stats["max_response_time"] = max(response_times)
        stats["median_response_time"] = statistics.median(response_times)
        stats["p95_response_time"] = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0]
    
    return stats

def run_stress_test(sql_file, max_concurrent_queries, log_file):
    """运行压测主函数"""
    queries = load_sql_queries(sql_file)
    total_queries = len(queries)
    
    print(f"\n开始测试，同时执行的SQL数量: {max_concurrent_queries}")
    print(f"总SQL数量: {total_queries}")
    
    # 创建任务列表
    tasks = []
    for i, query in enumerate(queries, 1):
        query_name = f"Query-{i}"
        tasks.append((query, query_name))
    
    def process_query(task):
        query, query_name = task
        print(f"\n开始执行 [{query_name}]")
        print(f"SQL: {query[:100]}...")  # 只打印前100个字符避免输出过长
        
        stats = execute_single_query(query, query_name)
        
        # 转换结果格式以兼容现有的日志记录函数
        log_stats = {
            "query": stats["query"],
            "total_requests": 1,
            "success_count": 1 if stats["success"] else 0,
            "failure_count": 0 if stats["success"] else 1,
            "total_duration": stats["total_duration"]
        }
        
        if stats["success"]:
            log_stats.update({
                "avg_response_time": stats["response_time"],
                "min_response_time": stats["response_time"],
                "max_response_time": stats["response_time"],
                "median_response_time": stats["response_time"],
                "p95_response_time": stats["response_time"]
            })
        else:
            # 添加错误信息和日志
            if "error" in stats:
                log_stats["error"] = stats["error"]
            if "logs" in stats:
                log_stats["logs"] = stats["logs"]
        
        # 记录查询结果到日志文件
        log_query_result(query_name, log_stats, log_file)
        
        return query_name, log_stats
    
    # 使用线程池并发执行查询
    with ThreadPoolExecutor(max_workers=max_concurrent_queries) as executor:
        futures = [executor.submit(process_query, task) for task in tasks]
        
        # 等待所有查询完成并收集结果
        for future in futures:
            query_name, stats = future.result()
            test_results[query_name] = stats

def save_test_results(output_file):
    """保存测试结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    print(f"\n测试结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="SQL查询平台压测工具")
    parser.add_argument("-f", "--file", type=str, required=True, 
                       help="包含SQL语句的文件路径 (多条SQL用分号分隔)")
    parser.add_argument("-c", "--concurrency", type=int, required=True, 
                       help="同时执行的SQL数量")
    parser.add_argument("-o", "--output", type=str, default="stress_test_results.json",
                       help="测试结果输出文件路径 (默认: stress_test_results.json)")
    parser.add_argument("-l", "--log", type=str, default="stress_test_results.log",
                       help="日志文件路径 (默认: stress_test_results.log)")
    
    args = parser.parse_args()
    
    # 禁用SSL警告
    requests.packages.urllib3.disable_warnings()
    
    print(f"开始压测: 同时执行的SQL数量={args.concurrency}, SQL文件={args.file}")
    print(f"日志文件: {args.log}")
    
    # 确保日志文件目录存在
    log_dir = os.path.dirname(args.log)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
        
    run_stress_test(args.file, args.concurrency, args.log)
    save_test_results(args.output)

if __name__ == "__main__":
    main()