createtab_stmt,
CREATE EXTERNAL TABLE `medins_info_b_paimon`(,
  `medins_code` string COMMENT 'from deserializer', ,
  `uscc` string COMMENT 'from deserializer', ,
  `medins_name` string COMMENT 'from deserializer', ,
  `medins_abbr` string COMMENT 'from deserializer', ,
  `fax_no` string COMMENT 'from deserializer', ,
  `main_resper` string COMMENT 'from deserializer', ,
  `admdvs` string COMMENT 'from deserializer', ,
  `addr` string COMMENT 'from deserializer', ,
  `medins_type` string COMMENT 'from deserializer', ,
  `medins_type_name` string COMMENT 'from deserializer', ,
  `fixmedins_type` string COMMENT 'from deserializer', ,
  `reg_stas` string COMMENT 'from deserializer', ,
  `enrd_staf_psncnt` int COMMENT 'from deserializer', ,
  `hosp_dept_cnt` int COMMENT 'from deserializer', ,
  `hosp_key_dept_cnt` int COMMENT 'from deserializer', ,
  `senr_profttl_psncnt` int COMMENT 'from deserializer', ,
  `mid_profttl_psncnt` int COMMENT 'from deserializer', ,
  `pro_techstf_psncnt` int COMMENT 'from deserializer', ,
  `depsenr_profttl_psncnt` int COMMENT 'from deserializer', ,
  `aprv_bed_cnt` int COMMENT 'from deserializer', ,
  `biz_psncnt` int COMMENT 'from deserializer', ,
  `oth_psncnt` int COMMENT 'from deserializer', ,
  `biz_area` string COMMENT 'from deserializer', ,
  `medinslv` string COMMENT 'from deserializer', ,
  `hosp_lv` string COMMENT 'from deserializer', ,
  `lnt` string COMMENT 'from deserializer', ,
  `lat` string COMMENT 'from deserializer', ,
  `begntime` timestamp COMMENT 'from deserializer', ,
  `endtime` timestamp COMMENT 'from deserializer', ,
  `memo` string COMMENT 'from deserializer', ,
  `grst_hosp_flag` string COMMENT 'from deserializer', ,
  `cred_lv` string COMMENT 'from deserializer', ,
  `cred_lv_name` string COMMENT 'from deserializer', ,
  `prnt_medins_code` string COMMENT 'from deserializer', ,
  `vali_flag` string COMMENT 'from deserializer', ,
  `medins_natu` string COMMENT 'from deserializer', ,
  `medins_natu_name` string COMMENT 'from deserializer', ,
  `npmo_flag` string COMMENT 'from deserializer', ,
  `rid` string COMMENT 'from deserializer', ,
  `crter_id` string COMMENT 'from deserializer', ,
  `crter_name` string COMMENT 'from deserializer', ,
  `crte_time` timestamp COMMENT 'from deserializer', ,
  `updt_time` timestamp COMMENT 'from deserializer', ,
  `crte_optins_no` string COMMENT 'from deserializer', ,
  `opter_id` string COMMENT 'from deserializer', ,
  `opter_name` string COMMENT 'from deserializer', ,
  `opt_time` timestamp COMMENT 'from deserializer', ,
  `optins_no` string COMMENT 'from deserializer', ,
  `ver` string COMMENT 'from deserializer', ,
  `sync_prnt_flag` string COMMENT 'from deserializer', ,
  `medins_grade` string COMMENT 'from deserializer', ,
  `ver_rid` string COMMENT 'from deserializer', ,
  `ver_name` string COMMENT 'from deserializer', ,
  `medins_info_id` string COMMENT 'from deserializer', ,
  `legent_name` string COMMENT 'from deserializer', ,
  `legrep_name` string COMMENT 'from deserializer', ,
  `medins_prac_lic_regno` string COMMENT 'from deserializer', ,
  `biznat` string COMMENT 'from deserializer', ,
  `econ_type` string COMMENT 'from deserializer', ,
  `afil_rlts` string COMMENT 'from deserializer', ,
  `trtitem` string COMMENT 'from deserializer', ,
  `medins_prac_lic_expy` timestamp COMMENT 'from deserializer', ,
  `bank_name` string COMMENT 'from deserializer', ,
  `bankacct` string COMMENT 'from deserializer', ,
  `bank` string COMMENT 'from deserializer', ,
  `inchg_hosp_resper_name` string COMMENT 'from deserializer', ,
  `inchg_hosp_resper_tel` string COMMENT 'from deserializer', ,
  `hi_resper_name` string COMMENT 'from deserializer', ,
  `hi_resper_tel` string COMMENT 'from deserializer', ,
  `hi_tel` string COMMENT 'from deserializer', ,
  `hi_email` string COMMENT 'from deserializer', ,
  `medins_cert_elec_doc` string COMMENT 'from deserializer', ,
  `medins_prac_lic_elec_doc` string COMMENT 'from deserializer', ,
  `asgcode_dr_cnt` int COMMENT 'from deserializer', ,
  `asgcode_nurs_cnt` int COMMENT 'from deserializer', ,
  `act_addr_info` string COMMENT 'from deserializer', ,
  `act_addr_code` string COMMENT 'from deserializer', ,
  `ec_open_flag` string COMMENT 'from deserializer', ,
  `dif_dscr` string COMMENT 'from deserializer', ,
  `slicetag` string COMMENT 'from deserializer', ,
  `gx_op_code` string COMMENT 'from deserializer', ,
  `gx_op_time` timestamp COMMENT 'from deserializer', ,
  `reg_mgt_org_type` string COMMENT 'from deserializer', ,
  `ons_fom` string COMMENT 'from deserializer'),
ROW FORMAT SERDE ,
  'org.apache.paimon.hive.PaimonSerDe' ,
STORED BY ,
  'org.apache.paimon.hive.PaimonStorageHandler' ,
WITH SERDEPROPERTIES ( ,
  'serialization.format'='1'),
LOCATION,
  'hdfs://ns1/domain/ns1/scs_cd_data_hudi/hd_medins_info_b',
TBLPROPERTIES (,
  'bucketing_version'='2', ,
  'transient_lastDdlTime'='1738922752'),
