校验脚本
SELECT COUNT(1) FROM CLRED_SETL_D WHERE IFNULL(MEDFEE_SUMAMT,0)!=(IFNULL(FULAMT_OWNPAY_AMT,0)+IFNULL(PRESELFPAY_AMT,0)+IFNULL(OVERLMT_SELFPAY,0)+IFNULL(INSCP_AMT,0))
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_D B WHERE B.CLCT_SUMAMT <>(SELECT SUM(A.CLCT_AMT) FROM STAF_PSN_CLCT_DETL_EXT_D A WHERE A.PSN_CLCT_DETL_ID=B.PSN_CLCT_DETL_ID )
SELECT COUNT(1) FROM ( SELECT PSN_NO , YEAR, INSUTYPE , CUM_TYPE_CODE , SUM(CUM) AS CUM FROM PSN_TRT_INFO_D) A , ( SELECT PSN_NO , YEAR, INSUTYP<PERSON> , CUM_TYPE_CODE , SUM(CUM) AS CUM FROM PSN_TRT_SUM_INFO_D) B WHERE A.PSN_NO = B.PSN_NO AND A.INSUTYPE = B.INSUTYPE AND A.YEAR = B.YEAR AND A.CUM_TYPE_CODE = B.CUM_TYPE_CODE AND A.CUM <> B.CUM
SELECT COUNT(1) FROM (SELECT PSN_NO,SETL_ID,SUM(DET_ITEM_FEE_SUMAMT) AS DET_ITEM_FEE_SUMAMT FROM FEE_LIST_D GROUP BY PSN_NO,SETL_ID ) A,SETL_D B WHERE A.SETL_ID = B.SETL_ID AND A.PSN_NO=B.PSN_NO AND A.DET_ITEM_FEE_SUMAMT <> B.MEDFEE_SUMAMT
SELECT COUNT(1) FROM ACCT_PAY_DETL_D A WHERE IFNULL( PAY_SUMAMT, 0 ) != ( IFNULL( UEBMI_ACCT_CRTYEAR_PAY, 0 ) + IFNULL( UEBMI_ACCT_LASTY_PAY, 0 ) + IFNULL( UEBMI_ACCT_PTYEAR_PAY, 0 ) + IFNULL( CVLSERV_ACCT_CRTYEAR_PAY, 0 ) + IFNULL( CVLSERV_ACCT_LASTY_PAY, 0 ) + IFNULL( CVLSERV_ACCT_PTYEAR_PAY, 0 ) + IFNULL( OTH_ACCT_CRTYEAR_PAY, 0 ) + IFNULL( OTH_ACCT_LASTY_PAY, 0 ) + IFNULL( OTH_ACCT_PTYEAR_PAY, 0 ) + IFNULL( OTH_ACCT_PAY, 0 ) )
SELECT COUNT(1) FROM( SELECT A.UEBMI_ACCT_CRTYEAR_PAY, SUM( B.UEBMI_ACCT_CRTYEAR_PAY ) SUMACCT , A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID , A.UEBMI_ACCT_CRTYEAR_PAY HAVING UEBMI_ACCT_CRTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM ( SELECT A.UEBMI_ACCT_LASTY_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.UEBMI_ACCT_LASTY_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.UEBMI_ACCT_LASTY_PAY HAVING UEBMI_ACCT_LASTY_PAY != SUMACCT ) C
SELECT COUNT(1) FROM ( SELECT A.UEBMI_ACCT_PTYEAR_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.UEBMI_ACCT_PTYEAR_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.UEBMI_ACCT_PTYEAR_PAY HAVING UEBMI_ACCT_PTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.CVLSERV_ACCT_CRTYEAR_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.CVLSERV_ACCT_CRTYEAR_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.CVLSERV_ACCT_CRTYEAR_PAYHAVING CVLSERV_ACCT_CRTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.CVLSERV_ACCT_LASTY_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.CVLSERV_ACCT_LASTY_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.CVLSERV_ACCT_LASTY_PAYHAVING CVLSERV_ACCT_LASTY_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.CVLSERV_ACCT_PTYEAR_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.CVLSERV_ACCT_PTYEAR_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.CVLSERV_ACCT_PTYEAR_PAYHAVING CVLSERV_ACCT_PTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.OTH_ACCT_CRTYEAR_PAY, A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, SUM( B.OTH_ACCT_CRTYEAR_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID, A.OTH_ACCT_CRTYEAR_PAYHAVING OTH_ACCT_CRTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.OTH_ACCT_LASTY_PAY, SUM( B.OTH_ACCT_LASTY_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID , A.OTH_ACCT_LASTY_PAYHAVING OTH_ACCT_LASTY_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT A.OTH_ACCT_PTYEAR_PAY, SUM( B.OTH_ACCT_PTYEAR_PAY ) SUMACCT FROM ACCT_PAY_LED_D A, ACCT_PAY_DETL_D B WHERE A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 AND B.VALI_FLAG = 1 GROUP BY A.YEAR, A.PSN_NO, A.PSN_INSU_RLTS_ID , A.OTH_ACCT_PTYEAR_PAYHAVING OTH_ACCT_PTYEAR_PAY != SUMACCT ) C
SELECT COUNT(1) FROM (SELECT PSN_NO,PSN_INSU_RLTS_ID ,YEAR,SUM(PAY_SUMAMT) AS PAY_SUMAMT FROM ACCT_PAY_DETL_D WHERE VALI_FLAG='1' GROUP BY PSN_NO,PSN_INSU_RLTS_ID ,YEAR) A,ACCT_PAY_LED_D B WHERE A.PSN_NO=B.PSN_NO AND A.YEAR = B.YEAR AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.PAY_SUMAMT <> (B.UEBMI_ACCT_CRTYEAR_PAY+B.UEBMI_ACCT_LASTY_PAY +B.UEBMI_ACCT_PTYEAR_PAY+B.CVLSERV_ACCT_CRTYEAR_PAY +B.CVLSERV_ACCT_LASTY_PAY+B.CVLSERV_ACCT_PTYEAR_PAY+B.OTH_ACCT_CRTYEAR_PAY+B.OTH_ACCT_LASTY_PAY+B.OTH_ACCT_PTYEAR_PAY+B.OTH_ACCT_PAY )
SELECT COUNT(1) FROM CLRED_SETL_D WHERE IFNULL(FUND_PAY_SUMAMT,0)<>(IFNULL(OTHFUND_PAY,0)+IFNULL(MAF_PAY,0)+IFNULL(HIFDM_PAY,0)+IFNULL(HIFOB_PAY,0)+IFNULL(HIFMI_PAY,0)+IFNULL(CVLSERV_PAY,0)+IFNULL(HIFES_PAY,0)+IFNULL(HIFP_PAY,0))
SELECT COUNT(1) FROM FEE_LIST_D A WHERE DET_ITEM_FEE_SUMAMT<>FULAMT_OWNPAY_AMT+OVERLMT_SELFPAY+PRESELFPAY_AMT+INSCP_AMT;
SELECT COUNT(1) FROM NOCLR_SETL_D WHERE ( IFNULL( OTHFUND_PAY, 0 )+ IFNULL( MAF_PAY, 0 )+ IFNULL( HIFDM_PAY, 0 )+ IFNULL( HIFOB_PAY, 0 )+ IFNULL( HIFMI_PAY, 0 )+ IFNULL( CVLSERV_PAY, 0 )+ IFNULL( HIFES_PAY, 0 )+ IFNULL( HIFP_PAY, 0 )) <>IFNULL(FUND_PAY_SUMAMT,0)
SELECT COUNT(1) FROM ( SELECT A.DFR_SUMAMT, SUM( B.FUND_PAYAMT ) FUNDSUM FROM MED_DFR_DETL_D A, MED_DFR_FUND_SBIT_D B WHERE A.DFR_ID = B.DFR_ID AND A.DFR_DETL_ID = B.DFR_DETL_ID GROUP BY A.DFR_ID, A.DFR_DETL_ID, A.DFR_SUMAMT HAVING FUNDSUM != A.DFR_SUMAMT ) C
SELECT COUNT(1) FROM SETL_D WHERE IFNULL(FUND_PAY_SUMAMT,0)<>(IFNULL(OTHFUND_PAY,0)+IFNULL(MAF_PAY,0)+IFNULL(HIFDM_PAY,0)+IFNULL(HIFOB_PAY,0)+IFNULL(HIFMI_PAY,0)+IFNULL(CVLSERV_PAY,0)+IFNULL(HIFES_PAY,0)+IFNULL(HIFP_PAY,0))
"SELECT
  COUNT( 1 )
 FROM
  SETL_D D,
  ( SELECT SETL_ID, SUM( ITEM_SUMAMT ) AS ITEM_SUMAMT FROM SETL_FEE_STT_D WHERE VALI_FLAG ='1' GROUP BY SETL_ID ) F
 WHERE
  D.SETL_ID = F.SETL_ID
  AND D.VALI_FLAG = '1'
  AND D.MEDFEE_SUMAMT != F.ITEM_SUMAMT"
SELECT COUNT(1) FROM (SELECT PSN_NO,PSN_INSU_RLTS_ID ,YEAR,SUM(PAY_SUMAMT) AS PAY_SUMAMT FROM ACCT_PAY_DETL_D WHERE VALI_FLAG='1' GROUP BY PSN_NO,PSN_INSU_RLTS_ID ,YEAR) A,ACCT_PAY_LED_D B WHERE A.PSN_NO=B.PSN_NO AND A.YEAR = B.YEAR AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.PAY_SUMAMT <> (B.UEBMI_ACCT_CRTYEAR_PAY+B.UEBMI_ACCT_LASTY_PAY +B.UEBMI_ACCT_PTYEAR_PAY+B.CVLSERV_ACCT_CRTYEAR_PAY +B.CVLSERV_ACCT_LASTY_PAY+B.CVLSERV_ACCT_PTYEAR_PAY+B.OTH_ACCT_CRTYEAR_PAY+B.OTH_ACCT_LASTY_PAY+B.OTH_ACCT_PTYEAR_PAY+B.OTH_ACCT_PAY )
SELECT COUNT(1) FROM EMP_CLCT_DETL_D A WHERE NOT EXISTS (SELECT 1 FROM INSUCENT_DB.EMP_INSU_D WHERE EMP_NO=A.EMP_NO AND INSUTYPE=A.INSUTYPE)
SELECT COUNT(1) FROM EMP_CLCT_DETL_D WHERE EMP_NO NOT IN (SELECT EMP_NO FROM CUSTCENT_DB.INSU_EMP_INFO_B)
SELECT COUNT(1) FROM RSDT_PSN_CLCT_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM RSDT_PSN_CLCT_DETL_D A WHERE NOT EXISTS (SELECT 1 FROM INSUCENT_DB.PSN_INSU_D B A.PSN_INSU_RLTS_ID=B.PSN_INSU_RLTS_ID AND A.INSUTYPE=B.INSUTYPE)
SELECT COUNT(1) FROM RSDT_PSN_CLCT_DETL_EXT_D A WHERE NOT EXISTS (SELECT 1 FROM RSDT_PSN_CLCT_DETL_D WHERE RSDT_CLCT_DETL_ID=A.RSDT_CLCT_DETL_ID AND INSUTYPE=A.INSUTYPE )
SELECT COUNT(1) FROM RSDT_PSN_CLCT_DETL_EXT_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_D WHERE EMP_NO NOT IN (SELECT EMP_NO FROM CUSTCENT_DB.INSU_EMP_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_D WHERE PSN_INSU_RLTS_ID NOT IN (SELECT PSN_INSU_RLTS_ID FROM INSUCENT_DB.PSN_INSU_HIS_D)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_EXT_D WHERE EMP_NO NOT IN (SELECT EMP_NO FROM CUSTCENT_DB.INSU_EMP_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_EXT_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_EXT_D A WHERE NOT EXISTS (SELECT 1 FROM STAF_PSN_CLCT_DETL_D WHERE PSN_CLCT_DETL_ID=A.PSN_CLCT_DETL_ID AND INSUTYPE=A.INSUTYPE )
SELECT COUNT(1) FROM ACCT_PAY_DETL_D A WHERE ACCT_INCEXPD_TYPE = '201' AND NOT EXISTS(SELECT 1 FROM SETL_D B WHERE A.PSN_NO = B.PSN_NO AND A.ACCT_INCEXPD_SOUC_ID = B.SETL_ID)
SELECT COUNT(1) FROM CLRED_SETL_D A WHERE NOT EXISTS (SELECT 1 FROM SETL_D WHERE SETL_ID = A.SETL_ID)
SELECT COUNT(1) FROM CLRED_SETL_D A WHERE A.MDTRT_ID NOT IN (SELECT MDTRT_ID FROM MDTRT_D )
SELECT COUNT(1) FROM CLRED_SETL_D A WHERE NOT EXISTS(SELECT 1 FROM MEDINS_CLR_SUM_D B WHERE A.FIXMEDINS_CODE = B.FIXMEDINS_CODE AND B.CLR_APPY_EVT_ID = B.CLR_APPY_EVT_ID )
SELECT COUNT(1) FROM FEE_LIST_D A WHERE NOT EXISTS (SELECT 1 FROM MDTRT_D WHERE MDTRT_ID = A.MDTRT_ID)
SELECT COUNT(1) FROM FEE_LIST_D A WHERE A.SETL_ID NOT IN ( SELECT SETL_ID FROM SETL_D ) AND SETL_ID <>'' AND SETL_ID IS NOT NULL
SELECT COUNT(1) FROM MED_DFR_DETL_D A WHERE A.DFR_ID NOT IN ( SELECT DFR_ID FROM MED_DFR_D)
SELECT COUNT(1) FROM NOCLR_SETL_D A WHERE NOT EXISTS (SELECT 1 FROM SETL_D WHERE SETL_ID = A.SETL_ID)
SELECT COUNT(1) FROM NOCLR_SETL_D A WHERE A.MDTRT_ID NOT IN ( SELECT MDTRT_ID FROM MDTRT_D )
SELECT COUNT(1) FROM OUTMED_SETL_D A WHERE A.INIT_SETL_ID NOT IN (SELECT MDTRT_ID FROM OUTMEN_MDTRT_D )
SELECT COUNT(1) FROM OUTMED_SETL_D A WHERE A.MDTRT_ID NOT IN (SELECT MDTRT_ID FROM OUTMED_MDTRT_D )
SELECT COUNT(1) FROM OUTMED_SETL_D A WHERE A.MDTRT_ID NOT IN (SELECT MDTRT_ID FROM OUTMED_MDTRT_D )
SELECT COUNT(1) FROM SETL_D A WHERE A.MDTRT_ID NOT IN (SELECT MDTRT_ID FROM MDTRT_D )
SELECT COUNT(1) FROM ACCT_PAY_DETL_D A WHERE NOT EXISTS (SELECT 1 FROM ACCT_PAY_LED_D B WHERE A.PSN_NO = B.PSN_NO A.YEAR=B.YEAR AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID)
SELECT COUNT(1) FROM SETL_D D WHERE D.MED_TYPE = '14' AND ( D.DISE_NO IS NULL OR D.DISE_NAME IS NULL )
SELECT COUNT(1) FROM SETL_D D WHERE D.CLR_WAY = '2' AND ( D.DISE_NO IS NULL OR D.DISE_NAME IS NULL )
SELECT COUNT(1) FROM SETL_D D WHERE D.INIT_SETL_ID NOT IN ( SELECT D2.SETL_ID FROM SETL_D D2 WHERE D2.REFD_SETL_FLAG = '1' )
SELECT COUNT(1) FROM SETL_D D WHERE D.REFD_SETL_FLAG = '1' AND D.SETL_ID NOT IN ( SELECT D2.INIT_SETL_ID FROM SETL_D D2 WHERE D2.INIT_SETL_ID IS NOT NULL )
SELECT COUNT(1) FROM SETL_D S WHERE S.PSN_INSU_RLTS_ID IS NOT NULL AND S.PSN_INSU_RLTS_ID NOT IN ( SELECT PSN_INSU_RLTS_ID FROM INSUCENT_DB.PSN_INSU_D ) AND S.PSN_INSU_RLTS_ID NOT IN ( SELECT PSN_INSU_RLTS_ID FROM INSUCENT_DB.PSN_INSU_HIS_D );
SELECT COUNT(1) FROM FEE_LIST_D WHERE (BILG_DEPT_CODG IS NULL OR BILG_DEPT_CODG = '') AND (FIXMEDINS_CODE IS NULL OR FIXMEDINS_CODE = '' OR SUBSTR(FIXMEDINS_CODE,1,1) <> 'P');
SELECT COUNT(1) FROM FEE_LIST_D WHERE (BILG_DEPT_NAME IS NULL OR BILG_DEPT_NAME = '') AND (FIXMEDINS_CODE IS NULL OR FIXMEDINS_CODE = '' OR SUBSTR(FIXMEDINS_CODE,1,1) <> 'P');
"SELECT COUNT(1) FROM SETL_D T WHERE NOT EXISTS (SELECT 1 FROM SETL_DIAG_LIST_D T1 WHERE T1.MDTRT_ID = T.MDTRT_ID AND T.PSN_NO = T1.PSN_NO AND MAINDIAG_FLAG = '1')
 AND SUBSTR(MED_TYPE,1,1) = '2' ;"


"SELECT COUNT(1)
 FROM EMP_CLCT_DETL_D WHERE AMT <> (EMP_CLCT_AMT+PSN_CLCT_AMT+OTH_CLCT_AMT)"
"SELECT COUNT(1)
 FROM EMP_CLCT_DETL_D WHERE TO_DATE(ACCRYM_END,'YYYYMM') < TO_DATE(ACCRYM_BEGN,'YYYYMM')"
"SELECT COUNT(1)
 FROM EMP_CLCT_DETL_D A WHERE NOT EXISTS (SELECT 1 FROM INSUCENT_DB.EMP_INSU_D WHERE EMP_NO=A.EMP_NO AND INSUTYPE=A.INSUTYPE )"
"SELECT COUNT(1)
 FROM EMP_CLCT_DETL_D A WHERE EMP_NO NOT IN (SELECT EMP_NO FROM CUSTCENT_DB.INSU_EMP_INFO_B )"
"SELECT COUNT(1)
 FROM EMP_CLCT_DETL_D WHERE EMP_NO IS NULL"
"SELECT COUNT(1)
 FROM PSN_INFO_B WHERE VALI_FLAG = '1'AND PSN_CERT_TYPE='01' AND LENGTH(CERTNO) <> 18 AND LENGTH(CERTNO) <> 15 "
"SELECT COUNT(1)
 FROM ACCT_INC_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B )"
"SELECT COUNT(1)
 FROM ACCT_INC_DETL_D WHERE VALI_FLAG = '1' INSU_ADMDVS NOT IN (SELECT ADMDVS FROM POLCENT_DB.ADMDVS_A )"
"SELECT COUNT(1)
 FROM ACCT_INC_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_NO FROM INSUCENT_DB.PSN_INSU_D) "
"SELECT COUNT(1)
 FROM ACCT_INC_DETL_D WHERE VALI_FLAG = '1' AND
 PSN_INSU_RLTS_ID NOT IN (SELECT PSN_INSU_RLTS_ID FROM INSUCENT_DB.PSN_INSU_D) "
"SELECT COUNT(1)
 FROM ACCT_INC_LED_D WHERE VALI_FLAG = '1' AND PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B WHERE VALI_FLAG = '1') "
"SELECT COUNT(1)
 FROM ACCT_INC_LED_D WHERE VALI_FLAG = '1' AND PSN_NO NOT IN (SELECT PSN_NO FROM INSUCENT_DB.PSN_INSU_D ) "
"SELECT COUNT(1)
 FROM ACCT_INC_LED_D WHERE VALI_FLAG = '1' AND INSU_ADMDVS NOT IN (SELECT ADMDVS FROM POLCENT_DB.ADMDVS_A ) "
"SELECT COUNT(1)
 FROM ACCT_INC_LED_D WHERE VALI_FLAG = '1' AND PSN_INSU_RLTS_ID NOT IN (SELECT PSN_INSU_RLTS_ID FROM INSUCENT_DB.PSN_INSU_D) "
SELECT COUNT(1) FROM ACCT_INC_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM ACCT_INC_LED_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM RSDT_PSN_CLCT_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM STAF_PSN_CLCT_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM CENT_MANL_REIM_REG_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM INHOSP_INFO_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM MATN_ALWN_CRTF_D A WHERE A.PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM MATN_ALWN_REG_D A WHERE A.PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM MDTRT_D A WHERE A.PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM NOCLR_FUND_SBIT_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM NOCLR_SETL_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM OPSP_REG_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM OUT_APPY_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM PSN_FIL_SUM_INFO_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM PSN_FIX_REG_DETL_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM PSN_TRT_INFO_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM PSN_TRT_SUM_INFO_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM REFL_APPY_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM SETL_DIAG_LIST_D A WHERE A.PSN_NO NOT IN ( SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM TRT_SETL_FUND_SBIT_D WHERE PSN_NO NOT IN (SELECT PSN_MGTCODE FROM BASINFOCENT_DB.PSN_INFO_B)
SELECT COUNT(1) FROM PSN_TRAT_YEAR_D A WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B B WHERE A.PSN_NO=B.PSN_MGTCODE)
SELECT COUNT(1) FROM (SELECT PSN_NO,PSN_INSU_RLTS_ID ,YEAR,SUM(PAY_SUMAMT) AS PAY_SUMAMT FROM ACCT_PAY_DETL_D WHERE VALI_FLAG='1' GROUP BY PSN_NO,PSN_INSU_RLTS_ID ,YEAR) A,ACCT_PAY_LED_D B WHERE A.PSN_NO=B.PSN_NO AND A.YEAR = B.YEAR AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.PAY_SUMAMT <> (B.UEBMI_ACCT_CRTYEAR_PAY+B.UEBMI_ACCT_LASTY_PAY +B.UEBMI_ACCT_PTYEAR_PAY+B.CVLSERV_ACCT_CRTYEAR_PAY +B.CVLSERV_ACCT_LASTY_PAY+B.CVLSERV_ACCT_PTYEAR_PAY+B.OTH_ACCT_CRTYEAR_PAY+B.OTH_ACCT_LASTY_PAY+B.OTH_ACCT_PTYEAR_PAY+B.OTH_ACCT_PAY )
SELECT COUNT(1) FROM FEE_LIST_D A WHERE DET_ITEM_FEE_SUMAMT<>FULAMT_OWNPAY_AMT+OVERLMT_SELFPAY+PRESELFPAY_AMT+INSCP_AMT;
SELECT COUNT(1) FROM NOCLR_SETL_D WHERE ( IFNULL( OTHFUND_PAY, 0 )+ IFNULL( MAF_PAY, 0 )+ IFNULL( HIFDM_PAY, 0 )+ IFNULL( HIFOB_PAY, 0 )+ IFNULL( HIFMI_PAY, 0 )+ IFNULL( CVLSERV_PAY, 0 )+ IFNULL( HIFES_PAY, 0 )+ IFNULL( HIFP_PAY, 0 )) <>IFNULL(FUND_PAY_SUMAMT,0)
SELECT COUNT(1) FROM ( SELECT A.DFR_SUMAMT, SUM( B.FUND_PAYAMT ) FUNDSUM FROM MED_DFR_DETL_D A, MED_DFR_FUND_SBIT_D B WHERE A.DFR_ID = B.DFR_ID AND A.DFR_DETL_ID = B.DFR_DETL_ID GROUP BY A.DFR_ID, A.DFR_DETL_ID, A.DFR_SUMAMT HAVING FUNDSUM != A.DFR_SUMAMT ) C
"SELECT COUNT(1) FROM SETL_D T WHERE NOT EXISTS (SELECT 1 FROM SETL_DIAG_LIST_D T1 WHERE T1.MDTRT_ID = T.MDTRT_ID AND T.PSN_NO = T1.PSN_NO AND MAINDIAG_FLAG = '1')
 AND SUBSTR(MED_TYPE,1,1) = '2' ;"
"SELECT COUNT(1)
 FROM SETL_D A
 WHERE NOT EXISTS ( SELECT 1 FROM FEE_LIST_D B
  WHERE A.SETL_ID = B.SETL_ID
  AND A.PSN_NO = B.PSN_NO
  AND B.VALI_FLAG = '1')
 AND A.VALI_FLAG = '1'
 AND A.PAY_LOC <> '4'
 AND A.REFD_SETL_FLAG ='0'"
"SELECT COUNT(1)
 FROM DOC_D D
 LEFT JOIN ADMDVS_DS A
 ON D.ADMDVS = A.ADMDVS LEFT
 JOIN FCT_AGG_IMS_PROV_EXT PRV
 ON D.PRV = PRV.PROV
 WHERE D.DWH_SOFT_DELETE_FLG != '1'
 AND D.DOC_STAS = '13'
 AND A.ADMDVS_LV = '1'"
"SELECT COUNT(1)
 FROM DOC_D D
 WHERE D.DWH_SOFT_DELETE_FLG != '1'
 AND (
 D.ADMDVS_NAME LIKE '%测试%'
 OR D.MEDINS_NAME LIKE '%测试%'
 OR D.PATN_NAME LIKE '%测试%'
 OR D.DSCG_DEPT_NAME LIKE '%测试%'
 OR D.DR_NAME LIKE '%测试%'
 ) "
"SELECT COUNT(1) FROM FEE_LIST_D T1 WHERE (NOT EXISTS ( SELECT 1 FROM SELFPREP_INFO_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG ) OR
 NOT EXISTS ( SELECT 1 FROM TCMHERB_INFO_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM WM_TCMPAT_INFO_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM MCS_ASOC_INFO_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM MCS_INFO_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM TRT_SERV_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM NEW_TRT_SERV_B WHERE MED_LIST_CODG = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM REGT_INFO_B WHERE HI_REGT_CODE = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM TCMF_PARC_PROD_D WHERE TCMF_PARC_CODE = T1.MED_LIST_CODG )
 OR NOT EXISTS ( SELECT 1 FROM IMST_DATA_TEMP_CODG_D WHERE TEMP_CODE = T1.MED_LIST_CODG )
  );"
SELECT COUNT(1) FROM ACCT_PAY_DETL_D T1 WHERE NOT EXISTS ( SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_NO = T2.PSN_NO )
SELECT COUNT(1) FROM ACCT_PAY_DETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM ACCT_PAY_DETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM ADMDVS_A T2 WHERE T1.INSU_ADMDVS = T2.ADMDVS );
SELECT COUNT(1) FROM ACCT_PAY_LED_D T1 WHERE NOT EXISTS(SELECT 1 FROM ADMDVS_A T2 WHERE T1.INSU_ADMDVS = T2.ADMDVS );
SELECT COUNT(1) FROM ACCT_PAY_LED_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM FEE_LIST_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM FEE_LIST_D T1 WHERE NOT EXISTS(SELECT 1 FROM SETL_D T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM FEE_LIST_D T1 WHERE NOT EXISTS(SELECT 1 FROM ADMDVS_A T2 WHERE T1.INSU_ADMDVS = T2.ADMDVS );
SELECT COUNT(1) FROM PSN_INSU_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM PSN_INSU_D T1 WHERE ((MAX_ACCTPRD REGEXP '[^0-9]') OR LENGTH(MAX_ACCTPRD) <> 6)
SELECT COUNT(1) FROM PSN_INSU_HIS_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM PSN_INSU_HIS_D T1 WHERE ((MAX_ACCTPRD REGEXP '[^0-9]') OR LENGTH(MAX_ACCTPRD) <> 6)
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS( SELECT 1 FROM (SELECT PSN_NO,SETL_ID FROM PSN_TRT_INFO_D) WHERE SETL_ID = T1.SETL_ID AND PSN_NO=T1.PSN_NO)
SELECT COUNT(1) FROM SETL_D T1 WHERE 0 <> (MEDFEE_SUMAMT - (HIFP_PAY + CVLSERV_PAY + HIFES_PAY + HIFMI_PAY + HIFOB_PAY + HIFDM_PAY + MAF_PAY + OTHFUND_PAY) - PSN_PAY - OWNPAY_HOSP_PART );
SELECT COUNT(1) FROM SETL_D T1 WHERE(ACCT_PAY + CASH_PAYAMT - FULAMT_OWNPAY_AMT - OWNPAY_HOSP_PART ) < 0;
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_NO = T2.PSN_NO );
"SELECT COUNT(1) FROM ACCT_PAY_DETL_D WHERE
 NVL( PAY_SUMAMT, 0 ) <> ( NVL( UEBMI_ACCT_CRTYEAR_PAY, 0 ) + NVL( UEBMI_ACCT_LASTY_PAY, 0 ) + NVL( UEBMI_ACCT_PTYEAR_PAY, 0 ) + NVL( CVLSERV_ACCT_CRTYEAR_PAY, 0 ) + NVL( CVLSERV_ACCT_LASTY_PAY, 0 ) + NVL( CVLSERV_ACCT_PTYEAR_PAY, 0 ) + NVL( OTH_ACCT_CRTYEAR_PAY, 0 ) + NVL( OTH_ACCT_LASTY_PAY, 0 ) + NVL( OTH_ACCT_PTYEAR_PAY, 0 ) + NVL( OTH_ACCT_PAY, 0 ) )"
SELECT COUNT(1) FROM ACCT_PAY_LED_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM FEE_LIST_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS( SELECT 1 FROM MDTRT_D T2 WHERE T1.MDTRT_ID = T2.MDTRT_ID AND T1.PSN_NO = T2.PSN_NO)
SELECT COUNT(1) FROM SETL_D T1 WHERE T1.BEGNDATE > T1.ENDDATE;
SELECT COUNT(1) FROM SETL_D T1 WHERE(FUND_PAY_SUMAMT - (HIFP_PAY + CVLSERV_PAY + HIFES_PAY + HIFMI_PAY + HIFOB_PAY + HIFDM_PAY + MAF_PAY + OTHFUND_PAY) ) <> 0
SELECT COUNT(1) FROM SETL_D T1 ,( SELECT SETL_ID ,SUM(ITEM_SUMAMT) AS ITEM_SUMAMT ,PSN_NO ,MDTRT_ID FROM SETL_FEE_STT_D GROUP BY SETL_ID ,PSN_NO ,MDTRT_ID ) F WHERE T1.SETL_ID = F.SETL_ID AND T1.PSN_NO = F.PSN_NO AND T1.MDTRT_ID = F.MDTRT_ID AND T1.PAY_LOC = '2' AND T1.MEDFEE_SUMAMT <> F.ITEM_SUMAMT
"SELECT COUNT(1) FROM SETL_D T1 WHERE T1.REFD_SETL_FLAG = '1'
 AND T1.SETL_ID NOT IN ( SELECT D2.INIT_SETL_ID FROM SETL_INFO D2 WHERE D2.INIT_SETL_ID IS NOT NULL )"
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM ADMDVS_A T2 WHERE T1.INSU_ADMDVS = T2.ADMDVS );
SELECT COUNT(1) FROM SETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INSU_D T2 WHERE T1.PSN_INSU_RLTS_ID = T2.PSN_INSU_RLTS_ID );
SELECT COUNT(1) FROM ACCT_PAY_DETL_D T1 WHERE NOT EXISTS(SELECT 1 FROM PSN_INFO_B T2 WHERE T1.PSN_NO = T2.PSN_NO );
SELECT COUNT(1) FROM ( SELECT T1.SETL_ID ,T1.MDTRT_ID ,MIN(T1.MEDFEE_SUMAMT) AS MEDFEE_SUMAMT ,SUM(T2.DET_ITEM_FEE_SUMAMT) AS DET_ITEM_FEE_SUMAMT FROM SETL_D T1 LEFT JOIN FEE_LIST_D T2 ON T1.SETL_ID = T2.SETL_ID AND T1.MDTRT_ID = T2.MDTRT_ID AND T1.PSN_NO = T2.PSN_NO GROUP BY T1.SETL_ID ,T1.MDTRT_ID ) TMP WHERE TMP.MEDFEE_SUMAMT <> TMP.DET_ITEM_FEE_SUMAMT
SELECT COUNT(1) FROM EMP_INSU_D T1 WHERE NOT EXISTS(SELECT 1 FROM ADMDVS_A T2 WHERE T1.INSU_ADMDVS = T2.ADMDVS );

































































