用python写一个压测脚本，对一个sql查询平台进行并发数和返回时间测试，调用请求接口，根据请求接口返回的taskCode每秒循环调用回调接口接口，直到回调接口的返回参数taskStatus为0则本次调用结束，要求脚本可以配置并发数，cookies参数统一配置，统计接口每次完整调用时长和完整调用平均时长，获取请求接口Body中raw参数中的code参数的内容为统计名称字段，要调用的接口的“请求接口Body中raw参数中的code参数”从文档D:\python\result.sql中读取，该文件中有多条sql用分号分隔的，对每条sql分别调用一次。
请求接口  POST请求 https://10.108.138.10:9437/dataDev/query/submitQuery
Body中raw参数：
{"projectId":"59","tenantId":"13","tenantName":"成都医保","projectCode":"drsjcx","projectName":"东软数据查询","projectRoleId":"235","code":"select  * from drsjcx.data_qua_detail limit 1;","setConfigStr":"set hive.tez.container.size=16384;\nset tez.task.resource.memory.mb=16384;\nset tez.am.resource.memory.mb=16384;\nset hive.groupby.skewindata=false;","dbName":"drsjcx","dbId":"22cbd0c5e0a24c25a2e18b7e89c64f27","schemaName":"","dataSourceId":"5","innerSource":false,"env":"2","tempTag":"1","engineeType":"hive","clusterId":"5","hadoopAccount":"prd_cdyb_drsjcx","queueName":"data_static","dataType":1,"geerEncrypt":false}
Headers参数：
Content-Type：application/json;charset=UTF-8
Cookies参数：
controller-platform-Token：0b319253-e01e-4736-9f72-01df38df5fd6

响应示例：{"msg":"操作成功","code":200,"data":{"taskCode":"48588b4166c9485a8a56a254f35e1775"}}

回调接口 GET请求  https://10.108.138.10:9437/dataDev/query/getQueryLog
请求参数： taskCode=48588b4166c9485a8a56a254f35e1775&engineeType=hive&currentLine=1
其中taskCode使用请求接口返回的taskCode
响应示例：{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "logs": [
            "Java HotSpot(TM) 64-Bit Server VM warning: Using the ParNew young collector with the Serial old collector is deprecated and will likely be removed in a future release",
            "which: no hbase in (/opt/jdk1.8.0/bin:/opt/jdk1.8.0/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/opt/TDP/hive/bin:/opt/TDP/hadoop/bin:/opt/TDP/hadoop/sbin:/root/bin:/opt/TDP/hive/bin:/opt/TDP/hive/bin:/opt/TDP/hadoop/bin:/opt/TDP/hadoop/sbin)",
            "Java HotSpot(TM) 64-Bit Server VM warning: Using the ParNew young collector with the Serial old collector is deprecated and will likely be removed in a future release",
            "SLF4J: Class path contains multiple SLF4J bindings.",
            "SLF4J: Found binding in [jar:file:/opt/TDP/apache-hive-3.1.2-bin/lib/log4j-slf4j-impl-2.18.0.jar!/org/slf4j/impl/StaticLoggerBinder.class]",
            "SLF4J: Found binding in [jar:file:/opt/TDP/apache-hive-3.1.2-bin/lib/slf4j-log4j12-1.7.10.jar!/org/slf4j/impl/StaticLoggerBinder.class]",
            "SLF4J: Found binding in [jar:file:/opt/TDP/hadoop-3.3.2/share/hadoop/common/lib/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]",
            "SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.",
            "SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]"
        ],
        "taskStatus": 1
    }
}

当响应中的taskStatus参数为0的时候 则本次请求结束