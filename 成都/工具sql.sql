CREATE TABLE `data_qua_detail_dic`(
  `table_name` string,
  `rule` string,
  `drty_desc` string,
  `primary_key` string
) ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe' STORED AS INPUTFORMAT 'org.apache.hadoop.mapred.TextInputFormat' OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat' LOCATION 'hdfs://ns1/domain/ns1/proj/drsjcx/data_qua_detail' TBLPROPERTIES (
  'bucketing_version' = '2',
  'transient_lastDdlTime' = '1747985809'
)

CREATE TABLE `data_qua_detail_money`(
  `table_name` string,
  `rule` string,
  `drty_desc` string,
  `primary_key` string
)

CREATE TABLE `data_qua_detail_dic`(
  `table_name` string,
  `rule` string,
  `drty_desc` string,
  `primary_key` string
)
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key

TRUNCATE TABLE drsjcx.data_qua_detail;
TRUNCATE TABLE data_qua_detail_money;


select table_name ,rule,count(1) from drsjcx.data_qua_detail group by table_name ,rule;

conda activate myenv

python -m locust -f D:\python\locustfile.py --host=https://httpbin.org


python sql_stress_test.py -c 10 -s "select  * from drsjcx.data_qua_detail limit 10;" -n "test_query"

python sql_stress_dic.py -f "D:\python\result.sql" -c 1 -o "test_results.json"
python sql_stress_money.py -f "D:\python\result_money-50.sql" -c 1 -o "test_results_money.json"


python sql_stress_dic.py -f result_money-50.sql -c 1 -l D:\python\stress_money.log

python sql_stress_dic.py -f test.sql -c 1 -l D:\python\stress_test.log
python sql_stress_dic_every.py -f test.sql -c 2 -l D:\python\stress_test.log

python sql_stress_dic.py -f result_dic_299.sql -c 1 -l D:\python\stress_dic_20250530.log -o D:\python\stress_dic_20250530.json
python sql_stress_dic.py -f result_money_50.sql -c 1 -l D:\python\stress_money_20250530.log -o D:\python\stress_money_20250530.json


select * from drsjcx.data_qua_detail limit 10;
select * from drsjcx.data_qua_detail_money limit 10;