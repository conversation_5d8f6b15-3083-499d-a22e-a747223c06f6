
=== 2025-05-29 16:04:30 - Query-1 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.33秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:转换后脚本-合并 SELECT    'acct_pay_led_d_paimon' AS table_name,   '金额校验' AS rule,   '基本医疗账户本年支出=sum（个人账户支出明细表基本医疗账户本年支出）' AS drty_desc,   CONCAT('rid=', CAST(a.rid AS STRING), 'and crte_time=', CAST(a.crte_time AS STRING)) AS primary_key FROM    scs_cd_data_hudi.acct_pay_led_d_paimon a  LEFT JOIN (   SELECT      year,      psn_no,      psn_insu_rlts_id,      SUM(uebmi_acct_crtyear_pay) AS sumacct    FROM      scs_cd_data_hudi.acct_pay_detl_d_paimon    WHERE      vali_flag = '1'   GROUP BY      year, psn_no, psn_insu_rlts_id ) b ON a.year = b.year    AND a.psn_no = b.psn_no    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id  WHERE    a.vali_flag = 1    AND a.uebmi_acct_crtyear_pay <> b.sumacct
执行中.........
转换后脚本-合并 SELECT    'acct_pay_led_d_paimon' AS table_name,   '金额校验' AS rule,   '基本医疗账户本年支出=sum（个人账户支出明细表基本医疗账户本年支出）' AS drty_desc,   CONCAT('rid=', CAST(a.rid AS STRING), 'and crte_time=', CAST(a.crte_time AS STRING)) AS primary_key FROM    scs_cd_data_hudi.acct_pay_led_d_paimon a  LEFT JOIN (   SELECT      year,      psn_no,      psn_insu_rlts_id,      SUM(uebmi_acct_crtyear_pay) AS sumacct    FROM      scs_cd_data_hudi.acct_pay_detl_d_paimon    WHERE      vali_flag = '1'   GROUP BY      year, psn_no, psn_insu_rlts_id ) b ON a.year = b.year    AND a.psn_no = b.psn_no    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id  WHERE    a.vali_flag = 1    AND a.uebmi_acct_crtyear_pay <> b.sumacct
Error while compiling statement: FAILED: ParseException line 1:5 cannot recognize input near '-' 'SELECT' ''acct_pay_led_d_paimon''
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:转换后脚本-合并 SELECT    'acct_pay_led_d_paimon' AS table_name,   '金额校验' AS rule,   '基本医疗账户本年支出=sum（个人账户支出明细表基本医疗账户本年支出）' AS drty_desc,   CONCAT('rid=', CAST(a.rid AS STRING), 'and crte_time=', CAST(a.crte_time AS STRING)) AS primary_key FROM    scs_cd_data_hudi.acct_pay_led_d_paimon a  LEFT JOIN (   SELECT      year,      psn_no,      psn_insu_rlts_id,      SUM(uebmi_acct_crtyear_pay) AS sumacct    FROM      scs_cd_data_hudi.acct_pay_detl_d_paimon    WHERE      vali_flag = '1'   GROUP BY      year, psn_no, psn_insu_rlts_id ) b ON a.year = b.year    AND a.psn_no = b.psn_no    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id  WHERE    a.vali_flag = 1    AND a.uebmi_acct_crtyear_pay <> b.sumacct
- 执行中.........
- 转换后脚本-合并 SELECT    'acct_pay_led_d_paimon' AS table_name,   '金额校验' AS rule,   '基本医疗账户本年支出=sum（个人账户支出明细表基本医疗账户本年支出）' AS drty_desc,   CONCAT('rid=', CAST(a.rid AS STRING), 'and crte_time=', CAST(a.crte_time AS STRING)) AS primary_key FROM    scs_cd_data_hudi.acct_pay_led_d_paimon a  LEFT JOIN (   SELECT      year,      psn_no,      psn_insu_rlts_id,      SUM(uebmi_acct_crtyear_pay) AS sumacct    FROM      scs_cd_data_hudi.acct_pay_detl_d_paimon    WHERE      vali_flag = '1'   GROUP BY      year, psn_no, psn_insu_rlts_id ) b ON a.year = b.year    AND a.psn_no = b.psn_no    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id  WHERE    a.vali_flag = 1    AND a.uebmi_acct_crtyear_pay <> b.sumacct
- Error while compiling statement: FAILED: ParseException line 1:5 cannot recognize input near '-' 'SELECT' ''acct_pay_led_d_paimon''
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:04:31 - Query-2 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.34秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户上年支出=SUM（个人账户支出明细表基本医疗账户上年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_LASTY_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_LASTY_PAY<>b.sumacct
执行中.........
insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户上年支出=SUM（个人账户支出明细表基本医疗账户上年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_LASTY_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_LASTY_PAY<>b.sumacct
Error while compiling statement: FAILED: ParseException line 1:483 missing EOF at 'A' near 'b'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户上年支出=SUM（个人账户支出明细表基本医疗账户上年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_LASTY_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_LASTY_PAY<>b.sumacct
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户上年支出=SUM（个人账户支出明细表基本医疗账户上年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_LASTY_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_LASTY_PAY<>b.sumacct
- Error while compiling statement: FAILED: ParseException line 1:483 missing EOF at 'A' near 'b'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:04:32 - Query-3 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.31秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户历年支出=SUM（个人账户支出明细表基本医疗账户历年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_PTYEAR_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_PTYEAR_PAY<>b.sumacct
执行中.........
insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户历年支出=SUM（个人账户支出明细表基本医疗账户历年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_PTYEAR_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_PTYEAR_PAY<>b.sumacct
Error while compiling statement: FAILED: ParseException line 1:484 missing EOF at 'A' near 'b'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户历年支出=SUM（个人账户支出明细表基本医疗账户历年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_PTYEAR_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_PTYEAR_PAY<>b.sumacct
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT 'acct_pay_led_d_paimon' as table_name,'金额校验' as rule, '基本医疗账户历年支出=SUM（个人账户支出明细表基本医疗账户历年支出）' as drty_desc, concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key FROM scs_cd_data_hudi.acct_pay_led_d_paimon a  left join (select year,psn_no,psn_insu_rlts_id,sum(UEBMI_ACCT_PTYEAR_PAY) as sumacct from scs_cd_data_hudi.acct_pay_detl_d_paimon where vali_flag='1' group by  year,psn_no,psn_insu_rlts_id) b A.YEAR = B.YEAR AND A.PSN_NO = B.PSN_NO AND A.PSN_INSU_RLTS_ID = B.PSN_INSU_RLTS_ID AND A.VALI_FLAG = 1 and a.UEBMI_ACCT_PTYEAR_PAY<>b.sumacct
- Error while compiling statement: FAILED: ParseException line 1:484 missing EOF at 'A' near 'b'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:08:01 - Query-4 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 209.08秒
平均响应时间: 209.08秒
最小响应时间: 209.08秒
最大响应时间: 209.08秒
中位数响应时间: 209.08秒
95百分位响应时间: 209.08秒

=== 2025-05-29 16:12:57 - Query-5 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 296.52秒
平均响应时间: 296.52秒
最小响应时间: 296.52秒
最大响应时间: 296.52秒
中位数响应时间: 296.52秒
95百分位响应时间: 296.52秒

=== 2025-05-29 16:16:24 - Query-6 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 206.71秒
平均响应时间: 206.71秒
最小响应时间: 206.71秒
最大响应时间: 206.71秒
中位数响应时间: 206.71秒
95百分位响应时间: 206.71秒

=== 2025-05-29 16:19:50 - Query-7 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 205.98秒
平均响应时间: 205.98秒
最小响应时间: 205.98秒
最大响应时间: 205.98秒
中位数响应时间: 205.98秒
95百分位响应时间: 205.98秒

=== 2025-05-29 16:23:14 - Query-8 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 204.34秒
平均响应时间: 204.34秒
最小响应时间: 204.34秒
最大响应时间: 204.34秒
中位数响应时间: 204.34秒
95百分位响应时间: 204.34秒

=== 2025-05-29 16:26:39 - Query-9 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 204.17秒
平均响应时间: 204.17秒
最小响应时间: 204.17秒
最大响应时间: 204.17秒
中位数响应时间: 204.17秒
95百分位响应时间: 204.17秒

=== 2025-05-29 16:30:21 - Query-10 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 222.20秒
平均响应时间: 222.20秒
最小响应时间: 222.20秒
最大响应时间: 222.20秒
中位数响应时间: 222.20秒
95百分位响应时间: 222.20秒

=== 2025-05-29 16:32:20 - Query-11 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 119.02秒
平均响应时间: 119.02秒
最小响应时间: 119.02秒
最大响应时间: 119.02秒
中位数响应时间: 119.02秒
95百分位响应时间: 119.02秒

=== 2025-05-29 16:42:52 - Query-12 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 591.14秒
平均响应时间: 591.14秒
最小响应时间: 591.14秒
最大响应时间: 591.14秒
中位数响应时间: 591.14秒
95百分位响应时间: 591.14秒

=== 2025-05-29 16:44:50 - Query-13 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 117.98秒
平均响应时间: 117.98秒
最小响应时间: 117.98秒
最大响应时间: 117.98秒
中位数响应时间: 117.98秒
95百分位响应时间: 117.98秒

=== 2025-05-29 16:44:51 - Query-14 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.34秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'MED_DFR_DETL_D_paimon' AS table_name,     '金额校验' AS rule,     '拨付总额=相同拨付明细ID、拨付ID的基金支付金额之和' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a WHERE EXISTS (     SELECT 1     FROM (         SELECT              dfr_id,              dfr_detl_id,             SUM(fund_payamt) AS fund_sum         FROM scs_cd_data_hudi.med_dfr_fund_sbit_d         GROUP BY dfr_id, dfr_detl_id     ) b     WHERE a.dfr_id = b.dfr_id       AND a.dfr_detl_id = b.dfr_detl_id       AND a.dfr_sumamt <> b.fund_sum ) AND a.dfr_sumamt IS NOT NULL
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'MED_DFR_DETL_D_paimon' AS table_name,     '金额校验' AS rule,     '拨付总额=相同拨付明细ID、拨付ID的基金支付金额之和' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a WHERE EXISTS (     SELECT 1     FROM (         SELECT              dfr_id,              dfr_detl_id,             SUM(fund_payamt) AS fund_sum         FROM scs_cd_data_hudi.med_dfr_fund_sbit_d         GROUP BY dfr_id, dfr_detl_id     ) b     WHERE a.dfr_id = b.dfr_id       AND a.dfr_detl_id = b.dfr_detl_id       AND a.dfr_sumamt <> b.fund_sum ) AND a.dfr_sumamt IS NOT NULL
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:439 Table not found 'med_dfr_fund_sbit_d'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'MED_DFR_DETL_D_paimon' AS table_name,     '金额校验' AS rule,     '拨付总额=相同拨付明细ID、拨付ID的基金支付金额之和' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a WHERE EXISTS (     SELECT 1     FROM (         SELECT              dfr_id,              dfr_detl_id,             SUM(fund_payamt) AS fund_sum         FROM scs_cd_data_hudi.med_dfr_fund_sbit_d         GROUP BY dfr_id, dfr_detl_id     ) b     WHERE a.dfr_id = b.dfr_id       AND a.dfr_detl_id = b.dfr_detl_id       AND a.dfr_sumamt <> b.fund_sum ) AND a.dfr_sumamt IS NOT NULL
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'MED_DFR_DETL_D_paimon' AS table_name,     '金额校验' AS rule,     '拨付总额=相同拨付明细ID、拨付ID的基金支付金额之和' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a WHERE EXISTS (     SELECT 1     FROM (         SELECT              dfr_id,              dfr_detl_id,             SUM(fund_payamt) AS fund_sum         FROM scs_cd_data_hudi.med_dfr_fund_sbit_d         GROUP BY dfr_id, dfr_detl_id     ) b     WHERE a.dfr_id = b.dfr_id       AND a.dfr_detl_id = b.dfr_detl_id       AND a.dfr_sumamt <> b.fund_sum ) AND a.dfr_sumamt IS NOT NULL
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:439 Table not found 'med_dfr_fund_sbit_d'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:47:10 - Query-15 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 138.55秒
平均响应时间: 138.55秒
最小响应时间: 138.55秒
最大响应时间: 138.55秒
中位数响应时间: 138.55秒
95百分位响应时间: 138.55秒

=== 2025-05-29 16:53:23 - Query-16 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 372.95秒
平均响应时间: 372.95秒
最小响应时间: 372.95秒
最大响应时间: 372.95秒
中位数响应时间: 372.95秒
95百分位响应时间: 372.95秒

=== 2025-05-29 16:53:23 - Query-17 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.32秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号和险种必须在单位参保信息表中能够获取' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.emp_insu_d b     WHERE b.emp_no = a.emp_no        AND b.insutype = a.insutype ) AND a.emp_no IS NOT NULL AND a.emp_no <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号和险种必须在单位参保信息表中能够获取' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.emp_insu_d b     WHERE b.emp_no = a.emp_no        AND b.insutype = a.insutype ) AND a.emp_no IS NOT NULL AND a.emp_no <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
Error while compiling statement: FAILED: ParseException line 1:366 cannot recognize input near '.' 'emp_insu_d' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号和险种必须在单位参保信息表中能够获取' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.emp_insu_d b     WHERE b.emp_no = a.emp_no        AND b.insutype = a.insutype ) AND a.emp_no IS NOT NULL AND a.emp_no <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号和险种必须在单位参保信息表中能够获取' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.emp_insu_d b     WHERE b.emp_no = a.emp_no        AND b.insutype = a.insutype ) AND a.emp_no IS NOT NULL AND a.emp_no <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
- Error while compiling statement: FAILED: ParseException line 1:366 cannot recognize input near '.' 'emp_insu_d' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:53:23 - Query-18 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.34秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
Error while compiling statement: FAILED: ParseException line 1:381 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'EMP_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '单位缴费明细表中的单位编号需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:381 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:53:24 - Query-19 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.34秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号（PSN_NO）需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号（PSN_NO）需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
Error while compiling statement: FAILED: ParseException line 1:387 cannot recognize input near '.' 'psn_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号（PSN_NO）需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号（PSN_NO）需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:387 cannot recognize input near '.' 'psn_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:53:24 - Query-20 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.25秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员参保关系id(PSN_INSU_RLTS_ID)需存在于参保中心人员参保信息中（PSN_INSU_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id       AND b.insutype = a.insutype ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员参保关系id(PSN_INSU_RLTS_ID)需存在于参保中心人员参保信息中（PSN_INSU_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id       AND b.insutype = a.insutype ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
Error while compiling statement: FAILED: ParseException line 1:398 cannot recognize input near '.' 'psn_insu_d' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员参保关系id(PSN_INSU_RLTS_ID)需存在于参保中心人员参保信息中（PSN_INSU_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id       AND b.insutype = a.insutype ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '人员参保关系id(PSN_INSU_RLTS_ID)需存在于参保中心人员参保信息中（PSN_INSU_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id       AND b.insutype = a.insutype ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> '' AND a.insutype IS NOT NULL AND a.insutype <> ''
- Error while compiling statement: FAILED: ParseException line 1:398 cannot recognize input near '.' 'psn_insu_d' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:31 - Query-21 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 127.29秒
平均响应时间: 127.29秒
最小响应时间: 127.29秒
最大响应时间: 127.29秒
中位数响应时间: 127.29秒
95百分位响应时间: 127.29秒

=== 2025-05-29 16:55:32 - Query-22 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.35秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号需存在于基本信息中心PSN_INFO_B（人员基本信息表）中' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号需存在于基本信息中心PSN_INFO_B（人员基本信息表）中' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
Error while compiling statement: FAILED: ParseException line 1:390 cannot recognize input near '.' 'psn_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号需存在于基本信息中心PSN_INFO_B（人员基本信息表）中' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '人员编号需存在于基本信息中心PSN_INFO_B（人员基本信息表）中' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:390 cannot recognize input near '.' 'psn_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:34 - Query-23 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.38秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
Error while compiling statement: FAILED: ParseException line 1:378 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:378 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:35 - Query-24 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.38秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
Error while compiling statement: FAILED: ParseException line 1:375 cannot recognize input near '.' 'psn_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:375 cannot recognize input near '.' 'psn_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:37 - Query-25 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.33秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '参保关系id需存在于参保中心人员参保信息历史表中（PSN_INSU_HIS_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_his_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '参保关系id需存在于参保中心人员参保信息历史表中（PSN_INSU_HIS_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_his_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> ''
Error while compiling statement: FAILED: ParseException line 1:385 cannot recognize input near '.' 'psn_insu_his_d' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '参保关系id需存在于参保中心人员参保信息历史表中（PSN_INSU_HIS_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_his_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,     '关联性' AS rule,     '参保关系id需存在于参保中心人员参保信息历史表中（PSN_INSU_HIS_D）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.insucent_db_paimon.psn_insu_his_d b     WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id ) AND a.psn_insu_rlts_id IS NOT NULL AND a.psn_insu_rlts_id <> ''
- Error while compiling statement: FAILED: ParseException line 1:385 cannot recognize input near '.' 'psn_insu_his_d' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:38 - Query-26 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.33秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
Error while compiling statement: FAILED: ParseException line 1:386 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.custcent_db_paimon.insu_emp_info_b b     WHERE b.emp_no = a.emp_no ) AND a.emp_no IS NOT NULL AND a.emp_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:386 cannot recognize input near '.' 'insu_emp_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:55:39 - Query-27 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.31秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
执行中.........
INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
Error while compiling statement: FAILED: ParseException line 1:383 cannot recognize input near '.' 'psn_info_b' 'b' in table source
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- 执行中.........
- INSERT INTO drsjcx.data_qua_detail  SELECT      'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,     '关联性' AS rule,     '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,     CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a WHERE NOT EXISTS (     SELECT 1      FROM scs_cd_data_hudi.basinfocent_db_paimon.psn_info_b b     WHERE b.psn_mgtcode = a.psn_no ) AND a.psn_no IS NOT NULL AND a.psn_no <> ''
- Error while compiling statement: FAILED: ParseException line 1:383 cannot recognize input near '.' 'psn_info_b' 'b' in table source
- getQueryLog end error!
- ------------------------------------------------------------------
