import requests
import time
import threading
import json
from concurrent.futures import ThreadPoolExecutor
import statistics
import argparse

# 配置参数
REQUEST_URL = "https://10.108.138.10:9437/dataDev/query/submitQuery"
CALLBACK_URL = "https://10.108.138.10:9437/dataDev/query/getQueryLog"
COOKIES = {"controller-platform-Token": "0b319253-e01e-4736-9f72-01df38df5fd6"}
HEADERS = {"Content-Type": "application/json;charset=UTF-8"}

# 请求体模板
REQUEST_BODY = {
    "projectId": "59",
    "tenantId": "13",
    "tenantName": "成都医保",
    "projectCode": "drsjcx",
    "projectName": "东软数据查询",
    "projectRoleId": "235",
    "code": "select  * from drsjcx.data_qua_detail limit 1;",
    "setConfigStr": "set hive.tez.container.size=16384;\nset tez.task.resource.memory.mb=16384;\nset tez.am.resource.memory.mb=16384;\nset hive.groupby.skewindata=false;",
    "dbName": "drsjcx",
    "dbId": "22cbd0c5e0a24c25a2e18b7e89c64f27",
    "schemaName": "",
    "dataSourceId": "5",
    "innerSource": False,
    "env": "2",
    "tempTag": "1",
    "engineeType": "hive",
    "clusterId": "5",
    "hadoopAccount": "prd_cdyb_drsjcx",
    "queueName": "data_static",
    "dataType": 1,
    "geerEncrypt": False
}

# 全局统计变量
total_requests = 0
success_requests = 0
failed_requests = 0
response_times = []
lock = threading.Lock()

def make_request(sql_query, request_name):
    global total_requests, success_requests, failed_requests
    
    start_time = time.time()
    request_body = REQUEST_BODY.copy()
    request_body["code"] = sql_query
    
    try:
        # 发送初始请求
        response = requests.post(
            REQUEST_URL,
            headers=HEADERS,
            cookies=COOKIES,
            json=request_body,
            verify=False  # 忽略SSL证书验证
        )
        
        if response.status_code != 200:
            with lock:
                failed_requests += 1
                print(f"请求失败: {response.status_code} - {response.text}")
            return None
        
        task_code = response.json()["data"]["taskCode"]
        
        # 轮询回调接口
        while True:
            callback_params = {
                "taskCode": task_code,
                "engineeType": "hive",
                "currentLine": 1
            }
            
            callback_response = requests.get(
                CALLBACK_URL,
                params=callback_params,
                cookies=COOKIES,
                verify=False
            )
            
            if callback_response.status_code != 200:
                with lock:
                    failed_requests += 1
                    print(f"回调请求失败: {callback_response.status_code} - {callback_response.text}")
                return None
            
            data = callback_response.json()["data"]
            if data["taskStatus"] == 0:
                break
                
            time.sleep(1)  # 每秒检查一次
        
        end_time = time.time()
        duration = end_time - start_time
        
        with lock:
            success_requests += 1
            response_times.append(duration)
            print(f"请求完成: {request_name} | 耗时: {duration:.2f}秒")
            
        return duration
        
    except Exception as e:
        with lock:
            failed_requests += 1
            print(f"请求异常: {str(e)}")
        return None

def run_test(concurrency, sql_query, request_name):
    global total_requests
    
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = []
        for i in range(concurrency):
            total_requests += 1
            future = executor.submit(make_request, sql_query, f"{request_name}_{i+1}")
            futures.append(future)
        
        # 等待所有请求完成
        for future in futures:
            future.result()

def main():
    parser = argparse.ArgumentParser(description="SQL查询平台压测工具")
    parser.add_argument("-c", "--concurrency", type=int, required=True, help="并发数")
    parser.add_argument("-s", "--sql", type=str, required=True, help="要测试的SQL语句")
    parser.add_argument("-n", "--name", type=str, required=True, help="测试名称")
    
    args = parser.parse_args()
    
    print(f"开始压测: 并发数={args.concurrency}, SQL={args.sql}, 名称={args.name}")
    start_time = time.time()
    
    run_test(args.concurrency, args.sql, args.name)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    print("\n压测结果:")
    print(f"总请求数: {total_requests}")
    print(f"成功请求数: {success_requests}")
    print(f"失败请求数: {failed_requests}")
    print(f"总耗时: {total_duration:.2f}秒")
    
    if success_requests > 0:
        print(f"平均响应时间: {statistics.mean(response_times):.2f}秒")
        print(f"最小响应时间: {min(response_times):.2f}秒")
        print(f"最大响应时间: {max(response_times):.2f}秒")
        print(f"中位数响应时间: {statistics.median(response_times):.2f}秒")
        print(f"95百分位响应时间: {statistics.quantiles(response_times, n=20)[18]:.2f}秒")

if __name__ == "__main__":
    # 禁用SSL警告
    requests.packages.urllib3.disable_warnings()
    main()