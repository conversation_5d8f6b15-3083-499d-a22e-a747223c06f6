数据字典校验
多少规则 数据量 脏数据分布   最大  占比
具体哪些脏数据 哪些字典导致


金额校验

写一个抽取word内容到excel的脚本，
word路径D:\work\demo\福建\福建省三医联动平台_数据采集标准规范-值域代码-征求意见稿-0902-岳群.docx
excel路径D:\work\demo\福建\值域代码-岳群.xlsx
请参考word内容，抽取数据到excel中，word的表头不固定，请分析多种情况，确保excel内容能够正确填充。





先表 再字段 
单表数据量上亿的都有哪些表
脏数据占比高
top5

表层面 字段层面 表+字段层面


表  表+字段 纯字段 


我有一个数据统计表，表头如下，其中一个表中可能会有多条数据，涉及多个字段：
表名	脏数据字段	脏数据数据量	表总数据量
我需要对这些数据进行多维度统计分析，包括但不限于：脏数据数量top5的表；脏数据占比top5的表；同一个表中脏数据字段占该表脏数据所有字段总量的百分比的top5脏数据字段；脏数据字段总数量top5字段；
数量如果大于1000都要用万来表示，百分比均保留两位小数。
数据基础要完全依赖于上传的文件，请生成分析报告。


请分析我提供的关于数据库多表字典字段脏数据统计的Excel内容。该Excel包含多个sheet页，每个sheet页对应不同的统计维度。

逐一分析每个sheet页：详细解读每个sheet页的数据，识别关键的脏数据统计项，报告中要对每个sheet页逐一分析，每个sheet页一个大标题，分析内容要体现数据。
进行多角度分析：针对每个字典字段的脏数据，从不同层面（如：脏数据类型、影响范围、趋势等）进行深入剖析。
生成一份详尽的报告：
报告中每个分析部分都需有清晰的标题，明确指出该部分的分析角度。
所有数量单位请统一换算为**“万”**。
所有百分比请保留两位小数。

[>99999999]0!.00,,"亿";0.00,"万"

=IF(C2>=100000000, TEXT(C2/100000000, "0.00") & "亿", TEXT(C2/10000, "0.00") & "万")


数据量大  占比50以上 
改成top20


放第三章的头
对于量大的表 进行字段统计   表   数据  多字段  字段占比 


错误码值 举例 

结论 
1 表说明
2 表咋用
3 重点说明 举例 


=TEXTJOIN("，", TRUE, FILTER('分项汇总'!A:A, '分项汇总'!B:B=A2))
=TEXTJOIN(",",TRUE,FILTER('分项汇总'!A:A,'分项汇总'!B:B=A2,""))