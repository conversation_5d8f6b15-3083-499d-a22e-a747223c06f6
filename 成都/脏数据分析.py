import pandas as pd
import matplotlib.pyplot as plt

# 读取 Excel 文件
file_path = 'D:\work\demo\成都\字典校验整理精简版.xlsx'  # 修改为你的路径
df = pd.read_excel(file_path)

# 增加脏数据率列
df['脏数据率'] = df['脏数据数据量'] / df['表总数据量']

# 按表聚合分析
table_summary = df.groupby('表名').agg({
    '脏数据数据量': 'sum',
    '表总数据量': 'first',  # 假设每个表的总量一致
})
table_summary['脏数据率'] = table_summary['脏数据数据量'] / table_summary['表总数据量']
table_summary = table_summary.sort_values(by='脏数据率', ascending=False)

# 按字段聚合分析
field_summary = df.groupby('脏数据字段')['脏数据数据量'].sum().sort_values(ascending=False)

# 输出分析内容
print("🔍 按表分析结果（前10）:")
print(table_summary.head(10))

print("\n🔍 脏数据最多的字段（前10）:")
print(field_summary.head(10))

# 可视化：脏数据率Top10表
plt.figure(figsize=(10,6))
table_summary['脏数据率'].head(10).plot(kind='barh')
plt.title('脏数据率 Top 10 表')
plt.xlabel('脏数据率')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

# 可视化：字段脏数据量Top10
plt.figure(figsize=(10,6))
field_summary.head(10).plot(kind='barh', color='orange')
plt.title('脏数据最多的字段 Top 10')
plt.xlabel('脏数据数据量')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()
