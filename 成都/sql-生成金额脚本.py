import pandas as pd
import re


def convert_to_hive_sql(sql):
    # 转换IFNULL为COALESCE
    hive_sql = sql.replace('IFNULL', 'coalesce')
    
    # 替换表名为scs_cd_data_hudi.前缀和_paimon后缀
    hive_sql = re.sub(r'\b(from|join)\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                      lambda m: f"{m.group(1)} scs_cd_data_hudi.{m.group(2)}_paimon",
                      hive_sql, flags=re.IGNORECASE)

    # 检查是否有子查询，如果没有则添加rid和crte_time
    if "select" in hive_sql.lower() and "from" in hive_sql.lower():
        if "rid," not in hive_sql.lower() and "crte_time," not in hive_sql.lower():
            # 在第一个SELECT后添加字段
            hive_sql = re.sub(r'(select\s+)',
                              r'\1rid, crte_time, ',
                              hive_sql, flags=re.IGNORECASE, count=1)
    
    # 将SQL转换为小写
    hive_sql = hive_sql.lower()
    
    return hive_sql


def generate_sql_template(row):
    # 获取各变量
    rule_category = row['规则类别']
    rule_description = row['规则描述']
    table_name = row['表英文名']
    original_sql = row['校验脚本辅助列']

    # 转换为Hive SQL
    hive_sql = convert_to_hive_sql(original_sql)

    # 生成最终SQL（模板部分保持原样，只有子查询部分是小写）
    template = f"""insert into drsjcx.data_qua_detail 
 SELECT '{table_name}_paimon' as table_name,
 '{rule_category}' as rule,
 '{rule_description}' as drty_desc,
 concat(cast(rid as string),'-',cast(crte_time as string)) as primary_key
 FROM ({hive_sql}) t;"""
    return template.strip()


def main():
    # 读取原始Excel文件
    input_file = r'D:\work\demo\成都\金额质检.xlsx'
    output_file = r'D:\work\demo\成都\金额质检结果.xlsx'

    # 读取第一个sheet页
    df = pd.read_excel(input_file, sheet_name=0)
    
    # 保存原始列顺序
    original_columns = df.columns.tolist()

    # 生成转换后的SQL脚本
    df['转换后脚本-脚本-陈路'] = df.apply(generate_sql_template, axis=1)

    # 将新列添加到原始列顺序中
    final_columns = original_columns + ['转换后脚本-脚本-陈路']
    
    # 重新排列列顺序
    df = df[final_columns]

    # 保存结果到新文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

    print(f"处理完成，结果已保存到: {output_file}")


if __name__ == "__main__":
    main()