insert into drsjcx.data_qua_detail_money
SELECT 'clred_setl_d_paimon' as table_name,'金额校验' as rule,
'医疗总费用 <> 全自费+先行自付+超限价自费+政策范围内费用' as drty_desc,
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.CLRED_SETL_D_paimon
WHERE COALESCE(MEDFEE_SUMAMT,0)!=(COALESCE(FULAMT_OWNPAY_AMT,0)+COALESCE(PRESELFPAY_AMT,0)+COALESCE(OVERLMT_SELFPAY,0)+COALESCE(INSCP_AMT,0)) ;
insert into drsjcx.data_qua_detail_money
SELECT 'setl_d_paimon' as table_name,'金额校验' as rule,
'结算表费用总额与费用明细表费用总额是否匹配' as drty_desc,
concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.setl_d_paimon a 
left join 
(SELECT PSN_NO,SETL_ID,SUM(DET_ITEM_FEE_SUMAMT) AS DET_ITEM_FEE_SUMAMT FROM scs_cd_data_hudi.fee_list_d_paimon GROUP BY PSN_NO,SETL_ID ) b
WHERE A.SETL_ID = B.SETL_ID AND A.PSN_NO=B.PSN_NO AND a.MEDFEE_SUMAMT<>b.DET_ITEM_FEE_SUMAMT ;
insert into drsjcx.data_qua_detail_money
SELECT 'acct_pay_detl_d_paimon' as table_name,'金额校验' as rule,
'支出总金额=基本医疗账户本年支出+基本医疗账户上年支出+基本医疗账户历年支出+公务员账户本年支出+公务员账户上年支出+公务员账户历年支出+其他账户本年支出+其他账户上年支出+其他账户历年支出+其他账户支出' as drty_desc,
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.acct_pay_detl_d_paimon A 
WHERE COALESCE( PAY_SUMAMT, 0 ) <> ( COALESCE( UEBMI_ACCT_CRTYEAR_PAY, 0 ) + COALESCE( UEBMI_ACCT_LASTY_PAY, 0 ) + COALESCE( UEBMI_ACCT_PTYEAR_PAY, 0 )
 + COALESCE( CVLSERV_ACCT_CRTYEAR_PAY, 0 ) + COALESCE( CVLSERV_ACCT_LASTY_PAY, 0 ) + COALESCE( CVLSERV_ACCT_PTYEAR_PAY, 0 ) + 
 COALESCE( OTH_ACCT_CRTYEAR_PAY, 0 ) + COALESCE( OTH_ACCT_LASTY_PAY, 0 ) + COALESCE( OTH_ACCT_PTYEAR_PAY, 0 ) + COALESCE( OTH_ACCT_PAY, 0 ) );
insert into drsjcx.data_qua_detail_money
SELECT 
  'acct_pay_led_d_paimon' AS table_name,
  '金额校验' AS rule,
  '基本医疗账户本年支出=sum（个人账户支出明细表基本医疗账户本年支出）' AS drty_desc,
  CONCAT('rid=', CAST(a.rid AS STRING), 'and crte_time=', CAST(a.crte_time AS STRING)) AS primary_key
FROM 
  scs_cd_data_hudi.acct_pay_led_d_paimon a 
LEFT JOIN (
  SELECT 
    year, 
    psn_no, 
    psn_insu_rlts_id, 
    SUM(uebmi_acct_crtyear_pay) AS sumacct 
  FROM 
    scs_cd_data_hudi.acct_pay_detl_d_paimon 
  WHERE 
    vali_flag = '1'
  GROUP BY 
    year, psn_no, psn_insu_rlts_id
) b
ON a.year = b.year 
  AND a.psn_no = b.psn_no 
  AND a.psn_insu_rlts_id = b.psn_insu_rlts_id 
WHERE 
  a.vali_flag = 1 
  AND a.uebmi_acct_crtyear_pay <> b.sumacct;
INSERT INTO drsjcx.data_qua_detail_money
SELECT 
    'acct_pay_led_d_paimon' AS table_name,
    '金额校验' AS rule,
    '基本医疗账户上年支出=SUM（个人账户支出明细表基本医疗账户上年支出）' AS drty_desc,
    CONCAT('rid=', CAST(a.rid AS string), ' and crte_time=', CAST(a.crte_time AS string)) AS Primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a 
LEFT JOIN (
    SELECT 
        year,
        psn_no,
        psn_insu_rlts_id,
        SUM(UEBMI_ACCT_LASTY_PAY) AS sumacct 
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon 
    WHERE vali_flag = '1'
    GROUP BY year, psn_no, psn_insu_rlts_id
) b ON a.YEAR = b.YEAR 
    AND a.PSN_NO = b.PSN_NO 
    AND a.PSN_INSU_RLTS_ID = b.PSN_INSU_RLTS_ID 
WHERE a.VALI_FLAG = '1' 
    AND a.UEBMI_ACCT_LASTY_PAY <> b.sumacct;
INSERT INTO drsjcx.data_qua_detail_money
SELECT 
    'acct_pay_led_d_paimon' AS table_name,
    '金额校验' AS rule,
    '基本医疗账户历年支出=SUM（个人账户支出明细表基本医疗账户历年支出）' AS drty_desc,
    CONCAT('rid=', CAST(a.rid AS string), ' and crte_time=', CAST(a.crte_time AS string)) AS Primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a 
LEFT JOIN (
    SELECT 
        year,
        psn_no,
        psn_insu_rlts_id,
        SUM(UEBMI_ACCT_PTYEAR_PAY) AS sumacct 
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon 
    WHERE vali_flag = '1'
    GROUP BY year, psn_no, psn_insu_rlts_id
) b ON a.YEAR = b.YEAR 
    AND a.PSN_NO = b.PSN_NO 
    AND a.PSN_INSU_RLTS_ID = b.PSN_INSU_RLTS_ID 
WHERE a.VALI_FLAG = '1' 
    AND a.UEBMI_ACCT_PTYEAR_PAY <> b.sumacct;
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '公务员账户本年支出=SUM（个人账户支出明细表公务员账户本年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(cvlserv_acct_crtyear_pay AS DECIMAL(18,2))) AS sum_cvlserv_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.cvlserv_acct_crtyear_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_cvlserv_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '公务员账户上年支出=SUM（个人账户支出明细表公务员账户上年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(cvlserv_acct_lasty_pay AS DECIMAL(18,2))) AS sum_cvlserv_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.cvlserv_acct_lasty_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_cvlserv_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '公务员账户历年支出=SUM（个人账户支出明细表公务员账户历年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(cvlserv_acct_ptyear_pay AS DECIMAL(18,2))) AS sum_cvlserv_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.cvlserv_acct_ptyear_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_cvlserv_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '其他账户本年支出=SUM（个人账户支出明细表其他账户本年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(oth_acct_crtyear_pay AS DECIMAL(18,2))) AS sum_oth_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.oth_acct_crtyear_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_oth_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '其他账户上年支出=SUM（个人账户支出明细表其他账户上年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(oth_acct_lasty_pay AS DECIMAL(18,2))) AS sum_oth_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.oth_acct_lasty_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_oth_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '其他账户历年支出=SUM（个人账户支出明细表其他账户历年支出）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_led_d_paimon a
JOIN (
    SELECT 
        `year`,
        psn_no,
        psn_insu_rlts_id,
        SUM(CAST(oth_acct_ptyear_pay AS DECIMAL(18,2))) AS sum_oth_acct
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon
    WHERE vali_flag = 1
    GROUP BY `year`, psn_no, psn_insu_rlts_id
) b ON a.`year` = b.`year` 
    AND a.psn_no = b.psn_no 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE a.vali_flag = 1
    AND CAST(COALESCE(a.oth_acct_ptyear_pay, 0) AS DECIMAL(18,2)) <> CAST(COALESCE(b.sum_oth_acct, 0) AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_LED_D_paimon' AS table_name,
    '金额校验' AS rule,
    '个人账户支出台账和支出明细表支出总额是否匹配' AS drty_desc,
    CONCAT(CAST(b.rid AS STRING), '-', CAST(b.crte_time AS STRING)) AS primary_key
FROM (
    SELECT 
        psn_no,
        psn_insu_rlts_id,
        `year`,
        SUM(CAST(pay_sumamt AS DECIMAL(18,2))) AS pay_sumamt
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon 
    WHERE vali_flag = '1'
    GROUP BY psn_no, psn_insu_rlts_id, `year`
) a
JOIN scs_cd_data_hudi.acct_pay_led_d_paimon b ON 
    a.psn_no = b.psn_no 
    AND a.`year` = b.`year` 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE CAST(a.pay_sumamt AS DECIMAL(18,2)) <> CAST(
    COALESCE(b.uebmi_acct_crtyear_pay, 0) + 
    COALESCE(b.uebmi_acct_lasty_pay, 0) + 
    COALESCE(b.uebmi_acct_ptyear_pay, 0) +
    COALESCE(b.cvlserv_acct_crtyear_pay, 0) + 
    COALESCE(b.cvlserv_acct_lasty_pay, 0) + 
    COALESCE(b.cvlserv_acct_ptyear_pay, 0) +
    COALESCE(b.oth_acct_crtyear_pay, 0) + 
    COALESCE(b.oth_acct_lasty_pay, 0) + 
    COALESCE(b.oth_acct_ptyear_pay, 0) + 
    COALESCE(b.oth_acct_pay, 0) 
    AS DECIMAL(18,2));
insert into drsjcx.data_qua_detail_money
SELECT 'clred_setl_d_paimon' as table_name,'金额校验' as rule,
'统筹基金支出+补充医疗保险基金支出+公务员医疗补助基金支出+大额医疗补助基金支出+大病补充医疗保险基金支出+伤残人员医疗保障基金支出+医疗救助基金支出+其它基金支付 应等于基金支付总额' as drty_desc,
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.clred_setl_d_paimon 
WHERE COALESCE(FUND_PAY_SUMAMT,0)<>(COALESCE(OTHFUND_PAY,0)+COALESCE(MAF_PAY,0)+COALESCE(HIFDM_PAY,0)+COALESCE(HIFOB_PAY,0)+
COALESCE(HIFMI_PAY,0)+COALESCE(CVLSERV_PAY,0)+COALESCE(HIFES_PAY,0)+COALESCE(HIFP_PAY,0)) ;
insert into drsjcx.data_qua_detail_money
SELECT 'fee_list_d_paimon' as table_name,'金额校验' as rule,
    '单条费用明细的总额与全自费金额、先行自付金额、超限价金额、符合范围金额是否平衡' as drty_desc,
    concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.fee_list_d_paimon
WHERE DET_ITEM_FEE_SUMAMT<>FULAMT_OWNPAY_AMT+OVERLMT_SELFPAY+PRESELFPAY_AMT+INSCP_AMT;
insert into drsjcx.data_qua_detail_money
SELECT 'noclr_setl_d_paimon' as table_name,'金额校验' as rule,
    '统筹基金支出+补充医疗保险基金支出+公务员医疗补助基金支出+大额医疗补助基金支出+大病补充医疗保险基金支出+伤残人员医疗保障基金支出+医疗救助基金支出+其它基金支付<>基金支付总额' as drty_desc,
    concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.NOCLR_SETL_D_paimon
WHERE ( COALESCE( OTHFUND_PAY, 0 )+ COALESCE( MAF_PAY, 0 )+ COALESCE( HIFDM_PAY, 0 )+ COALESCE( HIFOB_PAY, 0 )+ COALESCE( HIFMI_PAY, 0 )+ 
COALESCE( CVLSERV_PAY, 0 )+ COALESCE( HIFES_PAY, 0 )+ COALESCE( HIFP_PAY, 0 )) <>COALESCE(FUND_PAY_SUMAMT,0);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'MED_DFR_DETL_D_paimon' AS table_name,
    '金额校验' AS rule,
    '拨付总额=相同拨付明细ID、拨付ID的基金支付金额之和' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a
WHERE EXISTS (
    SELECT 1
    FROM (
        SELECT 
            dfr_id, 
            dfr_detl_id,
            SUM(fund_payamt) AS fund_sum
        FROM scs_cd_data_hudi.med_dfr_fund_sbit_d_paimon
        GROUP BY dfr_id, dfr_detl_id
    ) b
    WHERE a.dfr_id = b.dfr_id
      AND a.dfr_detl_id = b.dfr_detl_id
      AND a.dfr_sumamt <> b.fund_sum
)
AND a.dfr_sumamt IS NOT NULL;
insert into drsjcx.data_qua_detail_money
SELECT 'setl_d_paimon' as table_name,'金额校验' as rule,
'统筹基金支出+补充医疗保险基金支出+公务员医疗补助基金支出+大额医疗补助基金支出+大病补充医疗保险基金支出+伤残人员医疗保障基金支出+医疗救助基金支出+其它基金支付<>基金支付总额' as drty_desc,
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.SETL_D_paimon
WHERE COALESCE(FUND_PAY_SUMAMT,0)<>(COALESCE(OTHFUND_PAY,0)+COALESCE(MAF_PAY,0)+COALESCE(HIFDM_PAY,0)+COALESCE(HIFOB_PAY,0)+
COALESCE(HIFMI_PAY,0)+COALESCE(CVLSERV_PAY,0)+COALESCE(HIFES_PAY,0)+COALESCE(HIFP_PAY,0)) ;
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_DETL_D_paimon' AS table_name,
    '金额校验' AS rule,
    '个人账户支出台账和支出明细表支出总额是否匹配' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM (
    SELECT 
        rid,
        crte_time,
        psn_no,
        psn_insu_rlts_id,
        `year`,
        SUM(CAST(pay_sumamt AS DECIMAL(18,2))) AS pay_sumamt
    FROM scs_cd_data_hudi.acct_pay_detl_d_paimon 
    WHERE vali_flag = '1'
    GROUP BY rid, crte_time, psn_no, psn_insu_rlts_id, `year`
) a
JOIN scs_cd_data_hudi.acct_pay_led_d_paimon b ON 
    a.psn_no = b.psn_no 
    AND a.`year` = b.`year` 
    AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
WHERE CAST(a.pay_sumamt AS DECIMAL(18,2)) <> CAST(
    COALESCE(b.uebmi_acct_crtyear_pay, 0) + 
    COALESCE(b.uebmi_acct_lasty_pay, 0) + 
    COALESCE(b.uebmi_acct_ptyear_pay, 0) +
    COALESCE(b.cvlserv_acct_crtyear_pay, 0) + 
    COALESCE(b.cvlserv_acct_lasty_pay, 0) + 
    COALESCE(b.cvlserv_acct_ptyear_pay, 0) +
    COALESCE(b.oth_acct_crtyear_pay, 0) + 
    COALESCE(b.oth_acct_lasty_pay, 0) + 
    COALESCE(b.oth_acct_ptyear_pay, 0) + 
    COALESCE(b.oth_acct_pay, 0) 
    AS DECIMAL(18,2));
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'EMP_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '单位缴费明细表中的单位编号和险种必须在单位参保信息表中能够获取' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.emp_insu_d_paimon b
    WHERE b.emp_no = a.emp_no 
      AND b.insutype = a.insutype
)
AND a.emp_no IS NOT NULL
AND a.emp_no <> ''
AND a.insutype IS NOT NULL
AND a.insutype <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'EMP_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '单位缴费明细表中的单位编号需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.emp_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.insu_emp_info_b_paimon b
    WHERE b.emp_no = a.emp_no
)
AND a.emp_no IS NOT NULL
AND a.emp_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '人员编号（PSN_NO）需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_info_b_paimon b
    WHERE b.psn_mgtcode = a.psn_no
)
AND a.psn_no IS NOT NULL
AND a.psn_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'RSDT_PSN_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '人员参保关系id(PSN_INSU_RLTS_ID)需存在于参保中心人员参保信息中（PSN_INSU_D）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_insu_d_paimon b
    WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id
      AND b.insutype = a.insutype
)
AND a.psn_insu_rlts_id IS NOT NULL
AND a.psn_insu_rlts_id <> ''
AND a.insutype IS NOT NULL
AND a.insutype <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,
    '关联性' AS rule,
    '居民缴费明细ID需要存在于居民缴费明细表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.rsdt_psn_clct_detl_d_paimon b
    WHERE b.rsdt_clct_detl_id = a.rsdt_clct_detl_id
      AND b.insutype = a.insutype
)
AND a.rsdt_clct_detl_id IS NOT NULL
AND a.rsdt_clct_detl_id <> ''
AND a.insutype IS NOT NULL
AND a.insutype <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'RSDT_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,
    '关联性' AS rule,
    '人员编号需存在于基本信息中心PSN_INFO_B（人员基本信息表）中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.rsdt_psn_clct_detl_ext_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_info_b_paimon b
    WHERE b.psn_mgtcode = a.psn_no
)
AND a.psn_no IS NOT NULL
AND a.psn_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.insu_emp_info_b_paimon b
    WHERE b.emp_no = a.emp_no
)
AND a.emp_no IS NOT NULL
AND a.emp_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_info_b_paimon b
    WHERE b.psn_mgtcode = a.psn_no
)
AND a.psn_no IS NOT NULL
AND a.psn_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_D_paimon' AS table_name,
    '关联性' AS rule,
    '参保关系id需存在于参保中心人员参保信息历史表中（PSN_INSU_HIS_D）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_insu_his_d_paimon b
    WHERE b.psn_insu_rlts_id = a.psn_insu_rlts_id
)
AND a.psn_insu_rlts_id IS NOT NULL
AND a.psn_insu_rlts_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,
    '关联性' AS rule,
    '需存在于用户中心参保单位信息表中（INSU_EMP_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.insu_emp_info_b_paimon b
    WHERE b.emp_no = a.emp_no
)
AND a.emp_no IS NOT NULL
AND a.emp_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,
    '关联性' AS rule,
    '需存在于基本中心人员基本信息中（PSN_INFO_B）' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.psn_info_b_paimon b
    WHERE b.psn_mgtcode = a.psn_no
)
AND a.psn_no IS NOT NULL
AND a.psn_no <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'STAF_PSN_CLCT_DETL_EXT_D_paimon' AS table_name,
    '关联性' AS rule,
    '个人缴费明细ID（PSN_CLCT_DETL_ID）需要存在于职工缴费明细表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.staf_psn_clct_detl_ext_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.staf_psn_clct_detl_d_paimon b
    WHERE b.psn_clct_detl_id = a.psn_clct_detl_id
      AND b.insutype = a.insutype
)
AND a.psn_clct_detl_id IS NOT NULL
AND a.psn_clct_detl_id <> ''
AND a.insutype IS NOT NULL
AND a.insutype <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_DETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '医保消费数据必须存在于结算表SETL_D中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_detl_d_paimon a
WHERE a.acct_incexpd_type = '201' 
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.setl_d_paimon b 
      WHERE a.psn_no = b.psn_no 
        AND a.acct_incexpd_souc_id = b.setl_id
  )
  AND a.acct_incexpd_souc_id IS NOT NULL
  AND a.acct_incexpd_souc_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'CLRED_SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '已清算结算信息表中的结算ID必须存在于结算信息表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.clred_setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.setl_d_paimon s
    WHERE s.setl_id = a.setl_id
)
AND a.setl_id IS NOT NULL
AND a.setl_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'CLRED_SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '就诊ID存在就诊信息表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.clred_setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.mdtrt_d_paimon m
    WHERE m.mdtrt_id = a.mdtrt_id
)
AND a.mdtrt_id IS NOT NULL
AND a.mdtrt_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'CLRED_SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '机构清算申请事件ID必须存在于定点医药机构清算汇总表 MEDINS_CLR_SUM_D中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.clred_setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.medins_clr_sum_d_paimon b 
    WHERE a.fixmedins_code = b.fixmedins_code 
      AND a.clr_appy_evt_id = b.clr_appy_evt_id
)
AND a.clr_appy_evt_id IS NOT NULL
AND a.clr_appy_evt_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'FEE_LIST_D_paimon' AS table_name,
    '业务约束' AS rule,
    '费用明细信息表中的就诊ID必须存在于就诊信息表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.fee_list_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.mdtrt_d_paimon m
    WHERE m.mdtrt_id = a.mdtrt_id
)
AND a.mdtrt_id IS NOT NULL
AND a.mdtrt_id <> '';
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'FEE_LIST_D_paimon' AS table_name,
    '业务约束' AS rule,
    '结算ID需存在于结算信息表' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.fee_list_d_paimon a
WHERE a.setl_id IS NOT NULL 
  AND a.setl_id <> ''
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.setl_d_paimon s
      WHERE s.setl_id = a.setl_id
  );
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'MED_DFR_DETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '医疗拨付明细表的拨付ID存在于医疗拨付信息表里' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.med_dfr_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.med_dfr_d_paimon b
    WHERE b.dfr_id = a.dfr_id
);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'NOCLR_SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '未清算结算信息表中的“结算ID”必须存在于结算信息表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.noclr_setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.setl_d_paimon s
    WHERE s.setl_id = a.setl_id
);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'NOCLR_SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '就诊ID存在就诊信息表中' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.noclr_setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.mdtrt_d_paimon b
    WHERE b.mdtrt_id = a.mdtrt_id
);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '结算信息表中的就诊ID在就诊信息表中有' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.mdtrt_d_paimon b
    WHERE b.mdtrt_id = a.mdtrt_id
);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'ACCT_PAY_DETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '个人账户支出明细信息必须在台账信息中有对应的台账' AS drty_desc,
    CONCAT(CAST(a.rid AS STRING), '-', CAST(a.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.acct_pay_detl_d_paimon a
WHERE NOT EXISTS (
    SELECT 1 
    FROM scs_cd_data_hudi.acct_pay_led_d_paimon b 
    WHERE a.psn_no = b.psn_no 
      AND a.YEAR = b.YEAR 
      AND a.psn_insu_rlts_id = b.psn_insu_rlts_id
);
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '“结算信息表”中当“医疗类别”为门诊慢特病时，病种编码与病种名称不应为空' AS drty_desc,
    CONCAT(CAST(d.rid AS STRING), '-', CAST(d.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon d
WHERE d.med_type = '14' 
  AND (d.dise_no IS NULL OR d.dise_no = '' OR d.dise_name IS NULL OR d.dise_name = '');
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '“结算信息表”中当“清算方式”为“单病种”清算时，“病种编码”与“病种名称”不应为空' AS drty_desc,
    CONCAT(CAST(d.rid AS STRING), '-', CAST(d.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon d
WHERE d.clr_way = '2' 
  AND (d.dise_no IS NULL OR d.dise_no = '' OR d.dise_name IS NULL OR d.dise_name = '');
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '“结算信息表”中“原结算ID”应在“结算信息表”的“结算ID”中存在，且该条结算交易的“退费结算标志”应为1' AS drty_desc,
    CONCAT(CAST(d.rid AS STRING), '-', CAST(d.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon d
WHERE d.init_setl_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.setl_d_paimon d2 
      WHERE d2.setl_id = d.init_setl_id 
        AND d2.refd_setl_flag = '1'
  );
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '“退费结算标志”=1的“结算ID”应该在“结算信息表”的“结算原ID”中存在' AS drty_desc,
    CONCAT(CAST(d.rid AS STRING), '-', CAST(d.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon d
WHERE d.refd_setl_flag = '1' 
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.setl_d_paimon d2 
      WHERE d2.init_setl_id IS NOT NULL 
        AND d2.init_setl_id = d.setl_id
  );
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '结算信息表中人员参保关系ID如果不为空，应在人员参保信息表或在人员参保历史信息表中存在' AS drty_desc,
    CONCAT(CAST(s.rid AS STRING), '-', CAST(s.crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.setl_d_paimon s
WHERE s.psn_insu_rlts_id IS NOT NULL 
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.psn_insu_d_paimon i
      WHERE i.psn_insu_rlts_id = s.psn_insu_rlts_id
  )
  AND NOT EXISTS (
      SELECT 1 
      FROM scs_cd_data_hudi.psn_insu_his_d_paimon h
      WHERE h.psn_insu_rlts_id = s.psn_insu_rlts_id
  );
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'FEE_LIST_D_paimon' AS table_name,
    '业务约束' AS rule,
    '医院就诊结算费用明细表中，科室编码不能为空。' AS drty_desc,
    CONCAT(CAST(rid AS STRING), '-', CAST(crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.fee_list_d_paimon
WHERE (bilg_dept_codg IS NULL OR bilg_dept_codg = '') 
  AND (fixmedins_code IS NULL OR fixmedins_code = '' OR SUBSTR(fixmedins_code, 1, 1) <> 'p');
INSERT INTO drsjcx.data_qua_detail_money 
SELECT 
    'FEE_LIST_D_paimon' AS table_name,
    '业务约束' AS rule,
    '医院就诊结算费用明细表中，科室名称不能为空。' AS drty_desc,
    CONCAT(CAST(rid AS STRING), '-', CAST(crte_time AS STRING)) AS primary_key
FROM scs_cd_data_hudi.fee_list_d_paimon
WHERE (bilg_dept_name IS NULL OR bilg_dept_name = '') 
  AND (fixmedins_code IS NULL OR fixmedins_code = '' OR SUBSTR(fixmedins_code, 1, 1) <> 'p');
INSERT INTO drsjcx.data_qua_detail_money 
SELECT
    'SETL_D_paimon' AS table_name,
    '业务约束' AS rule,
    '医院就诊结算应在结算诊断信息表中存在主诊断信息。' AS drty_desc,
    concat(
        cast(rid AS string),
        '-',
        cast(crte_time AS string)
    ) AS primary_key 
FROM
    scs_cd_data_hudi.setl_d_paimon t 
WHERE
    NOT EXISTS (
        SELECT
            1 
        FROM
            scs_cd_data_hudi.setl_diag_list_d_paimon t1 
        WHERE
            t1.mdtrt_id = t.mdtrt_id 
            AND t1.psn_no = t.psn_no 
            AND t1.maindiag_flag = '1' 
    ) 
    AND substr(t.med_type, 1, 1) = '2';