import re


def clean_table_name_component(name_part):
    """
    清理表名或模式名部分，去除包围的引号。
    例如：`name` -> name, "name" -> name, [name] -> name
    """
    name_part = name_part.strip()
    if len(name_part) >= 2:  # 确保至少有两个字符以便判断引号
        if name_part.startswith('`') and name_part.endswith('`'):
            return name_part[1:-1]
        if name_part.startswith('"') and name_part.endswith('"'):
            return name_part[1:-1]
        if name_part.startswith('[') and name_part.endswith(']'):  # 主要用于 SQL Server
            return name_part[1:-1]
    return name_part


def extract_table_from_identifier(identifier_string):
    """
    从一个可能包含模式和别名的标识符字符串中提取实际的表名。
    例如："schema.table alias" -> "table"
            "`database`.`orders` o" -> "orders"
            "products" -> "products"
            "[dbo].[Sales Detail]" -> "Sales Detail"
    会跳过子查询（以 '(' 开头的标识符）。
    """
    identifier_string = identifier_string.strip()
    if not identifier_string or identifier_string.startswith('('):  # 跳过子查询
        return None

    # 尝试移除表别名 (e.g., "table_name alias" or "table_name AS alias")
    # 这个正则表达式尝试匹配末尾的 "AS alias_name" 或 "alias_name"
    # 别名本身可以是单词字符或被引起来的
    no_alias_string = re.sub(
        r"\s+(?:AS\s+)?(?:`[^`]+`|\"[^\"]+\"|\[[^\]]+\]|[\w-]+)$",
        "",
        identifier_string,
        flags=re.IGNORECASE
    ).strip()

    # 如果移除别名后字符串为空，或者看起来不像是移除了别名（例如原字符串就是单个词）
    # 则尝试更简单的分割方法（主要针对 `table alias` 形式）
    if not no_alias_string or len(
            no_alias_string.split()) > 1 and not '.' in no_alias_string:  # "foo bar" but not "schema.foo bar"
        parts = identifier_string.split()
        if parts:
            first_part = parts[0]
            is_alias_likely = False
            if len(parts) > 1:
                second_part_upper = parts[1].upper()
                if second_part_upper == "AS" and len(parts) > 2:
                    is_alias_likely = True
                elif second_part_upper not in [
                    'ON', 'USING', 'WHERE', 'JOIN', 'LEFT', 'RIGHT', 'INNER', 'FULL',
                    'GROUP', 'ORDER', 'HAVING', 'LIMIT', 'UNION', 'INTERSECT', 'EXCEPT', 'WINDOW'
                ]:
                    is_alias_likely = True

            if is_alias_likely or len(parts) == 1:
                no_alias_string = first_part
            else:  # 可能是复杂情况，或者没有别名
                no_alias_string = identifier_string

    if not no_alias_string or no_alias_string.startswith('('):  # 再次检查
        return None

    # 分割模式和表名 (e.g., "schema.table" or "`schema`.`table`")
    name_components = re.split(r'\s*\.\s*', no_alias_string)
    if not name_components:
        return None

    # 获取最后一部分，这应该是表名
    table_name_part_raw = name_components[-1]
    cleaned_table_name = clean_table_name_component(table_name_part_raw)

    if not cleaned_table_name:
        return None

    # 基本的有效性检查，避免将SQL关键字（除非被引用）等误认为表名
    is_quoted_originally = (
            (table_name_part_raw.startswith('`') and table_name_part_raw.endswith('`')) or
            (table_name_part_raw.startswith('"') and table_name_part_raw.endswith('"')) or
            (table_name_part_raw.startswith('[') and table_name_part_raw.endswith(']'))
    )

    common_sql_keywords = [
        'AS', 'ON', 'JOIN', 'SELECT', 'FROM', 'WHERE', 'BY', 'LEFT', 'RIGHT', 'INNER', 'OUTER',
        'FULL', 'TABLE', 'VIEW', 'INDEX', 'GROUP', 'ORDER', 'LIMIT', 'USING', 'UNION',
        'INTERSECT', 'EXCEPT', 'HAVING', 'WINDOW'
    ]
    if cleaned_table_name.upper() in common_sql_keywords and not is_quoted_originally:
        return None

    # 对于未引用的名称，通常遵循特定的标识符规则（例如，以字母或下划线开头）
    # 引用的名称可以更宽松。
    if not is_quoted_originally:
        if not re.match(r"^[a-zA-Z_][\w-]*$", cleaned_table_name):  # 允许字母、数字、下划线、连字符，以字母或下划线开头
            # 如果不匹配上述规则，但包含字母（例如数字开头的表名但包含字母），也可能有效
            if not any(c.isalpha() for c in cleaned_table_name):
                return None  # 如果完全不像一个标准的未引用标识符

    return cleaned_table_name


def extract_tables_from_select_statements(sql_file_path):
    """
    从 SQL 文件（主要包含 SELECT 语句）中提取所有引用的表名。
    """
    all_table_names = set()
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 基础的注释移除
        sql_content = re.sub(r"--[^\r\n]*", "", sql_content)  # 移除单行注释
        sql_content = re.sub(r"/\*.*?\*/", "", sql_content, flags=re.DOTALL)  # 移除多行注释
        sql_content = sql_content.strip()  # 移除首尾空白

        if not sql_content:
            return []

        # 定义子句结束的标志性关键字或符号
        # 这些关键字通常标志着 FROM 或 JOIN 后面的表名列表的结束
        terminators = r"(?:\s(?:WHERE|GROUP\sBY|ORDER\sBY|HAVING|LIMIT|UNION|INTERSECT|EXCEPT|WINDOW|(?:CROSS\s+|INNER\s+|LEFT\s+(?:OUTER\s+)?|RIGHT\s+(?:OUTER\s+)?|FULL\s+(?:OUTER\s+)?)?JOIN|ON)|\(|\)|;|\Z)"

        # 1. 处理 FROM 子句
        #    FROM\s+([\s\S]+?)  -> 捕获 FROM 后的内容，非贪婪模式
        #    (?= ... )          -> 正向先行断言，确保内容后紧跟终结符
        from_clause_matches = re.finditer(r"FROM\s+([\s\S]+?)(?=" + terminators + ")", sql_content,
                                          re.IGNORECASE | re.DOTALL)
        for match in from_clause_matches:
            from_block_content = match.group(1).strip()

            # FROM 子句中的表名可能以逗号分隔
            table_candidates_in_from = from_block_content.split(',')
            for candidate_str in table_candidates_in_from:
                candidate_str = candidate_str.strip()
                if candidate_str:  # 确保不是空的字符串（例如，由末尾逗号产生）
                    table_name = extract_table_from_identifier(candidate_str)
                    if table_name:
                        all_table_names.add(table_name)

        # 2. 处理 JOIN 子句 (包括 LEFT JOIN, RIGHT JOIN, INNER JOIN, FULL JOIN, CROSS JOIN)
        #    (?:(?:CROSS...)\s)?JOIN\s+  -> 匹配各种 JOIN 类型
        #    ([\s\S]+?)                  -> 捕获 JOIN 后的表标识符
        join_clause_terminators = r"(?:\s(?:ON|USING|WHERE|GROUP\sBY|ORDER\sBY|HAVING|LIMIT|UNION|INTERSECT|EXCEPT|(?:CROSS\s+|INNER\s+|LEFT\s+(?:OUTER\s+)?|RIGHT\s+(?:OUTER\s+)?|FULL\s+(?:OUTER\s+)?)?JOIN|WINDOW)|\(|\)|;|\Z)"
        join_clause_matches = re.finditer(
            r"(?:(?:CROSS|INNER|LEFT(?:\sOUTER)?|RIGHT(?:\sOUTER)?|FULL(?:\sOUTER)?)\s)?JOIN\s+([\s\S]+?)(?=" + join_clause_terminators + ")",
            sql_content,
            re.IGNORECASE | re.DOTALL
        )
        for match in join_clause_matches:
            # match.group(1) 包含 JOIN 关键字后的表名（可能带别名）
            join_block_content = match.group(1).strip()
            if join_block_content:
                table_name = extract_table_from_identifier(join_block_content)
                if table_name:
                    all_table_names.add(table_name)

        return sorted(list(all_table_names))

    except FileNotFoundError:
        print(f"错误: 文件 '{sql_file_path}' 未找到。")
        return []
    except Exception as e:
        print(f"处理文件 '{sql_file_path}' 时发生错误: {e}")
        return []


# --- 使用方法 ---
if __name__ == "__main__":
    # 将下面的路径替换为你的 SQL 文件路径
    sql_file_to_parse = r"D:\work\demo\成都\表名.sql"
    # 为了测试，可以创建一个包含以下内容的 表名.sql 文件：
    # -- 示例 SQL 内容:
    # SELECT c.customer_name, o.order_date
    # FROM customers c JOIN orders AS o ON c.customer_id = o.customer_id
    # WHERE c.city = '成都';
    #
    # SELECT * FROM product_details pd, "Transaction Data" td
    # WHERE pd.product_id = td.p_id;
    #
    # SELECT a.col1, b.col2 FROM schema1.tableA AS a LEFT JOIN `schema2`.`tableB` b ON a.id = b.id
    # RIGHT JOIN tableC c ON b.cid = c.id
    # FULL OUTER JOIN [dbo].[Sales Summary] ss ON c.sid = ss.id
    # CROSS JOIN lookup_table lt;
    #
    # SELECT name FROM (SELECT id, name FROM user_temp_table) AS user_summary WHERE id > 100;
    #
    # SELECT DISTINCT department FROM department_table;

    extracted_tables = extract_tables_from_select_statements(sql_file_to_parse)

    if extracted_tables:
        print(f"在文件 '{sql_file_to_parse}' 的 SELECT 语句中找到的表名:")
        for table in extracted_tables:
            print(table)
    else:
        print(f"在文件 '{sql_file_to_parse}' 中没有找到表名，或处理过程中发生错误。")

    # 你可以创建一些测试用例文件来验证
    # test_sql_content_1 = """
    # SELECT c.name FROM customers c JOIN sales s ON c.id = s.cid;
    # SELECT * FROM products p, `order_details` od WHERE p.id = od.pid;
    # """
    # with open("test1.sql", "w", encoding="utf-8") as f:
    #     f.write(test_sql_content_1)
    # tables1 = extract_tables_from_select_statements("test1.sql")
    # print("\nTest 1 Tables:", tables1) # 预期: ['customers', 'order_details', 'products', 'sales'] (顺序可能不同)

    # test_sql_content_2 = """
    # SELECT col FROM "My Schema"."My Table" AS mt JOIN anotherTable at ON mt.id = at.id;
    # FROM [db].[logs] l WHERE l.date > '2023-01-01';
    # """ # 第二个FROM前面没有SELECT，但也会被独立解析
    # with open("test2.sql", "w", encoding="utf-8") as f:
    #     f.write(test_sql_content_2)
    # tables2 = extract_tables_from_select_statements("test2.sql")
    # print("\nTest 2 Tables:", tables2) # 预期: ['My Table', 'anotherTable', 'logs']