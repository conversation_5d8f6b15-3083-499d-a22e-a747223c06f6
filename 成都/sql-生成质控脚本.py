# -*- coding: utf-8 -*-
"""
Created on Sun May 18 13:17:33 2025

@author: 24551
"""
import pandas as pd
import os
import codecs


# 字典表
dict=pd.read_excel(r'D:\work\demo\成都\nat_data_dic_a_paimon表.xlsx')
# 表结构
tb_column=pd.read_excel(r'D:\work\demo\成都\parsed_columns.xlsx')
# 写入文件路径
file_path = r'D:\work\demo\成都\result'


# 处理字典表
dict_cols=set(list(dict['dic_type_code']))
dict_comp=[]
for d_col in dict_cols:
    strs='('
    for v in dict[dict['dic_type_code']==d_col]['nat_dic_val_code']:
        # 添加原始值和带\n的值
        # strs=strs+"'"+v+"','"+v+"\\n',"
        strs=strs+"'"+v+"',"
    strs=strs[:-1]+')'
    dict_comp.append([d_col.lower(),strs])
dict_comp=pd.DataFrame(dict_comp)
dict_comp.columns=['字段名','字典值']


# 表结构
tb_column=pd.read_excel(r'D:\work\demo\成都\parsed_columns.xlsx')
tb_column=pd.merge(tb_column,dict_comp,on='字段名',how='left').fillna('')
tbs=set(list(tb_column['表名']))

rule_i=1
result=[]
sqls=''
for tb_name in tbs:
    print(tb_name)
    tb_c=tb_column[tb_column['表名']==tb_name]
    tb_sql=''
    case_parts = []
    
    # 检查是否同时包含rid和crte_time字段
    has_rid = '字段名' in tb_c.columns and 'rid' in tb_c['字段名'].str.lower().values
    has_crte_time = '字段名' in tb_c.columns and 'crte_time' in tb_c['字段名'].str.lower().values
    
    if not (has_rid and has_crte_time):
        result.append([tb_name, '', '', f'缺少必需字段: {"rid" if not has_rid else ""}{" 和 " if not has_rid and not has_crte_time else ""}{"crte_time" if not has_crte_time else ""}'])
        continue
    
    for i in range(len(tb_c)):
        col=tb_c['字段名'].iloc[i]
        s=tb_c['字典值'].iloc[i]
        flag=tb_c['flag'].iloc[i]
        if s!='' and flag=='':
            case_part = f"""CASE WHEN {col} not in {s} and {col} is not null and cast({col} as string) != '' 
                 THEN concat(cast({col} as string), '不在{col}字典范围内') END"""
            case_parts.append(case_part)
            print(rule_i)
            rule_i=rule_i+1    # 规则个数
    
    if case_parts:
        # 根据表名前缀选择数据库
        db_name = 'scs_cd_chxdata_hudi' if tb_name.startswith('longterm_') else 'scs_cd_data_hudi'
        tb_sql = f"""insert into drsjcx.data_qua_detail
SELECT 
    '{tb_name}' as table_name,
    '数据字典校验' as rule,
    drty_desc,
    primary_key
FROM (
    SELECT 
        concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,
        concat({','.join(case_parts)}) as drty_desc
    FROM {db_name}.{tb_name}
    WHERE gx_op_code != '2' 
    AND gx_op_code != 'D'
) t
WHERE drty_desc is not null;
"""
        sqls = sqls + tb_sql + "\n\n"
        result.append([tb_name, flag, tb_sql, '已生成SQL'])
    else:
        result.append([tb_name, flag, '', '无需生成SQL：表中没有需要字典校验的字段'])

result=pd.DataFrame(result)
result.columns = ['表名', 'flag', 'SQL', '说明']
result.to_excel(file_path+'.xlsx')

# 保存成txt
with open(file_path+'.sql','w',encoding='utf-8') as fw:
    fw.write(sqls)
    

  
    
    
    
    
    
    
    

