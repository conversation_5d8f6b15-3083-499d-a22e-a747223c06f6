
=== 2025-05-29 16:20:33 - Query-1 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 108.81秒
平均响应时间: 108.81秒
最小响应时间: 108.81秒
最大响应时间: 108.81秒
中位数响应时间: 108.81秒
95百分位响应时间: 108.81秒

=== 2025-05-29 16:23:51 - Query-2 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 198.03秒
平均响应时间: 198.03秒
最小响应时间: 198.03秒
最大响应时间: 198.03秒
中位数响应时间: 198.03秒
95百分位响应时间: 198.03秒

=== 2025-05-29 16:25:47 - Query-3 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 116.72秒
平均响应时间: 116.72秒
最小响应时间: 116.72秒
最大响应时间: 116.72秒
中位数响应时间: 116.72秒
95百分位响应时间: 116.72秒

=== 2025-05-29 16:25:49 - Query-4 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.36秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'smsbm_medical_bank_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fee_type not in ('1','2') and fee_type is not null and cast(fee_type as string) != ''                   THEN concat(cast(fee_type as string), '不在fee_type字典范围内') END,CASE WHEN pay_flag not in ('0','1') and pay_flag is not null and cast(pay_flag as string) != ''                   THEN concat(cast(pay_flag as string), '不在pay_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.smsbm_medical_bank_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'smsbm_medical_bank_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fee_type not in ('1','2') and fee_type is not null and cast(fee_type as string) != ''                   THEN concat(cast(fee_type as string), '不在fee_type字典范围内') END,CASE WHEN pay_flag not in ('0','1') and pay_flag is not null and cast(pay_flag as string) != ''                   THEN concat(cast(pay_flag as string), '不在pay_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.smsbm_medical_bank_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:182 Invalid table alias or column reference 'rid': (possible column names are: id, create_by, create_date, last_modified_by, last_modified_date, last_modified_version, fund_batch_num, insurance_code, country_code, country_name, voucher_month, voucher_category, pay_type, total_count, total_money, agency_name, pay_no, pay_name, pay_time, bank_offer_time, data_status, refund_voucher, fail_reason_code, fail_reason, cancel_pay_time, cancel_people, cancel_pay_reason, voucher_category_name, pay_type_name, data_status_name, refund_voucher_name, pay_bank_code, send_file_name, bank_file_name, send_file_time, bank_file_time, agency_code, insurance_name, business_code, access_date_time, pay_business_type, pay_business_type_name, pay_bank_name, accounting_agency, summary_code, fail_count, fail_money, success_count, success_money, cancel_count, cancel_money, pay_period, settle_type, settle_type_name, refund_vou_guid, refund_vou_no, refund_vou_date, batch_no, en_acc_vou_guid, en_acc_vou_no, en_acc_vou_date, bill_no, pay_bank_no, pay_bank_type, subject_type, subject_code, subject_name, pay_people, audit_time, audit_people, audit_status, pay_info, pay_flag, remark, receive_type, receive_type_name, receive_no, receive_name, receive_bank_code, receive_bank_name, receive_bank_type, receive_bank_no, en_acc_vou_status, serial_no, push_status, issue_type, rec_acct_update_status, has_commit_cancel_pay_data, create_rec_bill_status, amount_type, inter_bank_flag, auto_pay_flag, fast_success_count, fast_success_money, account_flag, medical_pay_type, medical_pay_name, settle_bill_id, journal_status, journal_no, journal_date, rec_acc_update_time, ordination_fund_pay_type_code, ordination_fund_pay_type_name, fee_type, back_day_book_acct_id, back_day_acct_bill_code, back_day_acct_bill_guid, back_day_book_status, back_day_book_date, pay_fail_day_book_acct_id, pay_fail_day_acct_bill_code, pay_fail_day_acct_bill_guid, pay_fail_day_book_status, pay_fail_day_book_date, print_status, manual_pay_time, submitter, acceptor, clear_type, agency_business_flag, biz_no, second_do, slicetag, gx_op_code, gx_op_time, split_push_fail_res)
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'smsbm_medical_bank_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fee_type not in ('1','2') and fee_type is not null and cast(fee_type as string) != ''                   THEN concat(cast(fee_type as string), '不在fee_type字典范围内') END,CASE WHEN pay_flag not in ('0','1') and pay_flag is not null and cast(pay_flag as string) != ''                   THEN concat(cast(pay_flag as string), '不在pay_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.smsbm_medical_bank_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'smsbm_medical_bank_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fee_type not in ('1','2') and fee_type is not null and cast(fee_type as string) != ''                   THEN concat(cast(fee_type as string), '不在fee_type字典范围内') END,CASE WHEN pay_flag not in ('0','1') and pay_flag is not null and cast(pay_flag as string) != ''                   THEN concat(cast(pay_flag as string), '不在pay_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.smsbm_medical_bank_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:182 Invalid table alias or column reference 'rid': (possible column names are: id, create_by, create_date, last_modified_by, last_modified_date, last_modified_version, fund_batch_num, insurance_code, country_code, country_name, voucher_month, voucher_category, pay_type, total_count, total_money, agency_name, pay_no, pay_name, pay_time, bank_offer_time, data_status, refund_voucher, fail_reason_code, fail_reason, cancel_pay_time, cancel_people, cancel_pay_reason, voucher_category_name, pay_type_name, data_status_name, refund_voucher_name, pay_bank_code, send_file_name, bank_file_name, send_file_time, bank_file_time, agency_code, insurance_name, business_code, access_date_time, pay_business_type, pay_business_type_name, pay_bank_name, accounting_agency, summary_code, fail_count, fail_money, success_count, success_money, cancel_count, cancel_money, pay_period, settle_type, settle_type_name, refund_vou_guid, refund_vou_no, refund_vou_date, batch_no, en_acc_vou_guid, en_acc_vou_no, en_acc_vou_date, bill_no, pay_bank_no, pay_bank_type, subject_type, subject_code, subject_name, pay_people, audit_time, audit_people, audit_status, pay_info, pay_flag, remark, receive_type, receive_type_name, receive_no, receive_name, receive_bank_code, receive_bank_name, receive_bank_type, receive_bank_no, en_acc_vou_status, serial_no, push_status, issue_type, rec_acct_update_status, has_commit_cancel_pay_data, create_rec_bill_status, amount_type, inter_bank_flag, auto_pay_flag, fast_success_count, fast_success_money, account_flag, medical_pay_type, medical_pay_name, settle_bill_id, journal_status, journal_no, journal_date, rec_acc_update_time, ordination_fund_pay_type_code, ordination_fund_pay_type_name, fee_type, back_day_book_acct_id, back_day_acct_bill_code, back_day_acct_bill_guid, back_day_book_status, back_day_book_date, pay_fail_day_book_acct_id, pay_fail_day_acct_bill_code, pay_fail_day_acct_bill_guid, pay_fail_day_book_status, pay_fail_day_book_date, print_status, manual_pay_time, submitter, acceptor, clear_type, agency_business_flag, biz_no, second_do, slicetag, gx_op_code, gx_op_time, split_push_fail_res)
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:26:56 - Query-5 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 66.81秒
平均响应时间: 66.81秒
最小响应时间: 66.81秒
最大响应时间: 66.81秒
中位数响应时间: 66.81秒
95百分位响应时间: 66.81秒

=== 2025-05-29 16:26:57 - Query-6 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.35秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_asst_reg_dev_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN asst_type not in ('01','02') and asst_type is not null and cast(asst_type as string) != ''                   THEN concat(cast(asst_type as string), '不在asst_type字典范围内') END,CASE WHEN asst_prod_type not in ('01','02','03','99') and asst_prod_type is not null and cast(asst_prod_type as string) != ''                   THEN concat(cast(asst_prod_type as string), '不在asst_prod_type字典范围内') END,CASE WHEN prcunt_no not in ('01','02','03','04','05','06','07','08','09','10','11','12') and prcunt_no is not null and cast(prcunt_no as string) != ''                   THEN concat(cast(prcunt_no as string), '不在prcunt_no字典范围内') END,CASE WHEN overlmt_dspo_way not in ('1','2','3','4','5','6') and overlmt_dspo_way is not null and cast(overlmt_dspo_way as string) != ''                   THEN concat(cast(overlmt_dspo_way as string), '不在overlmt_dspo_way字典范围内') END,CASE WHEN selfpay_prop_type not in ('10','20','30','40') and selfpay_prop_type is not null and cast(selfpay_prop_type as string) != ''                   THEN concat(cast(selfpay_prop_type as string), '不在selfpay_prop_type字典范围内') END,CASE WHEN chrgitm_lv not in ('01','02','03','04') and chrgitm_lv is not null and cast(chrgitm_lv as string) != ''                   THEN concat(cast(chrgitm_lv as string), '不在chrgitm_lv字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_asst_reg_dev_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_asst_reg_dev_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN asst_type not in ('01','02') and asst_type is not null and cast(asst_type as string) != ''                   THEN concat(cast(asst_type as string), '不在asst_type字典范围内') END,CASE WHEN asst_prod_type not in ('01','02','03','99') and asst_prod_type is not null and cast(asst_prod_type as string) != ''                   THEN concat(cast(asst_prod_type as string), '不在asst_prod_type字典范围内') END,CASE WHEN prcunt_no not in ('01','02','03','04','05','06','07','08','09','10','11','12') and prcunt_no is not null and cast(prcunt_no as string) != ''                   THEN concat(cast(prcunt_no as string), '不在prcunt_no字典范围内') END,CASE WHEN overlmt_dspo_way not in ('1','2','3','4','5','6') and overlmt_dspo_way is not null and cast(overlmt_dspo_way as string) != ''                   THEN concat(cast(overlmt_dspo_way as string), '不在overlmt_dspo_way字典范围内') END,CASE WHEN selfpay_prop_type not in ('10','20','30','40') and selfpay_prop_type is not null and cast(selfpay_prop_type as string) != ''                   THEN concat(cast(selfpay_prop_type as string), '不在selfpay_prop_type字典范围内') END,CASE WHEN chrgitm_lv not in ('01','02','03','04') and chrgitm_lv is not null and cast(chrgitm_lv as string) != ''                   THEN concat(cast(chrgitm_lv as string), '不在chrgitm_lv字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_asst_reg_dev_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:1761 Table not found 'longterm_asst_reg_dev_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_asst_reg_dev_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN asst_type not in ('01','02') and asst_type is not null and cast(asst_type as string) != ''                   THEN concat(cast(asst_type as string), '不在asst_type字典范围内') END,CASE WHEN asst_prod_type not in ('01','02','03','99') and asst_prod_type is not null and cast(asst_prod_type as string) != ''                   THEN concat(cast(asst_prod_type as string), '不在asst_prod_type字典范围内') END,CASE WHEN prcunt_no not in ('01','02','03','04','05','06','07','08','09','10','11','12') and prcunt_no is not null and cast(prcunt_no as string) != ''                   THEN concat(cast(prcunt_no as string), '不在prcunt_no字典范围内') END,CASE WHEN overlmt_dspo_way not in ('1','2','3','4','5','6') and overlmt_dspo_way is not null and cast(overlmt_dspo_way as string) != ''                   THEN concat(cast(overlmt_dspo_way as string), '不在overlmt_dspo_way字典范围内') END,CASE WHEN selfpay_prop_type not in ('10','20','30','40') and selfpay_prop_type is not null and cast(selfpay_prop_type as string) != ''                   THEN concat(cast(selfpay_prop_type as string), '不在selfpay_prop_type字典范围内') END,CASE WHEN chrgitm_lv not in ('01','02','03','04') and chrgitm_lv is not null and cast(chrgitm_lv as string) != ''                   THEN concat(cast(chrgitm_lv as string), '不在chrgitm_lv字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_asst_reg_dev_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_asst_reg_dev_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN asst_type not in ('01','02') and asst_type is not null and cast(asst_type as string) != ''                   THEN concat(cast(asst_type as string), '不在asst_type字典范围内') END,CASE WHEN asst_prod_type not in ('01','02','03','99') and asst_prod_type is not null and cast(asst_prod_type as string) != ''                   THEN concat(cast(asst_prod_type as string), '不在asst_prod_type字典范围内') END,CASE WHEN prcunt_no not in ('01','02','03','04','05','06','07','08','09','10','11','12') and prcunt_no is not null and cast(prcunt_no as string) != ''                   THEN concat(cast(prcunt_no as string), '不在prcunt_no字典范围内') END,CASE WHEN overlmt_dspo_way not in ('1','2','3','4','5','6') and overlmt_dspo_way is not null and cast(overlmt_dspo_way as string) != ''                   THEN concat(cast(overlmt_dspo_way as string), '不在overlmt_dspo_way字典范围内') END,CASE WHEN selfpay_prop_type not in ('10','20','30','40') and selfpay_prop_type is not null and cast(selfpay_prop_type as string) != ''                   THEN concat(cast(selfpay_prop_type as string), '不在selfpay_prop_type字典范围内') END,CASE WHEN chrgitm_lv not in ('01','02','03','04') and chrgitm_lv is not null and cast(chrgitm_lv as string) != ''                   THEN concat(cast(chrgitm_lv as string), '不在chrgitm_lv字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_asst_reg_dev_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:1761 Table not found 'longterm_asst_reg_dev_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:26:58 - Query-7 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.32秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_psn_injr_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN psn_cert_type not in ('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','90','99','990102','990201','9902','9901','18','19','1701','1702','1703','9903','9904','20','21','22','23') and psn_cert_type is not null and cast(psn_cert_type as string) != ''                   THEN concat(cast(psn_cert_type as string), '不在psn_cert_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_psn_injr_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_psn_injr_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN psn_cert_type not in ('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','90','99','990102','990201','9902','9901','18','19','1701','1702','1703','9903','9904','20','21','22','23') and psn_cert_type is not null and cast(psn_cert_type as string) != ''                   THEN concat(cast(psn_cert_type as string), '不在psn_cert_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_psn_injr_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:855 Table not found 'longterm_psn_injr_info_b_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_psn_injr_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN psn_cert_type not in ('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','90','99','990102','990201','9902','9901','18','19','1701','1702','1703','9903','9904','20','21','22','23') and psn_cert_type is not null and cast(psn_cert_type as string) != ''                   THEN concat(cast(psn_cert_type as string), '不在psn_cert_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_psn_injr_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_psn_injr_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN psn_cert_type not in ('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','90','99','990102','990201','9902','9901','18','19','1701','1702','1703','9903','9904','20','21','22','23') and psn_cert_type is not null and cast(psn_cert_type as string) != ''                   THEN concat(cast(psn_cert_type as string), '不在psn_cert_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_psn_injr_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:855 Table not found 'longterm_psn_injr_info_b_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:29:15 - Query-8 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 136.81秒
平均响应时间: 136.81秒
最小响应时间: 136.81秒
最大响应时间: 136.81秒
中位数响应时间: 136.81秒
95百分位响应时间: 136.81秒

=== 2025-05-29 16:30:28 - Query-9 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 72.73秒
平均响应时间: 72.73秒
最小响应时间: 72.73秒
最大响应时间: 72.73秒
中位数响应时间: 72.73秒
95百分位响应时间: 72.73秒

=== 2025-05-29 16:31:31 - Query-10 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 63.04秒
平均响应时间: 63.04秒
最小响应时间: 63.04秒
最大响应时间: 63.04秒
中位数响应时间: 63.04秒
95百分位响应时间: 63.04秒

=== 2025-05-29 16:32:37 - Query-11 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 66.21秒
平均响应时间: 66.21秒
最小响应时间: 66.21秒
最大响应时间: 66.21秒
中位数响应时间: 66.21秒
95百分位响应时间: 66.21秒

=== 2025-05-29 16:32:38 - Query-12 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.31秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_rels_train_task_cert_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN acp_stas not in ('0','1','2','3','4','5') and acp_stas is not null and cast(acp_stas as string) != ''                   THEN concat(cast(acp_stas as string), '不在acp_stas字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_rels_train_task_cert_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_rels_train_task_cert_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN acp_stas not in ('0','1','2','3','4','5') and acp_stas is not null and cast(acp_stas as string) != ''                   THEN concat(cast(acp_stas as string), '不在acp_stas字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_rels_train_task_cert_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:669 Table not found 'longterm_rels_train_task_cert_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_rels_train_task_cert_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN acp_stas not in ('0','1','2','3','4','5') and acp_stas is not null and cast(acp_stas as string) != ''                   THEN concat(cast(acp_stas as string), '不在acp_stas字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_rels_train_task_cert_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_rels_train_task_cert_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN acp_stas not in ('0','1','2','3','4','5') and acp_stas is not null and cast(acp_stas as string) != ''                   THEN concat(cast(acp_stas as string), '不在acp_stas字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_rels_train_task_cert_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:669 Table not found 'longterm_rels_train_task_cert_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:32:40 - Query-13 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.35秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_org_fund_sbit_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fund_pay_type not in ('310100','310101','310102','310200','310400','310500','320100','320200','330100','330200','340100','340200','350100','370100','370200','390100','390101','390102','390200','390300','390400','390500','410100','510100','610100','610200','620100','630100','640100','999106','999109','999110','999996','999997','99999701','99999702','99999705','99999706','99999707','99999708','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','99999731','99999732','99999733','99999734','99999735','99999737','99999747','99999748','99999749','99999750','99999751','99999752','99999753','999702','999703','999704','999706','390104','99999757','99999758','360200','360300','999998','310300','640103','640104','390103','650101','650102','610103','330102','610102','999111','99999765','99999766','99999767','99999768','99999769','99999770','390700','390801','310701','999761','999762','999763','310800','3990100','99999759','99999760','310103','99999771','99999778','99999779','610104','610105','99999780','340300','99999781','99999782','99999783','370300','99999790','910101','910102','310900','99999784','99999787','99999788','99999785','320300','999201','999202','99999772','310203','310302','350102','370201','390301','610107','640101','999103','999700','999701','999718','999900','99999602','99999703','99999741','99999756','310600','320400','340103','99999704','399100','620102','999705','999720','310301','390201','310601','310700','310802','320500','310107','390107','99999775','390501','9999981','99999704') and fund_pay_type is not null and cast(fund_pay_type as string) != ''                   THEN concat(cast(fund_pay_type as string), '不在fund_pay_type字典范围内') END,CASE WHEN poolarea_fund_pay_type not in ('310101','310102','310103','310201','310202','310203','310301','310302','310401','310501','310601','320101','320201','330101','330201','340101','340102','350101','350102','360101','360102','370101','37010101','37010102','370102','370201','390101','390102','390103','390201','390202','390301','390401','390501','390601','410101','510101','610101','610102','610103','610104','610105','610201','620100','620101','620102','630101','640101','640102','999102','999103','999104','999109','999110','999702','999703','999704','999705','999707','999708','999709','999710','999711','999712','999713','999714','999715','999716','999717','999718','999719','99999601','99999602','999997','99999701','99999702','99999703','99999706','99999707','99999708','99999709','99999710','99999711','99999712','99999713','99999714','99999715','99999716','99999717','99999718','99999719','99999720','99999738','99999739','99999741','99999742','99999745','99999746','99999748','99999751','99999752','99999753','99999754','99999756','999998','999999','999706','999720','999721','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','340103','999722','99999731','99999732','999111','330102','390104','99999757','99999758','360201','360301','99999759','99999760','390105','640103','640104','650101','650102','610106','610107','999723','999801','999802','999803','99999761','99999762','99999763','99999764','99999765','99999766','99999767','99999768','99999769','99999770','9999975302','9999975301','390701','390801','310701','999761','999762','999763','310801','310204','310205','320301','310206','620112','620122','620132','620142','3990101','660101','660102','31020401','31020402','99999771','99999778','99999779','610110','610111','310104','370301','99999799','910101','910102','310901','99999785','99999786','99999787','99999788','99999780','340301','999804','99999772','910103','999701','99999755','99999791','31020101','31020102','320300','399100','3991501','31020401','31020402','99999781','99999782','330103','320102','310105','310107','390107','320103','320104','31020103','31020104','9999981','999701','99999755') and poolarea_fund_pay_type is not null and cast(poolarea_fund_pay_type as string) != ''                   THEN concat(cast(poolarea_fund_pay_type as string), '不在poolarea_fund_pay_type字典范围内') END,CASE WHEN clr_type_lv2 not in ('3101','3102','3103','3104','3105','3106','3401','3402','3403','3901','3902','3903','3904','3905','5101','5102','9901','9999','999901','999902','999903','999904','999905','999992','999906','999907','999908','999909','999910','999911','999912','999913','999914','999915','999916','999917','999918','999919','999920','999921','999922','999923','999924','999925','999926','999927','999929','999930','999931','999932','999934','999936','999937','999938','999940','999942','999943','999944','999946','999952','999953','999956','999961','999962','999963','999971','999972','999973','999981','999982','999983','999991','6101','6102','999974','3906','999964','999965','999966','999967','999968','999969','999970','999975','999976','999977','999978','999979','999957','999958','999959','999984','999985','999986','99984','995101','995102','995103','995104','995105','995106','995107','995108','995109','995110','995111','995112','995113','995114','995115','995116','995117','995118','995119','995120','995121','995122','3107','3108','3109','3110','3501','3502','3503','3504','4102','5103','5104','999801','999802','310101','310201','310301','310401','310601','310102','310202','310302','310402','310602','310103','310203','310303','310403','310603','888801','888802','888805','888807','888808','888809','888813','888817','888818','888822','888828','888829','888831','888832','888833','888834','888835','888836','888837','888838','4103','3907','999988','999993','310304','390201','311101','311102','311201','311202','310305','390202','310104','310105','310106','390601','340301','710101','710102','710103','710104','710105','710106','710201','710202','710203','710204','710205','710206','999994','995126','995127','995128','995129','999999','999996','999997','999998','310409','3405','3406','3911','3913','3914','7131','7134','7139','993101','993102','993601','993602','995131','995132','995133','995134','995135','995136','995137','312102','312101','392102','392101','999391','530102','530202','310991','390991','310990','390990','311203','311204','995130','888839','888840','995139','995140','995141','995100','888841','99985','99901','99902','999806','999808','310411','390301','310412','390302','312801','312802','392801','392802','990101','990102','990103','990104','990105','399051','399052','399053','999804','999805','999806','999808','993903','993103','993904','993104','9988','9989','9990','9991','310306','390203','888842','888843','993907','993105','993106','993908','993909','999803','999933','999935','999945','999947','999955','999960','999980','999987','999941','999989','999990','9992','999954','999928','999939','999948','999949','999950','999951','993905','993906','993907','99950','999501','999502','888844','888845','99960','999001','888846','3505','3506','3507','999601','999602') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN clr_type_lv2 not in ('99966','99962','99963','99964','99965') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_fund_sbit_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_org_fund_sbit_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fund_pay_type not in ('310100','310101','310102','310200','310400','310500','320100','320200','330100','330200','340100','340200','350100','370100','370200','390100','390101','390102','390200','390300','390400','390500','410100','510100','610100','610200','620100','630100','640100','999106','999109','999110','999996','999997','99999701','99999702','99999705','99999706','99999707','99999708','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','99999731','99999732','99999733','99999734','99999735','99999737','99999747','99999748','99999749','99999750','99999751','99999752','99999753','999702','999703','999704','999706','390104','99999757','99999758','360200','360300','999998','310300','640103','640104','390103','650101','650102','610103','330102','610102','999111','99999765','99999766','99999767','99999768','99999769','99999770','390700','390801','310701','999761','999762','999763','310800','3990100','99999759','99999760','310103','99999771','99999778','99999779','610104','610105','99999780','340300','99999781','99999782','99999783','370300','99999790','910101','910102','310900','99999784','99999787','99999788','99999785','320300','999201','999202','99999772','310203','310302','350102','370201','390301','610107','640101','999103','999700','999701','999718','999900','99999602','99999703','99999741','99999756','310600','320400','340103','99999704','399100','620102','999705','999720','310301','390201','310601','310700','310802','320500','310107','390107','99999775','390501','9999981','99999704') and fund_pay_type is not null and cast(fund_pay_type as string) != ''                   THEN concat(cast(fund_pay_type as string), '不在fund_pay_type字典范围内') END,CASE WHEN poolarea_fund_pay_type not in ('310101','310102','310103','310201','310202','310203','310301','310302','310401','310501','310601','320101','320201','330101','330201','340101','340102','350101','350102','360101','360102','370101','37010101','37010102','370102','370201','390101','390102','390103','390201','390202','390301','390401','390501','390601','410101','510101','610101','610102','610103','610104','610105','610201','620100','620101','620102','630101','640101','640102','999102','999103','999104','999109','999110','999702','999703','999704','999705','999707','999708','999709','999710','999711','999712','999713','999714','999715','999716','999717','999718','999719','99999601','99999602','999997','99999701','99999702','99999703','99999706','99999707','99999708','99999709','99999710','99999711','99999712','99999713','99999714','99999715','99999716','99999717','99999718','99999719','99999720','99999738','99999739','99999741','99999742','99999745','99999746','99999748','99999751','99999752','99999753','99999754','99999756','999998','999999','999706','999720','999721','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','340103','999722','99999731','99999732','999111','330102','390104','99999757','99999758','360201','360301','99999759','99999760','390105','640103','640104','650101','650102','610106','610107','999723','999801','999802','999803','99999761','99999762','99999763','99999764','99999765','99999766','99999767','99999768','99999769','99999770','9999975302','9999975301','390701','390801','310701','999761','999762','999763','310801','310204','310205','320301','310206','620112','620122','620132','620142','3990101','660101','660102','31020401','31020402','99999771','99999778','99999779','610110','610111','310104','370301','99999799','910101','910102','310901','99999785','99999786','99999787','99999788','99999780','340301','999804','99999772','910103','999701','99999755','99999791','31020101','31020102','320300','399100','3991501','31020401','31020402','99999781','99999782','330103','320102','310105','310107','390107','320103','320104','31020103','31020104','9999981','999701','99999755') and poolarea_fund_pay_type is not null and cast(poolarea_fund_pay_type as string) != ''                   THEN concat(cast(poolarea_fund_pay_type as string), '不在poolarea_fund_pay_type字典范围内') END,CASE WHEN clr_type_lv2 not in ('3101','3102','3103','3104','3105','3106','3401','3402','3403','3901','3902','3903','3904','3905','5101','5102','9901','9999','999901','999902','999903','999904','999905','999992','999906','999907','999908','999909','999910','999911','999912','999913','999914','999915','999916','999917','999918','999919','999920','999921','999922','999923','999924','999925','999926','999927','999929','999930','999931','999932','999934','999936','999937','999938','999940','999942','999943','999944','999946','999952','999953','999956','999961','999962','999963','999971','999972','999973','999981','999982','999983','999991','6101','6102','999974','3906','999964','999965','999966','999967','999968','999969','999970','999975','999976','999977','999978','999979','999957','999958','999959','999984','999985','999986','99984','995101','995102','995103','995104','995105','995106','995107','995108','995109','995110','995111','995112','995113','995114','995115','995116','995117','995118','995119','995120','995121','995122','3107','3108','3109','3110','3501','3502','3503','3504','4102','5103','5104','999801','999802','310101','310201','310301','310401','310601','310102','310202','310302','310402','310602','310103','310203','310303','310403','310603','888801','888802','888805','888807','888808','888809','888813','888817','888818','888822','888828','888829','888831','888832','888833','888834','888835','888836','888837','888838','4103','3907','999988','999993','310304','390201','311101','311102','311201','311202','310305','390202','310104','310105','310106','390601','340301','710101','710102','710103','710104','710105','710106','710201','710202','710203','710204','710205','710206','999994','995126','995127','995128','995129','999999','999996','999997','999998','310409','3405','3406','3911','3913','3914','7131','7134','7139','993101','993102','993601','993602','995131','995132','995133','995134','995135','995136','995137','312102','312101','392102','392101','999391','530102','530202','310991','390991','310990','390990','311203','311204','995130','888839','888840','995139','995140','995141','995100','888841','99985','99901','99902','999806','999808','310411','390301','310412','390302','312801','312802','392801','392802','990101','990102','990103','990104','990105','399051','399052','399053','999804','999805','999806','999808','993903','993103','993904','993104','9988','9989','9990','9991','310306','390203','888842','888843','993907','993105','993106','993908','993909','999803','999933','999935','999945','999947','999955','999960','999980','999987','999941','999989','999990','9992','999954','999928','999939','999948','999949','999950','999951','993905','993906','993907','99950','999501','999502','888844','888845','99960','999001','888846','3505','3506','3507','999601','999602') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN clr_type_lv2 not in ('99966','99962','99963','99964','99965') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_fund_sbit_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:7805 Table not found 'longterm_org_fund_sbit_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_org_fund_sbit_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fund_pay_type not in ('310100','310101','310102','310200','310400','310500','320100','320200','330100','330200','340100','340200','350100','370100','370200','390100','390101','390102','390200','390300','390400','390500','410100','510100','610100','610200','620100','630100','640100','999106','999109','999110','999996','999997','99999701','99999702','99999705','99999706','99999707','99999708','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','99999731','99999732','99999733','99999734','99999735','99999737','99999747','99999748','99999749','99999750','99999751','99999752','99999753','999702','999703','999704','999706','390104','99999757','99999758','360200','360300','999998','310300','640103','640104','390103','650101','650102','610103','330102','610102','999111','99999765','99999766','99999767','99999768','99999769','99999770','390700','390801','310701','999761','999762','999763','310800','3990100','99999759','99999760','310103','99999771','99999778','99999779','610104','610105','99999780','340300','99999781','99999782','99999783','370300','99999790','910101','910102','310900','99999784','99999787','99999788','99999785','320300','999201','999202','99999772','310203','310302','350102','370201','390301','610107','640101','999103','999700','999701','999718','999900','99999602','99999703','99999741','99999756','310600','320400','340103','99999704','399100','620102','999705','999720','310301','390201','310601','310700','310802','320500','310107','390107','99999775','390501','9999981','99999704') and fund_pay_type is not null and cast(fund_pay_type as string) != ''                   THEN concat(cast(fund_pay_type as string), '不在fund_pay_type字典范围内') END,CASE WHEN poolarea_fund_pay_type not in ('310101','310102','310103','310201','310202','310203','310301','310302','310401','310501','310601','320101','320201','330101','330201','340101','340102','350101','350102','360101','360102','370101','37010101','37010102','370102','370201','390101','390102','390103','390201','390202','390301','390401','390501','390601','410101','510101','610101','610102','610103','610104','610105','610201','620100','620101','620102','630101','640101','640102','999102','999103','999104','999109','999110','999702','999703','999704','999705','999707','999708','999709','999710','999711','999712','999713','999714','999715','999716','999717','999718','999719','99999601','99999602','999997','99999701','99999702','99999703','99999706','99999707','99999708','99999709','99999710','99999711','99999712','99999713','99999714','99999715','99999716','99999717','99999718','99999719','99999720','99999738','99999739','99999741','99999742','99999745','99999746','99999748','99999751','99999752','99999753','99999754','99999756','999998','999999','999706','999720','999721','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','340103','999722','99999731','99999732','999111','330102','390104','99999757','99999758','360201','360301','99999759','99999760','390105','640103','640104','650101','650102','610106','610107','999723','999801','999802','999803','99999761','99999762','99999763','99999764','99999765','99999766','99999767','99999768','99999769','99999770','9999975302','9999975301','390701','390801','310701','999761','999762','999763','310801','310204','310205','320301','310206','620112','620122','620132','620142','3990101','660101','660102','31020401','31020402','99999771','99999778','99999779','610110','610111','310104','370301','99999799','910101','910102','310901','99999785','99999786','99999787','99999788','99999780','340301','999804','99999772','910103','999701','99999755','99999791','31020101','31020102','320300','399100','3991501','31020401','31020402','99999781','99999782','330103','320102','310105','310107','390107','320103','320104','31020103','31020104','9999981','999701','99999755') and poolarea_fund_pay_type is not null and cast(poolarea_fund_pay_type as string) != ''                   THEN concat(cast(poolarea_fund_pay_type as string), '不在poolarea_fund_pay_type字典范围内') END,CASE WHEN clr_type_lv2 not in ('3101','3102','3103','3104','3105','3106','3401','3402','3403','3901','3902','3903','3904','3905','5101','5102','9901','9999','999901','999902','999903','999904','999905','999992','999906','999907','999908','999909','999910','999911','999912','999913','999914','999915','999916','999917','999918','999919','999920','999921','999922','999923','999924','999925','999926','999927','999929','999930','999931','999932','999934','999936','999937','999938','999940','999942','999943','999944','999946','999952','999953','999956','999961','999962','999963','999971','999972','999973','999981','999982','999983','999991','6101','6102','999974','3906','999964','999965','999966','999967','999968','999969','999970','999975','999976','999977','999978','999979','999957','999958','999959','999984','999985','999986','99984','995101','995102','995103','995104','995105','995106','995107','995108','995109','995110','995111','995112','995113','995114','995115','995116','995117','995118','995119','995120','995121','995122','3107','3108','3109','3110','3501','3502','3503','3504','4102','5103','5104','999801','999802','310101','310201','310301','310401','310601','310102','310202','310302','310402','310602','310103','310203','310303','310403','310603','888801','888802','888805','888807','888808','888809','888813','888817','888818','888822','888828','888829','888831','888832','888833','888834','888835','888836','888837','888838','4103','3907','999988','999993','310304','390201','311101','311102','311201','311202','310305','390202','310104','310105','310106','390601','340301','710101','710102','710103','710104','710105','710106','710201','710202','710203','710204','710205','710206','999994','995126','995127','995128','995129','999999','999996','999997','999998','310409','3405','3406','3911','3913','3914','7131','7134','7139','993101','993102','993601','993602','995131','995132','995133','995134','995135','995136','995137','312102','312101','392102','392101','999391','530102','530202','310991','390991','310990','390990','311203','311204','995130','888839','888840','995139','995140','995141','995100','888841','99985','99901','99902','999806','999808','310411','390301','310412','390302','312801','312802','392801','392802','990101','990102','990103','990104','990105','399051','399052','399053','999804','999805','999806','999808','993903','993103','993904','993104','9988','9989','9990','9991','310306','390203','888842','888843','993907','993105','993106','993908','993909','999803','999933','999935','999945','999947','999955','999960','999980','999987','999941','999989','999990','9992','999954','999928','999939','999948','999949','999950','999951','993905','993906','993907','99950','999501','999502','888844','888845','99960','999001','888846','3505','3506','3507','999601','999602') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN clr_type_lv2 not in ('99966','99962','99963','99964','99965') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_fund_sbit_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_org_fund_sbit_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN fund_pay_type not in ('310100','310101','310102','310200','310400','310500','320100','320200','330100','330200','340100','340200','350100','370100','370200','390100','390101','390102','390200','390300','390400','390500','410100','510100','610100','610200','620100','630100','640100','999106','999109','999110','999996','999997','99999701','99999702','99999705','99999706','99999707','99999708','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','99999731','99999732','99999733','99999734','99999735','99999737','99999747','99999748','99999749','99999750','99999751','99999752','99999753','999702','999703','999704','999706','390104','99999757','99999758','360200','360300','999998','310300','640103','640104','390103','650101','650102','610103','330102','610102','999111','99999765','99999766','99999767','99999768','99999769','99999770','390700','390801','310701','999761','999762','999763','310800','3990100','99999759','99999760','310103','99999771','99999778','99999779','610104','610105','99999780','340300','99999781','99999782','99999783','370300','99999790','910101','910102','310900','99999784','99999787','99999788','99999785','320300','999201','999202','99999772','310203','310302','350102','370201','390301','610107','640101','999103','999700','999701','999718','999900','99999602','99999703','99999741','99999756','310600','320400','340103','99999704','399100','620102','999705','999720','310301','390201','310601','310700','310802','320500','310107','390107','99999775','390501','9999981','99999704') and fund_pay_type is not null and cast(fund_pay_type as string) != ''                   THEN concat(cast(fund_pay_type as string), '不在fund_pay_type字典范围内') END,CASE WHEN poolarea_fund_pay_type not in ('310101','310102','310103','310201','310202','310203','310301','310302','310401','310501','310601','320101','320201','330101','330201','340101','340102','350101','350102','360101','360102','370101','37010101','37010102','370102','370201','390101','390102','390103','390201','390202','390301','390401','390501','390601','410101','510101','610101','610102','610103','610104','610105','610201','620100','620101','620102','630101','640101','640102','999102','999103','999104','999109','999110','999702','999703','999704','999705','999707','999708','999709','999710','999711','999712','999713','999714','999715','999716','999717','999718','999719','99999601','99999602','999997','99999701','99999702','99999703','99999706','99999707','99999708','99999709','99999710','99999711','99999712','99999713','99999714','99999715','99999716','99999717','99999718','99999719','99999720','99999738','99999739','99999741','99999742','99999745','99999746','99999748','99999751','99999752','99999753','99999754','99999756','999998','999999','999706','999720','999721','99999721','99999722','99999723','99999724','99999725','99999726','99999727','99999728','99999729','99999730','340103','999722','99999731','99999732','999111','330102','390104','99999757','99999758','360201','360301','99999759','99999760','390105','640103','640104','650101','650102','610106','610107','999723','999801','999802','999803','99999761','99999762','99999763','99999764','99999765','99999766','99999767','99999768','99999769','99999770','9999975302','9999975301','390701','390801','310701','999761','999762','999763','310801','310204','310205','320301','310206','620112','620122','620132','620142','3990101','660101','660102','31020401','31020402','99999771','99999778','99999779','610110','610111','310104','370301','99999799','910101','910102','310901','99999785','99999786','99999787','99999788','99999780','340301','999804','99999772','910103','999701','99999755','99999791','31020101','31020102','320300','399100','3991501','31020401','31020402','99999781','99999782','330103','320102','310105','310107','390107','320103','320104','31020103','31020104','9999981','999701','99999755') and poolarea_fund_pay_type is not null and cast(poolarea_fund_pay_type as string) != ''                   THEN concat(cast(poolarea_fund_pay_type as string), '不在poolarea_fund_pay_type字典范围内') END,CASE WHEN clr_type_lv2 not in ('3101','3102','3103','3104','3105','3106','3401','3402','3403','3901','3902','3903','3904','3905','5101','5102','9901','9999','999901','999902','999903','999904','999905','999992','999906','999907','999908','999909','999910','999911','999912','999913','999914','999915','999916','999917','999918','999919','999920','999921','999922','999923','999924','999925','999926','999927','999929','999930','999931','999932','999934','999936','999937','999938','999940','999942','999943','999944','999946','999952','999953','999956','999961','999962','999963','999971','999972','999973','999981','999982','999983','999991','6101','6102','999974','3906','999964','999965','999966','999967','999968','999969','999970','999975','999976','999977','999978','999979','999957','999958','999959','999984','999985','999986','99984','995101','995102','995103','995104','995105','995106','995107','995108','995109','995110','995111','995112','995113','995114','995115','995116','995117','995118','995119','995120','995121','995122','3107','3108','3109','3110','3501','3502','3503','3504','4102','5103','5104','999801','999802','310101','310201','310301','310401','310601','310102','310202','310302','310402','310602','310103','310203','310303','310403','310603','888801','888802','888805','888807','888808','888809','888813','888817','888818','888822','888828','888829','888831','888832','888833','888834','888835','888836','888837','888838','4103','3907','999988','999993','310304','390201','311101','311102','311201','311202','310305','390202','310104','310105','310106','390601','340301','710101','710102','710103','710104','710105','710106','710201','710202','710203','710204','710205','710206','999994','995126','995127','995128','995129','999999','999996','999997','999998','310409','3405','3406','3911','3913','3914','7131','7134','7139','993101','993102','993601','993602','995131','995132','995133','995134','995135','995136','995137','312102','312101','392102','392101','999391','530102','530202','310991','390991','310990','390990','311203','311204','995130','888839','888840','995139','995140','995141','995100','888841','99985','99901','99902','999806','999808','310411','390301','310412','390302','312801','312802','392801','392802','990101','990102','990103','990104','990105','399051','399052','399053','999804','999805','999806','999808','993903','993103','993904','993104','9988','9989','9990','9991','310306','390203','888842','888843','993907','993105','993106','993908','993909','999803','999933','999935','999945','999947','999955','999960','999980','999987','999941','999989','999990','9992','999954','999928','999939','999948','999949','999950','999951','993905','993906','993907','99950','999501','999502','888844','888845','99960','999001','888846','3505','3506','3507','999601','999602') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN clr_type_lv2 not in ('99966','99962','99963','99964','99965') and clr_type_lv2 is not null and cast(clr_type_lv2 as string) != ''                   THEN concat(cast(clr_type_lv2 as string), '不在clr_type_lv2字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_fund_sbit_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:7805 Table not found 'longterm_org_fund_sbit_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:32:41 - Query-14 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.53秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'org_cred_rep_apply_reg_info_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN appy_stas not in ('0','1','2','3','4','5','6','7','8') and appy_stas is not null and cast(appy_stas as string) != ''                   THEN concat(cast(appy_stas as string), '不在appy_stas字典范围内') END) as error_array     FROM scs_cd_data_hudi.org_cred_rep_apply_reg_info_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'org_cred_rep_apply_reg_info_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN appy_stas not in ('0','1','2','3','4','5','6','7','8') and appy_stas is not null and cast(appy_stas as string) != ''                   THEN concat(cast(appy_stas as string), '不在appy_stas字典范围内') END) as error_array     FROM scs_cd_data_hudi.org_cred_rep_apply_reg_info_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:193 Invalid table alias or column reference 'rid': (possible column names are: cred_rep_appy_reg_id, appy_stas, appy_dscr, slicetag, gx_op_code, gx_op_time)
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'org_cred_rep_apply_reg_info_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN appy_stas not in ('0','1','2','3','4','5','6','7','8') and appy_stas is not null and cast(appy_stas as string) != ''                   THEN concat(cast(appy_stas as string), '不在appy_stas字典范围内') END) as error_array     FROM scs_cd_data_hudi.org_cred_rep_apply_reg_info_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'org_cred_rep_apply_reg_info_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN appy_stas not in ('0','1','2','3','4','5','6','7','8') and appy_stas is not null and cast(appy_stas as string) != ''                   THEN concat(cast(appy_stas as string), '不在appy_stas字典范围内') END) as error_array     FROM scs_cd_data_hudi.org_cred_rep_apply_reg_info_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:193 Invalid table alias or column reference 'rid': (possible column names are: cred_rep_appy_reg_id, appy_stas, appy_dscr, slicetag, gx_op_code, gx_op_time)
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:32:42 - Query-15 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 0.31秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_eval_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN eval_psn_type not in ('01','02','03') and eval_psn_type is not null and cast(eval_psn_type as string) != ''                   THEN concat(cast(eval_psn_type as string), '不在eval_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_eval_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_plan_eval_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN eval_psn_type not in ('01','02','03') and eval_psn_type is not null and cast(eval_psn_type as string) != ''                   THEN concat(cast(eval_psn_type as string), '不在eval_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_eval_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:678 Table not found 'longterm_plan_eval_psn_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_eval_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN eval_psn_type not in ('01','02','03') and eval_psn_type is not null and cast(eval_psn_type as string) != ''                   THEN concat(cast(eval_psn_type as string), '不在eval_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_eval_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_plan_eval_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN eval_psn_type not in ('01','02','03') and eval_psn_type is not null and cast(eval_psn_type as string) != ''                   THEN concat(cast(eval_psn_type as string), '不在eval_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_eval_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:678 Table not found 'longterm_plan_eval_psn_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:34:48 - Query-16 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 126.53秒
平均响应时间: 126.53秒
最小响应时间: 126.53秒
最大响应时间: 126.53秒
中位数响应时间: 126.53秒
95百分位响应时间: 126.53秒

=== 2025-05-29 16:34:50 - Query-17 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.30秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_servitem_cate_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN nurscare_psn_type not in ('01','02','03','04','05','99','06','07','08','09') and nurscare_psn_type is not null and cast(nurscare_psn_type as string) != ''                   THEN concat(cast(nurscare_psn_type as string), '不在nurscare_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_servitem_cate_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_servitem_cate_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN nurscare_psn_type not in ('01','02','03','04','05','99','06','07','08','09') and nurscare_psn_type is not null and cast(nurscare_psn_type as string) != ''                   THEN concat(cast(nurscare_psn_type as string), '不在nurscare_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_servitem_cate_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:738 Table not found 'longterm_servitem_cate_info_b_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_servitem_cate_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN nurscare_psn_type not in ('01','02','03','04','05','99','06','07','08','09') and nurscare_psn_type is not null and cast(nurscare_psn_type as string) != ''                   THEN concat(cast(nurscare_psn_type as string), '不在nurscare_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_servitem_cate_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_servitem_cate_info_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN nurscare_psn_type not in ('01','02','03','04','05','99','06','07','08','09') and nurscare_psn_type is not null and cast(nurscare_psn_type as string) != ''                   THEN concat(cast(nurscare_psn_type as string), '不在nurscare_psn_type字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_servitem_cate_info_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:738 Table not found 'longterm_servitem_cate_info_b_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:34:51 - Query-18 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.31秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'item_det_detail_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN clr_type not in ('11','12','14','15','18','21','27','28','41','51','53','99','9907','9908','9909','9914','9925','9949','9950','9972','9974','9975','9977','9978','9985','9986','9987','99914','99915','99922','99923','99927','99928','99933','99934','99938','99939','99940','99943','99945','99946','99948','99949','99950','99952','99953','99958','99959','99960','99961','99962','99963','99967','99968','99970','99971','99972','9901','9902','9904','9910','9911','9912','9913','9916','9917','9918','9919','9920','9921','9922','9923','9924','9926','9927','99973','99974','9952','99975','99976','99977','99978','99979','99980','99981','99982','99983','99984','99985','99986','99987','99992','99993','99988','99989','99990','99991','9994','9995','99951','4101','991','992','994','996','997','999','99588','99589','99994','1401','1402','999391','530202','530102','3401','3402','16','9988','9989','9990','9991','99995','99996','99997','99998','9903','9905','9906','9928','9929','999841','999842','99931','9953','4102','4103','999881','999891','999901','888806','888807','110104','999991','99964','99965','100001','100002','100003') and clr_type is not null and cast(clr_type as string) != ''                   THEN concat(cast(clr_type as string), '不在clr_type字典范围内') END) as error_array     FROM scs_cd_data_hudi.item_det_detail_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'item_det_detail_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN clr_type not in ('11','12','14','15','18','21','27','28','41','51','53','99','9907','9908','9909','9914','9925','9949','9950','9972','9974','9975','9977','9978','9985','9986','9987','99914','99915','99922','99923','99927','99928','99933','99934','99938','99939','99940','99943','99945','99946','99948','99949','99950','99952','99953','99958','99959','99960','99961','99962','99963','99967','99968','99970','99971','99972','9901','9902','9904','9910','9911','9912','9913','9916','9917','9918','9919','9920','9921','9922','9923','9924','9926','9927','99973','99974','9952','99975','99976','99977','99978','99979','99980','99981','99982','99983','99984','99985','99986','99987','99992','99993','99988','99989','99990','99991','9994','9995','99951','4101','991','992','994','996','997','999','99588','99589','99994','1401','1402','999391','530202','530102','3401','3402','16','9988','9989','9990','9991','99995','99996','99997','99998','9903','9905','9906','9928','9929','999841','999842','99931','9953','4102','4103','999881','999891','999901','888806','888807','110104','999991','99964','99965','100001','100002','100003') and clr_type is not null and cast(clr_type as string) != ''                   THEN concat(cast(clr_type as string), '不在clr_type字典范围内') END) as error_array     FROM scs_cd_data_hudi.item_det_detail_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:179 Invalid table alias or column reference 'rid': (possible column names are: item_det_detail_id, clr_ym, clr_type, admdvs, hi_item_name, hi_item_code, medins_code, medins_name, mdtrt_id, item_sum_fee, item_pre_det_cnt, item_recheck_det_cnt, item_pre_det_amt, item_withhold_amt, item_recheck_det_amt, violation_rule_name, slicetag, gx_op_code, gx_op_time)
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'item_det_detail_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN clr_type not in ('11','12','14','15','18','21','27','28','41','51','53','99','9907','9908','9909','9914','9925','9949','9950','9972','9974','9975','9977','9978','9985','9986','9987','99914','99915','99922','99923','99927','99928','99933','99934','99938','99939','99940','99943','99945','99946','99948','99949','99950','99952','99953','99958','99959','99960','99961','99962','99963','99967','99968','99970','99971','99972','9901','9902','9904','9910','9911','9912','9913','9916','9917','9918','9919','9920','9921','9922','9923','9924','9926','9927','99973','99974','9952','99975','99976','99977','99978','99979','99980','99981','99982','99983','99984','99985','99986','99987','99992','99993','99988','99989','99990','99991','9994','9995','99951','4101','991','992','994','996','997','999','99588','99589','99994','1401','1402','999391','530202','530102','3401','3402','16','9988','9989','9990','9991','99995','99996','99997','99998','9903','9905','9906','9928','9929','999841','999842','99931','9953','4102','4103','999881','999891','999901','888806','888807','110104','999991','99964','99965','100001','100002','100003') and clr_type is not null and cast(clr_type as string) != ''                   THEN concat(cast(clr_type as string), '不在clr_type字典范围内') END) as error_array     FROM scs_cd_data_hudi.item_det_detail_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'item_det_detail_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN clr_type not in ('11','12','14','15','18','21','27','28','41','51','53','99','9907','9908','9909','9914','9925','9949','9950','9972','9974','9975','9977','9978','9985','9986','9987','99914','99915','99922','99923','99927','99928','99933','99934','99938','99939','99940','99943','99945','99946','99948','99949','99950','99952','99953','99958','99959','99960','99961','99962','99963','99967','99968','99970','99971','99972','9901','9902','9904','9910','9911','9912','9913','9916','9917','9918','9919','9920','9921','9922','9923','9924','9926','9927','99973','99974','9952','99975','99976','99977','99978','99979','99980','99981','99982','99983','99984','99985','99986','99987','99992','99993','99988','99989','99990','99991','9994','9995','99951','4101','991','992','994','996','997','999','99588','99589','99994','1401','1402','999391','530202','530102','3401','3402','16','9988','9989','9990','9991','99995','99996','99997','99998','9903','9905','9906','9928','9929','999841','999842','99931','9953','4102','4103','999881','999891','999901','888806','888807','110104','999991','99964','99965','100001','100002','100003') and clr_type is not null and cast(clr_type as string) != ''                   THEN concat(cast(clr_type as string), '不在clr_type字典范围内') END) as error_array     FROM scs_cd_data_hudi.item_det_detail_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10004]: Line 1:179 Invalid table alias or column reference 'rid': (possible column names are: item_det_detail_id, clr_ym, clr_type, admdvs, hi_item_name, hi_item_code, medins_code, medins_name, mdtrt_id, item_sum_fee, item_pre_det_cnt, item_recheck_det_cnt, item_pre_det_amt, item_withhold_amt, item_recheck_det_amt, violation_rule_name, slicetag, gx_op_code, gx_op_time)
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:40:58 - Query-19 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 367.25秒
平均响应时间: 367.25秒
最小响应时间: 367.25秒
最大响应时间: 367.25秒
中位数响应时间: 367.25秒
95百分位响应时间: 367.25秒

=== 2025-05-29 16:42:14 - Query-20 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 76.33秒
平均响应时间: 76.33秒
最小响应时间: 76.33秒
最大响应时间: 76.33秒
中位数响应时间: 76.33秒
95百分位响应时间: 76.33秒

=== 2025-05-29 16:43:23 - Query-21 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 68.75秒
平均响应时间: 68.75秒
最小响应时间: 68.75秒
最大响应时间: 68.75秒
中位数响应时间: 68.75秒
95百分位响应时间: 68.75秒

=== 2025-05-29 16:43:25 - Query-22 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.38秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_org_serv_type_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN longterm_serv_type_no not in ('01','02','03','04','05') and longterm_serv_type_no is not null and cast(longterm_serv_type_no as string) != ''                   THEN concat(cast(longterm_serv_type_no as string), '不在longterm_serv_type_no字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_serv_type_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_org_serv_type_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN longterm_serv_type_no not in ('01','02','03','04','05') and longterm_serv_type_no is not null and cast(longterm_serv_type_no as string) != ''                   THEN concat(cast(longterm_serv_type_no as string), '不在longterm_serv_type_no字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_serv_type_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:728 Table not found 'longterm_org_serv_type_b_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_org_serv_type_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN longterm_serv_type_no not in ('01','02','03','04','05') and longterm_serv_type_no is not null and cast(longterm_serv_type_no as string) != ''                   THEN concat(cast(longterm_serv_type_no as string), '不在longterm_serv_type_no字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_serv_type_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_org_serv_type_b_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN longterm_serv_type_no not in ('01','02','03','04','05') and longterm_serv_type_no is not null and cast(longterm_serv_type_no as string) != ''                   THEN concat(cast(longterm_serv_type_no as string), '不在longterm_serv_type_no字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_org_serv_type_b_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:728 Table not found 'longterm_org_serv_type_b_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:43:26 - Query-23 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.33秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_rcmd_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_rcmd_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_plan_rcmd_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_rcmd_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:471 Table not found 'longterm_plan_rcmd_psn_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_rcmd_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_rcmd_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_plan_rcmd_psn_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_rcmd_psn_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:471 Table not found 'longterm_plan_rcmd_psn_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:43:28 - Query-24 ===
总请求数: 1
成功请求数: 0
失败请求数: 1
总耗时: 1.43秒

错误信息: 开始设置队列:SET mapreduce.job.queuename=data_static
开始设置数据库:use drsjcx
开始设置运行参数:set hive.tez.container.size=16384
开始设置运行参数:
set tez.task.resource.memory.mb=16384
开始设置运行参数:
set tez.am.resource.memory.mb=16384
开始设置运行参数:
set hive.groupby.skewindata=false
提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_serv_ext_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN cert_type not in ('01','02','03','0201','0202','0203','0204','0205','0206','0207','0208','0209','04') and cert_type is not null and cast(cert_type as string) != ''                   THEN concat(cast(cert_type as string), '不在cert_type字典范围内') END,CASE WHEN gend not in ('0','1','2','9') and gend is not null and cast(gend as string) != ''                   THEN concat(cast(gend as string), '不在gend字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_serv_ext_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
执行中.........
insert into drsjcx.data_qua_detail SELECT      'longterm_plan_serv_ext_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN cert_type not in ('01','02','03','0201','0202','0203','0204','0205','0206','0207','0208','0209','04') and cert_type is not null and cast(cert_type as string) != ''                   THEN concat(cast(cert_type as string), '不在cert_type字典范围内') END,CASE WHEN gend not in ('0','1','2','9') and gend is not null and cast(gend as string) != ''                   THEN concat(cast(gend as string), '不在gend字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_serv_ext_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:889 Table not found 'longterm_plan_serv_ext_d_paimon'
getQueryLog end error!
------------------------------------------------------------------
详细日志:
- 开始设置队列:SET mapreduce.job.queuename=data_static
- 开始设置数据库:use drsjcx
- 开始设置运行参数:set hive.tez.container.size=16384
- 开始设置运行参数:
- set tez.task.resource.memory.mb=16384
- 开始设置运行参数:
- set tez.am.resource.memory.mb=16384
- 开始设置运行参数:
- set hive.groupby.skewindata=false
- 提交语句:insert into drsjcx.data_qua_detail SELECT      'longterm_plan_serv_ext_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN cert_type not in ('01','02','03','0201','0202','0203','0204','0205','0206','0207','0208','0209','04') and cert_type is not null and cast(cert_type as string) != ''                   THEN concat(cast(cert_type as string), '不在cert_type字典范围内') END,CASE WHEN gend not in ('0','1','2','9') and gend is not null and cast(gend as string) != ''                   THEN concat(cast(gend as string), '不在gend字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_serv_ext_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- 执行中.........
- insert into drsjcx.data_qua_detail SELECT      'longterm_plan_serv_ext_d_paimon' as table_name,     '数据字典校验' as rule,     drty_desc,     primary_key FROM (     SELECT          concat(cast(rid as string), '-', cast(crte_time as string)) as primary_key,         array(CASE WHEN cert_type not in ('01','02','03','0201','0202','0203','0204','0205','0206','0207','0208','0209','04') and cert_type is not null and cast(cert_type as string) != ''                   THEN concat(cast(cert_type as string), '不在cert_type字典范围内') END,CASE WHEN gend not in ('0','1','2','9') and gend is not null and cast(gend as string) != ''                   THEN concat(cast(gend as string), '不在gend字典范围内') END,CASE WHEN vali_flag not in ('0','1') and vali_flag is not null and cast(vali_flag as string) != ''                   THEN concat(cast(vali_flag as string), '不在vali_flag字典范围内') END) as error_array     FROM scs_cd_data_hudi.longterm_plan_serv_ext_d_paimon     WHERE gx_op_code != '2'      AND gx_op_code != 'D' ) t LATERAL VIEW EXPLODE(error_array) error_table AS drty_desc WHERE drty_desc is not null
- Error while compiling statement: FAILED: SemanticException [Error 10001]: Line 1:889 Table not found 'longterm_plan_serv_ext_d_paimon'
- getQueryLog end error!
- ------------------------------------------------------------------

=== 2025-05-29 16:45:44 - Query-25 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 136.13秒
平均响应时间: 136.13秒
最小响应时间: 136.13秒
最大响应时间: 136.13秒
中位数响应时间: 136.13秒
95百分位响应时间: 136.13秒

=== 2025-05-29 16:47:52 - Query-26 ===
总请求数: 1
成功请求数: 1
失败请求数: 0
总耗时: 128.16秒
平均响应时间: 128.16秒
最小响应时间: 128.16秒
最大响应时间: 128.16秒
中位数响应时间: 128.16秒
95百分位响应时间: 128.16秒
