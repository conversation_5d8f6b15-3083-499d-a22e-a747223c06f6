createtab_stmt,
CREATE EXTERNAL TABLE `fixmedins_b_paimon`(,
  `fixmedins_code` string COMMENT 'from deserializer', ,
  `medins_mgtcode` string COMMENT 'from deserializer', ,
  `uscc` string COMMENT 'from deserializer', ,
  `orgcode` string COMMENT 'from deserializer', ,
  `fix_blng_admdvs` string COMMENT 'from deserializer', ,
  `fixmedins_name` string COMMENT 'from deserializer', ,
  `fixmedins_type` string COMMENT 'from deserializer', ,
  `hosp_lv` string COMMENT 'from deserializer', ,
  `lmtpric_hosp_lv` string COMMENT 'from deserializer', ,
  `dedc_hosp_lv` string COMMENT 'from deserializer', ,
  `begntime` timestamp COMMENT 'from deserializer', ,
  `endtime` timestamp COMMENT 'from deserializer', ,
  `hi_resper_name` string COMMENT 'from deserializer', ,
  `hi_resper_tel` string COMMENT 'from deserializer', ,
  `hi_resper_cert_type` string COMMENT 'from deserializer', ,
  `hi_resper_certno` string COMMENT 'from deserializer', ,
  `out_fixmedins_type` string COMMENT 'from deserializer', ,
  `fix_onln_open_flag` string COMMENT 'from deserializer', ,
  `out_onln_open_type` string COMMENT 'from deserializer', ,
  `nat_plaf_code` string COMMENT 'from deserializer', ,
  `prov_plaf_code` string COMMENT 'from deserializer', ,
  `vali_flag` string COMMENT 'from deserializer', ,
  `memo` string COMMENT 'from deserializer', ,
  `rid` string COMMENT 'from deserializer', ,
  `updt_time` timestamp COMMENT 'from deserializer', ,
  `crter_id` string COMMENT 'from deserializer', ,
  `crter_name` string COMMENT 'from deserializer', ,
  `crte_time` timestamp COMMENT 'from deserializer', ,
  `crte_optins_no` string COMMENT 'from deserializer', ,
  `opter_id` string COMMENT 'from deserializer', ,
  `opter_name` string COMMENT 'from deserializer', ,
  `opt_time` timestamp COMMENT 'from deserializer', ,
  `optins_no` string COMMENT 'from deserializer', ,
  `poolarea_no` string COMMENT 'from deserializer', ,
  `slicetag` string COMMENT 'from deserializer', ,
  `gx_op_code` string COMMENT 'from deserializer', ,
  `gx_op_time` timestamp COMMENT 'from deserializer'),
ROW FORMAT SERDE ,
  'org.apache.paimon.hive.PaimonSerDe' ,
STORED BY ,
  'org.apache.paimon.hive.PaimonStorageHandler' ,
WITH SERDEPROPERTIES ( ,
  'serialization.format'='1'),
LOCATION,
  'hdfs://ns1/domain/ns1/scs_cd_data_hudi/hd_fixmedins_b',
TBLPROPERTIES (,
  'bucketing_version'='2', ,
  'transient_lastDdlTime'='1738919760'),
