import pandas as pd
import os
import re
from datetime import datetime

def add_paimon_suffix(sql):
    """为SQL中的所有表名添加_paimon后缀"""
    # 查找FROM和JOIN后面的表名
    table_pattern = r'(?i)(FROM|JOIN)\s+([A-Za-z0-9_\.]+)'
    
    def replace_table(match):
        clause = match.group(1)  # FROM 或 JOIN
        table = match.group(2)   # 表名
        
        # 如果表名已经以_paimon结尾，则不添加
        if not table.lower().endswith('_paimon'):
            # 如果表名包含schema，需要分别处理
            if '.' in table:
                schema, table_name = table.rsplit('.', 1)
                return f"{clause} {schema}.{table_name}_paimon"
            else:
                return f"{clause} {table}_paimon"
        return match.group(0)
    
    # 处理主查询和子查询中的表名
    sql = re.sub(table_pattern, replace_table, sql)
    
    # 处理INSERT INTO语句中的表名
    insert_pattern = r'(?i)INSERT\s+INTO\s+([A-Za-z0-9_\.]+)'
    sql = re.sub(insert_pattern, lambda m: f"INSERT INTO {m.group(1)}", sql)
    
    return sql

def get_opposite_description(desc):
    """将规则描述转换为反义"""
    if not isinstance(desc, str):
        return "数据异常"
        
    # 替换比较运算符
    opposite_map = {
        '=': '<>',
        '<>': '=',
        '!=': '=',
        '>': '<=',
        '>=': '<',
        '<': '>=',
        '<=': '>',
        '存在': '不存在',
        '不存在': '存在',
        '为空': '不为空',
        '不为空': '为空',
        'IS NULL': 'IS NOT NULL',
        'IS NOT NULL': 'IS NULL',
        'IN': 'NOT IN',
        'NOT IN': 'IN',
        'EXISTS': 'NOT EXISTS',
        'NOT EXISTS': 'EXISTS'
    }
    
    result = desc
    for k, v in opposite_map.items():
        result = result.replace(k, v)
    
    return result

def process_subquery(sql):
    """处理子查询中的表名和函数"""
    # 替换所有IFNULL/NVL为COALESCE
    sql = re.sub(r'(?i)IFNULL\(', 'COALESCE(', sql)
    sql = re.sub(r'(?i)NVL\(', 'COALESCE(', sql)
    
    # 为子查询中的表添加_paimon后缀
    return add_paimon_suffix(sql)

def get_main_table(sql):
    """获取SQL中的主表名"""
    # 移除注释
    sql = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    
    # 处理子查询
    if re.search(r'FROM\s*\(', sql, re.IGNORECASE):
        # 如果是子查询，递归处理内部SQL
        subquery_match = re.search(r'FROM\s*\((.*?)\)\s*(?:AS\s+)?(\w+)', sql, re.IGNORECASE | re.DOTALL)
        if subquery_match:
            inner_sql = subquery_match.group(1)
            return get_main_table(inner_sql)
    
    # 查找FROM后的第一个表名
    from_match = re.search(r'FROM\s+([A-Za-z0-9_\.]+)(?:\s+(?:AS\s+)?(\w+))?', sql, re.IGNORECASE)
    if from_match:
        table_name = from_match.group(1)
        # 如果有schema，只返回表名部分
        if '.' in table_name:
            return table_name.split('.')[-1]
        return table_name
    return None

def transform_check_sql(table_name, check_sql, rule_desc):
    """转换检查SQL为目标格式"""
    try:
        if not isinstance(check_sql, str) or not check_sql.strip():
            return None
            
        # 处理子查询和函数
        processed_sql = process_subquery(check_sql)
        
        # 获取主表名
        main_table = get_main_table(processed_sql)
        if main_table:
            table_name = main_table
            
        # 转换表名为小写并添加后缀
        table_name_lower = table_name.lower() + '_paimon'
        
        # 检查是否是多表关联的聚合查询
        if re.search(r'GROUP\s+BY.*HAVING', processed_sql, re.IGNORECASE | re.DOTALL):
            # 提取子查询中的表和字段信息
            subquery_match = re.search(r'SELECT\s+(.*?)\s+FROM\s+(.*?)\s+WHERE\s+(.*?)\s+GROUP\s+BY\s+(.*?)\s+HAVING\s+(.*?)\s*(?:\)|$)', processed_sql, re.IGNORECASE | re.DOTALL)
            if subquery_match:
                select_cols = subquery_match.group(1).strip()
                from_tables = subquery_match.group(2).strip()
                where_cond = subquery_match.group(3).strip()
                group_by_cols = subquery_match.group(4).strip()
                having_cond = subquery_match.group(5).strip()
                
                # 解析表名和别名
                tables = {}
                for table_def in re.finditer(r'([A-Za-z0-9_\.]+)\s+(?:AS\s+)?([A-Za-z0-9_]+)?', from_tables):
                    table_name = table_def.group(1)
                    alias = table_def.group(2) or table_name
                    tables[alias] = table_name.lower() + '_paimon'
                
                # 构建新的SQL
                # 1. 提取需要聚合的字段
                agg_fields = re.findall(r'SUM\((.*?)\)', select_cols, re.IGNORECASE)
                if agg_fields:
                    agg_field = agg_fields[0].strip()
                    
                    # 2. 提取比较字段
                    comp_fields = re.findall(r'([A-Za-z0-9_\.]+)\s*!=\s*([A-Za-z0-9_\.]+)', having_cond)
                    if comp_fields:
                        field1, field2 = comp_fields[0]
                        
                        # 3. 构建子查询
                        join_conditions = []
                        for cond in where_cond.split('AND'):
                            if 'VALI_FLAG' not in cond.upper():
                                join_conditions.append(cond.strip())
                        
                        transformed_sql = f"""insert into drsjcx.data_qua_detail 
SELECT '{table_name_lower}' as table_name,
'金额校验' as rule,
'{get_opposite_description(rule_desc)}' as drty_desc,
concat('rid=',cast(a.rid as string),'and crte_time=',cast(a.crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.{table_name_lower} a 
left join (
    select {','.join(col.strip() for col in group_by_cols.split(','))},
    sum({agg_field}) as sumacct 
    from scs_cd_data_hudi.{tables['B']} 
    where vali_flag='1'
    group by {group_by_cols}
) b on {' and '.join(f'a.{cond.split("=")[0].strip()} = b.{cond.split("=")[1].strip()}' for cond in join_conditions)}
where a.vali_flag = '1' and a.{field1.split('.')[-1]}<>b.sumacct"""
                        
                        return transformed_sql
            
        # 如果不是多表关联的聚合查询，使用原有逻辑
        where_condition = None
        where_match = re.search(r'WHERE\s+(.+?)(?:GROUP\s+BY|ORDER\s+BY|HAVING|$)', processed_sql, re.IGNORECASE | re.DOTALL)
        if where_match:
            where_condition = where_match.group(1).strip()
        else:
            condition_match = re.search(r'FROM\s+[^\s]+\s+(?:WHERE\s+)?(.+?)(?:GROUP\s+BY|ORDER\s+BY|$)', processed_sql, re.IGNORECASE | re.DOTALL)
            if condition_match:
                where_condition = condition_match.group(1).strip()
            else:
                print(f"警告: 无法在SQL中找到条件: {processed_sql}")
                return None
        
        # 生成转换后的SQL
        transformed_sql = f"""insert into drsjcx.data_qua_detail 
SELECT '{table_name_lower}' as table_name,
'金额校验' as rule,
'{get_opposite_description(rule_desc)}' as drty_desc,
concat('rid=',cast(rid as string),'and crte_time=',cast(crte_time as string)) as Primary_key
FROM scs_cd_data_hudi.{table_name_lower}
WHERE {where_condition}"""
        
        return transformed_sql
        
    except Exception as e:
        print(f"转换SQL时发生错误: {str(e)}")
        print(f"表名: {table_name}")
        print(f"原SQL: {check_sql}")
        print(f"规则描述: {rule_desc}")
        return None

def main():
    try:
        # 输入和输出文件路径
        input_file = "成都/金额质检.xlsx"
        output_file = "成都/金额质检结果.xlsx"
        
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file, engine='openpyxl')
        
        print(f"共读取 {len(df)} 条记录")
        
        # 确保存在必要的列
        required_columns = ['表英文名', '规则描述', '校验脚本']
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            raise ValueError(f"缺少必要的列: {', '.join(missing_cols)}")
        
        # 添加新列
        if '转换后脚本-陈路' not in df.columns:
            df['转换后脚本-陈路'] = None
        
        # 转换SQL
        print("正在转换SQL...")
        for index, row in df.iterrows():
            print(f"\n处理第 {index + 1} 条记录:")
            table_name = row['表英文名']
            check_sql = row['校验脚本']
            rule_desc = row.get('规则描述', '')
            
            if pd.isna(table_name) or pd.isna(check_sql):
                print(f"跳过空记录: 表名={table_name}, SQL={check_sql}")
                continue
            
            print(f"表名: {table_name}")
            print(f"原SQL: {check_sql}")
            print(f"规则描述: {rule_desc}")
            
            transformed_sql = transform_check_sql(table_name, check_sql, rule_desc)
            if transformed_sql:
                # 更新转换后脚本-陈路列
                df.at[index, '转换后脚本-陈路'] = transformed_sql
                print("转换成功!")
            else:
                print("转换失败，保留原值")
        
        # 写入Excel文件
        print(f"\n正在写入结果到文件: {output_file}")
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
            df.to_excel(writer, index=False, sheet_name='Sheet1')
            
            # 获取工作表
            worksheet = writer.sheets['Sheet1']
            
            # 调整列宽以适应内容
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                )
                worksheet.column_dimensions[chr(65 + idx)].width = min(max_length + 2, 100)
        
        print("转换完成！")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main() 