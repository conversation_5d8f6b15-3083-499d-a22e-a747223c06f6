import os
import sqlparse
from typing import Set, List, Union, Tuple
from sqlparse.sql import TokenList, Token, Identifier, IdentifierList, Parenthesis, Where, Function
from sqlparse.tokens import Keyword, DML, Punctuation, Whitespace, Name


def clean_table_name(name: str) -> str:
    """
    清理表名，去除别名和schema前缀。

    Args:
        name: 原始表名

    Returns:
        str: 清理后的表名
    """
    # 去除引号和空白
    name = name.strip('`"\' ')
    
    # 处理schema.table格式
    if '.' in name:
        parts = name.split('.')
        name = parts[-1].strip('`"\' ')
    
    # 去除别名（如果有）
    parts = name.split()
    name = parts[0].strip('`"\' ')
    
    # 如果名字看起来像子查询别名，返回空
    if name.endswith(')') or name == 'C' or 'TMP' in name:
        return ''
        
    return name


def get_table_name(identifier: Union[Identifier, Token]) -> str:
    """
    从标识符中提取表名。

    Args:
        identifier: SQL标识符或Token

    Returns:
        str: 提取的表名
    """
    # 如果是Token，直接返回其值
    if isinstance(identifier, Token):
        return clean_table_name(identifier.value)
    
    # 如果是Identifier，需要处理可能的schema和别名
    parts = []
    for token in identifier.tokens:
        if not token.is_whitespace and token.ttype not in (Keyword, Punctuation):
            # 去掉引号
            value = token.value.strip('`"\' ')
            if value.lower() not in ('as', 'on', 'using', 'where', 'group', 'order', 'having'):
                parts.append(value)
    
    # 如果有schema (例如: schema.table)
    if len(parts) > 1 and '.' in ''.join(parts):
        # 找到包含点号的部分
        for part in parts:
            if '.' in part:
                return clean_table_name(part)
        return clean_table_name(parts[-1])
    
    # 如果有别名 (例如: table alias 或 table AS alias)
    if len(parts) > 1:
        return clean_table_name(parts[0])
    
    return clean_table_name(parts[0]) if parts else ''


def extract_tables_from_statement(statement: TokenList) -> Set[str]:
    """
    从SQL语句中提取所有表名。

    Args:
        statement: SQL语句的TokenList对象

    Returns:
        Set[str]: 包含所有唯一表名的集合
    """
    tables = set()
    
    def process_token(token):
        """递归处理token"""
        nonlocal tables
        
        # 如果是子查询或括号内的内容，递归处理
        if isinstance(token, (Parenthesis, Function)):
            for t in token.tokens:
                process_token(t)
            return
            
        # 如果是Where子句，递归处理
        if isinstance(token, Where):
            for t in token.tokens:
                process_token(t)
            return
            
        # 如果是标识符列表，处理每个标识符
        if isinstance(token, IdentifierList):
            for identifier in token.get_identifiers():
                process_token(identifier)
            return
            
        # 如果是标识符，可能是表名
        if isinstance(token, Identifier):
            # 检查是否在FROM或JOIN后面
            idx = token.parent.token_index(token)
            prev_token = None
            for i in range(idx-1, -1, -1):
                if not token.parent.tokens[i].is_whitespace:
                    prev_token = token.parent.tokens[i]
                    break
                    
            if (prev_token and prev_token.ttype is Keyword and 
                prev_token.value.upper() in ('FROM', 'JOIN', 'INTO', 'UPDATE')):
                table_name = get_table_name(token)
                if table_name:
                    tables.add(table_name)
            return
            
        # 如果是TokenList，递归处理
        if isinstance(token, TokenList):
            for t in token.tokens:
                process_token(t)
                
    # 处理语句中的所有token
    for token in statement.tokens:
        process_token(token)
        
    return tables


def extract_table_names_from_sql(sql_file_path: str) -> List[str]:
    """
    从SQL文件中提取所有表名。

    Args:
        sql_file_path (str): SQL文件的路径

    Returns:
        List[str]: 排序后的唯一表名列表

    Raises:
        FileNotFoundError: 如果文件不存在
        Exception: 处理过程中的其他错误
    """
    try:
        # 读取SQL文件
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        print("开始解析SQL文件...")
        # 解析SQL语句
        statements = sqlparse.parse(sql_content)
        print(f"找到 {len(statements)} 个SQL语句")
        
        # 收集所有表名
        all_tables = set()
        for i, statement in enumerate(statements, 1):
            # 跳过空语句和注释
            if statement.get_type() == 'UNKNOWN':
                continue
                
            print(f"\n处理第 {i} 个语句:")
            print(f"语句类型: {statement.get_type()}")
            print(f"语句内容: {statement}")
            
            # 提取表名
            tables = extract_tables_from_statement(statement)
            if tables:
                print(f"找到表名: {tables}")
            all_tables.update(tables)

        # 返回排序后的表名列表
        result = sorted(list(all_tables))
        print(f"\n总共找到 {len(result)} 个唯一表名")
        return result

    except FileNotFoundError:
        print(f"错误: 文件 '{sql_file_path}' 未找到。")
        return []
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return []


# --- 使用示例 ---
if __name__ == "__main__":
    # 将下面的路径替换为你的SQL文件路径
    sql_file = r"D:\work\demo\成都\表名.sql"
    
    tables = extract_table_names_from_sql(sql_file)
    
    if tables:
        print(f"\n在文件 '{sql_file}' 中找到的表名:")
        for table_name in tables:
            print(table_name)
    else:
        print(f"\n在文件 '{sql_file}' 中没有找到表名，或处理过程中发生错误。") 