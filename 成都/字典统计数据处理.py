import pandas as pd

# 加载数据
# 请将 'your_data.csv' 替换为您实际的文件路径
try:
    df = pd.read_csv('字典校验.csv')
except FileNotFoundError:
    print("错误: '字典校验.csv' 未找到。请确保CSV文件与脚本在同一目录下，或提供完整路径。")
    exit()

# 创建一个空列表，用于存储新的行数据
new_rows = []

# 遍历原始DataFrame的每一行
for index, row in df.iterrows():
    table_name = row['table_name']
    drty_desc_str = row['drty_desc']
    cnt = row['cnt']

    # 使用 '；' 分割 drty_desc 字符串
    descriptions = drty_desc_str.split('；')

    # 处理每一个拆分后的描述
    for desc in descriptions:
        if desc.strip():  # 确保描述不为空白
            # 从描述中提取关键部分 (例如：从 'N11不在clr_type字典范围内' 中提取 'clr_type')
            # 假设模式是 'xxx不在<key>字典范围内'
            parts = desc.split('不在')
            if len(parts) > 1:
                # 进一步拆分以获取精确的 key，并移除 '字典范围内'
                key_part = parts[1].replace('字典范围内', '').strip()
                if key_part: # 确保 key_part 不为空
                    new_rows.append({'table_name': table_name, 'drty_desc': key_part, 'cnt': cnt})
            # 如果存在其他不符合此模式的描述，您可能需要调整或添加额外的处理逻辑
            # 基于您的例子，似乎总是包含 '字典范围内'
            pass

# 从处理后的行数据创建新的DataFrame
new_df = pd.DataFrame(new_rows)

# 将新的DataFrame保存到新的CSV或Excel文件
new_df.to_csv('cleaned_data.csv', index=False, encoding='utf-8-sig')
# 或者保存为Excel文件：
# new_df.to_excel('cleaned_data.xlsx', index=False)

print("数据已成功转换并保存到 'cleaned_data.csv'")