import re
import pandas as pd
from pathlib import Path


def load_dictionary(excel_path):
    """加载数据字典Excel文件"""
    df = pd.read_excel(excel_path, sheet_name='列表')
    # 创建小写字典标识符到字典名称的映射
    return {str(k).lower(): v for k, v in zip(df['字典标识符'], df['字典名称'])}


def extract_content(line, dictionary):
    """提取内容并转换为英文-中文格式"""
    pattern = r'不在(.*?)字典范围内'
    matches = re.findall(pattern, line)

    results = []
    for match in matches:
        key = match.strip()
        if not key:
            continue
        # 查找字典中的中文名
        chinese_name = dictionary.get(key.lower(), "未知字典")
        results.append(f"{key}-{chinese_name}")

    return results


def process_files(text_file, excel_file, output_file):
    # 加载数据字典
    dictionary = load_dictionary(excel_file)

    with open(text_file, 'r', encoding='utf-8') as f_in, \
            open(output_file, 'w', encoding='utf-8') as f_out:

        for line in f_in:
            stripped_line = line.strip()
            if not stripped_line:  # 空行处理
                f_out.write("\n")
                continue

            extracted = extract_content(stripped_line, dictionary)
            if extracted:
                f_out.write("; ".join(extracted) + "\n")
            else:
                f_out.write("\n")


if __name__ == "__main__":
    # 文件路径
    base_dir = r"D:\work\demo\成都"
    text_file = Path(base_dir) / "规则去掉无用内容.txt"
    excel_file = Path(base_dir) / "数据组-数据字典-医保-20250414.xlsx"
    output_file = Path(base_dir) / "规则处理结果.txt"

    # 处理文件
    process_files(text_file, excel_file, output_file)

    print(f"处理完成！结果已保存到: {output_file}")
